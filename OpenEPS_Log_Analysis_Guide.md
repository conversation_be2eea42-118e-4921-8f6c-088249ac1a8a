# OpenEPS: Complete Log Analysis Guide

## Introduction

This guide provides comprehensive instructions for analyzing OpenEPS logs. After reading the Architecture Guide, you now understand the system components. This guide focuses on **interpreting the logs they generate**.

**Key Insight**: OpenEPS logs are distributed across multiple processes and files. Understanding the correlation between them is crucial for effective troubleshooting.

---

## Log File Locations and Naming Conventions

### Primary Log Files:

1. **Journal Files** (Transaction logs)
   - **Location**: `C:\Program Files\MicroTrax\OpenEPS\`
   - **Naming**: `Journal_Lane##.txt` (e.g., `Journal_Lane01.txt`)
   - **Purpose**: Detailed transaction processing logs per lane

2. **OpenEPS Engine Logs**
   - **Location**: Same as journal files
   - **Naming**: `OpenEPSLog.txt`, `MTXEPSLog.txt`
   - **Purpose**: Core engine operations and errors

3. **OpenEpsNet Service Logs**
   - **Location**: Service installation directory
   - **Naming**: `OpenEpsNetLog.txt`
   - **Purpose**: .NET service layer operations

4. **APL Client Logs**
   - **Location**: APL Client installation directory  
   - **Naming**: `APLClientLog.txt`
   - **Purpose**: Approved Product List updates

5. **VT2 Application Logs**
   - **Location**: VT2 installation directory
   - **Naming**: `VT2Log.txt`
   - **Purpose**: Virtual Terminal operations

### Log Rotation Pattern:
```
OpenEpsNetLog.txt          (Current day)
OpenEpsNetLog_20241211.txt (Previous day)
OpenEpsNetLog_20241210.txt (Day before)
```

---

## Log Format and Structure

### Standard Log Entry Format:
```
MM/DD/YY HH:NN:SS.ZZZ [SOURCE] MESSAGE
```

**Example**:
```
12/11/24 14:23:45.123 [OpenEPS] Transaction started: Lane 3, Amount $45.67
12/11/24 14:23:45.234 [SEPS] Routing to Chase host
12/11/24 14:23:46.456 [OpenEPS] Response received: 00 (Approved)
```

### Log Entry Components:
- **Timestamp**: `MM/DD/YY HH:NN:SS.ZZZ` (millisecond precision)
- **Source**: Component generating the log entry
- **Message**: Detailed information about the event

### Common Source Identifiers:
- `[OpenEPS]` - Core OpenEPS engine
- `[SEPS]` - ServerEPS communication
- `[VT2]` - Virtual Terminal
- `[APL]` - APL Client
- `[TERM]` - Terminal communication
- `[HOST]` - Host communication
- `[DEBUG]` - Debug information
- `[ERROR]` - Error conditions

---

## Transaction Flow in Logs

### Typical Successful Transaction Pattern:

```
12/11/24 14:23:45.123 [OpenEPS] MTX_POS_SET_PurchaseAmount: 4567 (cents)
12/11/24 14:23:45.124 [OpenEPS] MTX_POS_SET_TenderType: 2 (Credit)
12/11/24 14:23:45.125 [OpenEPS] MTX_POS_SET_LaneNumber: 3
12/11/24 14:23:45.126 [OpenEPS] MTX_POS_SendTransaction: Starting
12/11/24 14:23:45.127 [OpenEPS] Transaction State: trsNone → trsTenderType
12/11/24 14:23:45.128 [OpenEPS] Transaction State: trsTenderType → trsScatReady
12/11/24 14:23:45.200 [TERM] Requesting card data from terminal
12/11/24 14:23:47.500 [TERM] Card swiped: ****1234 (masked)
12/11/24 14:23:47.501 [OpenEPS] Transaction State: trsScatReady → trsValidateData
12/11/24 14:23:47.502 [OpenEPS] Card validation successful
12/11/24 14:23:47.503 [OpenEPS] Transaction State: trsValidateData → trsSent
12/11/24 14:23:47.504 [SEPS] Routing to host: Chase
12/11/24 14:23:47.505 [HOST] Sending ISO 8583 message to Chase
12/11/24 14:23:48.234 [HOST] Response received from Chase: 00
12/11/24 14:23:48.235 [OpenEPS] Transaction State: trsSent → trsApproved
12/11/24 14:23:48.236 [OpenEPS] MTX_POS_SET_ResponseCode: 00
12/11/24 14:23:48.237 [OpenEPS] MTX_POS_SET_AuthorizationNumber: 123456
12/11/24 14:23:48.238 [OpenEPS] Transaction completed successfully
```

### Key Patterns to Look For:

1. **State Transitions**: `Transaction State: X → Y`
2. **API Calls**: `MTX_POS_SET_*` and `MTX_POS_GET_*`
3. **Host Communication**: `Routing to host:` and `Response received:`
4. **Error Conditions**: `ERROR:`, `WARN:`, `EXCEPTION:`

---

## Error Code Reference

### Transaction Response Codes:

#### Success Codes:
- **00** - Approved
- **101** - Approved (MTX internal)

#### Common Decline Codes:
- **01** - Refer to Card Issuer
- **02** - Refer to Card Issuer (Special Condition)
- **03** - Invalid Merchant
- **04** - Pick Up Card
- **05** - Do Not Honor
- **06** - Error
- **07** - Pick Up Card (Special Condition)
- **12** - Invalid Transaction
- **13** - Invalid Amount
- **14** - Invalid Card Number
- **15** - No Such Issuer
- **30** - Format Error
- **41** - Lost Card - Pick Up
- **43** - Stolen Card - Pick Up
- **51** - Insufficient Funds
- **54** - Expired Card
- **55** - Incorrect PIN
- **57** - Transaction Not Permitted to Cardholder
- **58** - Transaction Not Permitted to Terminal
- **61** - Exceeds Withdrawal Amount Limit
- **62** - Restricted Card
- **63** - Security Violation
- **65** - Exceeds Withdrawal Frequency Limit
- **75** - Allowable Number of PIN Tries Exceeded
- **91** - Issuer or Switch Inoperative
- **96** - System Malfunction

### System Error Codes:

#### OpenEPS Internal Errors (0-99):
- **0** - ERR_OK (Success)
- **1** - ERR_EXCEPTION (General exception)
- **2** - ERR_WRONG_FILESIZE (File size mismatch)

#### ServerEPS Communication Errors (100-199):
- **101** - ERR_SERVER_EPS_EMPTY_LOGIN_XML
- **103** - ERR_SE_GetCodeFile_EXCEPTION
- **104** - ERR_SE_LogDeclinedTransaction
- **105** - ERR_UPLOADRECEIPT
- **106** - ERR_UPLOADFILESIZE_TOOBIG
- **107** - ERR_SE_SendOfflineFwd
- **108** - ERR_SE_SendTOR

#### Network/Communication Errors (200-255):
- **200** - ERR_REMOTE (Remote server error)
- **201** - ERR_EXCEPTION_DLL_WRAPPER
- **202** - ERR_EWSDLLoadException
- **203** - ERR_ESOAPHTTPException
- **204** - ERR_UNABLE_TO_LOAD_WSDL
- **206** - ERR_SSL_NOT_ESTABLISHED
- **210** - ERR_NO_RESPONSE
- **250** - ERR_TRY_AGAIN
- **251** - ERR_SERVER_EPS_TRN_DOWN
- **252** - ERR_SERVER_EPS_SVC_DOWN
- **253** - ERR_PROXY_DOWN

#### Lane Service Errors (8000+):
- **8001** - LS_INTERNAL_DATABASE_ERROR
- **8002** - LS_INVALID_FILE_UPLOAD_REQUEST
- **8101** - LS_STORE_NOT_DEFINED
- **8102** - LS_RECEIPT_ALREADY_UPLOADED

---

## Common Log Patterns and Their Meanings

### 1. **Normal Transaction Processing**

**Pattern**: Sequential state transitions with timing
```
[OpenEPS] Transaction State: trsNone → trsTenderType
[OpenEPS] Transaction State: trsTenderType → trsScatReady
[TERM] Requesting card data
[TERM] Card data received
[OpenEPS] Transaction State: trsScatReady → trsSent
[HOST] Response: 00 (Approved)
[OpenEPS] Transaction State: trsSent → trsApproved
```

**What it means**: Normal transaction flow with no issues.

### 2. **Host Communication Failure**

**Pattern**: Network errors followed by offline processing
```
[HOST] Sending to Chase host: *************:443
[HOST] ERROR: Connection timeout after 30 seconds
[OpenEPS] Host unavailable - switching to offline mode
[OpenEPS] Transaction approved offline
[OpenEPS] Queuing for later transmission
```

**What it means**: Network connectivity issue, system operating in offline mode.

### 3. **Terminal Communication Issues**

**Pattern**: Terminal errors and retry attempts
```
[TERM] Sending request to terminal on COM1
[TERM] ERROR: No response from terminal
[TERM] Retrying terminal communication (attempt 2/3)
[TERM] ERROR: Terminal communication failed
[OpenEPS] Manual entry required
```

**What it means**: Physical terminal (PIN pad) not responding.

### 4. **Configuration Problems**

**Pattern**: Missing or invalid configuration files
```
[OpenEPS] Loading configuration from Setup.txt
[OpenEPS] ERROR: Setup.txt file not found or corrupted
[OpenEPS] Using default configuration values
[OpenEPS] WARN: Some features may not work correctly
```

**What it means**: Configuration file issues affecting system behavior.

### 5. **Memory/Resource Issues**

**Pattern**: Resource exhaustion warnings
```
[OpenEPS] WARN: Low memory condition detected (Available: 45MB)
[OpenEPS] ERROR: Failed to allocate transaction buffer
[OpenEPS] CRITICAL: Memory usage exceeds 90% threshold
[OpenEPS] Attempting garbage collection
[OpenEPS] FATAL: System restart required
```

**What it means**: System running out of memory, may need restart.

### 6. **Offline Transaction Processing**

**Pattern**: Store and forward operations
```
[OpenEPS] Host connection lost - entering offline mode
[OpenEPS] Offline transaction #1: $45.67 approved locally
[OpenEPS] Offline transaction #2: $23.45 approved locally
[OpenEPS] Host connection restored
[OpenEPS] Uploading 2 offline transactions
[HOST] Offline transaction #1 confirmed by host
[HOST] Offline transaction #2 confirmed by host
[OpenEPS] All offline transactions processed
```

**What it means**: System working without host connection, then reconciling.

---

## Troubleshooting Common Issues

### Issue 1: "Transaction Stuck in Processing"

**Log Symptoms**:
```
[OpenEPS] Transaction State: trsSent
[HOST] Sending to host...
[No further entries for 60+ seconds]
```

**Diagnosis**: Host timeout or network issue
**Solution**: Check network connectivity, restart if needed

### Issue 2: "Terminal Not Responding"

**Log Symptoms**:
```
[TERM] ERROR: Terminal not responding on COM1
[TERM] Retrying... (attempt 3/3)
[TERM] ERROR: All retry attempts failed
```

**Diagnosis**: Physical terminal connection issue
**Solution**: Check cables, power, terminal settings

### Issue 3: "Configuration File Errors"

**Log Symptoms**:
```
[OpenEPS] ERROR: Setup.txt file corrupted
[OpenEPS] Using default values
[OpenEPS] WARN: Host routing may be incorrect
```

**Diagnosis**: Configuration file corruption
**Solution**: Request new configuration from host

### Issue 4: "Memory Leaks"

**Log Symptoms**:
```
[OpenEPS] Memory usage: 85% (increasing over time)
[OpenEPS] WARN: Potential memory leak detected
[OpenEPS] ERROR: Failed to allocate memory
```

**Diagnosis**: Memory leak in application
**Solution**: Restart OpenEPS service/application

### Issue 5: "SSL/TLS Certificate Issues"

**Log Symptoms**:
```
[HOST] ERROR: SSL handshake failed
[HOST] Certificate validation error
[HOST] ERR_SSL_NOT_ESTABLISHED (206)
```

**Diagnosis**: SSL certificate expired or invalid
**Solution**: Update certificates, check system time

---

## Advanced Log Analysis Techniques

### 1. **Correlating Multi-Process Logs**

When analyzing issues, you need to correlate logs across multiple processes:

**Example**: Transaction processing across components
```
# Journal_Lane03.txt
14:23:45.123 [OpenEPS] Starting transaction

# OpenEpsNetLog.txt
14:23:45.124 [OEN] Received transaction request from Lane 3

# APLClientLog.txt
14:23:45.125 [APL] Validating EBT eligibility for item 12345
```

**Technique**: Use timestamps to correlate events across log files.

### 2. **Identifying Performance Bottlenecks**

Look for timing patterns in logs:

```
14:23:45.123 [OpenEPS] Transaction started
14:23:45.124 [HOST] Sending to host
14:23:48.567 [HOST] Response received  ← 3.4 second delay!
14:23:48.568 [OpenEPS] Transaction completed
```

**Analysis**: 3.4-second host response time indicates network or host performance issue.

### 3. **Tracking Transaction Lifecycle**

Use sequence numbers and timestamps to follow transactions:

```
[OpenEPS] MTX Sequence: 123456 - Transaction started
[HOST] MTX Sequence: 123456 - Sent to host
[HOST] MTX Sequence: 123456 - Response: 00
[OpenEPS] MTX Sequence: 123456 - Completed
```

### 4. **Error Pattern Analysis**

Look for recurring error patterns:

```
# Pattern: Every 5 minutes
14:20:00 [HOST] Connection timeout
14:25:00 [HOST] Connection timeout
14:30:00 [HOST] Connection timeout
```

**Analysis**: Systematic network issue, possibly firewall or routing problem.

---

## Log Analysis Tools and Techniques

### 1. **Text Processing Commands**

**Find all errors in a date range**:
```bash
grep "ERROR" OpenEpsNetLog.txt | grep "12/11/24"
```

**Count transaction types**:
```bash
grep "MTX_POS_SET_TenderType" Journal_Lane01.txt | sort | uniq -c
```

**Find slow transactions** (>5 seconds):
```bash
grep -A5 -B5 "Transaction started" Journal_Lane01.txt | grep -E "(started|completed)"
```

### 2. **Log Correlation Techniques**

**Merge logs by timestamp**:
```bash
sort -k1,2 Journal_Lane*.txt OpenEpsNetLog.txt > merged_logs.txt
```

**Find transactions by amount**:
```bash
grep "PurchaseAmount.*4567" Journal_Lane*.txt
```

### 3. **Performance Analysis**

**Calculate average response times**:
Look for patterns like:
```
[HOST] Sending... (timestamp A)
[HOST] Response... (timestamp B)
Response time = B - A
```

---

## Critical Log Monitoring Points

### 1. **System Health Indicators**

Monitor these log patterns for system health:

- **Memory usage trends**: `grep "Memory usage" *.txt`
- **Error rate increases**: `grep "ERROR" *.txt | wc -l`
- **Host connectivity**: `grep "Host.*down\|unavailable" *.txt`
- **Transaction success rate**: Compare approved vs declined transactions

### 2. **Security Monitoring**

Watch for security-related log entries:

- **Failed authentication**: `grep "Authentication failed" *.txt`
- **Certificate issues**: `grep "Certificate\|SSL.*error" *.txt`
- **Unusual transaction patterns**: Large amounts, frequent voids

### 3. **Compliance Monitoring**

Track compliance-related events:

- **PCI DSS violations**: `grep "PCI\|Card.*data" *.txt`
- **Audit trail gaps**: Missing transaction logs
- **Data encryption issues**: `grep "Encryption.*failed" *.txt`

---

## Emergency Response Procedures

### When You See These Log Patterns, Take Immediate Action:

1. **FATAL/CRITICAL Errors**:
   ```
   [OpenEPS] FATAL: System corruption detected
   [OpenEPS] CRITICAL: Database integrity compromised
   ```
   **Action**: Stop system immediately, contact support

2. **Security Violations**:
   ```
   [OpenEPS] Security violation detected
   [OpenEPS] Unauthorized access attempt
   ```
   **Action**: Isolate system, review security logs

3. **Data Loss Indicators**:
   ```
   [OpenEPS] ERROR: Transaction log corrupted
   [OpenEPS] ERROR: Unable to write to journal
   ```
   **Action**: Stop processing, backup existing data

---

## Best Practices for Log Analysis

### 1. **Establish Baselines**
- Know normal transaction volumes per hour/day
- Understand typical response times
- Recognize normal error rates

### 2. **Use Structured Approach**
1. **Identify the timeframe** of the issue
2. **Gather all relevant log files** from that period
3. **Correlate events** across different components
4. **Look for patterns** rather than isolated events
5. **Trace complete transaction flows** when possible

### 3. **Document Findings**
- Keep a log analysis journal
- Document recurring issues and solutions
- Share findings with team members

### 4. **Proactive Monitoring**
- Set up automated log monitoring
- Create alerts for critical error patterns
- Regular log review and cleanup

---

## Conclusion

Effective OpenEPS log analysis requires understanding both the system architecture and the specific log patterns each component generates. By following the techniques in this guide, you'll be able to:

- Quickly identify the root cause of issues
- Correlate events across multiple system components
- Recognize patterns that indicate systemic problems
- Take appropriate action based on log evidence

Remember: **Logs tell the story of what happened**. Your job is to read that story accurately and act on the information it provides.

The next guide will cover Business Process Flow Documentation to help you understand how these technical logs relate to actual business operations.
