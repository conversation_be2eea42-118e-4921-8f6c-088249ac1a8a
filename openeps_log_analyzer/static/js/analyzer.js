/**
 * OpenEPS Log Analyzer Frontend JavaScript
 * Handles user interactions and displays analysis results
 */

class OpenEPSAnalyzer {
    constructor() {
        this.initializeEventListeners();
        this.currentAnalysis = null;
    }

    initializeEventListeners() {
        // Analyze button click
        document.getElementById('analyzeBtn').addEventListener('click', () => {
            this.analyzeLog();
        });

        // File upload handler
        document.getElementById('logFile').addEventListener('change', (event) => {
            this.handleFileUpload(event);
        });

        // Clear error alerts when user starts typing
        document.getElementById('logContent').addEventListener('input', () => {
            this.hideError();
        });
    }

    async handleFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Check file size (16MB limit)
        if (file.size > 16 * 1024 * 1024) {
            this.showError('File size exceeds 16MB limit. Please use a smaller file.');
            return;
        }

        try {
            const content = await this.readFileContent(file);
            document.getElementById('logContent').value = content;
            this.hideError();
        } catch (error) {
            this.showError('Failed to read file: ' + error.message);
        }
    }

    readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('File reading failed'));
            reader.readAsText(file);
        });
    }

    async analyzeLog() {
        const logContent = document.getElementById('logContent').value.trim();
        const analysisType = document.getElementById('analysisType').value;

        if (!logContent) {
            this.showError('Please provide log content to analyze.');
            return;
        }

        this.showLoading(true);
        this.hideError();

        try {
            const response = await fetch('/analyze', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    log_content: logContent,
                    analysis_type: analysisType
                })
            });

            const result = await response.json();

            if (result.success) {
                this.currentAnalysis = result;
                this.displayResults(result);
            } else {
                this.showError(result.error || 'Analysis failed');
            }
        } catch (error) {
            this.showError('Network error: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    displayResults(analysis) {
        // Show results section
        document.getElementById('resultsSection').style.display = 'block';
        
        // Scroll to results
        document.getElementById('resultsSection').scrollIntoView({ 
            behavior: 'smooth' 
        });

        // Update executive summary
        this.updateExecutiveSummary(analysis.explanations.executive_summary);

        // Update key metrics
        this.updateKeyMetrics(analysis);

        // Update tab contents
        this.updateOverviewTab(analysis);
        this.updateTransactionTab(analysis);
        this.updateErrorTab(analysis);
        this.updatePerformanceTab(analysis);
        this.updateBusinessTab(analysis);
        this.updateRecommendationTab(analysis);
    }

    updateExecutiveSummary(summary) {
        const summaryElement = document.getElementById('executiveSummary');
        summaryElement.innerHTML = this.formatMarkdown(summary);
    }

    updateKeyMetrics(analysis) {
        const patterns = analysis.patterns || {};
        const businessContext = analysis.business_context || {};

        // Health Score
        const healthScore = Math.round(patterns.health_score || 0);
        document.getElementById('healthScore').textContent = healthScore;
        
        // Total Transactions
        const totalTransactions = patterns.transaction_analysis?.total_transactions || 0;
        document.getElementById('totalTransactions').textContent = totalTransactions.toLocaleString();

        // Approval Rate
        const approvalRate = Math.round((patterns.transaction_analysis?.approval_rate || 0) * 100);
        document.getElementById('approvalRate').textContent = approvalRate + '%';

        // Error Rate
        const errorRate = Math.round((patterns.error_analysis?.error_rate || 0) * 100);
        document.getElementById('errorRate').textContent = errorRate + '%';
    }

    updateOverviewTab(analysis) {
        const content = document.getElementById('overviewContent');
        const logSummary = analysis.log_summary || {};
        const patterns = analysis.patterns || {};

        let html = `
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-info-circle me-2"></i>Log Summary</h6>
                    <ul class="list-unstyled">
                        <li><strong>Total Entries:</strong> ${logSummary.total_entries?.toLocaleString() || 'N/A'}</li>
                        <li><strong>Time Span:</strong> ${this.formatTimeSpan(logSummary.time_span)}</li>
                        <li><strong>Components:</strong> ${(logSummary.components_detected || []).join(', ')}</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6><i class="fas fa-heartbeat me-2"></i>System Health</h6>
                    ${this.renderSystemHealth(patterns.system_health)}
                </div>
            </div>
            
            <hr>
            
            <div class="row">
                <div class="col-12">
                    <h6><i class="fas fa-chart-line me-2"></i>Detailed Analysis</h6>
                    ${this.formatMarkdown(analysis.explanations?.detailed_analysis || 'No detailed analysis available')}
                </div>
            </div>
        `;

        content.innerHTML = html;
    }

    updateTransactionTab(analysis) {
        const content = document.getElementById('transactionContent');
        const transactionAnalysis = analysis.patterns?.transaction_analysis || {};
        const explanations = analysis.explanations || {};

        let html = `
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">${transactionAnalysis.total_transactions || 0}</h5>
                            <p class="card-text">Total Transactions</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">${Math.round((transactionAnalysis.approval_rate || 0) * 100)}%</h5>
                            <p class="card-text">Approval Rate</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">${Object.keys(transactionAnalysis.response_codes || {}).length}</h5>
                            <p class="card-text">Response Types</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <h6>Transaction Types</h6>
                    ${this.renderCounter(transactionAnalysis.transaction_types)}
                </div>
                <div class="col-md-6">
                    <h6>Response Codes</h6>
                    ${this.renderResponseCodes(transactionAnalysis.response_codes)}
                </div>
            </div>

            <hr>

            <div class="row">
                <div class="col-12">
                    <h6>Transaction Flow Analysis</h6>
                    ${this.formatMarkdown(explanations.transaction_flow_analysis || 'No transaction flow analysis available')}
                </div>
            </div>
        `;

        content.innerHTML = html;
    }

    updateErrorTab(analysis) {
        const content = document.getElementById('errorContent');
        const errorAnalysis = analysis.patterns?.error_analysis || {};
        const errorExplanations = analysis.explanations?.error_explanations || [];

        let html = `
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-danger">${errorAnalysis.total_errors || 0}</h5>
                            <p class="card-text">Total Errors</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">${Math.round((errorAnalysis.error_rate || 0) * 100)}%</h5>
                            <p class="card-text">Error Rate</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title ${this.getSeverityClass(errorAnalysis.severity_assessment)}">${(errorAnalysis.severity_assessment || 'none').toUpperCase()}</h5>
                            <p class="card-text">Severity</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-12">
                    <h6>Error Categories</h6>
                    ${this.renderErrorCategories(errorAnalysis.error_categories)}
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <h6>Detailed Error Explanations</h6>
                    ${this.renderErrorExplanations(errorExplanations)}
                </div>
            </div>
        `;

        content.innerHTML = html;
    }

    updatePerformanceTab(analysis) {
        const content = document.getElementById('performanceContent');
        const performanceAnalysis = analysis.patterns?.performance_analysis || {};

        let html = `
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">${(performanceAnalysis.average_response_time || 0).toFixed(2)}s</h5>
                            <p class="card-text">Avg Response Time</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">${(performanceAnalysis.max_response_time || 0).toFixed(2)}s</h5>
                            <p class="card-text">Max Response Time</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">${performanceAnalysis.slow_transaction_count || 0}</h5>
                            <p class="card-text">Slow Transactions</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-danger">${performanceAnalysis.timeout_count || 0}</h5>
                            <p class="card-text">Timeouts</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <h6>Slow Transactions</h6>
                    ${this.renderSlowTransactions(performanceAnalysis.slow_transactions)}
                </div>
                <div class="col-md-6">
                    <h6>Timeout Events</h6>
                    ${this.renderTimeoutEvents(performanceAnalysis.timeout_events)}
                </div>
            </div>
        `;

        content.innerHTML = html;
    }

    updateBusinessTab(analysis) {
        const content = document.getElementById('businessContent');
        const businessContext = analysis.business_context || {};
        const explanations = analysis.explanations || {};

        let html = `
            <div class="row mb-4">
                <div class="col-12">
                    <h6><i class="fas fa-dollar-sign me-2"></i>Revenue Impact</h6>
                    ${this.renderRevenueImpact(businessContext.revenue_impact)}
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-12">
                    <h6><i class="fas fa-users me-2"></i>Customer Experience</h6>
                    ${this.renderCustomerExperience(businessContext.customer_experience)}
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <h6><i class="fas fa-chart-bar me-2"></i>Business Impact Assessment</h6>
                    ${this.formatMarkdown(explanations.business_impact_assessment || 'No business impact assessment available')}
                </div>
            </div>
        `;

        content.innerHTML = html;
    }

    updateRecommendationTab(analysis) {
        const content = document.getElementById('recommendationContent');
        const recommendations = analysis.recommendations || [];
        const explanationRecommendations = analysis.explanations?.recommendations || [];

        let html = `
            <div class="row">
                <div class="col-12">
                    <h6><i class="fas fa-lightbulb me-2"></i>Immediate Actions</h6>
                    ${this.renderRecommendations([...recommendations, ...explanationRecommendations])}
                </div>
            </div>
        `;

        content.innerHTML = html;
    }

    // Utility rendering methods
    renderSystemHealth(systemHealth) {
        if (!systemHealth) return '<p>No system health data available</p>';

        let html = '<ul class="list-unstyled">';
        for (const [component, status] of Object.entries(systemHealth)) {
            const statusClass = status === 'good' ? 'text-success' : status === 'poor' ? 'text-danger' : 'text-warning';
            const statusIcon = status === 'good' ? 'check-circle' : status === 'poor' ? 'times-circle' : 'question-circle';
            html += `<li><i class="fas fa-${statusIcon} ${statusClass} me-2"></i>${component.replace('_', ' ')}: ${status}</li>`;
        }
        html += '</ul>';
        return html;
    }

    renderCounter(counter) {
        if (!counter || Object.keys(counter).length === 0) {
            return '<p class="text-muted">No data available</p>';
        }

        let html = '<ul class="list-unstyled">';
        for (const [key, value] of Object.entries(counter)) {
            html += `<li><span class="badge bg-primary me-2">${value}</span>${key}</li>`;
        }
        html += '</ul>';
        return html;
    }

    renderResponseCodes(responseCodes) {
        if (!responseCodes || Object.keys(responseCodes).length === 0) {
            return '<p class="text-muted">No response codes found</p>';
        }

        let html = '<ul class="list-unstyled">';
        for (const [code, count] of Object.entries(responseCodes)) {
            const badgeClass = code === '00' ? 'bg-success' : 'bg-warning';
            html += `<li><span class="badge ${badgeClass} me-2">${count}</span>Code ${code}</li>`;
        }
        html += '</ul>';
        return html;
    }

    renderRecommendations(recommendations) {
        if (!recommendations || recommendations.length === 0) {
            return '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>No immediate actions required. System appears to be operating normally.</div>';
        }

        let html = '';
        recommendations.forEach(rec => {
            const priorityClass = rec.priority === 'High' ? 'danger' : rec.priority === 'Medium' ? 'warning' : 'info';
            html += `
                <div class="card mb-3 border-${priorityClass}">
                    <div class="card-header bg-${priorityClass} text-white">
                        <h6 class="mb-0">${rec.title || 'Recommendation'}</h6>
                        <small>Priority: ${rec.priority || 'Medium'} | Category: ${rec.category || 'General'}</small>
                    </div>
                    <div class="card-body">
                        <p>${rec.description || 'No description available'}</p>
                        ${rec.actions ? `
                            <h6>Recommended Actions:</h6>
                            <ul>
                                ${rec.actions.map(action => `<li>${action}</li>`).join('')}
                            </ul>
                        ` : ''}
                    </div>
                </div>
            `;
        });

        return html;
    }

    renderErrorCategories(errorCategories) {
        if (!errorCategories || Object.keys(errorCategories).length === 0) {
            return '<div class="alert alert-success">No errors detected in the analyzed logs.</div>';
        }

        let html = '<div class="row">';
        for (const [category, data] of Object.entries(errorCategories)) {
            if (data.count > 0) {
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">${category.replace('_', ' ').toUpperCase()}</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Count:</strong> ${data.count}</p>
                                ${data.examples && data.examples.length > 0 ? `
                                    <p><strong>Example:</strong></p>
                                    <code class="small">${data.examples[0]}</code>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
            }
        }
        html += '</div>';
        return html;
    }

    renderErrorExplanations(errorExplanations) {
        if (!errorExplanations || errorExplanations.length === 0) {
            return '<div class="alert alert-info">No detailed error explanations available.</div>';
        }

        let html = '';
        errorExplanations.forEach(explanation => {
            html += `
                <div class="card mb-3 border-danger">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0">${explanation.title}</h6>
                        <small>Occurrences: ${explanation.occurrence_count}</small>
                    </div>
                    <div class="card-body">
                        <p><strong>Technical Cause:</strong> ${explanation.technical_cause}</p>
                        <p><strong>Business Impact:</strong> ${explanation.business_impact}</p>

                        ${explanation.root_causes ? `
                            <h6>Possible Root Causes:</h6>
                            <ul>
                                ${explanation.root_causes.map(cause => `<li>${cause}</li>`).join('')}
                            </ul>
                        ` : ''}

                        ${explanation.immediate_actions ? `
                            <h6>Immediate Actions:</h6>
                            <ul>
                                ${explanation.immediate_actions.map(action => `<li>${action}</li>`).join('')}
                            </ul>
                        ` : ''}
                    </div>
                </div>
            `;
        });

        return html;
    }

    renderSlowTransactions(slowTransactions) {
        if (!slowTransactions || slowTransactions.length === 0) {
            return '<div class="alert alert-success">No slow transactions detected.</div>';
        }

        let html = '<div class="list-group">';
        slowTransactions.forEach(transaction => {
            html += `
                <div class="list-group-item">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">Sequence: ${transaction.sequence}</h6>
                        <small class="text-danger">${transaction.duration.toFixed(2)}s</small>
                    </div>
                    <p class="mb-1">Started: ${new Date(transaction.start_time).toLocaleTimeString()}</p>
                    <small>Ended: ${new Date(transaction.end_time).toLocaleTimeString()}</small>
                </div>
            `;
        });
        html += '</div>';
        return html;
    }

    renderTimeoutEvents(timeoutEvents) {
        if (!timeoutEvents || timeoutEvents.length === 0) {
            return '<div class="alert alert-success">No timeout events detected.</div>';
        }

        let html = '<div class="list-group">';
        timeoutEvents.forEach(event => {
            html += `
                <div class="list-group-item">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1 text-danger">Timeout Event</h6>
                        <small>${new Date(event.timestamp).toLocaleTimeString()}</small>
                    </div>
                    <p class="mb-1"><code class="small">${event.message}</code></p>
                </div>
            `;
        });
        html += '</div>';
        return html;
    }

    renderRevenueImpact(revenueImpact) {
        if (!revenueImpact) {
            return '<div class="alert alert-info">No revenue impact data available.</div>';
        }

        return `
            <div class="row">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">$${(revenueImpact.total_transaction_value || 0).toFixed(2)}</h5>
                            <p class="card-text">Total Transaction Value</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-danger">$${(revenueImpact.declined_transaction_value || 0).toFixed(2)}</h5>
                            <p class="card-text">Declined Value</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">$${(revenueImpact.offline_transaction_value || 0).toFixed(2)}</h5>
                            <p class="card-text">Offline Value</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info">$${(revenueImpact.estimated_revenue_at_risk || 0).toFixed(2)}</h5>
                            <p class="card-text">Revenue at Risk</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderCustomerExperience(customerExperience) {
        if (!customerExperience) {
            return '<div class="alert alert-info">No customer experience data available.</div>';
        }

        const satisfactionScore = customerExperience.customer_satisfaction_score || 0;
        const scoreClass = satisfactionScore >= 80 ? 'success' : satisfactionScore >= 60 ? 'warning' : 'danger';

        return `
            <div class="row">
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-${scoreClass}">${satisfactionScore.toFixed(0)}/100</h5>
                            <p class="card-text">Satisfaction Score</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">${(customerExperience.average_transaction_time || 0).toFixed(2)}s</h5>
                            <p class="card-text">Avg Transaction Time</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">${customerExperience.customer_facing_errors || 0}</h5>
                            <p class="card-text">Customer-Facing Errors</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Utility methods
    formatMarkdown(text) {
        if (!text) return '';
        
        // Simple markdown-like formatting
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/^### (.*$)/gm, '<h6>$1</h6>')
            .replace(/^## (.*$)/gm, '<h5>$1</h5>')
            .replace(/^# (.*$)/gm, '<h4>$1</h4>')
            .replace(/\n/g, '<br>');
    }

    formatTimeSpan(timeSpan) {
        if (!timeSpan || !timeSpan.duration_seconds) return 'N/A';
        
        const seconds = timeSpan.duration_seconds;
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            return `${minutes}m`;
        } else {
            return `${Math.round(seconds)}s`;
        }
    }

    getSeverityClass(severity) {
        switch (severity) {
            case 'critical': return 'text-danger';
            case 'high': return 'text-danger';
            case 'medium': return 'text-warning';
            case 'low': return 'text-info';
            default: return 'text-success';
        }
    }

    showLoading(show) {
        const spinner = document.querySelector('.loading-spinner');
        const button = document.getElementById('analyzeBtn');
        
        if (show) {
            spinner.style.display = 'block';
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing...';
        } else {
            spinner.style.display = 'none';
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-search me-2"></i>Analyze Logs';
        }
    }

    showError(message) {
        const errorAlert = document.getElementById('errorAlert');
        const errorMessage = document.getElementById('errorMessage');
        
        errorMessage.textContent = message;
        errorAlert.style.display = 'block';
        
        // Scroll to error
        errorAlert.scrollIntoView({ behavior: 'smooth' });
    }

    hideError() {
        document.getElementById('errorAlert').style.display = 'none';
    }
}

// Initialize the analyzer when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new OpenEPSAnalyzer();
});
