"""
OpenEPS Log Parser
Parses OpenEPS log files and extracts structured information
"""

import re
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class OpenEPSLogParser:
    """
    Comprehensive parser for OpenEPS log files
    Handles multiple log formats and extracts structured data
    """
    
    def __init__(self):
        # OpenEPS log format patterns
        self.log_patterns = {
            # Standard format: MM/DD/YY HH:NN:SS.ZZZ [SOURCE] MESSAGE
            'standard': re.compile(
                r'(\d{2}/\d{2}/\d{2})\s+(\d{2}:\d{2}:\d{2}\.\d{3})\s+(?:\[([^\]]+)\])?\s*(.+)'
            ),
            
            # Alternative format: YYYY-MM-DD HH:NN:SS.ZZZ [SOURCE] MESSAGE
            'iso_format': re.compile(
                r'(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2}:\d{2}\.\d{3})\s+(?:\[([^\]]+)\])?\s*(.+)'
            ),
            
            # Simple format: HH:NN:SS MESSAGE
            'simple': re.compile(
                r'(\d{2}:\d{2}:\d{2})\s+(.+)'
            )
        }
        
        # Source component mappings
        self.source_mappings = {
            'OpenEPS': 'OpenEPS Engine',
            'SEPS': 'ServerEPS',
            'VT2': 'Virtual Terminal',
            'APL': 'APL Client',
            'TERM': 'Terminal',
            'HOST': 'Host Communication',
            'DEBUG': 'Debug',
            'ERROR': 'Error',
            'WARN': 'Warning',
            'INFO': 'Information'
        }
        
        # Transaction state patterns
        self.transaction_states = [
            'trsNone', 'trsTenderType', 'trsScatReady', 'trsTransType',
            'trsValidateData', 'trsAcquireData', 'trsSent', 'trsHostStatus',
            'trsApproved', 'trsDeclined', 'trsCancelled', 'trsVoided'
        ]
        
        # Error code patterns
        self.error_patterns = {
            'response_codes': re.compile(r'Response(?:\s+Code)?:\s*(\d{2,3})'),
            'error_codes': re.compile(r'Error(?:\s+Code)?:\s*(\d+)'),
            'mtx_sequence': re.compile(r'MTX\s+Sequence(?:\s+Number)?:\s*(\d+)'),
            'amount': re.compile(r'Amount:\s*\$?(\d+\.?\d*)'),
            'card_number': re.compile(r'\*{4}(\d{4})'),
            'auth_number': re.compile(r'Auth(?:orization)?(?:\s+Number)?:\s*(\w+)')
        }
    
    def parse_logs(self, log_content: str) -> List[Dict]:
        """
        Parse log content and return structured log entries
        """
        if not log_content.strip():
            return []
        
        lines = log_content.strip().split('\n')
        parsed_entries = []
        
        for line_num, line in enumerate(lines, 1):
            if not line.strip():
                continue
                
            try:
                parsed_entry = self._parse_log_line(line, line_num)
                if parsed_entry:
                    parsed_entries.append(parsed_entry)
            except Exception as e:
                logger.warning(f"Failed to parse line {line_num}: {e}")
                # Add unparsed line for completeness
                parsed_entries.append({
                    'line_number': line_num,
                    'raw_line': line,
                    'parse_error': str(e),
                    'timestamp': None,
                    'source': 'UNKNOWN',
                    'message': line,
                    'level': 'UNKNOWN'
                })
        
        logger.info(f"Parsed {len(parsed_entries)} log entries from {len(lines)} lines")
        return parsed_entries
    
    def _parse_log_line(self, line: str, line_num: int) -> Optional[Dict]:
        """
        Parse a single log line and extract structured information
        """
        line = line.strip()
        if not line:
            return None
        
        # Try different log format patterns
        for format_name, pattern in self.log_patterns.items():
            match = pattern.match(line)
            if match:
                return self._extract_log_data(match, format_name, line, line_num)
        
        # If no pattern matches, treat as unstructured
        return {
            'line_number': line_num,
            'raw_line': line,
            'timestamp': None,
            'source': self._detect_source(line),
            'message': line,
            'level': self._detect_level(line),
            'structured_data': self._extract_structured_data(line)
        }
    
    def _extract_log_data(self, match, format_name: str, line: str, line_num: int) -> Dict:
        """
        Extract structured data from regex match
        """
        if format_name == 'standard':
            date_str, time_str, source, message = match.groups()
            timestamp = self._parse_timestamp(f"{date_str} {time_str}", 'MM/dd/yy HH:MM:SS.fff')
        elif format_name == 'iso_format':
            date_str, time_str, source, message = match.groups()
            timestamp = self._parse_timestamp(f"{date_str} {time_str}", 'YYYY-MM-dd HH:MM:SS.fff')
        elif format_name == 'simple':
            time_str, message = match.groups()
            source = None
            # Use today's date for simple format
            today = datetime.now().strftime('%Y-%m-%d')
            timestamp = self._parse_timestamp(f"{today} {time_str}", 'YYYY-MM-dd HH:MM:SS')
        else:
            timestamp = None
            source = None
            message = line
        
        return {
            'line_number': line_num,
            'raw_line': line,
            'timestamp': timestamp,
            'source': self._normalize_source(source) if source else self._detect_source(message),
            'message': message.strip() if message else '',
            'level': self._detect_level(message if message else line),
            'structured_data': self._extract_structured_data(message if message else line)
        }
    
    def _parse_timestamp(self, timestamp_str: str, format_hint: str) -> Optional[datetime]:
        """
        Parse timestamp string into datetime object
        """
        try:
            if 'MM/dd/yy' in format_hint:
                # Handle MM/dd/yy HH:MM:SS.fff format
                return datetime.strptime(timestamp_str, '%m/%d/%y %H:%M:%S.%f')
            elif 'YYYY-MM-dd' in format_hint:
                # Handle YYYY-MM-dd HH:MM:SS.fff format
                return datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
            elif 'HH:MM:SS' in format_hint:
                # Handle simple time format (use today's date)
                today = datetime.now().strftime('%Y-%m-%d')
                return datetime.strptime(f"{today} {timestamp_str}", '%Y-%m-%d %H:%M:%S')
        except ValueError as e:
            logger.debug(f"Failed to parse timestamp '{timestamp_str}': {e}")
            return None
    
    def _normalize_source(self, source: str) -> str:
        """
        Normalize source component names
        """
        if not source:
            return 'UNKNOWN'
        
        source = source.strip().upper()
        return self.source_mappings.get(source, source)
    
    def _detect_source(self, message: str) -> str:
        """
        Detect source component from message content
        """
        message_upper = message.upper()
        
        # Check for specific component indicators
        if 'MTX_POS_' in message_upper:
            return 'POS Interface'
        elif 'SERVEREPS' in message_upper or 'SEPS' in message_upper:
            return 'ServerEPS'
        elif 'VT2' in message_upper or 'VIRTUAL TERMINAL' in message_upper:
            return 'Virtual Terminal'
        elif 'APL' in message_upper or 'APPROVED PRODUCT' in message_upper:
            return 'APL Client'
        elif 'TERMINAL' in message_upper or 'SCAT' in message_upper or 'XPI' in message_upper:
            return 'Terminal'
        elif 'HOST' in message_upper or 'CHASE' in message_upper or 'SHAZAM' in message_upper:
            return 'Host Communication'
        elif 'OPENEPS' in message_upper:
            return 'OpenEPS Engine'
        else:
            return 'UNKNOWN'
    
    def _detect_level(self, message: str) -> str:
        """
        Detect log level from message content
        """
        message_upper = message.upper()
        
        if any(keyword in message_upper for keyword in ['FATAL', 'CRITICAL']):
            return 'FATAL'
        elif any(keyword in message_upper for keyword in ['ERROR', 'EXCEPTION', 'FAILED']):
            return 'ERROR'
        elif any(keyword in message_upper for keyword in ['WARN', 'WARNING']):
            return 'WARN'
        elif any(keyword in message_upper for keyword in ['DEBUG']):
            return 'DEBUG'
        elif any(keyword in message_upper for keyword in ['TRACE']):
            return 'TRACE'
        else:
            return 'INFO'
    
    def _extract_structured_data(self, message: str) -> Dict:
        """
        Extract structured data from message content
        """
        structured_data = {}
        
        # Extract common patterns
        for pattern_name, pattern in self.error_patterns.items():
            match = pattern.search(message)
            if match:
                structured_data[pattern_name] = match.group(1)
        
        # Extract transaction state transitions
        state_transition = self._extract_state_transition(message)
        if state_transition:
            structured_data['state_transition'] = state_transition
        
        # Extract API calls
        api_call = self._extract_api_call(message)
        if api_call:
            structured_data['api_call'] = api_call
        
        # Extract transaction type
        transaction_type = self._extract_transaction_type(message)
        if transaction_type:
            structured_data['transaction_type'] = transaction_type
        
        return structured_data
    
    def _extract_state_transition(self, message: str) -> Optional[Dict]:
        """
        Extract transaction state transitions
        """
        # Pattern: "Transaction State: oldState → newState"
        pattern = re.compile(r'Transaction\s+State:\s*(\w+)\s*[→->]\s*(\w+)')
        match = pattern.search(message)
        if match:
            return {
                'from_state': match.group(1),
                'to_state': match.group(2)
            }
        
        # Pattern: "State: oldState → newState"
        pattern = re.compile(r'State:\s*(\w+)\s*[→->]\s*(\w+)')
        match = pattern.search(message)
        if match:
            return {
                'from_state': match.group(1),
                'to_state': match.group(2)
            }
        
        return None
    
    def _extract_api_call(self, message: str) -> Optional[str]:
        """
        Extract API function calls
        """
        # Pattern: MTX_POS_FUNCTION_NAME
        pattern = re.compile(r'(MTX_POS_\w+)')
        match = pattern.search(message)
        if match:
            return match.group(1)
        
        return None
    
    def _extract_transaction_type(self, message: str) -> Optional[str]:
        """
        Extract transaction type from message
        """
        message_upper = message.upper()
        
        transaction_types = [
            'PURCHASE', 'RETURN', 'VOID', 'BALANCE INQUIRY', 'PRE-AUTH',
            'ACTIVATION', 'RECHARGE', 'DEACTIVATION', 'FORCE'
        ]
        
        for trans_type in transaction_types:
            if trans_type in message_upper:
                return trans_type.title()
        
        return None
