#!/usr/bin/env python3
"""
Install missing dependencies for OpenEPS Log Analyzer
"""

import subprocess
import sys

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing OpenEPS Log Analyzer dependencies...")
    
    try:
        # Install requirements
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    required_modules = [
        'flask',
        'flask_cors',
        'requests'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module}")
            missing_modules.append(module)
    
    return len(missing_modules) == 0

if __name__ == '__main__':
    print("🚀 OpenEPS Log Analyzer - Dependency Installer")
    print("=" * 50)
    
    if install_dependencies():
        if test_imports():
            print("\n✅ All dependencies are ready!")
            print("You can now run: python app.py")
        else:
            print("\n❌ Some imports failed. Try running:")
            print("pip install flask flask-cors requests")
    else:
        print("\n❌ Installation failed. Try running manually:")
        print("pip install -r requirements.txt")
