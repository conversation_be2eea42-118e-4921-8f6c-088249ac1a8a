<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenEPS Log Analyzer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .log-textarea {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background-color: #1e1e1e;
            color: #d4d4d4;
            border: 1px solid #404040;
        }
        
        .analysis-card {
            border-left: 4px solid #007bff;
            margin-bottom: 20px;
        }
        
        .health-score {
            font-size: 2rem;
            font-weight: bold;
        }
        
        .health-excellent { color: #28a745; }
        .health-good { color: #ffc107; }
        .health-poor { color: #dc3545; }
        
        .recommendation-high { border-left-color: #dc3545; }
        .recommendation-medium { border-left-color: #ffc107; }
        .recommendation-low { border-left-color: #28a745; }
        
        .loading-spinner {
            display: none;
        }
        
        .log-entry {
            font-family: 'Courier New', monospace;
            font-size: 11px;
            padding: 2px 5px;
            margin: 1px 0;
            border-radius: 3px;
        }
        
        .log-error { background-color: #f8d7da; color: #721c24; }
        .log-warn { background-color: #fff3cd; color: #856404; }
        .log-info { background-color: #d1ecf1; color: #0c5460; }
        .log-debug { background-color: #e2e3e5; color: #383d41; }
        
        .component-badge {
            font-size: 0.75rem;
            margin-right: 5px;
        }
        
        .timeline-item {
            border-left: 2px solid #007bff;
            padding-left: 15px;
            margin-bottom: 10px;
            position: relative;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 5px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #007bff;
        }
        
        .metric-card {
            text-align: center;
            padding: 20px;
            border-radius: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-bottom: 20px;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="row bg-primary text-white py-3 mb-4">
            <div class="col">
                <h1 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    OpenEPS Log Analyzer
                </h1>
                <p class="mb-0">Intelligent analysis and explanation of OpenEPS payment system logs</p>
            </div>
        </div>

        <!-- Input Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-upload me-2"></i>
                            Log Input
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- File Upload -->
                        <div class="mb-3">
                            <label for="logFile" class="form-label">Upload Log File</label>
                            <input type="file" class="form-control" id="logFile" accept=".txt,.log">
                            <div class="form-text">Supported formats: .txt, .log (Max 16MB)</div>
                        </div>
                        
                        <!-- Text Input -->
                        <div class="mb-3">
                            <label for="logContent" class="form-label">Or Paste Log Content</label>
                            <textarea 
                                class="form-control log-textarea" 
                                id="logContent" 
                                rows="15" 
                                placeholder="Paste your OpenEPS log content here...

Example:
12/11/24 14:23:45.123 [OpenEPS] MTX_POS_SET_PurchaseAmount: 4567
12/11/24 14:23:45.124 [OpenEPS] MTX_POS_SET_TenderType: 2
12/11/24 14:23:45.125 [OpenEPS] MTX_POS_SendTransaction: Starting
12/11/24 14:23:45.200 [TERM] Requesting card data from terminal
12/11/24 14:23:47.500 [TERM] Card swiped: ****1234
12/11/24 14:23:47.504 [HOST] Routing to Chase host
12/11/24 14:23:48.234 [HOST] Response received: 00 (Approved)
12/11/24 14:23:48.235 [OpenEPS] Transaction completed successfully"></textarea>
                        </div>
                        
                        <!-- Analysis Options -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="analysisType" class="form-label">Analysis Type</label>
                                <select class="form-select" id="analysisType">
                                    <option value="comprehensive">Comprehensive Analysis</option>
                                    <option value="quick">Quick Analysis</option>
                                    <option value="business">Business Focus</option>
                                    <option value="technical">Technical Focus</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Analyze Button -->
                        <button type="button" class="btn btn-primary btn-lg" id="analyzeBtn">
                            <i class="fas fa-search me-2"></i>
                            Analyze Logs
                        </button>
                        
                        <!-- Loading Spinner -->
                        <div class="loading-spinner text-center mt-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Analyzing...</span>
                            </div>
                            <p class="mt-2">Analyzing your OpenEPS logs...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" style="display: none;">
            <!-- Executive Summary -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card analysis-card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-pie me-2"></i>
                                Executive Summary
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="executiveSummary"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Key Metrics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-value" id="healthScore">--</div>
                        <div class="metric-label">Health Score</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-value" id="totalTransactions">--</div>
                        <div class="metric-label">Total Transactions</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-value" id="approvalRate">--%</div>
                        <div class="metric-label">Approval Rate</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-value" id="errorRate">--%</div>
                        <div class="metric-label">Error Rate</div>
                    </div>
                </div>
            </div>

            <!-- Detailed Analysis Tabs -->
            <div class="row">
                <div class="col-12">
                    <ul class="nav nav-tabs" id="analysisTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                                <i class="fas fa-tachometer-alt me-1"></i>Overview
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="transactions-tab" data-bs-toggle="tab" data-bs-target="#transactions" type="button" role="tab">
                                <i class="fas fa-credit-card me-1"></i>Transactions
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="errors-tab" data-bs-toggle="tab" data-bs-target="#errors" type="button" role="tab">
                                <i class="fas fa-exclamation-triangle me-1"></i>Errors
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="performance-tab" data-bs-toggle="tab" data-bs-target="#performance" type="button" role="tab">
                                <i class="fas fa-stopwatch me-1"></i>Performance
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="business-tab" data-bs-toggle="tab" data-bs-target="#business" type="button" role="tab">
                                <i class="fas fa-chart-bar me-1"></i>Business Impact
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="recommendations-tab" data-bs-toggle="tab" data-bs-target="#recommendations" type="button" role="tab">
                                <i class="fas fa-lightbulb me-1"></i>Recommendations
                            </button>
                        </li>
                    </ul>
                    
                    <div class="tab-content" id="analysisTabContent">
                        <!-- Overview Tab -->
                        <div class="tab-pane fade show active" id="overview" role="tabpanel">
                            <div class="card">
                                <div class="card-body">
                                    <div id="overviewContent"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Transactions Tab -->
                        <div class="tab-pane fade" id="transactions" role="tabpanel">
                            <div class="card">
                                <div class="card-body">
                                    <div id="transactionContent"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Errors Tab -->
                        <div class="tab-pane fade" id="errors" role="tabpanel">
                            <div class="card">
                                <div class="card-body">
                                    <div id="errorContent"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Performance Tab -->
                        <div class="tab-pane fade" id="performance" role="tabpanel">
                            <div class="card">
                                <div class="card-body">
                                    <div id="performanceContent"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Business Impact Tab -->
                        <div class="tab-pane fade" id="business" role="tabpanel">
                            <div class="card">
                                <div class="card-body">
                                    <div id="businessContent"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Recommendations Tab -->
                        <div class="tab-pane fade" id="recommendations" role="tabpanel">
                            <div class="card">
                                <div class="card-body">
                                    <div id="recommendationContent"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Alert -->
        <div id="errorAlert" class="alert alert-danger" style="display: none;" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <span id="errorMessage"></span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/analyzer.js') }}"></script>
</body>
</html>
