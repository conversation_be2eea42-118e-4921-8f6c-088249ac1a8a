# OpenEPS Log Analyzer

A comprehensive web application for analyzing OpenEPS payment system logs with intelligent explanations in the same narrative style as the OpenEPS documentation guides.

## Features

### 🔍 **Intelligent Log Analysis**
- **Comprehensive Parsing**: Handles multiple OpenEPS log formats
- **Pattern Recognition**: Identifies transaction flows, error patterns, and system behaviors
- **Business Context**: Translates technical events into business impact
- **Narrative Explanations**: Provides detailed, educational explanations like the OpenEPS guides

### 📊 **Analysis Capabilities**
- **Transaction Analysis**: Complete transaction flow tracking and analysis
- **Error Analysis**: Categorizes and explains error patterns with root cause analysis
- **Performance Analysis**: Response time analysis and bottleneck identification
- **Business Impact**: Revenue impact assessment and customer experience metrics
- **System Health**: Overall system health scoring and component status

### 💡 **Intelligent Explanations**
- **Executive Summaries**: High-level system status and key findings
- **Detailed Technical Analysis**: In-depth technical explanations
- **Business Impact Assessment**: Revenue and operational impact analysis
- **Actionable Recommendations**: Prioritized action items with implementation guidance
- **Root Cause Analysis**: Detailed explanations of why issues occur

### 🎯 **User-Friendly Interface**
- **Web-Based**: No installation required, works in any modern browser
- **File Upload**: Drag-and-drop log file upload
- **Text Input**: Paste log content directly
- **Interactive Results**: Tabbed interface with detailed breakdowns
- **Real-Time Analysis**: Fast analysis with progress indicators

## Quick Start

### Prerequisites
- Python 3.7 or higher
- Modern web browser

### Installation

1. **Clone or download the application**:
   ```bash
   # If you have the files, navigate to the directory
   cd openeps_log_analyzer
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python app.py
   ```

4. **Open your browser**:
   Navigate to `http://localhost:5000`

### Docker Installation (Alternative)

1. **Create Dockerfile**:
   ```dockerfile
   FROM python:3.9-slim
   WORKDIR /app
   COPY requirements.txt .
   RUN pip install -r requirements.txt
   COPY . .
   EXPOSE 5000
   CMD ["python", "app.py"]
   ```

2. **Build and run**:
   ```bash
   docker build -t openeps-analyzer .
   docker run -p 5000:5000 openeps-analyzer
   ```

## Usage Guide

### 1. **Input Your Logs**

**Option A: File Upload**
- Click "Choose File" and select your OpenEPS log file
- Supports .txt and .log files up to 16MB

**Option B: Text Input**
- Paste your log content directly into the text area
- Supports all OpenEPS log formats

### 2. **Select Analysis Type**
- **Comprehensive**: Full analysis with all features
- **Quick**: Fast analysis focusing on key issues
- **Business**: Focus on business impact and revenue
- **Technical**: Deep technical analysis for IT teams

### 3. **Review Results**

The application provides results in multiple tabs:

- **Overview**: System summary and health score
- **Transactions**: Transaction analysis and flow tracking
- **Errors**: Error categorization and explanations
- **Performance**: Response time and bottleneck analysis
- **Business Impact**: Revenue and customer experience impact
- **Recommendations**: Prioritized action items

## Supported Log Formats

The analyzer supports multiple OpenEPS log formats:

### Standard Format
```
MM/DD/YY HH:NN:SS.ZZZ [SOURCE] MESSAGE
12/11/24 14:23:45.123 [OpenEPS] Transaction started
```

### ISO Format
```
YYYY-MM-DD HH:NN:SS.ZZZ [SOURCE] MESSAGE
2024-12-11 14:23:45.123 [OpenEPS] Transaction started
```

### Simple Format
```
HH:NN:SS MESSAGE
14:23:45 Transaction started
```

## Example Analysis

### Input Log Sample:
```
12/11/24 14:23:45.123 [OpenEPS] MTX_POS_SET_PurchaseAmount: 4567
12/11/24 14:23:45.124 [OpenEPS] MTX_POS_SET_TenderType: 2
12/11/24 14:23:45.125 [OpenEPS] MTX_POS_SendTransaction: Starting
12/11/24 14:23:45.200 [TERM] Requesting card data from terminal
12/11/24 14:23:47.500 [TERM] Card swiped: ****1234
12/11/24 14:23:47.504 [HOST] Routing to Chase host
12/11/24 14:23:48.234 [HOST] Response received: 00 (Approved)
12/11/24 14:23:48.235 [OpenEPS] Transaction completed successfully
```

### Analysis Output:
- **Transaction Type**: Credit card purchase
- **Amount**: $45.67
- **Duration**: 3.1 seconds
- **Status**: Successfully approved
- **Business Impact**: Positive revenue capture
- **Performance**: Within acceptable response time

## Architecture

### Backend Components
- **`app.py`**: Flask web application and API endpoints
- **`log_parser.py`**: OpenEPS log parsing engine
- **`pattern_analyzer.py`**: Pattern recognition and analysis
- **`business_analyzer.py`**: Business context and impact analysis
- **`explanation_generator.py`**: Narrative explanation generation

### Frontend Components
- **`templates/index.html`**: Web interface
- **`static/js/analyzer.js`**: Frontend JavaScript logic
- **Bootstrap 5**: UI framework for responsive design

## API Endpoints

### POST /analyze
Analyze log content and return comprehensive analysis.

**Request**:
```json
{
  "log_content": "log content here...",
  "analysis_type": "comprehensive"
}
```

**Response**:
```json
{
  "success": true,
  "log_summary": {...},
  "patterns": {...},
  "business_context": {...},
  "explanations": {...},
  "recommendations": [...]
}
```

### POST /upload
Upload log file for analysis.

### GET /health
Health check endpoint.

## Configuration

### Environment Variables
- `FLASK_ENV`: Set to `development` for debug mode
- `FLASK_PORT`: Port to run the application (default: 5000)
- `MAX_CONTENT_LENGTH`: Maximum file upload size (default: 16MB)

### Customization
You can customize the analysis by modifying:
- **Error patterns** in `pattern_analyzer.py`
- **Business rules** in `business_analyzer.py`
- **Explanation templates** in `explanation_generator.py`

## Troubleshooting

### Common Issues

**1. "No log content provided"**
- Ensure you've either uploaded a file or pasted content
- Check that the content is not empty

**2. "File size exceeds 16MB limit"**
- Split large log files into smaller chunks
- Use text input for smaller log samples

**3. "Analysis failed"**
- Check that the log format is supported
- Verify the log content is valid OpenEPS format

**4. Poor analysis results**
- Ensure logs contain complete transaction flows
- Include sufficient log data for pattern recognition

### Performance Tips
- For large log files, use "Quick Analysis" mode first
- Focus on specific time periods for detailed analysis
- Use file upload for better performance with large logs

## Contributing

This application is designed to be extensible. You can contribute by:

1. **Adding new log patterns** in the pattern analyzer
2. **Improving explanations** in the explanation generator
3. **Adding new analysis features** in the business analyzer
4. **Enhancing the UI** with additional visualizations

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the OpenEPS documentation guides
3. Examine the application logs for error details

## License

This application is provided as-is for OpenEPS system analysis and troubleshooting purposes.

---

**Note**: This analyzer is based on the comprehensive OpenEPS documentation guides and provides the same level of detailed, educational explanations for log analysis.
