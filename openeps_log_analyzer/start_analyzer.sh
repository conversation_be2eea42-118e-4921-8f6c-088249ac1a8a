#!/bin/bash

# OpenEPS Log Analyzer Startup Script
# This script can be run from anywhere and will find and start the analyzer

echo "🚀 OpenEPS Log Analyzer - Startup Script"
echo "=================================================="

# Find the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

echo "📁 Script directory: $SCRIPT_DIR"

# Change to the script directory
cd "$SCRIPT_DIR"

echo "📁 Changed to: $(pwd)"

# Check if app.py exists
if [ ! -f "app.py" ]; then
    echo "❌ Error: app.py not found in $SCRIPT_DIR"
    echo "   Please make sure all files are in the correct location"
    exit 1
fi

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Error: Python not found"
        echo "   Please install Python 3.7 or higher"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "🐍 Using Python: $PYTHON_CMD"

# Check if requirements are installed
echo "📦 Checking dependencies..."
if ! $PYTHON_CMD -c "import flask" 2>/dev/null; then
    echo "📦 Installing dependencies..."
    $PYTHON_CMD -m pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        echo "   Please run manually: pip install -r requirements.txt"
        exit 1
    fi
fi

echo "✅ Dependencies OK"
echo ""
echo "🌐 Starting OpenEPS Log Analyzer..."
echo "   Will try ports: 5001, 5002, 5003, 8000, 8080"
echo "   Press Ctrl+C to stop"
echo "=================================================="

# Start the application
$PYTHON_CMD app.py
