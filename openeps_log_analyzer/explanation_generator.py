"""
OpenEPS Explanation Generator
Generates human-readable explanations of log patterns and system behavior
in the same narrative style as the OpenEPS guides
"""

import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class ExplanationGenerator:
    """
    Generates comprehensive, narrative explanations of OpenEPS log analysis
    Mimics the detailed, educational style of the OpenEPS documentation guides
    """
    
    def __init__(self):
        # Response code explanations
        self.response_codes = {
            '00': {
                'meaning': 'Approved',
                'explanation': 'Transaction was successfully approved by the issuing bank. Customer payment accepted.',
                'business_impact': 'Positive - Revenue captured, customer satisfied',
                'next_steps': 'Complete transaction and provide receipt'
            },
            '01': {
                'meaning': 'Refer to Card Issuer',
                'explanation': 'Bank requires additional verification. Not necessarily a decline.',
                'business_impact': 'Neutral - May require phone authorization',
                'next_steps': 'Contact card issuer for authorization or try alternative payment'
            },
            '05': {
                'meaning': 'Do Not Honor',
                'explanation': 'Generic decline from issuing bank. Could be insufficient funds, account issues, or fraud prevention.',
                'business_impact': 'Negative - Lost sale opportunity, customer inconvenience',
                'next_steps': 'Suggest alternative payment method, customer should contact their bank'
            },
            '12': {
                'meaning': 'Invalid Transaction',
                'explanation': 'Transaction format or data is incorrect. System configuration or data entry issue.',
                'business_impact': 'Negative - System problem affecting transactions',
                'next_steps': 'Check system configuration, verify transaction data format'
            },
            '14': {
                'meaning': 'Invalid Card Number',
                'explanation': 'Card number failed validation checks. Could be damaged card or data entry error.',
                'business_impact': 'Negative - Customer inconvenience, potential lost sale',
                'next_steps': 'Try card again, check for damage, manual entry if necessary'
            },
            '51': {
                'meaning': 'Insufficient Funds',
                'explanation': 'Customer account does not have enough funds to cover the transaction.',
                'business_impact': 'Negative - Lost sale, customer embarrassment',
                'next_steps': 'Suggest lower amount or alternative payment method'
            },
            '54': {
                'meaning': 'Expired Card',
                'explanation': 'Card expiration date has passed. Customer needs updated card.',
                'business_impact': 'Negative - Lost sale opportunity',
                'next_steps': 'Customer needs to use different card or payment method'
            },
            '91': {
                'meaning': 'Issuer or Switch Inoperative',
                'explanation': 'Bank or payment network is temporarily unavailable. System issue, not customer issue.',
                'business_impact': 'High Negative - Multiple customers affected, system-wide problem',
                'next_steps': 'Try again later, use backup processor if available, consider offline approval'
            }
        }
        
        # Error pattern explanations
        self.error_explanations = {
            'host_timeout': {
                'title': 'Host Communication Timeout',
                'technical_cause': 'Network connection to payment processor exceeded timeout threshold',
                'business_impact': 'Customers experience delays, potential lost sales, poor customer experience',
                'root_causes': [
                    'Network connectivity issues between store and payment processor',
                    'Payment processor experiencing high load or technical issues',
                    'Firewall or routing problems blocking communication',
                    'DNS resolution failures'
                ],
                'immediate_actions': [
                    'Check network connectivity to payment processor',
                    'Verify firewall settings and port access',
                    'Contact payment processor to check system status',
                    'Consider switching to backup processor if available'
                ],
                'long_term_solutions': [
                    'Implement redundant network connections',
                    'Set up multiple payment processors for failover',
                    'Monitor network performance proactively',
                    'Establish SLA agreements with payment processors'
                ]
            },
            'terminal_error': {
                'title': 'Payment Terminal Communication Error',
                'technical_cause': 'Physical payment terminal (PIN pad) not responding to commands',
                'business_impact': 'Customers cannot complete card transactions, lane may need to close',
                'root_causes': [
                    'Physical connection issues (USB, serial cable problems)',
                    'Terminal hardware failure or malfunction',
                    'Driver or software compatibility issues',
                    'Power supply problems to terminal'
                ],
                'immediate_actions': [
                    'Check physical connections to terminal',
                    'Power cycle the terminal device',
                    'Test with different USB/serial port if available',
                    'Switch to manual card entry if supported'
                ],
                'long_term_solutions': [
                    'Maintain spare terminals for quick replacement',
                    'Regular terminal maintenance and cleaning',
                    'Update terminal drivers and firmware',
                    'Monitor terminal health proactively'
                ]
            },
            'configuration_error': {
                'title': 'System Configuration Problem',
                'technical_cause': 'Configuration files are missing, corrupted, or contain invalid settings',
                'business_impact': 'System may not function correctly, transactions may fail or route incorrectly',
                'root_causes': [
                    'Configuration file corruption due to disk issues',
                    'Manual configuration changes that introduced errors',
                    'Failed configuration updates from host system',
                    'File permission or access issues'
                ],
                'immediate_actions': [
                    'Request fresh configuration download from host',
                    'Restore configuration from known good backup',
                    'Check file permissions and disk space',
                    'Validate configuration file syntax'
                ],
                'long_term_solutions': [
                    'Implement configuration backup and versioning',
                    'Automate configuration validation checks',
                    'Monitor configuration file integrity',
                    'Establish change control procedures'
                ]
            }
        }
    
    def generate_explanations(self, parsed_logs: List[Dict], patterns: Dict, 
                            business_context: Dict, analysis_type: str = 'comprehensive') -> Dict:
        """
        Generate comprehensive explanations based on log analysis
        """
        explanations = {
            'executive_summary': self._generate_executive_summary(patterns, business_context),
            'detailed_analysis': self._generate_detailed_analysis(parsed_logs, patterns, business_context),
            'transaction_flow_analysis': self._generate_transaction_flow_analysis(parsed_logs),
            'error_explanations': self._generate_error_explanations(patterns),
            'business_impact_assessment': self._generate_business_impact_assessment(business_context),
            'recommendations': self._generate_recommendations(patterns, business_context),
            'technical_details': self._generate_technical_details(parsed_logs, patterns) if analysis_type == 'comprehensive' else None
        }
        
        return explanations
    
    def _generate_executive_summary(self, patterns: Dict, business_context: Dict) -> str:
        """
        Generate executive summary in narrative style
        """
        # Get key metrics
        total_transactions = patterns.get('transaction_analysis', {}).get('total_transactions', 0)
        approval_rate = patterns.get('transaction_analysis', {}).get('approval_rate', 0) * 100
        error_rate = patterns.get('error_analysis', {}).get('error_rate', 0) * 100
        health_score = patterns.get('health_score', 0)
        
        # Determine overall system status
        if health_score >= 90:
            status = "excellent condition"
            status_emoji = "✅"
        elif health_score >= 75:
            status = "good condition with minor issues"
            status_emoji = "⚠️"
        elif health_score >= 50:
            status = "experiencing significant issues"
            status_emoji = "🚨"
        else:
            status = "critical condition requiring immediate attention"
            status_emoji = "🔴"
        
        summary = f"""
{status_emoji} **OpenEPS System Status: {status.title()}**

**System Overview:**
Your OpenEPS payment processing system processed {total_transactions} transactions during the analyzed period. 
The system achieved a {approval_rate:.1f}% approval rate with an overall error rate of {error_rate:.1f}%.

**Key Findings:**
"""
        
        # Add specific findings based on analysis
        if error_rate > 10:
            summary += f"• **High Error Rate Alert**: The {error_rate:.1f}% error rate significantly exceeds normal thresholds (typically <5%). This indicates system issues that require immediate attention.\n"
        
        if approval_rate < 90:
            summary += f"• **Low Approval Rate**: The {approval_rate:.1f}% approval rate is below optimal levels (typically >95%), potentially impacting revenue.\n"
        
        performance_issues = patterns.get('performance_analysis', {}).get('performance_issues', False)
        if performance_issues:
            summary += "• **Performance Issues Detected**: Transaction response times exceed acceptable thresholds, affecting customer experience.\n"
        
        # Business impact
        revenue_at_risk = business_context.get('revenue_impact', {}).get('estimated_revenue_at_risk', 0)
        if revenue_at_risk > 0:
            summary += f"• **Revenue Impact**: Estimated ${revenue_at_risk:.2f} in revenue at risk due to system issues.\n"
        
        summary += f"""
**Overall Health Score: {health_score:.0f}/100**

This analysis provides detailed insights into your OpenEPS system's performance, identifies specific issues, 
and offers actionable recommendations to improve system reliability and business outcomes.
"""
        
        return summary.strip()
    
    def _generate_detailed_analysis(self, parsed_logs: List[Dict], patterns: Dict, business_context: Dict) -> str:
        """
        Generate detailed technical and business analysis
        """
        analysis = "## Detailed System Analysis\n\n"
        
        # Transaction Analysis
        trans_analysis = patterns.get('transaction_analysis', {})
        analysis += "### Transaction Processing Analysis\n\n"
        analysis += f"**Total Transactions Processed:** {trans_analysis.get('total_transactions', 0)}\n"
        analysis += f"**Approval Rate:** {trans_analysis.get('approval_rate', 0)*100:.1f}%\n\n"
        
        # Payment type breakdown
        payment_types = trans_analysis.get('payment_types', {})
        if payment_types:
            analysis += "**Payment Type Distribution:**\n"
            for payment_type, count in payment_types.items():
                analysis += f"• {payment_type}: {count} transactions\n"
            analysis += "\n"
        
        # Error Analysis
        error_analysis = patterns.get('error_analysis', {})
        analysis += "### Error Analysis\n\n"
        analysis += f"**Total Errors:** {error_analysis.get('total_errors', 0)}\n"
        analysis += f"**Error Rate:** {error_analysis.get('error_rate', 0)*100:.1f}%\n\n"
        
        error_categories = error_analysis.get('error_categories', {})
        if error_categories:
            analysis += "**Error Categories:**\n"
            for category, data in error_categories.items():
                if data['count'] > 0:
                    analysis += f"• **{category.replace('_', ' ').title()}**: {data['count']} occurrences\n"
                    if data['examples']:
                        analysis += f"  - Example: {data['examples'][0]}\n"
            analysis += "\n"
        
        # Performance Analysis
        perf_analysis = patterns.get('performance_analysis', {})
        analysis += "### Performance Analysis\n\n"
        avg_response = perf_analysis.get('average_response_time', 0)
        analysis += f"**Average Response Time:** {avg_response:.2f} seconds\n"
        
        if avg_response > 3.0:
            analysis += "⚠️ **Performance Issue**: Response times exceed acceptable thresholds (3 seconds)\n"
        
        slow_transactions = perf_analysis.get('slow_transaction_count', 0)
        if slow_transactions > 0:
            analysis += f"**Slow Transactions:** {slow_transactions} transactions took longer than expected\n"
        
        return analysis
    
    def _generate_transaction_flow_analysis(self, parsed_logs: List[Dict]) -> str:
        """
        Generate transaction flow analysis with step-by-step breakdown
        """
        flow_analysis = "## Transaction Flow Analysis\n\n"
        
        # Find complete transaction flows
        transaction_flows = self._extract_transaction_flows(parsed_logs)
        
        if transaction_flows:
            flow_analysis += f"**{len(transaction_flows)} complete transaction flows identified**\n\n"
            
            # Analyze first transaction flow as example
            if transaction_flows:
                example_flow = transaction_flows[0]
                flow_analysis += "### Example Transaction Flow:\n\n"
                
                for step_num, step in enumerate(example_flow, 1):
                    timestamp = step.get('timestamp')
                    message = step.get('message', '')
                    
                    time_str = timestamp.strftime('%H:%M:%S.%f')[:-3] if timestamp else 'Unknown'
                    flow_analysis += f"**Step {step_num}** ({time_str}): {message[:100]}...\n"
                
                flow_analysis += "\n**Flow Analysis:**\n"
                flow_analysis += self._analyze_transaction_flow_health(example_flow)
        else:
            flow_analysis += "No complete transaction flows found in the analyzed logs.\n"
        
        return flow_analysis
    
    def _generate_error_explanations(self, patterns: Dict) -> List[Dict]:
        """
        Generate detailed explanations for detected errors
        """
        explanations = []
        
        error_categories = patterns.get('error_analysis', {}).get('error_categories', {})
        
        for category, data in error_categories.items():
            if data['count'] > 0 and category in self.error_explanations:
                explanation = self.error_explanations[category].copy()
                explanation['occurrence_count'] = data['count']
                explanation['examples'] = data['examples']
                explanations.append(explanation)
        
        return explanations
    
    def _generate_business_impact_assessment(self, business_context: Dict) -> str:
        """
        Generate business impact assessment
        """
        assessment = "## Business Impact Assessment\n\n"
        
        # Revenue impact
        revenue_impact = business_context.get('revenue_impact', {})
        total_value = revenue_impact.get('total_transaction_value', 0)
        declined_value = revenue_impact.get('declined_transaction_value', 0)
        
        assessment += f"**Total Transaction Value:** ${total_value:.2f}\n"
        assessment += f"**Declined Transaction Value:** ${declined_value:.2f}\n"
        
        if declined_value > 0:
            decline_rate = (declined_value / total_value * 100) if total_value > 0 else 0
            assessment += f"**Revenue Loss Rate:** {decline_rate:.1f}%\n"
        
        # Customer experience
        customer_exp = business_context.get('customer_experience', {})
        satisfaction_score = customer_exp.get('customer_satisfaction_score', 0)
        assessment += f"\n**Customer Satisfaction Score:** {satisfaction_score:.0f}/100\n"
        
        if satisfaction_score < 80:
            assessment += "⚠️ **Customer Experience Alert**: Low satisfaction score indicates customer experience issues\n"
        
        return assessment
    
    def _generate_recommendations(self, patterns: Dict, business_context: Dict) -> List[Dict]:
        """
        Generate actionable recommendations
        """
        recommendations = []
        
        # Performance recommendations
        perf_issues = patterns.get('performance_analysis', {}).get('performance_issues', False)
        if perf_issues:
            recommendations.append({
                'priority': 'High',
                'category': 'Performance',
                'title': 'Address Performance Issues',
                'description': 'Transaction response times are affecting customer experience',
                'actions': [
                    'Monitor network connectivity to payment processors',
                    'Check system resources (CPU, memory, disk)',
                    'Consider load balancing or processor failover',
                    'Review transaction routing configuration'
                ]
            })
        
        # Error rate recommendations
        error_rate = patterns.get('error_analysis', {}).get('error_rate', 0)
        if error_rate > 0.1:
            recommendations.append({
                'priority': 'High',
                'category': 'Reliability',
                'title': 'Reduce Error Rate',
                'description': f'Error rate of {error_rate*100:.1f}% exceeds acceptable thresholds',
                'actions': [
                    'Investigate root causes of most common errors',
                    'Review system logs for patterns',
                    'Check configuration files for corruption',
                    'Verify network connectivity stability'
                ]
            })
        
        return recommendations
    
    def _generate_technical_details(self, parsed_logs: List[Dict], patterns: Dict) -> Dict:
        """
        Generate technical details for advanced users
        """
        return {
            'log_statistics': {
                'total_entries': len(parsed_logs),
                'components_detected': list(patterns.get('component_analysis', {}).get('component_activity', {}).keys()),
                'time_span': patterns.get('temporal_patterns', {}).get('total_time_span_hours', 0)
            },
            'component_breakdown': patterns.get('component_analysis', {}),
            'temporal_patterns': patterns.get('temporal_patterns', {})
        }
    
    def _extract_transaction_flows(self, parsed_logs: List[Dict]) -> List[List[Dict]]:
        """
        Extract complete transaction flows from logs
        """
        flows = []
        current_flow = []
        
        for log in parsed_logs:
            message = log.get('message', '').lower()
            
            # Start of transaction
            if any(keyword in message for keyword in ['sendtransaction', 'transaction started']):
                if current_flow:  # Save previous flow
                    flows.append(current_flow)
                current_flow = [log]
            
            # Part of current transaction
            elif current_flow and any(keyword in message for keyword in ['transaction', 'response', 'auth']):
                current_flow.append(log)
            
            # End of transaction
            elif current_flow and any(keyword in message for keyword in ['transaction complete', 'approved', 'declined']):
                current_flow.append(log)
                flows.append(current_flow)
                current_flow = []
        
        return flows
    
    def _analyze_transaction_flow_health(self, flow: List[Dict]) -> str:
        """
        Analyze the health of a transaction flow
        """
        if not flow:
            return "No flow data available"
        
        start_time = flow[0].get('timestamp')
        end_time = flow[-1].get('timestamp')
        
        if start_time and end_time:
            duration = (end_time - start_time).total_seconds()
            analysis = f"Transaction completed in {duration:.2f} seconds. "
            
            if duration < 3.0:
                analysis += "✅ Excellent response time."
            elif duration < 6.0:
                analysis += "⚠️ Acceptable response time."
            else:
                analysis += "🚨 Slow response time - investigate performance issues."
        else:
            analysis = "Unable to calculate transaction duration."
        
        # Check for errors in flow
        errors = [step for step in flow if step.get('level') in ['ERROR', 'FATAL']]
        if errors:
            analysis += f" {len(errors)} errors detected in transaction flow."
        
        return analysis
    
    def get_pattern_explanation(self, pattern_type: str) -> Dict:
        """
        Get detailed explanation for a specific pattern type
        """
        if pattern_type in self.error_explanations:
            return self.error_explanations[pattern_type]
        else:
            return {
                'title': f'Unknown Pattern: {pattern_type}',
                'explanation': 'No detailed explanation available for this pattern type.'
            }
