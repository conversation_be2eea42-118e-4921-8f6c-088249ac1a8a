"""
OpenEPS Explanation Generator
Generates human-readable explanations of log patterns and system behavior
in the same narrative style as the OpenEPS guides
"""

import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class ExplanationGenerator:
    """
    Generates comprehensive, narrative explanations of OpenEPS log analysis
    Mimics the detailed, educational style of the OpenEPS documentation guides
    """
    
    def __init__(self):
        # Response code explanations
        self.response_codes = {
            '00': {
                'meaning': 'Approved',
                'explanation': 'Transaction was successfully approved by the issuing bank. Customer payment accepted.',
                'business_impact': 'Positive - Revenue captured, customer satisfied',
                'next_steps': 'Complete transaction and provide receipt'
            },
            '01': {
                'meaning': 'Refer to Card Issuer',
                'explanation': 'Bank requires additional verification. Not necessarily a decline.',
                'business_impact': 'Neutral - May require phone authorization',
                'next_steps': 'Contact card issuer for authorization or try alternative payment'
            },
            '05': {
                'meaning': 'Do Not Honor',
                'explanation': 'Generic decline from issuing bank. Could be insufficient funds, account issues, or fraud prevention.',
                'business_impact': 'Negative - Lost sale opportunity, customer inconvenience',
                'next_steps': 'Suggest alternative payment method, customer should contact their bank'
            },
            '12': {
                'meaning': 'Invalid Transaction',
                'explanation': 'Transaction format or data is incorrect. System configuration or data entry issue.',
                'business_impact': 'Negative - System problem affecting transactions',
                'next_steps': 'Check system configuration, verify transaction data format'
            },
            '14': {
                'meaning': 'Invalid Card Number',
                'explanation': 'Card number failed validation checks. Could be damaged card or data entry error.',
                'business_impact': 'Negative - Customer inconvenience, potential lost sale',
                'next_steps': 'Try card again, check for damage, manual entry if necessary'
            },
            '51': {
                'meaning': 'Insufficient Funds',
                'explanation': 'Customer account does not have enough funds to cover the transaction.',
                'business_impact': 'Negative - Lost sale, customer embarrassment',
                'next_steps': 'Suggest lower amount or alternative payment method'
            },
            '54': {
                'meaning': 'Expired Card',
                'explanation': 'Card expiration date has passed. Customer needs updated card.',
                'business_impact': 'Negative - Lost sale opportunity',
                'next_steps': 'Customer needs to use different card or payment method'
            },
            '91': {
                'meaning': 'Issuer or Switch Inoperative',
                'explanation': 'Bank or payment network is temporarily unavailable. System issue, not customer issue.',
                'business_impact': 'High Negative - Multiple customers affected, system-wide problem',
                'next_steps': 'Try again later, use backup processor if available, consider offline approval'
            }
        }
        
        # Error pattern explanations
        self.error_explanations = {
            'host_timeout': {
                'title': 'Host Communication Timeout',
                'technical_cause': 'Network connection to payment processor exceeded timeout threshold',
                'business_impact': 'Customers experience delays, potential lost sales, poor customer experience',
                'root_causes': [
                    'Network connectivity issues between store and payment processor',
                    'Payment processor experiencing high load or technical issues',
                    'Firewall or routing problems blocking communication',
                    'DNS resolution failures'
                ],
                'immediate_actions': [
                    'Check network connectivity to payment processor',
                    'Verify firewall settings and port access',
                    'Contact payment processor to check system status',
                    'Consider switching to backup processor if available'
                ],
                'long_term_solutions': [
                    'Implement redundant network connections',
                    'Set up multiple payment processors for failover',
                    'Monitor network performance proactively',
                    'Establish SLA agreements with payment processors'
                ]
            },
            'terminal_error': {
                'title': 'Payment Terminal Communication Error',
                'technical_cause': 'Physical payment terminal (PIN pad) not responding to commands',
                'business_impact': 'Customers cannot complete card transactions, lane may need to close',
                'root_causes': [
                    'Physical connection issues (USB, serial cable problems)',
                    'Terminal hardware failure or malfunction',
                    'Driver or software compatibility issues',
                    'Power supply problems to terminal'
                ],
                'immediate_actions': [
                    'Check physical connections to terminal',
                    'Power cycle the terminal device',
                    'Test with different USB/serial port if available',
                    'Switch to manual card entry if supported'
                ],
                'long_term_solutions': [
                    'Maintain spare terminals for quick replacement',
                    'Regular terminal maintenance and cleaning',
                    'Update terminal drivers and firmware',
                    'Monitor terminal health proactively'
                ]
            },
            'configuration_error': {
                'title': 'System Configuration Problem',
                'technical_cause': 'Configuration files are missing, corrupted, or contain invalid settings',
                'business_impact': 'System may not function correctly, transactions may fail or route incorrectly',
                'root_causes': [
                    'Configuration file corruption due to disk issues',
                    'Manual configuration changes that introduced errors',
                    'Failed configuration updates from host system',
                    'File permission or access issues'
                ],
                'immediate_actions': [
                    'Request fresh configuration download from host',
                    'Restore configuration from known good backup',
                    'Check file permissions and disk space',
                    'Validate configuration file syntax'
                ],
                'long_term_solutions': [
                    'Implement configuration backup and versioning',
                    'Automate configuration validation checks',
                    'Monitor configuration file integrity',
                    'Establish change control procedures'
                ]
            }
        }
    
    def generate_explanations(self, parsed_logs: List[Dict], patterns: Dict, 
                            business_context: Dict, analysis_type: str = 'comprehensive') -> Dict:
        """
        Generate comprehensive explanations based on log analysis
        """
        explanations = {
            'application_lifecycle_analysis': self._generate_application_lifecycle_analysis(patterns, parsed_logs),
            'transaction_flow_analysis': self._generate_transaction_flow_analysis(patterns, parsed_logs),
            'emv_processing_analysis': self._generate_emv_processing_analysis(patterns, parsed_logs),
            'terminal_interaction_analysis': self._generate_terminal_interaction_analysis(patterns, parsed_logs),
            'host_communication_analysis': self._generate_host_communication_analysis(patterns, parsed_logs),
            'technical_summary': self._generate_technical_summary(patterns, parsed_logs),
            'failure_analysis': self._generate_failure_analysis(patterns),
            'root_cause_analysis': self._generate_root_cause_analysis(patterns, parsed_logs),
            'technical_recommendations': self._generate_technical_recommendations(patterns),
            'detailed_technical_breakdown': self._generate_detailed_technical_breakdown(parsed_logs, patterns) if analysis_type == 'comprehensive' else None
        }
        
        return explanations

    def _generate_application_lifecycle_analysis(self, patterns: Dict, parsed_logs: List[Dict]) -> str:
        """
        Generate comprehensive application lifecycle analysis
        """
        lifecycle = patterns.get('application_lifecycle', {})

        analysis = "## 🚀 OpenEPS Application Lifecycle Analysis\n\n"
        analysis += "### Application Startup Sequence\n\n"

        # Analyze each phase
        phases = ['startup', 'terminal_initialization', 'servereps_connection', 'signon_process', 'ready_for_transactions']
        phase_names = ['Application Startup', 'Terminal Initialization', 'ServerEPS Connection', 'Signon Process', 'Ready for Transactions']

        for i, (phase, phase_name) in enumerate(zip(phases, phase_names)):
            phase_data = lifecycle.get(phase, {})
            status = phase_data.get('status', 'unknown')
            duration = phase_data.get('duration')
            events = phase_data.get('events', [])

            status_icon = "✅" if status == "completed" else "❌" if status == "failed" else "⏳" if status == "started" else "❓"

            analysis += f"#### {i+1}. {phase_name} {status_icon}\n\n"
            analysis += f"**Status**: {status.title()}\n"

            if duration:
                analysis += f"**Duration**: {duration:.2f} seconds\n"

            if events:
                analysis += f"**Key Events** ({len(events)} total):\n"
                for event in events[:3]:  # Show first 3 events
                    analysis += f"- **{event.get('timestamp', 'Unknown time')}**: {event.get('event', 'Unknown event')}\n"
                if len(events) > 3:
                    analysis += f"- ... and {len(events) - 3} more events\n"

            analysis += "\n"

        # Overall assessment
        completed_phases = sum(1 for phase in phases if lifecycle.get(phase, {}).get('status') == 'completed')
        failed_phases = sum(1 for phase in phases if lifecycle.get(phase, {}).get('status') == 'failed')

        analysis += "### Overall Assessment\n\n"
        analysis += f"**Completed Phases**: {completed_phases}/{len(phases)}\n"
        analysis += f"**Failed Phases**: {failed_phases}\n"

        if completed_phases == len(phases):
            analysis += "✅ **Application successfully initialized and ready for transactions**\n\n"
        elif failed_phases > 0:
            analysis += "❌ **Application initialization failed - system not operational**\n\n"
        else:
            analysis += "⏳ **Application initialization in progress**\n\n"

        return analysis

    def _generate_transaction_flow_analysis(self, patterns: Dict, parsed_logs: List[Dict]) -> str:
        """
        Generate comprehensive transaction flow analysis
        """
        complete_flows = patterns.get('complete_transaction_flows', {})

        analysis = "## 💳 Complete Transaction Flow Analysis\n\n"

        # Transaction Statistics
        flow_stats = complete_flows.get('flow_analysis', {})
        total_transactions = flow_stats.get('total_transactions', 0)
        success_rate = flow_stats.get('success_rate', 0)
        avg_time = flow_stats.get('average_transaction_time', 0)

        analysis += "### Transaction Statistics\n\n"
        analysis += f"**Total Transactions Detected**: {total_transactions}\n"
        analysis += f"**Success Rate**: {success_rate:.1f}%\n"
        if avg_time:
            analysis += f"**Average Transaction Time**: {avg_time:.2f} seconds\n"
        analysis += "\n"

        # Successful Transactions
        successful = complete_flows.get('successful_transactions', [])
        if successful:
            analysis += f"### ✅ Successful Transactions ({len(successful)})\n\n"
            for i, txn in enumerate(successful[:3]):  # Show first 3
                analysis += f"#### Transaction {i+1}\n"
                analysis += f"- **Amount**: {txn.get('amount', 'Unknown')}\n"
                analysis += f"- **Tender Type**: {txn.get('tender_type', 'Unknown')}\n"
                analysis += f"- **Start Time**: {txn.get('start_time', 'Unknown')}\n"
                analysis += f"- **Completion Time**: {txn.get('end_time', 'Unknown')}\n"

                # EMV Flow Steps
                emv_steps = txn.get('emv_flow_steps', [])
                if emv_steps:
                    analysis += f"- **EMV Processing Steps**: {len(emv_steps)}\n"
                    for step in emv_steps[:2]:  # Show first 2 steps
                        analysis += f"  - {step.get('step', 'Unknown step')}\n"

                analysis += "\n"

        # Failed Transactions
        failed = complete_flows.get('failed_transactions', [])
        if failed:
            analysis += f"### ❌ Failed Transactions ({len(failed)})\n\n"
            for i, txn in enumerate(failed[:3]):  # Show first 3
                analysis += f"#### Failed Transaction {i+1}\n"
                analysis += f"- **Amount**: {txn.get('amount', 'Unknown')}\n"
                analysis += f"- **Failure Point**: {txn.get('failure_point', 'Unknown')}\n"
                analysis += f"- **Error Details**: {txn.get('error_details', 'Unknown')}\n"
                analysis += "\n"

        # Common Failure Points
        common_failures = flow_stats.get('common_failure_points', [])
        if common_failures:
            analysis += "### 🔍 Common Failure Points\n\n"
            for failure in common_failures:
                analysis += f"- **{failure.get('point', 'Unknown')}**: {failure.get('count', 0)} occurrences\n"
            analysis += "\n"

        return analysis

    def _generate_emv_processing_analysis(self, patterns: Dict, parsed_logs: List[Dict]) -> str:
        """
        Generate detailed EMV processing analysis
        """
        emv_data = patterns.get('emv_transaction_flow', {})

        analysis = "## 🔐 EMV Processing Analysis\n\n"

        # EMV Data Elements
        emv_elements = emv_data.get('emv_data_elements', {})
        if emv_elements:
            analysis += "### EMV Data Elements Detected\n\n"

            # Key EMV tags and their meanings
            emv_tag_descriptions = {
                'Amount_Authorized': 'Transaction amount in cents',
                'Terminal_Country_Code': 'Country code of the terminal',
                'Terminal_Capabilities': 'Terminal capabilities for EMV processing',
                'CVM_Results': 'Cardholder Verification Method results',
                'Application_Cryptogram': 'Cryptogram generated by the card',
                'Cryptogram_Information_Data': 'Information about the cryptogram',
                'Application_Interchange_Profile': 'Card application capabilities',
                'Terminal_Verification_Results': 'Results of terminal verification'
            }

            for tag, description in emv_tag_descriptions.items():
                if tag in emv_elements:
                    values = emv_elements[tag]
                    analysis += f"**{tag}**: {description}\n"
                    analysis += f"- Found in {len(values)} transaction(s)\n"
                    if values:
                        analysis += f"- Example value: `{values[0].get('value', 'N/A')}`\n"
                    analysis += "\n"

        # Cryptogram Processing
        cryptograms = emv_data.get('cryptogram_processing', [])
        if cryptograms:
            analysis += f"### Cryptogram Processing ({len(cryptograms)} events)\n\n"

            crypto_types = {}
            for crypto in cryptograms:
                crypto_type = crypto.get('cryptogram_data', {}).get('type', 'Unknown')
                crypto_types[crypto_type] = crypto_types.get(crypto_type, 0) + 1

            for crypto_type, count in crypto_types.items():
                analysis += f"**{crypto_type}**: {count} occurrence(s)\n"

                # Explain cryptogram types
                if crypto_type == 'ARQC':
                    analysis += "- Authorization Request Cryptogram - Card requests online authorization\n"
                elif crypto_type == 'TC':
                    analysis += "- Transaction Certificate - Card approves transaction offline\n"
                elif crypto_type == 'AAC':
                    analysis += "- Application Authentication Cryptogram - Card declines transaction\n"
                analysis += "\n"

        # Transaction Sessions
        sessions = emv_data.get('transaction_sessions', [])
        if sessions:
            analysis += f"### EMV Transaction Sessions ({len(sessions)})\n\n"
            for i, session in enumerate(sessions[:3]):  # Show first 3
                analysis += f"#### Session {i+1}\n"
                analysis += f"- **Transaction ID**: {session.get('transaction_id', 'Unknown')}\n"
                analysis += f"- **Amount**: {session.get('amount', 'Unknown')}\n"
                analysis += f"- **Status**: {session.get('completion_status', 'Unknown')}\n"

                emv_data_session = session.get('emv_data', {})
                if emv_data_session:
                    analysis += f"- **EMV Data Elements**: {len(emv_data_session)} detected\n"
                    for key in list(emv_data_session.keys())[:3]:  # Show first 3
                        analysis += f"  - {key}: `{emv_data_session[key]}`\n"

                analysis += "\n"

        return analysis

    def _generate_technical_summary(self, patterns: Dict, parsed_logs: List[Dict]) -> str:
        """
        Generate technical summary focusing on system failures and root causes
        """
        technical_failures = patterns.get('technical_failure_analysis', {})
        cascading_failures = patterns.get('cascading_failure_analysis', {})
        system_states = patterns.get('system_state_analysis', {})

        summary = "## 🔧 Technical Analysis Summary\n\n"

        # Primary blockers
        primary_blockers = []
        secondary_issues = []

        # Check for ServerEPS SSL issues
        ssl_issues = technical_failures.get('servereps_ssl_issues', [])
        if ssl_issues:
            primary_blockers.append("**ServerEPS SSL Certificate Validation Failure**")
            summary += "### 🔴 Primary Blocker: ServerEPS Login Failure\n\n"
            summary += "**Cause**: SSL certificate identity mismatch preventing secure connection to ServerEPS.\n\n"
            for issue in ssl_issues[:2]:  # Show first 2 examples
                summary += f"**Log Evidence**: `{issue['message'][:100]}...`\n\n"
            summary += "**Impact**: Cannot authenticate with ServerEPS, blocking configuration download and transaction processing.\n\n"

        # Check for SCAT terminal issues
        scat_issues = technical_failures.get('scat_terminal_issues', [])
        if scat_issues:
            if primary_blockers:
                secondary_issues.append("**SCAT Terminal Communication Failure**")
                summary += "### 🟡 Secondary Blocker: Payment Terminal Failure\n\n"
            else:
                primary_blockers.append("**SCAT Terminal Communication Failure**")
                summary += "### 🔴 Primary Blocker: Payment Terminal Failure\n\n"

            summary += "**Causes**: Multiple terminal communication issues:\n"
            summary += "- S95 command failure (device information request)\n"
            summary += "- Terminal declared 'dead' due to non-responsiveness\n"
            summary += "- P2P encryption capability mismatch\n\n"

            for issue in scat_issues[:2]:
                summary += f"**Log Evidence**: `{issue['message'][:100]}...`\n\n"
            summary += "**Impact**: Cannot process transactions or interact with payment terminal.\n\n"

        # Check for cascading failures
        if cascading_failures.get('ssl_to_login_failure'):
            summary += "### ⚠️ Cascading Failure Pattern Detected\n\n"
            summary += "SSL certificate failure → Login failure → System initialization failure\n\n"

        # Check for stuck states
        if system_states.get('stuck_in_error_loop'):
            summary += "### 🔄 System Stuck in Error Loop\n\n"
            summary += "Application is repeatedly attempting failed operations:\n"
            for indicator in system_states.get('error_loop_indicators', []):
                summary += f"- {indicator}\n"
            summary += "\n"

        # Summary conclusion
        if primary_blockers:
            summary += f"### 🎯 Critical Issues Identified: {len(primary_blockers)}\n\n"
            for blocker in primary_blockers:
                summary += f"1. {blocker}\n"

        if secondary_issues:
            summary += f"\n### ⚠️ Secondary Issues: {len(secondary_issues)}\n\n"
            for issue in secondary_issues:
                summary += f"- {issue}\n"

        return summary

    def _generate_failure_analysis(self, patterns: Dict) -> str:
        """
        Generate detailed failure analysis with technical specifics
        """
        technical_failures = patterns.get('technical_failure_analysis', {})

        analysis = "## 🔍 Detailed Failure Analysis\n\n"

        # ServerEPS SSL Analysis
        ssl_issues = technical_failures.get('servereps_ssl_issues', [])
        if ssl_issues:
            analysis += "### ServerEPS SSL Certificate Failure\n\n"
            analysis += "**Technical Details**:\n"
            analysis += "- **Error Type**: SSL Certificate Identity Mismatch\n"
            analysis += "- **Validation Result**: cvInvalid\n"
            analysis += "- **Root Cause**: Server certificate name doesn't match requested hostname\n\n"

            analysis += "**Likely Scenarios**:\n"
            analysis += "1. **Load Balancer Issue**: Traffic routed to server with wrong certificate\n"
            analysis += "2. **DNS/Routing Problem**: Connecting to wrong server endpoint\n"
            analysis += "3. **Certificate Deployment Error**: Wrong certificate installed on target server\n"
            analysis += "4. **Environment Mismatch**: Connecting to staging instead of production\n\n"

        # SCAT Terminal Analysis
        scat_issues = technical_failures.get('scat_terminal_issues', [])
        if scat_issues:
            analysis += "### SCAT Terminal Communication Breakdown\n\n"
            analysis += "**Failure Sequence**:\n"
            analysis += "1. **S95 Command Failure**: Device information request failed\n"
            analysis += "2. **Terminal Declared Dead**: Due to S95 failure\n"
            analysis += "3. **P2P Capability Mismatch**: System requires P2P, terminal doesn't support it\n"
            analysis += "4. **Communication Cascade**: All subsequent commands fail\n\n"

            analysis += "**Technical Root Causes**:\n"
            analysis += "- **Hardware**: Physical disconnection or terminal malfunction\n"
            analysis += "- **Driver Issues**: COM port drivers not functioning\n"
            analysis += "- **Configuration Mismatch**: Terminal type incorrectly configured\n"
            analysis += "- **Firmware Problems**: Terminal firmware incompatible or corrupted\n\n"

        # P2P Encryption Issues
        p2p_issues = technical_failures.get('p2p_encryption_issues', [])
        if p2p_issues:
            analysis += "### P2P Encryption Configuration Mismatch\n\n"
            analysis += "**Issue**: System requires P2P encryption but terminal reports `IsTermP2PCapable=N`\n\n"
            analysis += "**Solutions**:\n"
            analysis += "1. **Disable P2P Requirement** in terminal configuration\n"
            analysis += "2. **Update Terminal Firmware** to support P2P encryption\n"
            analysis += "3. **Change Terminal Type** to one that supports P2P\n\n"

        return analysis

    def _generate_cascading_failure_breakdown(self, patterns: Dict) -> str:
        """
        Generate cascading failure analysis
        """
        cascading = patterns.get('cascading_failure_analysis', {})

        if not cascading.get('failure_sequence'):
            return "## ✅ No Cascading Failures Detected\n\nSystem failures appear to be isolated incidents."

        breakdown = "## 🔗 Cascading Failure Analysis\n\n"
        breakdown += "**Failure Chain Detected**: One failure triggered subsequent failures.\n\n"

        breakdown += "### Failure Sequence:\n\n"
        for step in cascading.get('failure_sequence', []):
            breakdown += f"**Step {step['step']}**: {step['type']}\n"
            breakdown += f"- **Time**: {step['timestamp']}\n"
            breakdown += f"- **Evidence**: `{step['message'][:100]}...`\n\n"

        breakdown += "### Impact Analysis:\n\n"
        if cascading.get('ssl_to_login_failure'):
            breakdown += "- **SSL → Login**: Certificate failure prevented ServerEPS authentication\n"
        if cascading.get('terminal_dead_to_communication_errors'):
            breakdown += "- **Terminal Dead → Communication Errors**: Terminal failure caused all subsequent commands to fail\n"

        breakdown += "\n### Recovery Strategy:\n\n"
        breakdown += "**Fix in Order**:\n"
        breakdown += "1. **Resolve SSL certificate issue** (primary cause)\n"
        breakdown += "2. **Fix terminal communication** (secondary cause)\n"
        breakdown += "3. **Restart services** to clear error states\n"
        breakdown += "4. **Test end-to-end** functionality\n\n"

        return breakdown

    def _generate_root_cause_analysis(self, patterns: Dict, parsed_logs: List[Dict]) -> str:
        """
        Generate root cause analysis with specific technical details
        """
        analysis = "## 🎯 Root Cause Analysis\n\n"

        technical_failures = patterns.get('technical_failure_analysis', {})

        # SSL Certificate Root Cause
        ssl_issues = technical_failures.get('servereps_ssl_issues', [])
        if ssl_issues:
            analysis += "### SSL Certificate Identity Mismatch\n\n"
            analysis += "**Root Cause**: Certificate name mismatch between expected and actual server certificate.\n\n"

            # Extract specific certificate details from logs
            for log in parsed_logs:
                message = log.get('message', '')
                if 'identity mismatch' in message.lower():
                    analysis += f"**Log Evidence**: `{message}`\n\n"
                    break

            analysis += "**Technical Explanation**:\n"
            analysis += "- Client expects certificate for: `seps-emvcert.paymentslab.ncr.com`\n"
            analysis += "- Server presents certificate for: `sepssiab.paymentslab.ncrvoyix.com`\n"
            analysis += "- SSL validation fails due to hostname mismatch\n\n"

            analysis += "**Fix Required**: Update either the client configuration or server certificate.\n\n"

        # Terminal Communication Root Cause
        scat_issues = technical_failures.get('scat_terminal_issues', [])
        if scat_issues:
            analysis += "### SCAT Terminal Communication Failure\n\n"
            analysis += "**Root Cause**: Terminal fails to respond to S95 device information command.\n\n"

            analysis += "**Technical Chain**:\n"
            analysis += "1. S95 command sent to terminal\n"
            analysis += "2. Terminal fails to respond within timeout\n"
            analysis += "3. System marks terminal as 'dead'\n"
            analysis += "4. All subsequent commands fail with 'Cannot communicate with terminal'\n\n"

            analysis += "**Possible Hardware/Software Issues**:\n"
            analysis += "- Physical connection problem (USB/Serial cable)\n"
            analysis += "- Terminal power or hardware failure\n"
            analysis += "- COM port driver issues\n"
            analysis += "- Terminal firmware corruption\n\n"

        # P2P Encryption Root Cause
        p2p_issues = technical_failures.get('p2p_encryption_issues', [])
        if p2p_issues:
            analysis += "### P2P Encryption Capability Mismatch\n\n"
            analysis += "**Root Cause**: Configuration requires P2P encryption but terminal doesn't support it.\n\n"
            analysis += "**Technical Details**:\n"
            analysis += "- System configuration: `P2PRequired=true`\n"
            analysis += "- Terminal capability: `IsTermP2PCapable=N`\n"
            analysis += "- Result: Terminal declared incompatible and marked dead\n\n"

        return analysis

    def _generate_technical_recommendations(self, patterns: Dict) -> List[Dict]:
        """
        Generate specific technical recommendations for engineers
        """
        recommendations = []

        technical_failures = patterns.get('technical_failure_analysis', {})

        # SSL Certificate Recommendations
        ssl_issues = technical_failures.get('servereps_ssl_issues', [])
        if ssl_issues:
            recommendations.append({
                'priority': 'Critical',
                'category': 'SSL/TLS Configuration',
                'title': 'Fix SSL Certificate Identity Mismatch',
                'technical_details': {
                    'problem': 'Certificate hostname mismatch preventing ServerEPS connection',
                    'expected_cert': 'seps-emvcert.paymentslab.ncr.com',
                    'actual_cert': 'sepssiab.paymentslab.ncrvoyix.com'
                },
                'immediate_actions': [
                    'Verify DNS resolution for seps-emvcert.paymentslab.ncr.com',
                    'Check if traffic is being routed to wrong server',
                    'Contact NCR support for correct certificate information',
                    'Temporary: Disable SSL validation for testing (development only)'
                ],
                'configuration_changes': [
                    'Update ServerEPS URL to match certificate name',
                    'Install correct certificate in Windows certificate store',
                    'Verify firewall/proxy settings'
                ],
                'test_commands': [
                    'nslookup seps-emvcert.paymentslab.ncr.com',
                    'telnet seps-emvcert.paymentslab.ncr.com 443',
                    'openssl s_client -connect seps-emvcert.paymentslab.ncr.com:443'
                ]
            })

        # Terminal Communication Recommendations
        scat_issues = technical_failures.get('scat_terminal_issues', [])
        if scat_issues:
            recommendations.append({
                'priority': 'Critical',
                'category': 'Terminal Hardware/Communication',
                'title': 'Resolve SCAT Terminal Communication Failure',
                'technical_details': {
                    'problem': 'Terminal not responding to S95 device information command',
                    'symptoms': ['Set SCAT dead', 'Cannot communicate with terminal', 'S95 command timeout']
                },
                'immediate_actions': [
                    'Check physical USB/Serial cable connections',
                    'Verify terminal power and display status',
                    'Test terminal with different COM port',
                    'Check Windows Device Manager for COM port errors'
                ],
                'diagnostic_steps': [
                    'Use VT2.exe to test terminal communication directly',
                    'Check COM port settings (baud rate, parity, stop bits)',
                    'Test with terminal manufacturer diagnostic tools',
                    'Verify terminal firmware version compatibility'
                ],
                'configuration_changes': [
                    'Update terminal type in TerminalConfiguration.xml',
                    'Disable P2P requirement if terminal doesn\'t support it',
                    'Try different terminal communication protocol (XPI vs SCAT)'
                ]
            })

        # P2P Encryption Recommendations
        p2p_issues = technical_failures.get('p2p_encryption_issues', [])
        if p2p_issues:
            recommendations.append({
                'priority': 'High',
                'category': 'Terminal Configuration',
                'title': 'Fix P2P Encryption Configuration Mismatch',
                'technical_details': {
                    'problem': 'System requires P2P encryption but terminal doesn\'t support it',
                    'config_required': 'P2PRequired=true',
                    'terminal_capability': 'IsTermP2PCapable=N'
                },
                'immediate_actions': [
                    'Set P2PRequired=false in terminal configuration',
                    'Update terminal firmware to support P2P',
                    'Change terminal type to P2P-capable model'
                ],
                'configuration_files': [
                    'TerminalConfiguration.xml: <P2PRequired>false</P2PRequired>',
                    'OpenEPS.ini: [Terminal] P2PEncryption=disabled'
                ]
            })

        return recommendations

    def _generate_system_state_analysis(self, patterns: Dict) -> str:
        """
        Analyze current system state and stuck conditions
        """
        system_states = patterns.get('system_state_analysis', {})

        analysis = "## 🔄 System State Analysis\n\n"

        if system_states.get('stuck_in_error_loop'):
            analysis += "### ⚠️ System Stuck in Error Loop\n\n"
            analysis += "**Current State**: Application is repeatedly attempting failed operations.\n\n"
            analysis += "**Indicators**:\n"
            for indicator in system_states.get('error_loop_indicators', []):
                analysis += f"- {indicator}\n"
            analysis += "\n**Recovery Required**: System restart needed to clear error state.\n\n"

        initialization_failures = system_states.get('initialization_failures', [])
        if initialization_failures:
            analysis += "### 🚫 Initialization Failures\n\n"
            for failure in initialization_failures:
                analysis += f"**Component**: {failure['component']}\n"
                analysis += f"**Time**: {failure['timestamp']}\n"
                analysis += f"**Issue**: `{failure['message'][:100]}...`\n\n"

        current_state = system_states.get('current_state', 'unknown')
        analysis += f"### 📊 Current System State: {current_state.upper()}\n\n"

        if current_state == 'unknown':
            analysis += "**Status**: Cannot determine system state from available logs.\n"
            analysis += "**Recommendation**: Check for more recent log entries or restart system with verbose logging.\n\n"

        return analysis

    def _generate_detailed_technical_breakdown(self, parsed_logs: List[Dict], patterns: Dict) -> Dict:
        """
        Generate detailed technical breakdown for advanced analysis
        """
        return {
            'log_statistics': {
                'total_entries': len(parsed_logs),
                'error_entries': len([log for log in parsed_logs if log.get('level') in ['ERROR', 'FATAL']]),
                'components_detected': list(patterns.get('component_analysis', {}).get('component_activity', {}).keys()),
                'time_span': patterns.get('temporal_patterns', {}).get('total_time_span_hours', 0)
            },
            'technical_failure_summary': patterns.get('technical_failure_analysis', {}),
            'cascading_failure_details': patterns.get('cascading_failure_analysis', {}),
            'system_state_details': patterns.get('system_state_analysis', {}),
            'component_breakdown': patterns.get('component_analysis', {})
        }

    def _generate_executive_summary(self, patterns: Dict, business_context: Dict) -> str:
        """
        Generate executive summary in narrative style
        """
        # Get key metrics
        total_transactions = patterns.get('transaction_analysis', {}).get('total_transactions', 0)
        approval_rate = patterns.get('transaction_analysis', {}).get('approval_rate', 0) * 100
        error_rate = patterns.get('error_analysis', {}).get('error_rate', 0) * 100
        health_score = patterns.get('health_score', 0)
        
        # Determine overall system status
        if health_score >= 90:
            status = "excellent condition"
            status_emoji = "✅"
        elif health_score >= 75:
            status = "good condition with minor issues"
            status_emoji = "⚠️"
        elif health_score >= 50:
            status = "experiencing significant issues"
            status_emoji = "🚨"
        else:
            status = "critical condition requiring immediate attention"
            status_emoji = "🔴"
        
        summary = f"""
{status_emoji} **OpenEPS System Status: {status.title()}**

**System Overview:**
Your OpenEPS payment processing system processed {total_transactions} transactions during the analyzed period. 
The system achieved a {approval_rate:.1f}% approval rate with an overall error rate of {error_rate:.1f}%.

**Key Findings:**
"""
        
        # Add specific findings based on analysis
        if error_rate > 10:
            summary += f"• **High Error Rate Alert**: The {error_rate:.1f}% error rate significantly exceeds normal thresholds (typically <5%). This indicates system issues that require immediate attention.\n"
        
        if approval_rate < 90:
            summary += f"• **Low Approval Rate**: The {approval_rate:.1f}% approval rate is below optimal levels (typically >95%), potentially impacting revenue.\n"
        
        performance_issues = patterns.get('performance_analysis', {}).get('performance_issues', False)
        if performance_issues:
            summary += "• **Performance Issues Detected**: Transaction response times exceed acceptable thresholds, affecting customer experience.\n"
        
        # Business impact
        revenue_at_risk = business_context.get('revenue_impact', {}).get('estimated_revenue_at_risk', 0)
        if revenue_at_risk > 0:
            summary += f"• **Revenue Impact**: Estimated ${revenue_at_risk:.2f} in revenue at risk due to system issues.\n"
        
        summary += f"""
**Overall Health Score: {health_score:.0f}/100**

This analysis provides detailed insights into your OpenEPS system's performance, identifies specific issues, 
and offers actionable recommendations to improve system reliability and business outcomes.
"""
        
        return summary.strip()
    
    def _generate_detailed_analysis(self, parsed_logs: List[Dict], patterns: Dict, business_context: Dict) -> str:
        """
        Generate detailed technical and business analysis
        """
        analysis = "## Detailed System Analysis\n\n"
        
        # Transaction Analysis
        trans_analysis = patterns.get('transaction_analysis', {})
        analysis += "### Transaction Processing Analysis\n\n"
        analysis += f"**Total Transactions Processed:** {trans_analysis.get('total_transactions', 0)}\n"
        analysis += f"**Approval Rate:** {trans_analysis.get('approval_rate', 0)*100:.1f}%\n\n"
        
        # Payment type breakdown
        payment_types = trans_analysis.get('payment_types', {})
        if payment_types:
            analysis += "**Payment Type Distribution:**\n"
            for payment_type, count in payment_types.items():
                analysis += f"• {payment_type}: {count} transactions\n"
            analysis += "\n"
        
        # Error Analysis
        error_analysis = patterns.get('error_analysis', {})
        analysis += "### Error Analysis\n\n"
        analysis += f"**Total Errors:** {error_analysis.get('total_errors', 0)}\n"
        analysis += f"**Error Rate:** {error_analysis.get('error_rate', 0)*100:.1f}%\n\n"
        
        error_categories = error_analysis.get('error_categories', {})
        if error_categories:
            analysis += "**Error Categories:**\n"
            for category, data in error_categories.items():
                if data['count'] > 0:
                    analysis += f"• **{category.replace('_', ' ').title()}**: {data['count']} occurrences\n"
                    if data['examples']:
                        analysis += f"  - Example: {data['examples'][0]}\n"
            analysis += "\n"
        
        # Performance Analysis
        perf_analysis = patterns.get('performance_analysis', {})
        analysis += "### Performance Analysis\n\n"
        avg_response = perf_analysis.get('average_response_time', 0)
        analysis += f"**Average Response Time:** {avg_response:.2f} seconds\n"
        
        if avg_response > 3.0:
            analysis += "⚠️ **Performance Issue**: Response times exceed acceptable thresholds (3 seconds)\n"
        
        slow_transactions = perf_analysis.get('slow_transaction_count', 0)
        if slow_transactions > 0:
            analysis += f"**Slow Transactions:** {slow_transactions} transactions took longer than expected\n"
        
        return analysis
    
    def _generate_transaction_flow_analysis(self, parsed_logs: List[Dict]) -> str:
        """
        Generate transaction flow analysis with step-by-step breakdown
        """
        flow_analysis = "## Transaction Flow Analysis\n\n"
        
        # Find complete transaction flows
        transaction_flows = self._extract_transaction_flows(parsed_logs)
        
        if transaction_flows:
            flow_analysis += f"**{len(transaction_flows)} complete transaction flows identified**\n\n"
            
            # Analyze first transaction flow as example
            if transaction_flows:
                example_flow = transaction_flows[0]
                flow_analysis += "### Example Transaction Flow:\n\n"
                
                for step_num, step in enumerate(example_flow, 1):
                    timestamp = step.get('timestamp')
                    message = step.get('message', '')
                    
                    time_str = timestamp.strftime('%H:%M:%S.%f')[:-3] if timestamp else 'Unknown'
                    flow_analysis += f"**Step {step_num}** ({time_str}): {message[:100]}...\n"
                
                flow_analysis += "\n**Flow Analysis:**\n"
                flow_analysis += self._analyze_transaction_flow_health(example_flow)
        else:
            flow_analysis += "No complete transaction flows found in the analyzed logs.\n"
        
        return flow_analysis
    
    def _generate_error_explanations(self, patterns: Dict) -> List[Dict]:
        """
        Generate detailed explanations for detected errors
        """
        explanations = []
        
        error_categories = patterns.get('error_analysis', {}).get('error_categories', {})
        
        for category, data in error_categories.items():
            if data['count'] > 0 and category in self.error_explanations:
                explanation = self.error_explanations[category].copy()
                explanation['occurrence_count'] = data['count']
                explanation['examples'] = data['examples']
                explanations.append(explanation)
        
        return explanations
    
    def _generate_business_impact_assessment(self, business_context: Dict) -> str:
        """
        Generate business impact assessment
        """
        assessment = "## Business Impact Assessment\n\n"
        
        # Revenue impact
        revenue_impact = business_context.get('revenue_impact', {})
        total_value = revenue_impact.get('total_transaction_value', 0)
        declined_value = revenue_impact.get('declined_transaction_value', 0)
        
        assessment += f"**Total Transaction Value:** ${total_value:.2f}\n"
        assessment += f"**Declined Transaction Value:** ${declined_value:.2f}\n"
        
        if declined_value > 0:
            decline_rate = (declined_value / total_value * 100) if total_value > 0 else 0
            assessment += f"**Revenue Loss Rate:** {decline_rate:.1f}%\n"
        
        # Customer experience
        customer_exp = business_context.get('customer_experience', {})
        satisfaction_score = customer_exp.get('customer_satisfaction_score', 0)
        assessment += f"\n**Customer Satisfaction Score:** {satisfaction_score:.0f}/100\n"
        
        if satisfaction_score < 80:
            assessment += "⚠️ **Customer Experience Alert**: Low satisfaction score indicates customer experience issues\n"
        
        return assessment
    
    def _generate_recommendations(self, patterns: Dict, business_context: Dict) -> List[Dict]:
        """
        Generate actionable recommendations
        """
        recommendations = []
        
        # Performance recommendations
        perf_issues = patterns.get('performance_analysis', {}).get('performance_issues', False)
        if perf_issues:
            recommendations.append({
                'priority': 'High',
                'category': 'Performance',
                'title': 'Address Performance Issues',
                'description': 'Transaction response times are affecting customer experience',
                'actions': [
                    'Monitor network connectivity to payment processors',
                    'Check system resources (CPU, memory, disk)',
                    'Consider load balancing or processor failover',
                    'Review transaction routing configuration'
                ]
            })
        
        # Error rate recommendations
        error_rate = patterns.get('error_analysis', {}).get('error_rate', 0)
        if error_rate > 0.1:
            recommendations.append({
                'priority': 'High',
                'category': 'Reliability',
                'title': 'Reduce Error Rate',
                'description': f'Error rate of {error_rate*100:.1f}% exceeds acceptable thresholds',
                'actions': [
                    'Investigate root causes of most common errors',
                    'Review system logs for patterns',
                    'Check configuration files for corruption',
                    'Verify network connectivity stability'
                ]
            })
        
        return recommendations
    
    def _generate_technical_details(self, parsed_logs: List[Dict], patterns: Dict) -> Dict:
        """
        Generate technical details for advanced users
        """
        return {
            'log_statistics': {
                'total_entries': len(parsed_logs),
                'components_detected': list(patterns.get('component_analysis', {}).get('component_activity', {}).keys()),
                'time_span': patterns.get('temporal_patterns', {}).get('total_time_span_hours', 0)
            },
            'component_breakdown': patterns.get('component_analysis', {}),
            'temporal_patterns': patterns.get('temporal_patterns', {})
        }
    
    def _extract_transaction_flows(self, parsed_logs: List[Dict]) -> List[List[Dict]]:
        """
        Extract complete transaction flows from logs
        """
        flows = []
        current_flow = []
        
        for log in parsed_logs:
            message = log.get('message', '').lower()
            
            # Start of transaction
            if any(keyword in message for keyword in ['sendtransaction', 'transaction started']):
                if current_flow:  # Save previous flow
                    flows.append(current_flow)
                current_flow = [log]
            
            # Part of current transaction
            elif current_flow and any(keyword in message for keyword in ['transaction', 'response', 'auth']):
                current_flow.append(log)
            
            # End of transaction
            elif current_flow and any(keyword in message for keyword in ['transaction complete', 'approved', 'declined']):
                current_flow.append(log)
                flows.append(current_flow)
                current_flow = []
        
        return flows
    
    def _analyze_transaction_flow_health(self, flow: List[Dict]) -> str:
        """
        Analyze the health of a transaction flow
        """
        if not flow:
            return "No flow data available"
        
        start_time = flow[0].get('timestamp')
        end_time = flow[-1].get('timestamp')
        
        if start_time and end_time:
            duration = (end_time - start_time).total_seconds()
            analysis = f"Transaction completed in {duration:.2f} seconds. "
            
            if duration < 3.0:
                analysis += "✅ Excellent response time."
            elif duration < 6.0:
                analysis += "⚠️ Acceptable response time."
            else:
                analysis += "🚨 Slow response time - investigate performance issues."
        else:
            analysis = "Unable to calculate transaction duration."
        
        # Check for errors in flow
        errors = [step for step in flow if step.get('level') in ['ERROR', 'FATAL']]
        if errors:
            analysis += f" {len(errors)} errors detected in transaction flow."
        
        return analysis
    
    def get_pattern_explanation(self, pattern_type: str) -> Dict:
        """
        Get detailed explanation for a specific pattern type
        """
        if pattern_type in self.error_explanations:
            return self.error_explanations[pattern_type]
        else:
            return {
                'title': f'Unknown Pattern: {pattern_type}',
                'explanation': 'No detailed explanation available for this pattern type.'
            }
