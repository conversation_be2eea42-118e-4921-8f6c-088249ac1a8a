#!/usr/bin/env python3
"""
OpenEPS Log Analyzer - Simple Startup Script
Run this script from anywhere to start the OpenEPS Log Analyzer
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Find and start the OpenEPS Log Analyzer"""
    print("🚀 OpenEPS Log Analyzer - Quick Start")
    print("=" * 50)
    
    # Get the directory where this script is located
    script_dir = Path(__file__).parent.absolute()
    
    # Change to the script directory
    os.chdir(script_dir)
    print(f"📁 Working directory: {script_dir}")
    
    # Check if app.py exists
    if not (script_dir / 'app.py').exists():
        print("❌ Error: app.py not found in the script directory")
        print(f"   Looking in: {script_dir}")
        sys.exit(1)
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ Error: Python 3.7 or higher is required")
        print(f"   Current version: {sys.version}")
        sys.exit(1)
    
    # Try to install dependencies if needed
    try:
        import flask
        print("✅ Flask is available")
    except ImportError:
        print("📦 Installing Flask and dependencies...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✅ Dependencies installed")
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            print("   Please run manually: pip install -r requirements.txt")
            sys.exit(1)
    
    # Start the application
    print("\n🌐 Starting OpenEPS Log Analyzer...")
    print("   URL: http://localhost:5000")
    print("   Press Ctrl+C to stop")
    print("=" * 50)
    
    try:
        # Import and run the Flask app
        sys.path.insert(0, str(script_dir))
        from app import app
        app.run(debug=False, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n\n👋 OpenEPS Log Analyzer stopped")
    except Exception as e:
        print(f"\n❌ Error starting application: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure you're in the correct directory")
        print("2. Check that all files are present")
        print("3. Try: pip install -r requirements.txt")
        sys.exit(1)

if __name__ == '__main__':
    main()
