#!/usr/bin/env python3
"""
OpenEPS Log Analyzer
A comprehensive web application for analyzing OpenEPS logs with intelligent explanations.

This application provides the same detailed, narrative explanations as the OpenEPS guides,
but in an interactive format for real-time log analysis.
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
import os
import re
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import logging

# Import our analysis modules
from log_parser import OpenEPSLogParser
from pattern_analyzer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from business_analyzer import BusinessAnalyzer
from explanation_generator import ExplanationGenerator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Initialize analysis components
log_parser = OpenEPSLogParser()
pattern_analyzer = PatternAnalyzer()
business_analyzer = BusinessAnalyzer()
explanation_generator = ExplanationGenerator()

@app.route('/')
def index():
    """Main application page"""
    return render_template('index.html')

@app.route('/analyze', methods=['POST'])
def analyze_logs():
    """
    Main log analysis endpoint
    Accepts log content and returns comprehensive analysis
    """
    try:
        # Get log content from request
        log_content = request.json.get('log_content', '')
        analysis_type = request.json.get('analysis_type', 'comprehensive')
        
        if not log_content.strip():
            return jsonify({
                'error': 'No log content provided',
                'success': False
            })
        
        # Parse the logs
        logger.info(f"Analyzing {len(log_content)} characters of log data")
        parsed_logs = log_parser.parse_logs(log_content)
        
        # Analyze patterns
        patterns = pattern_analyzer.analyze_patterns(parsed_logs)
        
        # Business context analysis
        business_context = business_analyzer.analyze_business_context(parsed_logs, patterns)
        
        # Generate explanations
        explanations = explanation_generator.generate_explanations(
            parsed_logs, patterns, business_context, analysis_type
        )
        
        # Compile comprehensive analysis result
        analysis_result = {
            'success': True,
            'timestamp': datetime.now().isoformat(),
            'log_summary': {
                'total_entries': len(parsed_logs),
                'time_span': _get_time_span(parsed_logs),
                'components_detected': _get_components(parsed_logs),
                'transaction_count': _count_transactions(parsed_logs)
            },
            'patterns': patterns,
            'business_context': business_context,
            'explanations': explanations,
            'recommendations': _generate_recommendations(patterns, business_context)
        }
        
        return jsonify(analysis_result)
        
    except Exception as e:
        logger.error(f"Error analyzing logs: {str(e)}")
        return jsonify({
            'error': f'Analysis failed: {str(e)}',
            'success': False
        })

@app.route('/upload', methods=['POST'])
def upload_file():
    """
    Handle file upload for log analysis
    """
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided', 'success': False})
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected', 'success': False})
        
        # Read file content
        content = file.read().decode('utf-8', errors='ignore')
        
        # Analyze the content
        return analyze_logs_content(content)
        
    except Exception as e:
        logger.error(f"Error uploading file: {str(e)}")
        return jsonify({
            'error': f'File upload failed: {str(e)}',
            'success': False
        })

def analyze_logs_content(content: str) -> Dict:
    """Helper function to analyze log content"""
    parsed_logs = log_parser.parse_logs(content)
    patterns = pattern_analyzer.analyze_patterns(parsed_logs)
    business_context = business_analyzer.analyze_business_context(parsed_logs, patterns)
    explanations = explanation_generator.generate_explanations(
        parsed_logs, patterns, business_context, 'comprehensive'
    )
    
    return {
        'success': True,
        'timestamp': datetime.now().isoformat(),
        'log_summary': {
            'total_entries': len(parsed_logs),
            'time_span': _get_time_span(parsed_logs),
            'components_detected': _get_components(parsed_logs),
            'transaction_count': _count_transactions(parsed_logs)
        },
        'patterns': patterns,
        'business_context': business_context,
        'explanations': explanations,
        'recommendations': _generate_recommendations(patterns, business_context)
    }

@app.route('/explain/<pattern_type>')
def explain_pattern(pattern_type: str):
    """
    Get detailed explanation for a specific pattern type
    """
    try:
        explanation = explanation_generator.get_pattern_explanation(pattern_type)
        return jsonify({
            'success': True,
            'pattern_type': pattern_type,
            'explanation': explanation
        })
    except Exception as e:
        return jsonify({
            'error': f'Failed to get explanation: {str(e)}',
            'success': False
        })

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

# Helper functions
def _get_time_span(parsed_logs: List[Dict]) -> Dict:
    """Calculate time span of logs"""
    if not parsed_logs:
        return {'start': None, 'end': None, 'duration': None}
    
    timestamps = [log.get('timestamp') for log in parsed_logs if log.get('timestamp')]
    if not timestamps:
        return {'start': None, 'end': None, 'duration': None}
    
    start_time = min(timestamps)
    end_time = max(timestamps)
    duration = (end_time - start_time).total_seconds() if start_time and end_time else 0
    
    return {
        'start': start_time.isoformat() if start_time else None,
        'end': end_time.isoformat() if end_time else None,
        'duration_seconds': duration
    }

def _get_components(parsed_logs: List[Dict]) -> List[str]:
    """Get list of detected components"""
    components = set()
    for log in parsed_logs:
        if log.get('source'):
            components.add(log['source'])
    return sorted(list(components))

def _count_transactions(parsed_logs: List[Dict]) -> int:
    """Count number of transactions in logs"""
    transaction_count = 0
    for log in parsed_logs:
        if 'transaction' in log.get('message', '').lower():
            if any(keyword in log['message'].lower() for keyword in 
                   ['sendtransaction', 'transaction started', 'transaction complete']):
                transaction_count += 1
    return transaction_count

def _generate_recommendations(patterns: Dict, business_context: Dict) -> List[Dict]:
    """Generate actionable recommendations based on analysis"""
    recommendations = []
    
    # Check for performance issues
    if patterns.get('performance_issues'):
        recommendations.append({
            'type': 'performance',
            'priority': 'high',
            'title': 'Performance Issues Detected',
            'description': 'Slow response times detected that may impact customer experience',
            'actions': [
                'Check network connectivity to payment processors',
                'Monitor host response times',
                'Consider load balancing if multiple hosts available'
            ]
        })
    
    # Check for error patterns
    if patterns.get('error_rate', 0) > 0.1:  # More than 10% errors
        recommendations.append({
            'type': 'reliability',
            'priority': 'high',
            'title': 'High Error Rate Detected',
            'description': f"Error rate of {patterns.get('error_rate', 0)*100:.1f}% exceeds normal thresholds",
            'actions': [
                'Review error logs for common failure patterns',
                'Check system resources (memory, disk space)',
                'Verify configuration files are not corrupted'
            ]
        })
    
    # Check for offline transactions
    if business_context.get('offline_transactions', 0) > 0:
        recommendations.append({
            'type': 'connectivity',
            'priority': 'medium',
            'title': 'Offline Transactions Detected',
            'description': 'System operated in offline mode, ensure transactions are reconciled',
            'actions': [
                'Verify all offline transactions were uploaded to host',
                'Check network connectivity stability',
                'Review offline approval limits'
            ]
        })
    
    return recommendations

if __name__ == '__main__':
    # Create necessary directories
    os.makedirs('static', exist_ok=True)
    os.makedirs('templates', exist_ok=True)

    # Try different ports if 5000 is busy
    ports_to_try = [5001, 5002, 5003, 8000, 8080]

    for port in ports_to_try:
        try:
            print(f"🌐 Starting OpenEPS Log Analyzer on port {port}...")
            print(f"   URL: http://localhost:{port}")
            print("   Press Ctrl+C to stop")
            print("=" * 50)
            app.run(debug=True, host='0.0.0.0', port=port)
            break
        except OSError as e:
            if "Address already in use" in str(e):
                print(f"Port {port} is busy, trying next port...")
                continue
            else:
                raise e
    else:
        print("❌ Could not find an available port. Please check your system.")
