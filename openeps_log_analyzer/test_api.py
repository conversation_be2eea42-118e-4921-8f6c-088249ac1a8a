#!/usr/bin/env python3
"""
Test script to debug API connectivity issues
"""

import requests
import json

def test_api_connection():
    """Test API endpoints to debug connectivity issues"""
    
    # Test different ports
    ports = [5001, 5002, 5003, 8000, 8080]
    
    for port in ports:
        base_url = f"http://localhost:{port}"
        print(f"\n🔍 Testing port {port}...")
        
        # Test health endpoint
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            if response.status_code == 200:
                print(f"✅ Health check successful on port {port}")
                print(f"   Response: {response.json()}")
                
                # Test the analyze endpoint
                test_analyze_endpoint(base_url)
                return port
            else:
                print(f"❌ Health check failed: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"❌ Connection refused on port {port}")
        except requests.exceptions.Timeout:
            print(f"❌ Timeout on port {port}")
        except Exception as e:
            print(f"❌ Error on port {port}: {e}")
    
    print("\n❌ No working API found on any port")
    return None

def test_analyze_endpoint(base_url):
    """Test the analyze endpoint specifically"""
    print(f"🧪 Testing analyze endpoint...")
    
    # Test data
    test_data = {
        "log_content": "12/11/24 14:23:45.123 [OpenEPS] Test log entry",
        "analysis_type": "quick"
    }
    
    try:
        response = requests.post(
            f"{base_url}/analyze",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ Analyze endpoint working")
            result = response.json()
            print(f"   Success: {result.get('success')}")
            print(f"   Entries: {result.get('log_summary', {}).get('total_entries', 'N/A')}")
        else:
            print(f"❌ Analyze endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Analyze endpoint error: {e}")

def test_browser_compatibility():
    """Test what a browser would send"""
    print(f"\n🌐 Testing browser-like request...")
    
    # Find working port first
    working_port = test_api_connection()
    if not working_port:
        return
    
    base_url = f"http://localhost:{working_port}"
    
    # Simulate browser request
    test_data = {
        "log_content": """12/11/24 14:23:45.123 [OpenEPS] MTX_POS_SET_PurchaseAmount: 4567
12/11/24 14:23:45.124 [OpenEPS] MTX_POS_SET_TenderType: 2
12/11/24 14:23:45.125 [OpenEPS] MTX_POS_SendTransaction: Starting
12/11/24 14:23:48.234 [HOST] Response received: 00 (Approved)""",
        "analysis_type": "comprehensive"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Test Script)'
    }
    
    try:
        response = requests.post(
            f"{base_url}/analyze",
            json=test_data,
            headers=headers,
            timeout=15
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Full analysis successful!")
            print(f"   Total entries: {result.get('log_summary', {}).get('total_entries')}")
            print(f"   Components: {result.get('log_summary', {}).get('components_detected')}")
            print(f"   Health score: {result.get('patterns', {}).get('health_score', 'N/A')}")
        else:
            print(f"❌ Analysis failed")
            print(f"Response: {response.text[:500]}...")
            
    except Exception as e:
        print(f"❌ Browser test failed: {e}")

if __name__ == '__main__':
    print("🚀 OpenEPS Log Analyzer - API Test")
    print("=" * 50)
    
    test_browser_compatibility()
    
    print("\n" + "=" * 50)
    print("✅ Test completed")
