"""
OpenEPS Business Analyzer
Analyzes logs from a business perspective to understand operational impact
"""

import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from collections import defaultdict, Counter
import logging

logger = logging.getLogger(__name__)

class BusinessAnalyzer:
    """
    Analyzes OpenEPS logs from a business operations perspective
    Focuses on revenue impact, customer experience, and operational efficiency
    """
    
    def __init__(self):
        # Business impact thresholds
        self.impact_thresholds = {
            'high_value_transaction': 100.00,  # $100+
            'acceptable_response_time': 3.0,   # 3 seconds
            'poor_response_time': 10.0,        # 10 seconds
            'acceptable_approval_rate': 0.95,  # 95%
            'poor_approval_rate': 0.85         # 85%
        }
        
        # Lane type mappings
        self.lane_types = {
            'G': 'General/Attended',
            'U': 'Unattended/Self-Checkout',
            'F': 'Fuel/Gas Station',
            'P': 'Pharmacy',
            'H': 'Hospitality',
            'K': 'Kiosk'
        }
        
        # Payment type business rules
        self.payment_types = {
            1: {'name': 'Debit', 'typical_amount_range': (5, 200), 'requires_pin': True},
            2: {'name': 'Credit', 'typical_amount_range': (10, 500), 'requires_pin': False},
            3: {'name': 'EBT Food Stamps', 'typical_amount_range': (5, 150), 'requires_pin': True},
            4: {'name': 'EBT Cash', 'typical_amount_range': (5, 100), 'requires_pin': True},
            9: {'name': 'Gift Card', 'typical_amount_range': (5, 200), 'requires_pin': False}
        }
        
        # Business hours (can be configured)
        self.business_hours = {
            'peak_hours': [(11, 13), (17, 19)],  # 11-1pm, 5-7pm
            'business_start': 6,  # 6 AM
            'business_end': 22    # 10 PM
        }
    
    def analyze_business_context(self, parsed_logs: List[Dict], patterns: Dict) -> Dict:
        """
        Comprehensive business analysis of OpenEPS operations
        """
        if not parsed_logs:
            return {'error': 'No logs to analyze'}
        
        logger.info(f"Analyzing business context for {len(parsed_logs)} log entries")
        
        analysis = {
            'revenue_impact': self._analyze_revenue_impact(parsed_logs, patterns),
            'customer_experience': self._analyze_customer_experience(parsed_logs, patterns),
            'operational_efficiency': self._analyze_operational_efficiency(parsed_logs, patterns),
            'compliance_status': self._analyze_compliance(parsed_logs),
            'business_continuity': self._analyze_business_continuity(parsed_logs),
            'lane_performance': self._analyze_lane_performance(parsed_logs),
            'payment_mix': self._analyze_payment_mix(parsed_logs),
            'peak_hour_analysis': self._analyze_peak_hours(parsed_logs)
        }
        
        # Calculate overall business health score
        analysis['business_health_score'] = self._calculate_business_health_score(analysis)
        
        return analysis
    
    def _analyze_revenue_impact(self, parsed_logs: List[Dict], patterns: Dict) -> Dict:
        """
        Analyze potential revenue impact from system issues
        """
        revenue_data = {
            'total_transaction_value': 0.0,
            'declined_transaction_value': 0.0,
            'offline_transaction_value': 0.0,
            'failed_transaction_count': 0,
            'high_value_transactions_affected': 0,
            'estimated_revenue_at_risk': 0.0
        }
        
        for log in parsed_logs:
            message = log.get('message', '')
            structured_data = log.get('structured_data', {})
            
            # Extract transaction amounts
            amount_match = re.search(r'amount[:\s]*\$?(\d+\.?\d*)', message, re.IGNORECASE)
            if amount_match:
                amount = float(amount_match.group(1))
                revenue_data['total_transaction_value'] += amount
                
                # Check for declined transactions
                response_code = structured_data.get('response_codes')
                if response_code and response_code != '00':
                    revenue_data['declined_transaction_value'] += amount
                    if amount > self.impact_thresholds['high_value_transaction']:
                        revenue_data['high_value_transactions_affected'] += 1
                
                # Check for offline transactions
                if 'offline' in message.lower():
                    revenue_data['offline_transaction_value'] += amount
            
            # Count failed transactions
            if any(keyword in message.lower() for keyword in ['failed', 'error', 'timeout']):
                if 'transaction' in message.lower():
                    revenue_data['failed_transaction_count'] += 1
        
        # Estimate revenue at risk
        error_rate = patterns.get('error_analysis', {}).get('error_rate', 0)
        avg_transaction_value = revenue_data['total_transaction_value'] / max(1, patterns.get('transaction_analysis', {}).get('total_transactions', 1))
        revenue_data['estimated_revenue_at_risk'] = error_rate * avg_transaction_value * 100  # Estimate for 100 transactions
        
        return revenue_data
    
    def _analyze_customer_experience(self, parsed_logs: List[Dict], patterns: Dict) -> Dict:
        """
        Analyze customer experience metrics
        """
        experience_data = {
            'average_transaction_time': 0.0,
            'slow_transaction_count': 0,
            'customer_facing_errors': 0,
            'terminal_issues': 0,
            'card_read_failures': 0,
            'customer_satisfaction_score': 0.0
        }
        
        # Get performance data
        perf_analysis = patterns.get('performance_analysis', {})
        experience_data['average_transaction_time'] = perf_analysis.get('average_response_time', 0)
        experience_data['slow_transaction_count'] = perf_analysis.get('slow_transaction_count', 0)
        
        for log in parsed_logs:
            message = log.get('message', '').lower()
            
            # Customer-facing errors
            if any(keyword in message for keyword in ['declined', 'card error', 'please try again']):
                experience_data['customer_facing_errors'] += 1
            
            # Terminal issues affecting customers
            if any(keyword in message for keyword in ['terminal not responding', 'bad card read', 'swipe again']):
                experience_data['terminal_issues'] += 1
            
            # Card read failures
            if any(keyword in message for keyword in ['bad card read', 'card read error', 'magnetic stripe']):
                experience_data['card_read_failures'] += 1
        
        # Calculate customer satisfaction score (0-100)
        base_score = 100
        if experience_data['average_transaction_time'] > self.impact_thresholds['acceptable_response_time']:
            base_score -= 20
        if experience_data['average_transaction_time'] > self.impact_thresholds['poor_response_time']:
            base_score -= 30
        
        approval_rate = patterns.get('transaction_analysis', {}).get('approval_rate', 1.0)
        if approval_rate < self.impact_thresholds['acceptable_approval_rate']:
            base_score -= 25
        
        experience_data['customer_satisfaction_score'] = max(0, base_score)
        
        return experience_data
    
    def _analyze_operational_efficiency(self, parsed_logs: List[Dict], patterns: Dict) -> Dict:
        """
        Analyze operational efficiency metrics
        """
        efficiency_data = {
            'system_uptime_percentage': 0.0,
            'transaction_throughput': 0.0,
            'error_resolution_time': 0.0,
            'offline_periods': 0,
            'configuration_issues': 0,
            'manual_interventions': 0
        }
        
        # Calculate system uptime
        total_logs = len(parsed_logs)
        error_logs = len([log for log in parsed_logs if log.get('level') in ['ERROR', 'FATAL']])
        efficiency_data['system_uptime_percentage'] = ((total_logs - error_logs) / total_logs * 100) if total_logs > 0 else 0
        
        # Calculate transaction throughput
        time_span = patterns.get('temporal_patterns', {}).get('total_time_span_hours', 1)
        total_transactions = patterns.get('transaction_analysis', {}).get('total_transactions', 0)
        efficiency_data['transaction_throughput'] = total_transactions / time_span if time_span > 0 else 0
        
        # Count operational issues
        for log in parsed_logs:
            message = log.get('message', '').lower()
            
            if 'offline' in message:
                efficiency_data['offline_periods'] += 1
            
            if any(keyword in message for keyword in ['configuration', 'setup.txt', 'config error']):
                efficiency_data['configuration_issues'] += 1
            
            if any(keyword in message for keyword in ['manual', 'override', 'manager']):
                efficiency_data['manual_interventions'] += 1
        
        return efficiency_data
    
    def _analyze_compliance(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze compliance-related activities and issues
        """
        compliance_data = {
            'pci_compliance_events': 0,
            'audit_trail_completeness': 0.0,
            'security_violations': 0,
            'data_encryption_status': 'unknown',
            'compliance_score': 0.0
        }
        
        audit_events = 0
        security_events = 0
        
        for log in parsed_logs:
            message = log.get('message', '').lower()
            
            # PCI compliance events
            if any(keyword in message for keyword in ['pci', 'card data', 'encryption']):
                compliance_data['pci_compliance_events'] += 1
            
            # Security events
            if any(keyword in message for keyword in ['security', 'authentication', 'certificate']):
                security_events += 1
                if log.get('level') in ['ERROR', 'FATAL']:
                    compliance_data['security_violations'] += 1
            
            # Audit trail events
            if any(keyword in message for keyword in ['transaction', 'auth', 'response']):
                audit_events += 1
            
            # Data encryption status
            if 'encryption' in message:
                if 'failed' in message or 'error' in message:
                    compliance_data['data_encryption_status'] = 'compromised'
                else:
                    compliance_data['data_encryption_status'] = 'active'
        
        # Calculate audit trail completeness
        total_transactions = len([log for log in parsed_logs if 'transaction' in log.get('message', '').lower()])
        compliance_data['audit_trail_completeness'] = (audit_events / max(1, total_transactions)) * 100
        
        # Calculate compliance score
        base_score = 100
        if compliance_data['security_violations'] > 0:
            base_score -= 30
        if compliance_data['data_encryption_status'] == 'compromised':
            base_score -= 40
        if compliance_data['audit_trail_completeness'] < 90:
            base_score -= 20
        
        compliance_data['compliance_score'] = max(0, base_score)
        
        return compliance_data
    
    def _analyze_business_continuity(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze business continuity and disaster recovery aspects
        """
        continuity_data = {
            'offline_transaction_count': 0,
            'system_recovery_events': 0,
            'backup_system_activations': 0,
            'data_backup_events': 0,
            'continuity_score': 0.0
        }
        
        for log in parsed_logs:
            message = log.get('message', '').lower()
            
            if 'offline' in message and 'transaction' in message:
                continuity_data['offline_transaction_count'] += 1
            
            if any(keyword in message for keyword in ['recovery', 'restart', 'failover']):
                continuity_data['system_recovery_events'] += 1
            
            if any(keyword in message for keyword in ['backup', 'secondary', 'failover']):
                continuity_data['backup_system_activations'] += 1
            
            if any(keyword in message for keyword in ['upload', 'journal', 'backup']):
                continuity_data['data_backup_events'] += 1
        
        # Calculate continuity score
        base_score = 100
        if continuity_data['offline_transaction_count'] > 10:
            base_score -= 20
        if continuity_data['system_recovery_events'] > 5:
            base_score -= 15
        
        continuity_data['continuity_score'] = max(0, base_score)
        
        return continuity_data
    
    def _analyze_lane_performance(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze performance by lane
        """
        lane_data = defaultdict(lambda: {
            'transaction_count': 0,
            'error_count': 0,
            'average_response_time': 0.0,
            'lane_type': 'unknown'
        })
        
        for log in parsed_logs:
            message = log.get('message', '')
            
            # Extract lane number
            lane_match = re.search(r'lane\s*(\d+)', message, re.IGNORECASE)
            if lane_match:
                lane_num = lane_match.group(1)
                
                if 'transaction' in message.lower():
                    lane_data[lane_num]['transaction_count'] += 1
                
                if log.get('level') in ['ERROR', 'FATAL']:
                    lane_data[lane_num]['error_count'] += 1
                
                # Extract lane type if available
                type_match = re.search(r'type[:\s]*([GUFPHK])', message, re.IGNORECASE)
                if type_match:
                    lane_type = type_match.group(1).upper()
                    lane_data[lane_num]['lane_type'] = self.lane_types.get(lane_type, lane_type)
        
        return dict(lane_data)
    
    def _analyze_payment_mix(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze payment type distribution and performance
        """
        payment_data = {
            'payment_type_distribution': Counter(),
            'payment_type_performance': defaultdict(lambda: {
                'count': 0,
                'approval_rate': 0.0,
                'average_amount': 0.0
            })
        }
        
        for log in parsed_logs:
            message = log.get('message', '')
            structured_data = log.get('structured_data', {})
            
            # Extract tender type
            tender_match = re.search(r'tender.*?type[:\s]*(\d+)', message, re.IGNORECASE)
            if tender_match:
                tender_type = int(tender_match.group(1))
                payment_info = self.payment_types.get(tender_type, {'name': f'Type_{tender_type}'})
                payment_name = payment_info['name']
                
                payment_data['payment_type_distribution'][payment_name] += 1
                payment_data['payment_type_performance'][payment_name]['count'] += 1
        
        return payment_data
    
    def _analyze_peak_hours(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze system performance during peak business hours
        """
        peak_data = {
            'peak_hour_performance': {},
            'off_peak_performance': {},
            'peak_hour_issues': 0,
            'capacity_utilization': 0.0
        }
        
        peak_logs = []
        off_peak_logs = []
        
        for log in parsed_logs:
            timestamp = log.get('timestamp')
            if timestamp:
                hour = timestamp.hour
                is_peak = any(start <= hour < end for start, end in self.business_hours['peak_hours'])
                
                if is_peak:
                    peak_logs.append(log)
                else:
                    off_peak_logs.append(log)
        
        # Analyze peak vs off-peak performance
        peak_errors = len([log for log in peak_logs if log.get('level') in ['ERROR', 'FATAL']])
        off_peak_errors = len([log for log in off_peak_logs if log.get('level') in ['ERROR', 'FATAL']])
        
        peak_data['peak_hour_performance'] = {
            'total_activity': len(peak_logs),
            'error_count': peak_errors,
            'error_rate': peak_errors / len(peak_logs) if peak_logs else 0
        }
        
        peak_data['off_peak_performance'] = {
            'total_activity': len(off_peak_logs),
            'error_count': off_peak_errors,
            'error_rate': off_peak_errors / len(off_peak_logs) if off_peak_logs else 0
        }
        
        return peak_data
    
    def _calculate_business_health_score(self, analysis: Dict) -> float:
        """
        Calculate overall business health score (0-100)
        """
        scores = []
        
        # Customer experience score
        customer_score = analysis.get('customer_experience', {}).get('customer_satisfaction_score', 50)
        scores.append(customer_score)
        
        # Compliance score
        compliance_score = analysis.get('compliance_status', {}).get('compliance_score', 50)
        scores.append(compliance_score)
        
        # Business continuity score
        continuity_score = analysis.get('business_continuity', {}).get('continuity_score', 50)
        scores.append(continuity_score)
        
        # Operational efficiency (uptime percentage)
        efficiency_score = analysis.get('operational_efficiency', {}).get('system_uptime_percentage', 50)
        scores.append(efficiency_score)
        
        return sum(scores) / len(scores) if scores else 0
