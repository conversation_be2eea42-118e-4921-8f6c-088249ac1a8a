@echo off
REM OpenEPS Log Analyzer Startup Script for Windows
REM This script can be run from anywhere and will find and start the analyzer

echo 🚀 OpenEPS Log Analyzer - Startup Script
echo ==================================================

REM Get the directory where this batch file is located
set SCRIPT_DIR=%~dp0

echo 📁 Script directory: %SCRIPT_DIR%

REM Change to the script directory
cd /d "%SCRIPT_DIR%"

echo 📁 Changed to: %CD%

REM Check if app.py exists
if not exist "app.py" (
    echo ❌ Error: app.py not found in %SCRIPT_DIR%
    echo    Please make sure all files are in the correct location
    pause
    exit /b 1
)

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Python not found
    echo    Please install Python 3.7 or higher
    echo    Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 🐍 Using Python: 
python --version

REM Check if requirements are installed
echo 📦 Checking dependencies...
python -c "import flask" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 Installing dependencies...
    python -m pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies
        echo    Please run manually: pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo ✅ Dependencies OK
echo.
echo 🌐 Starting OpenEPS Log Analyzer...
echo    URL: http://localhost:5000
echo    Press Ctrl+C to stop
echo ==================================================

REM Start the application
python app.py

pause
