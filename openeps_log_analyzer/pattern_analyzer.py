"""
OpenEPS Pattern Analyzer
Analyzes log patterns to identify system behaviors, issues, and trends
"""

import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from collections import defaultdict, Counter
import logging

logger = logging.getLogger(__name__)

class PatternAnalyzer:
    """
    Analyzes OpenEPS logs for patterns, anomalies, and system behaviors
    """
    
    def __init__(self):
        # Define pattern recognition rules
        self.error_patterns = {
            'host_timeout': [
                'timeout', 'connection timeout', 'host.*timeout', 'no response'
            ],
            'terminal_error': [
                'terminal.*not responding', 'terminal.*error', 'bad card read',
                'terminal communication failed'
            ],
            'configuration_error': [
                'setup.txt.*error', 'configuration.*error', 'file.*corrupted',
                'missing.*configuration'
            ],
            'memory_error': [
                'memory.*error', 'out of memory', 'allocation.*failed',
                'low memory'
            ],
            'ssl_error': [
                'ssl.*error', 'certificate.*error', 'handshake.*failed',
                'ssl.*not established'
            ]
        }
        
        self.performance_indicators = {
            'slow_response': 5.0,      # seconds
            'very_slow_response': 10.0, # seconds
            'timeout_threshold': 30.0   # seconds
        }
        
        self.business_patterns = {
            'transaction_types': [
                'purchase', 'return', 'void', 'balance inquiry', 'pre-auth'
            ],
            'payment_types': [
                'credit', 'debit', 'ebt', 'gift card', 'fleet'
            ],
            'response_codes': {
                '00': 'Approved',
                '01': 'Refer to Card Issuer',
                '05': 'Do Not Honor',
                '12': 'Invalid Transaction',
                '14': 'Invalid Card Number',
                '51': 'Insufficient Funds',
                '54': 'Expired Card',
                '91': 'Issuer Inoperative'
            }
        }
    
    def analyze_patterns(self, parsed_logs: List[Dict]) -> Dict:
        """
        Comprehensive pattern analysis of parsed logs
        """
        if not parsed_logs:
            return {'error': 'No logs to analyze'}
        
        logger.info(f"Analyzing patterns in {len(parsed_logs)} log entries")
        
        analysis = {
            'error_analysis': self._analyze_errors(parsed_logs),
            'performance_analysis': self._analyze_performance(parsed_logs),
            'transaction_analysis': self._analyze_transactions(parsed_logs),
            'system_health': self._analyze_system_health(parsed_logs),
            'temporal_patterns': self._analyze_temporal_patterns(parsed_logs),
            'component_analysis': self._analyze_components(parsed_logs)
        }
        
        # Calculate overall health score
        analysis['health_score'] = self._calculate_health_score(analysis)
        
        return analysis
    
    def _analyze_errors(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze error patterns and categorize them
        """
        error_logs = [log for log in parsed_logs if log.get('level') in ['ERROR', 'FATAL']]
        total_logs = len(parsed_logs)
        error_count = len(error_logs)
        
        # Categorize errors
        error_categories = defaultdict(list)
        for log in error_logs:
            message = log.get('message', '').lower()
            categorized = False
            
            for category, patterns in self.error_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, message, re.IGNORECASE):
                        error_categories[category].append(log)
                        categorized = True
                        break
                if categorized:
                    break
            
            if not categorized:
                error_categories['other'].append(log)
        
        # Calculate error rate
        error_rate = error_count / total_logs if total_logs > 0 else 0
        
        return {
            'total_errors': error_count,
            'error_rate': error_rate,
            'error_categories': {
                category: {
                    'count': len(errors),
                    'examples': [e.get('message', '')[:100] for e in errors[:3]]
                }
                for category, errors in error_categories.items()
            },
            'severity_assessment': self._assess_error_severity(error_rate, error_categories)
        }
    
    def _analyze_performance(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze performance patterns and response times
        """
        performance_data = {
            'response_times': [],
            'slow_transactions': [],
            'timeout_events': [],
            'performance_issues': []
        }
        
        # Track transaction timing
        transaction_starts = {}
        
        for log in parsed_logs:
            message = log.get('message', '')
            timestamp = log.get('timestamp')
            
            if not timestamp:
                continue
            
            # Detect transaction start
            if 'sendtransaction' in message.lower() or 'transaction started' in message.lower():
                # Extract transaction identifier (sequence number, etc.)
                seq_match = re.search(r'sequence.*?(\d+)', message, re.IGNORECASE)
                if seq_match:
                    seq_num = seq_match.group(1)
                    transaction_starts[seq_num] = timestamp
            
            # Detect transaction completion
            elif 'transaction complete' in message.lower() or 'response.*received' in message.lower():
                seq_match = re.search(r'sequence.*?(\d+)', message, re.IGNORECASE)
                if seq_match:
                    seq_num = seq_match.group(1)
                    if seq_num in transaction_starts:
                        start_time = transaction_starts[seq_num]
                        duration = (timestamp - start_time).total_seconds()
                        performance_data['response_times'].append(duration)
                        
                        if duration > self.performance_indicators['slow_response']:
                            performance_data['slow_transactions'].append({
                                'sequence': seq_num,
                                'duration': duration,
                                'start_time': start_time.isoformat(),
                                'end_time': timestamp.isoformat()
                            })
            
            # Detect timeout events
            if 'timeout' in message.lower():
                performance_data['timeout_events'].append({
                    'timestamp': timestamp.isoformat(),
                    'message': message[:100]
                })
        
        # Calculate performance metrics
        if performance_data['response_times']:
            avg_response = sum(performance_data['response_times']) / len(performance_data['response_times'])
            max_response = max(performance_data['response_times'])
            min_response = min(performance_data['response_times'])
        else:
            avg_response = max_response = min_response = 0
        
        return {
            'average_response_time': avg_response,
            'max_response_time': max_response,
            'min_response_time': min_response,
            'slow_transaction_count': len(performance_data['slow_transactions']),
            'timeout_count': len(performance_data['timeout_events']),
            'performance_issues': len(performance_data['slow_transactions']) > 0 or len(performance_data['timeout_events']) > 0,
            'slow_transactions': performance_data['slow_transactions'][:5],  # Top 5
            'timeout_events': performance_data['timeout_events'][:5]  # Top 5
        }
    
    def _analyze_transactions(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze transaction patterns and business metrics
        """
        transaction_data = {
            'total_transactions': 0,
            'transaction_types': Counter(),
            'payment_types': Counter(),
            'response_codes': Counter(),
            'approval_rate': 0,
            'decline_reasons': Counter()
        }
        
        for log in parsed_logs:
            message = log.get('message', '').lower()
            structured_data = log.get('structured_data', {})
            
            # Count transactions
            if any(indicator in message for indicator in ['sendtransaction', 'transaction started']):
                transaction_data['total_transactions'] += 1
                
                # Extract transaction type
                trans_type = structured_data.get('transaction_type')
                if trans_type:
                    transaction_data['transaction_types'][trans_type] += 1
            
            # Extract response codes
            response_code = structured_data.get('response_codes')
            if response_code:
                transaction_data['response_codes'][response_code] += 1
                
                # Track decline reasons
                if response_code != '00':
                    reason = self.business_patterns['response_codes'].get(response_code, 'Unknown')
                    transaction_data['decline_reasons'][reason] += 1
        
        # Calculate approval rate
        total_responses = sum(transaction_data['response_codes'].values())
        approvals = transaction_data['response_codes'].get('00', 0)
        transaction_data['approval_rate'] = approvals / total_responses if total_responses > 0 else 0
        
        return transaction_data
    
    def _analyze_system_health(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze overall system health indicators
        """
        health_indicators = {
            'host_connectivity': 'unknown',
            'terminal_status': 'unknown',
            'configuration_status': 'unknown',
            'memory_status': 'unknown',
            'service_status': 'unknown'
        }
        
        recent_logs = [log for log in parsed_logs if log.get('timestamp') and 
                      (datetime.now() - log['timestamp']).total_seconds() < 3600]  # Last hour
        
        for log in recent_logs:
            message = log.get('message', '').lower()
            level = log.get('level', '')
            
            # Host connectivity
            if 'host.*connected' in message or 'connection.*established' in message:
                health_indicators['host_connectivity'] = 'good'
            elif 'host.*down' in message or 'connection.*failed' in message:
                health_indicators['host_connectivity'] = 'poor'
            
            # Terminal status
            if 'terminal.*ready' in message or 'terminal.*ok' in message:
                health_indicators['terminal_status'] = 'good'
            elif 'terminal.*error' in message or 'terminal.*not responding' in message:
                health_indicators['terminal_status'] = 'poor'
            
            # Configuration status
            if 'configuration.*loaded' in message or 'setup.*ok' in message:
                health_indicators['configuration_status'] = 'good'
            elif 'configuration.*error' in message or 'setup.*failed' in message:
                health_indicators['configuration_status'] = 'poor'
            
            # Memory status
            if 'low memory' in message or 'memory.*error' in message:
                health_indicators['memory_status'] = 'poor'
            elif level not in ['ERROR', 'FATAL']:
                health_indicators['memory_status'] = 'good'
        
        return health_indicators
    
    def _analyze_temporal_patterns(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze temporal patterns in the logs
        """
        if not parsed_logs:
            return {}
        
        # Group logs by hour
        hourly_activity = defaultdict(int)
        hourly_errors = defaultdict(int)
        
        for log in parsed_logs:
            timestamp = log.get('timestamp')
            if timestamp:
                hour = timestamp.hour
                hourly_activity[hour] += 1
                
                if log.get('level') in ['ERROR', 'FATAL']:
                    hourly_errors[hour] += 1
        
        # Find peak activity hours
        peak_hour = max(hourly_activity.items(), key=lambda x: x[1]) if hourly_activity else (0, 0)
        
        return {
            'hourly_activity': dict(hourly_activity),
            'hourly_errors': dict(hourly_errors),
            'peak_activity_hour': peak_hour[0],
            'peak_activity_count': peak_hour[1],
            'total_time_span_hours': self._calculate_time_span(parsed_logs)
        }
    
    def _analyze_components(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze activity by system component
        """
        component_activity = Counter()
        component_errors = Counter()
        
        for log in parsed_logs:
            source = log.get('source', 'UNKNOWN')
            component_activity[source] += 1
            
            if log.get('level') in ['ERROR', 'FATAL']:
                component_errors[source] += 1
        
        return {
            'component_activity': dict(component_activity),
            'component_errors': dict(component_errors),
            'most_active_component': component_activity.most_common(1)[0] if component_activity else ('None', 0),
            'most_error_prone_component': component_errors.most_common(1)[0] if component_errors else ('None', 0)
        }
    
    def _assess_error_severity(self, error_rate: float, error_categories: Dict) -> str:
        """
        Assess overall error severity
        """
        if error_rate > 0.2:  # More than 20% errors
            return 'critical'
        elif error_rate > 0.1:  # More than 10% errors
            return 'high'
        elif error_rate > 0.05:  # More than 5% errors
            return 'medium'
        elif error_rate > 0:
            return 'low'
        else:
            return 'none'
    
    def _calculate_health_score(self, analysis: Dict) -> float:
        """
        Calculate overall system health score (0-100)
        """
        score = 100.0
        
        # Deduct for errors
        error_rate = analysis.get('error_analysis', {}).get('error_rate', 0)
        score -= error_rate * 50  # Up to 50 points for errors
        
        # Deduct for performance issues
        if analysis.get('performance_analysis', {}).get('performance_issues'):
            score -= 20
        
        # Deduct for low approval rate
        approval_rate = analysis.get('transaction_analysis', {}).get('approval_rate', 1.0)
        if approval_rate < 0.9:  # Less than 90% approval
            score -= (0.9 - approval_rate) * 100
        
        return max(0, min(100, score))
    
    def _calculate_time_span(self, parsed_logs: List[Dict]) -> float:
        """
        Calculate time span of logs in hours
        """
        timestamps = [log.get('timestamp') for log in parsed_logs if log.get('timestamp')]
        if len(timestamps) < 2:
            return 0
        
        start_time = min(timestamps)
        end_time = max(timestamps)
        return (end_time - start_time).total_seconds() / 3600
