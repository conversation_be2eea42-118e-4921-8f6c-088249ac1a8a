"""
OpenEPS Pattern Analyzer
Analyzes log patterns to identify system behaviors, issues, and trends
"""

import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from collections import defaultdict, Counter
import logging

logger = logging.getLogger(__name__)

class PatternAnalyzer:
    """
    Analyzes OpenEPS logs for patterns, anomalies, and system behaviors
    """
    
    def __init__(self):
        # Define pattern recognition rules
        self.error_patterns = {
            'servereps_ssl_failure': [
                'identity mismatch', 'cvInvalid', 'SSL.*error', 'certificate.*error',
                'OpenEpsLogin returned an empty XML', 'Error connecting with SSL'
            ],
            'scat_terminal_failure': [
                'Set SCAT dead', 'Required Response DID NOT return a response',
                'Cannot communicate with terminal', 'SCAT.*dead', 'S95.*failed',
                'IsTermP2PCapable=N', 'P2P.*mismatch'
            ],
            'terminal_communication_error': [
                'terminal.*not responding', 'terminal.*error', 'bad card read',
                'terminal communication failed', 'XVER Parsing Error'
            ],
            'host_timeout': [
                'timeout', 'connection timeout', 'host.*timeout', 'no response'
            ],
            'configuration_error': [
                'setup.txt.*error', 'configuration.*error', 'file.*corrupted',
                'missing.*configuration'
            ],
            'offline_file_error': [
                'Division by zero', '\.eft.*error', 'offline.*processing.*error'
            ],
            'application_loop_error': [
                'Force throttling', 'TenderTypeStatus.*Error', 'stuck.*loop'
            ]
        }
        
        self.performance_indicators = {
            'slow_response': 5.0,      # seconds
            'very_slow_response': 10.0, # seconds
            'timeout_threshold': 30.0   # seconds
        }
        
        self.business_patterns = {
            'transaction_types': [
                'purchase', 'return', 'void', 'balance inquiry', 'pre-auth'
            ],
            'payment_types': [
                'credit', 'debit', 'ebt', 'gift card', 'fleet'
            ],
            'response_codes': {
                '00': 'Approved',
                '01': 'Refer to Card Issuer',
                '05': 'Do Not Honor',
                '12': 'Invalid Transaction',
                '14': 'Invalid Card Number',
                '51': 'Insufficient Funds',
                '54': 'Expired Card',
                '91': 'Issuer Inoperative'
            }
        }
    
    def analyze_patterns(self, parsed_logs: List[Dict]) -> Dict:
        """
        Comprehensive pattern analysis of parsed logs with full transaction flow understanding
        """
        if not parsed_logs:
            return {'error': 'No logs to analyze'}

        logger.info(f"Analyzing patterns in {len(parsed_logs)} log entries")

        analysis = {
            'application_lifecycle': self._analyze_application_lifecycle(parsed_logs),
            'terminal_interaction_flow': self._analyze_terminal_interactions(parsed_logs),
            'emv_transaction_flow': self._analyze_emv_transactions(parsed_logs),
            'host_communication_flow': self._analyze_host_communications(parsed_logs),
            'complete_transaction_flows': self._analyze_complete_transaction_flows(parsed_logs),
            'technical_failure_analysis': self._analyze_technical_failures(parsed_logs),
            'cascading_failure_analysis': self._analyze_cascading_failures(parsed_logs),
            'system_state_analysis': self._analyze_system_states(parsed_logs)
        }

        # Calculate overall health score
        analysis['health_score'] = self._calculate_health_score(analysis)

        return analysis

    def _analyze_application_lifecycle(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze the complete OpenEPS application startup and initialization flow
        """
        lifecycle_phases = {
            'startup': {'status': 'unknown', 'events': [], 'duration': None},
            'terminal_initialization': {'status': 'unknown', 'events': [], 'duration': None},
            'servereps_connection': {'status': 'unknown', 'events': [], 'duration': None},
            'signon_process': {'status': 'unknown', 'events': [], 'duration': None},
            'ready_for_transactions': {'status': 'unknown', 'events': [], 'duration': None}
        }

        startup_time = None
        terminal_init_time = None
        servereps_connect_time = None
        signon_time = None
        ready_time = None

        for log in parsed_logs:
            message = log.get('message', '').lower()
            timestamp = log.get('timestamp')

            # Application Startup Phase
            if any(pattern in message for pattern in ['application starting', 'openeps starting', 'mtx_eps.dll loaded']):
                lifecycle_phases['startup']['status'] = 'started'
                lifecycle_phases['startup']['events'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'event': 'Application startup initiated',
                    'details': log.get('message', '')
                })
                if not startup_time:
                    startup_time = timestamp

            # Terminal Initialization Phase
            if any(pattern in message for pattern in ['initializing.*terminal', 'scat.*init', 'terminal.*ready']):
                if lifecycle_phases['terminal_initialization']['status'] == 'unknown':
                    lifecycle_phases['terminal_initialization']['status'] = 'started'
                    terminal_init_time = timestamp

                lifecycle_phases['terminal_initialization']['events'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'event': 'Terminal initialization step',
                    'details': log.get('message', '')
                })

                if 'terminal.*ready' in message or 'scat.*ready' in message:
                    lifecycle_phases['terminal_initialization']['status'] = 'completed'

            # ServerEPS Connection Phase
            if any(pattern in message for pattern in ['openepslogin', 'servereps.*connect', 'ssl.*connect']):
                if lifecycle_phases['servereps_connection']['status'] == 'unknown':
                    lifecycle_phases['servereps_connection']['status'] = 'started'
                    servereps_connect_time = timestamp

                lifecycle_phases['servereps_connection']['events'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'event': 'ServerEPS connection attempt',
                    'details': log.get('message', '')
                })

                if 'login.*successful' in message or 'connected.*successfully' in message:
                    lifecycle_phases['servereps_connection']['status'] = 'completed'
                elif 'login.*failed' in message or 'ssl.*error' in message:
                    lifecycle_phases['servereps_connection']['status'] = 'failed'

            # Signon Process Phase
            if any(pattern in message for pattern in ['cashier.*signon', 'signon.*started', 'operator.*login']):
                if lifecycle_phases['signon_process']['status'] == 'unknown':
                    lifecycle_phases['signon_process']['status'] = 'started'
                    signon_time = timestamp

                lifecycle_phases['signon_process']['events'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'event': 'Signon process step',
                    'details': log.get('message', '')
                })

                if 'signon.*successful' in message or 'operator.*authenticated' in message:
                    lifecycle_phases['signon_process']['status'] = 'completed'

            # Ready for Transactions
            if any(pattern in message for pattern in ['ready.*for.*transactions', 'system.*ready', 'waiting.*for.*transaction']):
                lifecycle_phases['ready_for_transactions']['status'] = 'completed'
                lifecycle_phases['ready_for_transactions']['events'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'event': 'System ready for transactions',
                    'details': log.get('message', '')
                })
                if not ready_time:
                    ready_time = timestamp

        # Calculate durations
        if startup_time and terminal_init_time:
            lifecycle_phases['startup']['duration'] = (terminal_init_time - startup_time).total_seconds()
        if terminal_init_time and servereps_connect_time:
            lifecycle_phases['terminal_initialization']['duration'] = (servereps_connect_time - terminal_init_time).total_seconds()
        if servereps_connect_time and signon_time:
            lifecycle_phases['servereps_connection']['duration'] = (signon_time - servereps_connect_time).total_seconds()
        if signon_time and ready_time:
            lifecycle_phases['signon_process']['duration'] = (ready_time - signon_time).total_seconds()

        return lifecycle_phases

    def _analyze_terminal_interactions(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze terminal communication patterns and EMV card interactions
        """
        terminal_interactions = {
            'initialization_sequence': [],
            'card_detection_events': [],
            'emv_application_selection': [],
            'pin_entry_process': [],
            'terminal_commands': [],
            'communication_errors': []
        }

        for log in parsed_logs:
            message = log.get('message', '').lower()
            timestamp = log.get('timestamp')

            # Terminal Initialization Commands
            if any(pattern in message for pattern in ['s95', 's00', 'i05', 'terminal.*init']):
                terminal_interactions['initialization_sequence'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'command': self._extract_terminal_command(log.get('message', '')),
                    'response': self._extract_terminal_response(log.get('message', '')),
                    'details': log.get('message', '')
                })

            # Card Detection and Reading
            if any(pattern in message for pattern in ['card.*detected', 'card.*inserted', 'card.*swiped', 'track.*data']):
                terminal_interactions['card_detection_events'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'event_type': self._classify_card_event(log.get('message', '')),
                    'card_data': self._extract_card_data(log.get('message', '')),
                    'details': log.get('message', '')
                })

            # EMV Application Selection
            if any(pattern in message for pattern in ['aid.*select', 'application.*select', 'emv.*app']):
                terminal_interactions['emv_application_selection'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'aid': self._extract_aid(log.get('message', '')),
                    'application_name': self._extract_app_name(log.get('message', '')),
                    'details': log.get('message', '')
                })

            # PIN Entry Process
            if any(pattern in message for pattern in ['pin.*entry', 'pin.*verify', 'pin.*encrypt']):
                terminal_interactions['pin_entry_process'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'pin_event': self._classify_pin_event(log.get('message', '')),
                    'encryption_method': self._extract_encryption_method(log.get('message', '')),
                    'details': log.get('message', '')
                })

            # Terminal Communication Errors
            if any(pattern in message for pattern in ['terminal.*error', 'cannot.*communicate', 'timeout']):
                terminal_interactions['communication_errors'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'error_type': self._classify_terminal_error(log.get('message', '')),
                    'details': log.get('message', '')
                })

        return terminal_interactions

    def _analyze_emv_transactions(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze EMV transaction processing flow with detailed EMV data parsing
        """
        emv_transactions = {
            'transaction_sessions': [],
            'emv_data_elements': {},
            'cryptogram_processing': [],
            'authorization_flow': [],
            'emv_decision_points': []
        }

        current_transaction = None

        for log in parsed_logs:
            message = log.get('message', '').lower()
            timestamp = log.get('timestamp')
            original_message = log.get('message', '')

            # Transaction Start Detection
            if any(pattern in message for pattern in ['transaction.*start', 'mtx_pos_sendtransaction', 'purchase.*amount']):
                current_transaction = {
                    'transaction_id': self._extract_transaction_id(original_message),
                    'start_time': timestamp.isoformat() if timestamp else None,
                    'amount': self._extract_amount(original_message),
                    'tender_type': self._extract_tender_type(original_message),
                    'emv_flow_steps': [],
                    'emv_data': {},
                    'authorization_data': {},
                    'completion_status': 'in_progress'
                }
                emv_transactions['transaction_sessions'].append(current_transaction)

            if current_transaction:
                # EMV Application Selection
                if 'aid' in message or 'application.*select' in message:
                    aid_data = self._parse_emv_aid_data(original_message)
                    current_transaction['emv_flow_steps'].append({
                        'step': 'Application Selection',
                        'timestamp': timestamp.isoformat() if timestamp else None,
                        'data': aid_data,
                        'details': original_message
                    })
                    current_transaction['emv_data'].update(aid_data)

                # EMV Data Elements (Tags)
                emv_tags = self._extract_emv_tags(original_message)
                if emv_tags:
                    current_transaction['emv_flow_steps'].append({
                        'step': 'EMV Data Processing',
                        'timestamp': timestamp.isoformat() if timestamp else None,
                        'emv_tags': emv_tags,
                        'details': original_message
                    })
                    current_transaction['emv_data'].update(emv_tags)

                    # Store in global EMV data elements
                    for tag, value in emv_tags.items():
                        if tag not in emv_transactions['emv_data_elements']:
                            emv_transactions['emv_data_elements'][tag] = []
                        emv_transactions['emv_data_elements'][tag].append({
                            'value': value,
                            'transaction_id': current_transaction.get('transaction_id'),
                            'timestamp': timestamp.isoformat() if timestamp else None
                        })

                # Cryptogram Processing
                if any(pattern in message for pattern in ['arqc', 'tc', 'aac', 'cryptogram']):
                    crypto_data = self._parse_cryptogram_data(original_message)
                    current_transaction['emv_flow_steps'].append({
                        'step': 'Cryptogram Processing',
                        'timestamp': timestamp.isoformat() if timestamp else None,
                        'cryptogram_type': crypto_data.get('type'),
                        'cryptogram_value': crypto_data.get('value'),
                        'details': original_message
                    })

                    emv_transactions['cryptogram_processing'].append({
                        'transaction_id': current_transaction.get('transaction_id'),
                        'timestamp': timestamp.isoformat() if timestamp else None,
                        'cryptogram_data': crypto_data,
                        'details': original_message
                    })

                # Authorization Request/Response
                if any(pattern in message for pattern in ['auth.*request', 'auth.*response', 'host.*response']):
                    auth_data = self._parse_authorization_data(original_message)
                    current_transaction['emv_flow_steps'].append({
                        'step': 'Authorization Processing',
                        'timestamp': timestamp.isoformat() if timestamp else None,
                        'auth_data': auth_data,
                        'details': original_message
                    })
                    current_transaction['authorization_data'].update(auth_data)

                    emv_transactions['authorization_flow'].append({
                        'transaction_id': current_transaction.get('transaction_id'),
                        'timestamp': timestamp.isoformat() if timestamp else None,
                        'authorization_data': auth_data,
                        'details': original_message
                    })

                # Transaction Completion
                if any(pattern in message for pattern in ['transaction.*complete', 'approved', 'declined', 'cancelled']):
                    current_transaction['completion_status'] = self._extract_completion_status(original_message)
                    current_transaction['end_time'] = timestamp.isoformat() if timestamp else None
                    current_transaction = None  # End current transaction tracking

        return emv_transactions

    def _analyze_host_communications(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze host communication patterns and data exchange
        """
        host_communications = {
            'connection_events': [],
            'authorization_requests': [],
            'authorization_responses': [],
            'settlement_data': [],
            'communication_errors': []
        }

        for log in parsed_logs:
            message = log.get('message', '').lower()
            timestamp = log.get('timestamp')
            original_message = log.get('message', '')

            # Host Connection Events
            if any(pattern in message for pattern in ['host.*connect', 'host.*disconnect', 'tcp.*connect']):
                host_communications['connection_events'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'event_type': self._classify_host_event(original_message),
                    'host_details': self._extract_host_details(original_message),
                    'details': original_message
                })

            # Authorization Requests to Host
            if any(pattern in message for pattern in ['sending.*auth', 'auth.*request.*sent', 'iso.*8583']):
                auth_request_data = self._parse_iso8583_data(original_message)
                host_communications['authorization_requests'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'message_type': auth_request_data.get('message_type'),
                    'processing_code': auth_request_data.get('processing_code'),
                    'amount': auth_request_data.get('amount'),
                    'card_data': auth_request_data.get('card_data'),
                    'emv_data': auth_request_data.get('emv_data'),
                    'details': original_message
                })

            # Authorization Responses from Host
            if any(pattern in message for pattern in ['auth.*response', 'response.*received', 'approved', 'declined']):
                auth_response_data = self._parse_authorization_response(original_message)
                host_communications['authorization_responses'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'response_code': auth_response_data.get('response_code'),
                    'auth_code': auth_response_data.get('auth_code'),
                    'emv_response_data': auth_response_data.get('emv_response_data'),
                    'details': original_message
                })

            # Settlement Data
            if any(pattern in message for pattern in ['settlement', 'batch.*close', 'totals']):
                settlement_data = self._parse_settlement_data(original_message)
                host_communications['settlement_data'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'settlement_data': settlement_data,
                    'details': original_message
                })

            # Communication Errors
            if any(pattern in message for pattern in ['host.*error', 'timeout', 'connection.*failed']):
                host_communications['communication_errors'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'error_type': self._classify_host_error(original_message),
                    'details': original_message
                })

        return host_communications

    def _analyze_complete_transaction_flows(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze complete end-to-end transaction flows combining all components
        """
        complete_flows = {
            'successful_transactions': [],
            'failed_transactions': [],
            'incomplete_transactions': [],
            'flow_analysis': {}
        }

        # Group logs by transaction ID or time windows
        transaction_groups = self._group_logs_by_transaction(parsed_logs)

        for transaction_id, transaction_logs in transaction_groups.items():
            flow_analysis = self._analyze_single_transaction_flow(transaction_logs)

            if flow_analysis['completion_status'] == 'successful':
                complete_flows['successful_transactions'].append(flow_analysis)
            elif flow_analysis['completion_status'] == 'failed':
                complete_flows['failed_transactions'].append(flow_analysis)
            else:
                complete_flows['incomplete_transactions'].append(flow_analysis)

        # Overall flow analysis
        complete_flows['flow_analysis'] = {
            'total_transactions': len(transaction_groups),
            'success_rate': len(complete_flows['successful_transactions']) / len(transaction_groups) * 100 if transaction_groups else 0,
            'common_failure_points': self._identify_common_failure_points(complete_flows['failed_transactions']),
            'average_transaction_time': self._calculate_average_transaction_time(complete_flows['successful_transactions'])
        }

        return complete_flows

    def _analyze_technical_failures(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze specific technical failure patterns like ServerEPS SSL, SCAT terminal issues
        """
        technical_failures = {
            'servereps_ssl_issues': [],
            'scat_terminal_issues': [],
            'p2p_encryption_issues': [],
            'certificate_issues': [],
            'terminal_communication_breakdown': []
        }

        for log in parsed_logs:
            message = log.get('message', '').lower()
            timestamp = log.get('timestamp')

            # ServerEPS SSL Certificate Issues
            if any(pattern in message for pattern in ['identity mismatch', 'cvinvalid', 'ssl.*error']):
                technical_failures['servereps_ssl_issues'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'message': log.get('message', ''),
                    'type': 'SSL Certificate Validation Failure'
                })

            # SCAT Terminal Issues
            if any(pattern in message for pattern in ['set scat dead', 's95.*failed', 'required response did not return']):
                technical_failures['scat_terminal_issues'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'message': log.get('message', ''),
                    'type': 'SCAT Terminal Communication Failure'
                })

            # P2P Encryption Issues
            if any(pattern in message for pattern in ['istermp2pcapable=n', 'p2p.*mismatch', 'p2p.*required']):
                technical_failures['p2p_encryption_issues'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'message': log.get('message', ''),
                    'type': 'P2P Encryption Capability Mismatch'
                })

        return technical_failures

    def _analyze_cascading_failures(self, parsed_logs: List[Dict]) -> Dict:
        """
        Detect cascading failure patterns where one failure leads to others
        """
        cascading_patterns = {
            'ssl_to_login_failure': False,
            'terminal_dead_to_communication_errors': False,
            'login_failure_to_config_errors': False,
            'failure_sequence': []
        }

        # Look for SSL failure followed by login failure
        ssl_failure_time = None
        login_failure_time = None

        for log in parsed_logs:
            message = log.get('message', '').lower()
            timestamp = log.get('timestamp')

            if 'identity mismatch' in message or 'cvinvalid' in message:
                ssl_failure_time = timestamp
                cascading_patterns['failure_sequence'].append({
                    'step': 1,
                    'type': 'SSL Certificate Failure',
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'message': log.get('message', '')
                })

            if 'openepslogin returned an empty xml' in message and ssl_failure_time:
                login_failure_time = timestamp
                cascading_patterns['ssl_to_login_failure'] = True
                cascading_patterns['failure_sequence'].append({
                    'step': 2,
                    'type': 'Login Failure (caused by SSL)',
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'message': log.get('message', '')
                })

            if 'set scat dead' in message:
                cascading_patterns['failure_sequence'].append({
                    'step': 3,
                    'type': 'Terminal Declared Dead',
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'message': log.get('message', '')
                })

            if 'cannot communicate with terminal' in message:
                cascading_patterns['terminal_dead_to_communication_errors'] = True
                cascading_patterns['failure_sequence'].append({
                    'step': 4,
                    'type': 'Communication Errors (terminal dead)',
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'message': log.get('message', '')
                })

        return cascading_patterns

    def _analyze_system_states(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze system state transitions and stuck states
        """
        system_states = {
            'current_state': 'unknown',
            'stuck_in_error_loop': False,
            'initialization_failures': [],
            'state_transitions': [],
            'error_loop_indicators': []
        }

        force_throttling_count = 0
        tender_error_count = 0

        for log in parsed_logs:
            message = log.get('message', '').lower()
            timestamp = log.get('timestamp')

            # Check for stuck in error loop indicators
            if 'force throttling' in message:
                force_throttling_count += 1
                if force_throttling_count > 5:  # Arbitrary threshold
                    system_states['stuck_in_error_loop'] = True
                    system_states['error_loop_indicators'].append('Excessive force throttling messages')

            if 'tendertypestatus.*error' in message:
                tender_error_count += 1
                if tender_error_count > 3:
                    system_states['stuck_in_error_loop'] = True
                    system_states['error_loop_indicators'].append('Repeated tender type errors')

            # Track initialization failures
            if any(pattern in message for pattern in ['failed to get device information', 'openepslogin.*failed']):
                system_states['initialization_failures'].append({
                    'timestamp': timestamp.isoformat() if timestamp else None,
                    'component': 'ServerEPS' if 'openepslogin' in message else 'Terminal',
                    'message': log.get('message', '')
                })

        return system_states

    # Helper methods for EMV and transaction parsing
    def _extract_terminal_command(self, message: str) -> str:
        """Extract terminal command from log message"""
        import re
        command_match = re.search(r'(S\d+|I\d+|[A-Z]\d+)', message.upper())
        return command_match.group(1) if command_match else 'Unknown'

    def _extract_terminal_response(self, message: str) -> str:
        """Extract terminal response from log message"""
        if 'response' in message.lower():
            return message.split('response')[-1].strip()
        return ''

    def _classify_card_event(self, message: str) -> str:
        """Classify card detection event type"""
        message_lower = message.lower()
        if 'insert' in message_lower:
            return 'Card Inserted'
        elif 'swipe' in message_lower:
            return 'Card Swiped'
        elif 'tap' in message_lower or 'contactless' in message_lower:
            return 'Contactless'
        elif 'remove' in message_lower:
            return 'Card Removed'
        return 'Card Event'

    def _extract_card_data(self, message: str) -> Dict:
        """Extract card data from log message"""
        import re
        card_data = {}

        # Extract PAN (masked)
        pan_match = re.search(r'(\d{4})\*+(\d{4})', message)
        if pan_match:
            card_data['masked_pan'] = f"{pan_match.group(1)}****{pan_match.group(2)}"

        # Extract track data indicators
        if 'track' in message.lower():
            card_data['track_data_present'] = True

        return card_data

    def _extract_aid(self, message: str) -> str:
        """Extract AID (Application Identifier) from EMV message"""
        import re
        aid_match = re.search(r'AID[:\s]*([A-F0-9]{10,32})', message.upper())
        return aid_match.group(1) if aid_match else ''

    def _extract_app_name(self, message: str) -> str:
        """Extract application name from EMV message"""
        if 'visa' in message.lower():
            return 'Visa'
        elif 'mastercard' in message.lower() or 'master' in message.lower():
            return 'Mastercard'
        elif 'amex' in message.lower() or 'american express' in message.lower():
            return 'American Express'
        return 'Unknown'

    def _classify_pin_event(self, message: str) -> str:
        """Classify PIN entry event"""
        message_lower = message.lower()
        if 'entry' in message_lower:
            return 'PIN Entry'
        elif 'verify' in message_lower:
            return 'PIN Verification'
        elif 'encrypt' in message_lower:
            return 'PIN Encryption'
        return 'PIN Event'

    def _extract_encryption_method(self, message: str) -> str:
        """Extract encryption method from PIN message"""
        message_lower = message.lower()
        if 'dukpt' in message_lower:
            return 'DUKPT'
        elif 'master' in message_lower and 'session' in message_lower:
            return 'Master/Session'
        return 'Unknown'

    def _classify_terminal_error(self, message: str) -> str:
        """Classify terminal error type"""
        message_lower = message.lower()
        if 'timeout' in message_lower:
            return 'Communication Timeout'
        elif 'cannot communicate' in message_lower:
            return 'Communication Failure'
        elif 'dead' in message_lower:
            return 'Terminal Dead'
        return 'Terminal Error'

    def _extract_transaction_id(self, message: str) -> str:
        """Extract transaction ID from message"""
        import re
        # Look for various transaction ID patterns
        patterns = [
            r'TXN[:\s]*(\d+)',
            r'TRANS[:\s]*(\d+)',
            r'ID[:\s]*(\d+)',
            r'SEQ[:\s]*(\d+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, message.upper())
            if match:
                return match.group(1)

        return f"TXN_{hash(message) % 10000}"  # Generate ID if not found

    def _extract_amount(self, message: str) -> str:
        """Extract transaction amount from message"""
        import re
        amount_match = re.search(r'(\$?\d+\.?\d*)', message)
        return amount_match.group(1) if amount_match else '0.00'

    def _extract_tender_type(self, message: str) -> str:
        """Extract tender type from message"""
        message_lower = message.lower()
        if 'credit' in message_lower:
            return 'Credit'
        elif 'debit' in message_lower:
            return 'Debit'
        elif 'cash' in message_lower:
            return 'Cash'
        return 'Unknown'

    def _parse_emv_aid_data(self, message: str) -> Dict:
        """Parse EMV AID data from message"""
        aid_data = {}
        aid = self._extract_aid(message)
        if aid:
            aid_data['AID'] = aid
            aid_data['Application_Name'] = self._extract_app_name(message)
        return aid_data

    def _extract_emv_tags(self, message: str) -> Dict:
        """Extract EMV tags and values from message"""
        import re
        emv_tags = {}

        # Common EMV tag patterns
        tag_patterns = {
            '9F02': 'Amount_Authorized',
            '9F03': 'Amount_Other',
            '9F1A': 'Terminal_Country_Code',
            '9F33': 'Terminal_Capabilities',
            '9F34': 'CVM_Results',
            '9F35': 'Terminal_Type',
            '9F36': 'Application_Transaction_Counter',
            '9F37': 'Unpredictable_Number',
            '9F26': 'Application_Cryptogram',
            '9F27': 'Cryptogram_Information_Data',
            '82': 'Application_Interchange_Profile',
            '84': 'Dedicated_File_Name',
            '95': 'Terminal_Verification_Results'
        }

        for tag_hex, tag_name in tag_patterns.items():
            pattern = f'{tag_hex}[:\s]*([A-F0-9]+)'
            match = re.search(pattern, message.upper())
            if match:
                emv_tags[tag_name] = match.group(1)

        return emv_tags

    def _parse_cryptogram_data(self, message: str) -> Dict:
        """Parse cryptogram data from message"""
        import re
        crypto_data = {}

        message_upper = message.upper()

        # Cryptogram type
        if 'ARQC' in message_upper:
            crypto_data['type'] = 'ARQC'
        elif 'TC' in message_upper:
            crypto_data['type'] = 'TC'
        elif 'AAC' in message_upper:
            crypto_data['type'] = 'AAC'

        # Cryptogram value
        crypto_match = re.search(r'([A-F0-9]{16})', message_upper)
        if crypto_match:
            crypto_data['value'] = crypto_match.group(1)

        return crypto_data

    def _parse_authorization_data(self, message: str) -> Dict:
        """Parse authorization request/response data"""
        import re
        auth_data = {}

        # Response code
        response_match = re.search(r'RESPONSE[:\s]*(\d{2})', message.upper())
        if response_match:
            auth_data['response_code'] = response_match.group(1)

        # Authorization code
        auth_match = re.search(r'AUTH[:\s]*([A-Z0-9]{6})', message.upper())
        if auth_match:
            auth_data['auth_code'] = auth_match.group(1)

        return auth_data

    def _classify_host_event(self, message: str) -> str:
        """Classify host communication event"""
        message_lower = message.lower()
        if 'connect' in message_lower:
            return 'Connection Established'
        elif 'disconnect' in message_lower:
            return 'Connection Closed'
        return 'Host Event'

    def _extract_host_details(self, message: str) -> Dict:
        """Extract host connection details"""
        import re
        details = {}

        # Extract host URL
        url_match = re.search(r'https?://([^\s]+)', message)
        if url_match:
            details['host_url'] = url_match.group(1)

        return details

    def _parse_iso8583_data(self, message: str) -> Dict:
        """Parse ISO 8583 message data"""
        import re
        iso_data = {}

        # Message type
        msg_type_match = re.search(r'(\d{4})', message)
        if msg_type_match:
            iso_data['message_type'] = msg_type_match.group(1)

        # Processing code
        proc_code_match = re.search(r'Processing Code[:\s]*(\d{6})', message)
        if proc_code_match:
            iso_data['processing_code'] = proc_code_match.group(1)

        # Amount
        amount_match = re.search(r'Amount[:\s]*(\d+)', message)
        if amount_match:
            iso_data['amount'] = amount_match.group(1)

        return iso_data

    def _parse_authorization_response(self, message: str) -> Dict:
        """Parse authorization response data"""
        import re
        response_data = {}

        # Response code
        response_match = re.search(r'Response Code[:\s]*(\d{2})', message)
        if response_match:
            response_data['response_code'] = response_match.group(1)

        # Auth code
        auth_match = re.search(r'Auth Code[:\s]*([A-Z0-9]{6})', message)
        if auth_match:
            response_data['auth_code'] = auth_match.group(1)

        return response_data

    def _parse_settlement_data(self, message: str) -> Dict:
        """Parse settlement data"""
        return {'settlement_info': 'Settlement data detected'}

    def _classify_host_error(self, message: str) -> str:
        """Classify host communication error"""
        message_lower = message.lower()
        if 'timeout' in message_lower:
            return 'Host Timeout'
        elif 'connection failed' in message_lower:
            return 'Connection Failed'
        return 'Host Error'

    def _group_logs_by_transaction(self, parsed_logs: List[Dict]) -> Dict:
        """Group logs by transaction ID or time windows"""
        transactions = {}
        current_txn_id = None

        for log in parsed_logs:
            message = log.get('message', '').lower()

            # Detect transaction start
            if 'transaction initiated' in message or 'mtx_pos_sendtransaction' in message:
                current_txn_id = self._extract_transaction_id(log.get('message', ''))
                transactions[current_txn_id] = []

            # Add log to current transaction
            if current_txn_id:
                transactions[current_txn_id].append(log)

            # Detect transaction end
            if 'transaction completed' in message or 'transaction.*approved' in message:
                current_txn_id = None

        return transactions

    def _analyze_single_transaction_flow(self, transaction_logs: List[Dict]) -> Dict:
        """Analyze a single transaction flow"""
        flow = {
            'transaction_id': 'Unknown',
            'start_time': None,
            'end_time': None,
            'amount': 'Unknown',
            'completion_status': 'incomplete',
            'phases': {
                'initiation': False,
                'card_reading': False,
                'emv_processing': False,
                'authorization': False,
                'completion': False
            },
            'emv_data': {},
            'errors': []
        }

        for log in transaction_logs:
            message = log.get('message', '').lower()
            timestamp = log.get('timestamp')

            if 'transaction initiated' in message:
                flow['initiation'] = True
                flow['start_time'] = timestamp
                flow['amount'] = self._extract_amount(log.get('message', ''))
                flow['transaction_id'] = self._extract_transaction_id(log.get('message', ''))
                flow['phases']['initiation'] = True

            if 'card detected' in message or 'card inserted' in message:
                flow['phases']['card_reading'] = True

            if 'emv' in message or 'aid' in message:
                flow['phases']['emv_processing'] = True
                emv_tags = self._extract_emv_tags(log.get('message', ''))
                flow['emv_data'].update(emv_tags)

            if 'authorization' in message or 'host' in message:
                flow['phases']['authorization'] = True

            if 'transaction completed' in message or 'approved' in message:
                flow['phases']['completion'] = True
                flow['end_time'] = timestamp
                flow['completion_status'] = 'successful'

            if 'error' in message or 'failed' in message:
                flow['errors'].append(log.get('message', ''))
                flow['completion_status'] = 'failed'

        return flow

    def _identify_common_failure_points(self, failed_transactions: List[Dict]) -> List[Dict]:
        """Identify common failure points across failed transactions"""
        failure_points = {}

        for txn in failed_transactions:
            for error in txn.get('errors', []):
                if error not in failure_points:
                    failure_points[error] = 0
                failure_points[error] += 1

        return [{'point': point, 'count': count} for point, count in failure_points.items()]

    def _calculate_average_transaction_time(self, successful_transactions: List[Dict]) -> float:
        """Calculate average transaction time"""
        total_time = 0
        count = 0

        for txn in successful_transactions:
            start_time = txn.get('start_time')
            end_time = txn.get('end_time')
            if start_time and end_time:
                duration = (end_time - start_time).total_seconds()
                total_time += duration
                count += 1

        return total_time / count if count > 0 else 0

    def _analyze_errors(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze error patterns and categorize them
        """
        error_logs = [log for log in parsed_logs if log.get('level') in ['ERROR', 'FATAL']]
        total_logs = len(parsed_logs)
        error_count = len(error_logs)
        
        # Categorize errors
        error_categories = defaultdict(list)
        for log in error_logs:
            message = log.get('message', '').lower()
            categorized = False
            
            for category, patterns in self.error_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, message, re.IGNORECASE):
                        error_categories[category].append(log)
                        categorized = True
                        break
                if categorized:
                    break
            
            if not categorized:
                error_categories['other'].append(log)
        
        # Calculate error rate
        error_rate = error_count / total_logs if total_logs > 0 else 0
        
        return {
            'total_errors': error_count,
            'error_rate': error_rate,
            'error_categories': {
                category: {
                    'count': len(errors),
                    'examples': [e.get('message', '')[:100] for e in errors[:3]]
                }
                for category, errors in error_categories.items()
            },
            'severity_assessment': self._assess_error_severity(error_rate, error_categories)
        }
    
    def _analyze_performance(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze performance patterns and response times
        """
        performance_data = {
            'response_times': [],
            'slow_transactions': [],
            'timeout_events': [],
            'performance_issues': []
        }
        
        # Track transaction timing
        transaction_starts = {}
        
        for log in parsed_logs:
            message = log.get('message', '')
            timestamp = log.get('timestamp')
            
            if not timestamp:
                continue
            
            # Detect transaction start
            if 'sendtransaction' in message.lower() or 'transaction started' in message.lower():
                # Extract transaction identifier (sequence number, etc.)
                seq_match = re.search(r'sequence.*?(\d+)', message, re.IGNORECASE)
                if seq_match:
                    seq_num = seq_match.group(1)
                    transaction_starts[seq_num] = timestamp
            
            # Detect transaction completion
            elif 'transaction complete' in message.lower() or 'response.*received' in message.lower():
                seq_match = re.search(r'sequence.*?(\d+)', message, re.IGNORECASE)
                if seq_match:
                    seq_num = seq_match.group(1)
                    if seq_num in transaction_starts:
                        start_time = transaction_starts[seq_num]
                        duration = (timestamp - start_time).total_seconds()
                        performance_data['response_times'].append(duration)
                        
                        if duration > self.performance_indicators['slow_response']:
                            performance_data['slow_transactions'].append({
                                'sequence': seq_num,
                                'duration': duration,
                                'start_time': start_time.isoformat(),
                                'end_time': timestamp.isoformat()
                            })
            
            # Detect timeout events
            if 'timeout' in message.lower():
                performance_data['timeout_events'].append({
                    'timestamp': timestamp.isoformat(),
                    'message': message[:100]
                })
        
        # Calculate performance metrics
        if performance_data['response_times']:
            avg_response = sum(performance_data['response_times']) / len(performance_data['response_times'])
            max_response = max(performance_data['response_times'])
            min_response = min(performance_data['response_times'])
        else:
            avg_response = max_response = min_response = 0
        
        return {
            'average_response_time': avg_response,
            'max_response_time': max_response,
            'min_response_time': min_response,
            'slow_transaction_count': len(performance_data['slow_transactions']),
            'timeout_count': len(performance_data['timeout_events']),
            'performance_issues': len(performance_data['slow_transactions']) > 0 or len(performance_data['timeout_events']) > 0,
            'slow_transactions': performance_data['slow_transactions'][:5],  # Top 5
            'timeout_events': performance_data['timeout_events'][:5]  # Top 5
        }
    
    def _analyze_transactions(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze transaction patterns and business metrics
        """
        transaction_data = {
            'total_transactions': 0,
            'transaction_types': Counter(),
            'payment_types': Counter(),
            'response_codes': Counter(),
            'approval_rate': 0,
            'decline_reasons': Counter()
        }
        
        for log in parsed_logs:
            message = log.get('message', '').lower()
            structured_data = log.get('structured_data', {})
            
            # Count transactions
            if any(indicator in message for indicator in ['sendtransaction', 'transaction started']):
                transaction_data['total_transactions'] += 1
                
                # Extract transaction type
                trans_type = structured_data.get('transaction_type')
                if trans_type:
                    transaction_data['transaction_types'][trans_type] += 1
            
            # Extract response codes
            response_code = structured_data.get('response_codes')
            if response_code:
                transaction_data['response_codes'][response_code] += 1
                
                # Track decline reasons
                if response_code != '00':
                    reason = self.business_patterns['response_codes'].get(response_code, 'Unknown')
                    transaction_data['decline_reasons'][reason] += 1
        
        # Calculate approval rate
        total_responses = sum(transaction_data['response_codes'].values())
        approvals = transaction_data['response_codes'].get('00', 0)
        transaction_data['approval_rate'] = approvals / total_responses if total_responses > 0 else 0
        
        return transaction_data
    
    def _analyze_system_health(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze overall system health indicators
        """
        health_indicators = {
            'host_connectivity': 'unknown',
            'terminal_status': 'unknown',
            'configuration_status': 'unknown',
            'memory_status': 'unknown',
            'service_status': 'unknown'
        }
        
        recent_logs = [log for log in parsed_logs if log.get('timestamp') and 
                      (datetime.now() - log['timestamp']).total_seconds() < 3600]  # Last hour
        
        for log in recent_logs:
            message = log.get('message', '').lower()
            level = log.get('level', '')
            
            # Host connectivity
            if 'host.*connected' in message or 'connection.*established' in message:
                health_indicators['host_connectivity'] = 'good'
            elif 'host.*down' in message or 'connection.*failed' in message:
                health_indicators['host_connectivity'] = 'poor'
            
            # Terminal status
            if 'terminal.*ready' in message or 'terminal.*ok' in message:
                health_indicators['terminal_status'] = 'good'
            elif 'terminal.*error' in message or 'terminal.*not responding' in message:
                health_indicators['terminal_status'] = 'poor'
            
            # Configuration status
            if 'configuration.*loaded' in message or 'setup.*ok' in message:
                health_indicators['configuration_status'] = 'good'
            elif 'configuration.*error' in message or 'setup.*failed' in message:
                health_indicators['configuration_status'] = 'poor'
            
            # Memory status
            if 'low memory' in message or 'memory.*error' in message:
                health_indicators['memory_status'] = 'poor'
            elif level not in ['ERROR', 'FATAL']:
                health_indicators['memory_status'] = 'good'
        
        return health_indicators
    
    def _analyze_temporal_patterns(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze temporal patterns in the logs
        """
        if not parsed_logs:
            return {}
        
        # Group logs by hour
        hourly_activity = defaultdict(int)
        hourly_errors = defaultdict(int)
        
        for log in parsed_logs:
            timestamp = log.get('timestamp')
            if timestamp:
                hour = timestamp.hour
                hourly_activity[hour] += 1
                
                if log.get('level') in ['ERROR', 'FATAL']:
                    hourly_errors[hour] += 1
        
        # Find peak activity hours
        peak_hour = max(hourly_activity.items(), key=lambda x: x[1]) if hourly_activity else (0, 0)
        
        return {
            'hourly_activity': dict(hourly_activity),
            'hourly_errors': dict(hourly_errors),
            'peak_activity_hour': peak_hour[0],
            'peak_activity_count': peak_hour[1],
            'total_time_span_hours': self._calculate_time_span(parsed_logs)
        }
    
    def _analyze_components(self, parsed_logs: List[Dict]) -> Dict:
        """
        Analyze activity by system component
        """
        component_activity = Counter()
        component_errors = Counter()
        
        for log in parsed_logs:
            source = log.get('source', 'UNKNOWN')
            component_activity[source] += 1
            
            if log.get('level') in ['ERROR', 'FATAL']:
                component_errors[source] += 1
        
        return {
            'component_activity': dict(component_activity),
            'component_errors': dict(component_errors),
            'most_active_component': component_activity.most_common(1)[0] if component_activity else ('None', 0),
            'most_error_prone_component': component_errors.most_common(1)[0] if component_errors else ('None', 0)
        }
    
    def _assess_error_severity(self, error_rate: float, error_categories: Dict) -> str:
        """
        Assess overall error severity
        """
        if error_rate > 0.2:  # More than 20% errors
            return 'critical'
        elif error_rate > 0.1:  # More than 10% errors
            return 'high'
        elif error_rate > 0.05:  # More than 5% errors
            return 'medium'
        elif error_rate > 0:
            return 'low'
        else:
            return 'none'
    
    def _calculate_health_score(self, analysis: Dict) -> float:
        """
        Calculate overall system health score (0-100)
        """
        score = 100.0
        
        # Deduct for errors
        error_rate = analysis.get('error_analysis', {}).get('error_rate', 0)
        score -= error_rate * 50  # Up to 50 points for errors
        
        # Deduct for performance issues
        if analysis.get('performance_analysis', {}).get('performance_issues'):
            score -= 20
        
        # Deduct for low approval rate
        approval_rate = analysis.get('transaction_analysis', {}).get('approval_rate', 1.0)
        if approval_rate < 0.9:  # Less than 90% approval
            score -= (0.9 - approval_rate) * 100
        
        return max(0, min(100, score))
    
    def _calculate_time_span(self, parsed_logs: List[Dict]) -> float:
        """
        Calculate time span of logs in hours
        """
        timestamps = [log.get('timestamp') for log in parsed_logs if log.get('timestamp')]
        if len(timestamps) < 2:
            return 0
        
        start_time = min(timestamps)
        end_time = max(timestamps)
        return (end_time - start_time).total_seconds() / 3600
