# OpenEPS: API and Interface Documentation

## Introduction

This document provides comprehensive documentation of all APIs, interfaces, communication protocols, and message formats used in the OpenEPS system. Understanding these interfaces is crucial for integration, troubleshooting, and system maintenance.

**Key Insight**: OpenEPS uses multiple communication layers and protocols. Each serves a specific purpose and has distinct message formats and error handling patterns.

---

## Core DLL Exports

### MTX_EPS.DLL - Core Payment Engine

**Purpose**: Main payment processing engine loaded by POS systems.

#### Primary Transaction Functions:

**`SendTransaction`**
```pascal
function SendTransaction(const request: TByteDynArray; const timeout: Smallint; 
                        const clientActivationKey: WideString): TByteDynArray; stdcall;
```
- **Purpose**: Process payment transactions
- **Parameters**: 
  - `request`: ISO 8583 formatted transaction data
  - `timeout`: Transaction timeout in seconds
  - `clientActivationKey`: Authentication key
- **Returns**: ISO 8583 response message
- **Log Pattern**: `[HOST] SendTransaction: Starting...`

**`SendStatus`**
```pascal
function SendStatus(...): Boolean; stdcall;
```
- **Purpose**: Send status updates to host
- **Returns**: Success/failure boolean
- **Log Pattern**: `[HOST] SendStatus: Lane X status update`

**`SendOfflineFwd`**
```pascal
function SendOfflineFwd(const request: TByteDynArray; 
                       const clientActivationKey: WideString): integer; stdcall;
```
- **Purpose**: Forward offline transactions when connectivity restored
- **Returns**: Error code (0 = success)
- **Log Pattern**: `[HOST] SendOfflineFwd: Uploading offline transactions`

#### Configuration and Management Functions:

**`OpenEpsLogin`**
```pascal
function OpenEpsLogin(WSDLAddr: string; const CompanyNumber: Integer; 
                     const StoreNumber: Integer; const LaneNumber: Integer;
                     var HardError: integer; var HardErrorStr: string; 
                     var Files: NeededFileTypeArray): TServerEpsLoginResult;
```
- **Purpose**: Authenticate with ServerEPS and download configuration
- **Returns**: Login result enumeration
- **Log Pattern**: `[SEPS] Login attempt for Company[X] Store[Y] Lane[Z]`

**`GetConfigurationFile`**
```pascal
function GetConfigurationFile(...): Boolean; stdcall;
```
- **Purpose**: Download configuration files from host
- **Log Pattern**: `[SEPS] Downloading configuration file: Setup.txt`

**`UploadJournal`**
```pascal
function UploadJournal(...): Boolean; stdcall;
```
- **Purpose**: Upload transaction logs to host
- **Log Pattern**: `[SEPS] Uploading journal: 1,247 transactions`

### MTX_POS.DLL - POS Interface Layer

**Purpose**: Standardized API for POS system integration.

#### Transaction Control Functions:

**`MTX_POS_SendTransaction`**
```pascal
procedure MTX_POS_SendTransaction; stdcall;
```
- **Purpose**: Initiate transaction processing
- **Prerequisites**: All required fields must be set via SET functions
- **Log Pattern**: `[OpenEPS] MTX_POS_SendTransaction: Starting`

**`MTX_POS_Reset`**
```pascal
procedure MTX_POS_Reset; stdcall;
```
- **Purpose**: Reset transaction state to initial condition
- **Log Pattern**: `[OpenEPS] MTX_POS_Reset: Transaction state cleared`

**`MTX_POS_TransactionComplete`**
```pascal
procedure MTX_POS_TransactionComplete; stdcall;
```
- **Purpose**: Finalize transaction and cleanup resources
- **Log Pattern**: `[OpenEPS] MTX_POS_TransactionComplete: Transaction finalized`

#### Data Setting Functions (SET):

**`MTX_POS_SET_PurchaseAmount`**
```pascal
procedure MTX_POS_SET_PurchaseAmount(APurchaseAmount: TPurchaseAmount); stdcall;
```
- **Purpose**: Set transaction amount in cents
- **Parameter**: Amount in cents (e.g., 4567 = $45.67)
- **Log Pattern**: `[OpenEPS] MTX_POS_SET_PurchaseAmount: 4567`

**`MTX_POS_SET_TenderType`**
```pascal
procedure MTX_POS_SET_TenderType(ATenderType: TTenderType); stdcall;
```
- **Purpose**: Set payment method type
- **Values**: 1=Debit, 2=Credit, 3=EBT_FS, 4=EBT_CA, 9=Gift Card
- **Log Pattern**: `[OpenEPS] MTX_POS_SET_TenderType: 2 (Credit)`

**`MTX_POS_SET_LaneNumber`**
```pascal
procedure MTX_POS_SET_LaneNumber(ALaneNumber: TLaneNumber); stdcall;
```
- **Purpose**: Set lane identifier
- **Log Pattern**: `[OpenEPS] MTX_POS_SET_LaneNumber: 3`

**`MTX_POS_SET_CashierID`**
```pascal
procedure MTX_POS_SET_CashierID(ACashierID: TCashierID); stdcall;
```
- **Purpose**: Set cashier identifier
- **Log Pattern**: `[OpenEPS] MTX_POS_SET_CashierID: 1234`

#### Data Retrieval Functions (GET):

**`MTX_POS_GET_ResponseCode`**
```pascal
procedure MTX_POS_GET_ResponseCode(var AResponseCode: TResponseCode); stdcall;
```
- **Purpose**: Get transaction response code
- **Returns**: Response code (00=Approved, 05=Declined, etc.)
- **Log Pattern**: `[OpenEPS] MTX_POS_GET_ResponseCode: 00`

**`MTX_POS_GET_AuthorizationNumber`**
```pascal
procedure MTX_POS_GET_AuthorizationNumber(var AAuthNumber: TAuthNumber); stdcall;
```
- **Purpose**: Get authorization number from host
- **Log Pattern**: `[OpenEPS] MTX_POS_GET_AuthorizationNumber: 123456`

**`MTX_POS_GET_AccountBalance`**
```pascal
procedure MTX_POS_GET_AccountBalance(var ABalance: TAccountBalance); stdcall;
```
- **Purpose**: Get account balance (for balance inquiries)
- **Returns**: Balance in cents
- **Log Pattern**: `[OpenEPS] MTX_POS_GET_AccountBalance: 23456 ($234.56)`

#### Status and Validation Functions:

**`MTX_POS_ValidateData`**
```pascal
procedure MTX_POS_ValidateData(var AValidateData: TValidateData; 
                              var AFieldsMissing: TFldChrMap); stdcall;
```
- **Purpose**: Validate transaction data completeness
- **Returns**: Validation result and missing field map
- **Log Pattern**: `[OpenEPS] MTX_POS_ValidateData: Missing fields: [Amount]`

**`MTX_POS_GET_HostStatus`**
```pascal
procedure MTX_POS_GET_HostStatus(var AHostStatus: THostStatus); stdcall;
```
- **Purpose**: Get current host communication status
- **Returns**: Status code indicating host connectivity
- **Log Pattern**: `[OpenEPS] MTX_POS_GET_HostStatus: 1 (Connected)`

---

## Communication Protocols

### 1. ISO 8583 Message Format

**Purpose**: Standard financial message format for host communication.

#### Message Structure:
```
[MTI][Primary Bitmap][Secondary Bitmap][Data Elements]
```

#### Key Message Type Indicators (MTI):
- **0200**: Financial Transaction Request
- **0210**: Financial Transaction Response  
- **0400**: Reversal Transaction Request
- **0410**: Reversal Transaction Response
- **0800**: Network Management Request
- **0810**: Network Management Response

#### Critical Data Elements:
- **Field 2**: Primary Account Number (PAN)
- **Field 3**: Processing Code
- **Field 4**: Transaction Amount
- **Field 7**: Transmission Date/Time
- **Field 11**: System Trace Audit Number (STAN)
- **Field 12**: Local Transaction Time
- **Field 13**: Local Transaction Date
- **Field 22**: Point of Service Entry Mode
- **Field 39**: Response Code
- **Field 41**: Card Acceptor Terminal ID

#### Log Representation:
```
[HOST] ISO 8583 Message: MTI=0200, STAN=123456, Amount=4567
[HOST] Field 2 (PAN): ****1234
[HOST] Field 3 (Proc Code): 000000
[HOST] Field 4 (Amount): ************
[HOST] Field 39 (Response): 00
```

### 2. OpenEpsNet XML Protocol

**Purpose**: Communication between POS systems and OpenEpsNet service.

#### Request Message Format:
```xml
<Request Action="BeginTransaction" SessionID="12345" Lane="3">
  <Fields>
    <Field Name="PurchaseAmount" Value="4567"/>
    <Field Name="TenderType" Value="2"/>
    <Field Name="CashierID" Value="1234"/>
  </Fields>
</Request>
```

#### Response Message Format:
```xml
<Response Action="BeginTransaction" ReturnValue="0" SessionID="12345" Lane="3">
  <Fields>
    <Field Name="ResponseCode" Value="00"/>
    <Field Name="AuthorizationNumber" Value="123456"/>
    <Field Name="ReceiptData" Value="..."/>
  </Fields>
</Response>
```

#### Common Actions:
- **Start**: Initialize OpenEpsNet service
- **BeginTransaction**: Start new transaction
- **SendTransaction**: Process payment
- **EndTransaction**: Complete transaction
- **CancelTransaction**: Cancel current transaction

### 3. Socket Protocol (OpenEpsNet)

**Purpose**: Low-level communication protocol for OpenEpsNet.

#### Message Format:
```
[Header][MessageId][Command][Encoding][Length][Message][Footer]
  0x00    Word      Byte     Byte      Word     Bytes    0xFF
```

#### Protocol Details:
- **Header**: Always 0x00
- **MessageId**: 2-byte correlation ID (BigEndian)
- **Command**: 1=SendOpenEpsMsg, 250=Ping
- **Encoding**: 1=ASCII, 2=UTF8
- **Length**: Message length in bytes (BigEndian)
- **Footer**: Always 0xFF

#### Log Pattern:
```
[Socket] Sending message: ID=12345, Cmd=1, Len=256
[Socket] Message received: ID=12345, Response=OK
```

### 4. SOAP/HTTP Protocol (ServerEPS)

**Purpose**: Web service communication with ServerEPS for configuration and management.

#### Service Endpoints:
- **Transaction Service**: `/TransactionProcessor/TransactionService.asmx`
- **File Management**: `/OpenEpsHandler/FileManagementService.asmx`
- **Lane Service**: `/MtxLaneServices/LaneService.asmx`
- **Configuration**: `/OpenEpsHandler/OpenEps824.asmx`

#### SOAP Message Example:
```xml
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <SendTransaction xmlns="http://servereps.mtxeps.com/">
      <request>BASE64_ENCODED_ISO8583_MESSAGE</request>
      <timeout>30</timeout>
      <clientActivationKey>AUTHENTICATION_KEY</clientActivationKey>
    </SendTransaction>
  </soap:Body>
</soap:Envelope>
```

#### Authentication:
- **Client Activation Key**: Required for all ServerEPS calls
- **SSL/TLS**: Encrypted communication required
- **Certificate Validation**: Server certificates must be valid

### 5. Terminal Communication Protocols

**Purpose**: Communication with payment terminals (PIN pads, card readers).

#### Supported Terminal Types:
- **SCAT (Secure Card Authentication Terminal)**
- **XPI (eXtended Payment Interface)**
- **FA (First Atlantic)**
- **RBA (Retail Banking Alliance)**

#### Message Formats:

**SCAT Protocol**:
```
[STX][LEN][CMD][DATA][ETX][LRC]
```

**XPI Protocol**:
```
[Header][Length][Command][Parameters][Checksum]
```

#### Common Terminal Commands:
- **Reset**: Initialize terminal
- **Display**: Show message to customer
- **GetTrackData**: Request card swipe
- **GetPIN**: Request PIN entry
- **SignatureCapture**: Capture customer signature

#### Log Patterns:
```
[TERM] Sending SCAT command: DISPLAY "SWIPE CARD"
[TERM] XPI response received: TRACK_DATA_OK
[TERM] PIN entry completed: PIN_OK
[TERM] Signature captured: 1024 bytes
```

---

## Message Formats and Data Structures

### 1. Transaction Data Structure (MdMsgRec)

**Purpose**: Internal transaction data representation.

#### Key Fields:
```pascal
type
  MdMsgRec = record
    // Transaction Identification
    Stan: string6;                    // System Trace Audit Number
    ReferenceId: string;              // Unique transaction reference

    // Transaction Details
    TransactionType: TTransactionType; // Purchase, Return, Void, etc.
    TenderType: TTenderType;          // Credit, Debit, EBT, Gift Card
    PurchaseAmount: integer;          // Amount in cents
    CashBackAmount: integer;          // Cash back amount in cents

    // Card Information
    PersonalAccountNumber: string;    // Masked card number
    ExpirationDate: string4;          // MMYY format
    Track1Data: string;               // Encrypted track 1
    Track2Data: string;               // Encrypted track 2

    // Response Data
    ResponseCode: string2;            // Host response code
    AuthorizationNumber: string6;     // Authorization number
    HostResponseText: string;         // Host response message

    // Receipt Information
    CashPadDisp: string;             // Cashier display line
    PrimeCustDisp: string;           // Customer display line 1
    AltCustDisp: string;             // Customer display line 2
  end;
```

### 2. Configuration File Formats

#### Setup.txt Format:
```ini
[GENERAL]
CompanyNumber=1234
StoreNumber=5678
LaneCount=12
HostType=SEPS

[HOST_SEPS]
PrimaryURL=https://host1.example.com
SecondaryURL=https://host2.example.com
Timeout=30
RetryCount=3

[LANE_01]
LaneType=G
TerminalType=XPI
TerminalPort=COM1
```

#### XML Configuration Format:
```xml
<StoreConfigurations>
  <Store Number="5678">
    <Lanes>
      <Lane Number="1" Type="G" TerminalType="XPI"/>
      <Lane Number="2" Type="U" TerminalType="SCAT"/>
    </Lanes>
    <Hosts>
      <Host Name="Chase" URL="https://chase.example.com"/>
      <Host Name="Shazam" URL="https://shazam.example.com"/>
    </Hosts>
  </Store>
</StoreConfigurations>
```

### 3. Response Code Definitions

#### Standard Response Codes:
```xml
<ResponseCodes>
  <ResponseCode ResponseCode="00" Description="Approved">
    <CustomerLines>
      <Language Id="1">
        <Line1>Approved</Line1>
        <Line2>Thank You...</Line2>
      </Language>
    </CustomerLines>
  </ResponseCode>

  <ResponseCode ResponseCode="05" Description="Do Not Honor">
    <CustomerLines>
      <Language Id="1">
        <Line1>Declined</Line1>
        <Line2>Contact Bank</Line2>
      </Language>
    </CustomerLines>
  </ResponseCode>
</ResponseCodes>
```

---

## Integration Patterns

### 1. POS System Integration

**Typical Integration Flow**:
```
1. POS loads MTX_POS.DLL
2. POS calls MTX_POS_InitDLL(LaneNumber)
3. For each transaction:
   a. POS sets transaction data via MTX_POS_SET_* functions
   b. POS calls MTX_POS_SendTransaction()
   c. POS retrieves results via MTX_POS_GET_* functions
   d. POS calls MTX_POS_TransactionComplete()
4. POS calls MTX_POS_Reset() between transactions
```

**Error Handling Pattern**:
```pascal
// Set transaction data
MTX_POS_SET_PurchaseAmount(4567);
MTX_POS_SET_TenderType(2); // Credit

// Validate data before sending
MTX_POS_ValidateData(ValidationResult, MissingFields);
if ValidationResult = vdValid then
begin
  // Send transaction
  MTX_POS_SendTransaction();

  // Get response
  MTX_POS_GET_ResponseCode(ResponseCode);
  MTX_POS_GET_HostStatus(HostStatus);

  if ResponseCode = '00' then
    // Transaction approved
    MTX_POS_GET_AuthorizationNumber(AuthNumber)
  else
    // Transaction declined
    HandleDecline(ResponseCode);
end;
```

### 2. Service Integration (OpenEpsNet)

**Service Communication Pattern**:
```csharp
// C# example for service integration
var request = new OpenEpsRequest
{
    Action = "BeginTransaction",
    SessionID = "12345",
    Lane = "3",
    Fields = new Dictionary<string, string>
    {
        {"PurchaseAmount", "4567"},
        {"TenderType", "2"},
        {"CashierID", "1234"}
    }
};

var response = await openEpsService.SendRequestAsync(request);
if (response.ReturnValue == 0)
{
    // Success
    var responseCode = response.Fields["ResponseCode"];
    var authNumber = response.Fields["AuthorizationNumber"];
}
```

### 3. Host Communication Integration

**ISO 8583 Message Building**:
```pascal
// Build ISO 8583 message
IsoMessage := TIsoMessage.Create;
try
  IsoMessage.MTI := '0200';  // Financial request
  IsoMessage.SetField(2, MaskedPAN);
  IsoMessage.SetField(3, '000000');  // Purchase
  IsoMessage.SetField(4, FormatAmount(PurchaseAmount));
  IsoMessage.SetField(11, STAN);
  IsoMessage.SetField(22, '021');  // Swiped with PIN capability

  // Send to host
  ResponseMessage := SendToHost(IsoMessage.Pack);

  // Parse response
  ResponseIso := TIsoMessage.Create;
  ResponseIso.Unpack(ResponseMessage);
  ResponseCode := ResponseIso.GetField(39);
finally
  IsoMessage.Free;
  ResponseIso.Free;
end;
```

---

## Error Handling and Status Codes

### API Return Codes:
- **0**: Success
- **1**: General error
- **100-199**: Configuration errors
- **200-299**: Communication errors
- **300-399**: Validation errors
- **400-499**: Business logic errors
- **500-599**: System errors

### Host Status Codes:
- **0**: Not connected
- **1**: Connected and ready
- **2**: Connected but busy
- **3**: Connection error
- **4**: Authentication failed

### Validation Status:
- **vdValid**: All required data present
- **vdMissingRequired**: Required fields missing
- **vdInvalidFormat**: Data format errors
- **vdBusinessRuleViolation**: Business rule violations

---

## Security Considerations

### 1. Data Encryption:
- **Card Data**: Encrypted at point of entry
- **PIN Data**: DUKPT encryption
- **Communication**: SSL/TLS for all host communication

### 2. Authentication:
- **Client Activation Keys**: Required for ServerEPS
- **Digital Certificates**: For SSL/TLS communication
- **User Authentication**: Cashier/manager sign-on

### 3. Compliance:
- **PCI DSS**: Payment card data security
- **PA-DSS**: Payment application security
- **Data Masking**: Sensitive data masked in logs

---

## Performance Considerations

### 1. Transaction Throughput:
- **Target**: <3 seconds per transaction
- **Maximum**: 50 concurrent transactions per store
- **Timeout**: 30-60 seconds for host communication

### 2. Memory Management:
- **DLL Loading**: MTX_EPS.DLL loaded once per POS process
- **Transaction Buffers**: Allocated per transaction
- **Cleanup**: Proper resource cleanup required

### 3. Network Optimization:
- **Connection Pooling**: Reuse host connections
- **Compression**: Message compression for large data
- **Retry Logic**: Automatic retry for transient failures

---

## Conclusion

The OpenEPS API and interface architecture provides:

1. **Standardized Integration**: Consistent API across different POS systems
2. **Multiple Communication Protocols**: Support for various integration patterns
3. **Robust Error Handling**: Comprehensive error codes and status reporting
4. **Security Compliance**: Built-in security and compliance features
5. **Performance Optimization**: Efficient communication and resource management

Understanding these interfaces is essential for successful OpenEPS integration, troubleshooting, and maintenance operations.
