﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectGuid>{9ce724ec-c42f-4150-9bf1-0a290ab1db58}</ProjectGuid>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <DCC_DCCCompiler>DCC32</DCC_DCCCompiler>
    <DCC_DependencyCheckOutputName>R:\mtx_dbc.dll</DCC_DependencyCheckOutputName>
    <MainSource>mtx_dbc.dpr</MainSource>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Version>7.0</Version>
    <DCC_DebugInformation>False</DCC_DebugInformation>
    <DCC_LocalDebugSymbols>False</DCC_LocalDebugSymbols>
    <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
    <DCC_Define>RELEASE</DCC_Define>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <Version>7.0</Version>
    <DCC_Define>DEBUG;MSWINDOWS;LOGGING;XDEBUG</DCC_Define>
    <DCC_ExeOutput>R:\</DCC_ExeOutput>
    <DCC_UnitSearchPath>O:\D2007;..\Common\;..\OpenEPS;E:\Dev\Compo\LockBox for D2007\source;E:\dev\compo\Abbrevia 3.04\source;E:\Dev\Compo\XmlParser (D4-7);E:\Dev\Compo\ComponentAce\ZipForge\Source</DCC_UnitSearchPath>
    <DCC_ResourcePath>O:\D2007;..\Common\;..\OpenEPS;E:\Dev\Compo\LockBox for D2007\source;E:\dev\compo\Abbrevia 3.04\source;E:\Dev\Compo\XmlParser (D4-7);E:\Dev\Compo\ComponentAce\ZipForge\Source</DCC_ResourcePath>
    <DCC_ObjPath>O:\D2007;..\Common\;..\OpenEPS;E:\Dev\Compo\LockBox for D2007\source;E:\dev\compo\Abbrevia 3.04\source;E:\Dev\Compo\XmlParser (D4-7);E:\Dev\Compo\ComponentAce\ZipForge\Source</DCC_ObjPath>
    <DCC_IncludePath>O:\D2007;..\Common\;..\OpenEPS;E:\Dev\Compo\LockBox for D2007\source;E:\dev\compo\Abbrevia 3.04\source;E:\Dev\Compo\XmlParser (D4-7);E:\Dev\Compo\ComponentAce\ZipForge\Source</DCC_IncludePath>
    <DCC_WriteableConstants>True</DCC_WriteableConstants>
    <DCC_DcuOutput>O:\D2007</DCC_DcuOutput>
    <DCC_ObjOutput>O:\D2007</DCC_ObjOutput>
    <DCC_HppOutput>O:\D2007</DCC_HppOutput>
  </PropertyGroup>
  <ProjectExtensions>
    <Borland.Personality>Delphi.Personality</Borland.Personality>
    <Borland.ProjectType />
    <BorlandProject>
<BorlandProject><Delphi.Personality><Parameters><Parameters Name="UseLauncher">False</Parameters><Parameters Name="LoadAllSymbols">True</Parameters><Parameters Name="LoadUnspecifiedSymbols">False</Parameters></Parameters><VersionInfo><VersionInfo Name="IncludeVerInfo">True</VersionInfo><VersionInfo Name="AutoIncBuild">True</VersionInfo><VersionInfo Name="MajorVer">827</VersionInfo><VersionInfo Name="MinorVer">1</VersionInfo><VersionInfo Name="Release">0</VersionInfo><VersionInfo Name="Build">9</VersionInfo><VersionInfo Name="Debug">False</VersionInfo><VersionInfo Name="PreRelease">False</VersionInfo><VersionInfo Name="Special">False</VersionInfo><VersionInfo Name="Private">False</VersionInfo><VersionInfo Name="DLL">False</VersionInfo><VersionInfo Name="Locale">1033</VersionInfo><VersionInfo Name="CodePage">1252</VersionInfo></VersionInfo><VersionInfoKeys><VersionInfoKeys Name="CompanyName"></VersionInfoKeys><VersionInfoKeys Name="FileDescription"></VersionInfoKeys><VersionInfoKeys Name="FileVersion">827.1.0.9</VersionInfoKeys><VersionInfoKeys Name="InternalName"></VersionInfoKeys><VersionInfoKeys Name="LegalCopyright"></VersionInfoKeys><VersionInfoKeys Name="LegalTrademarks"></VersionInfoKeys><VersionInfoKeys Name="OriginalFilename"></VersionInfoKeys><VersionInfoKeys Name="ProductName"></VersionInfoKeys><VersionInfoKeys Name="ProductVersion">D2007.0.0.0</VersionInfoKeys><VersionInfoKeys Name="Comments"></VersionInfoKeys></VersionInfoKeys><Source><Source Name="MainSource">mtx_dbc.dpr</Source></Source></Delphi.Personality></BorlandProject></BorlandProject>
  </ProjectExtensions>
  <Import Project="$(MSBuildBinPath)\Borland.Delphi.Targets" />
  <ItemGroup>
    <DelphiCompile Include="mtx_dbc.dpr">
      <MainSource>MainSource</MainSource>
    </DelphiCompile>
    <DCCReference Include="..\OpenEPS\SEConfigurationFileResponse.pas" />
    <DCCReference Include="..\OpenEPS\SE_WebClient.pas" />
    <DCCReference Include="DialBackClient.pas" />
    <DCCReference Include="StoreConfigurations.pas" />
    <DCCReference Include="uDGProcessList.pas" />
    <DCCReference Include="uDialBackClient.pas" />
  </ItemGroup>
</Project>