unit uDiaBackupClientGUI;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, LMDCustomControl, LMDCustomPanel, LMDCustomBevelPanel,
  Registry,
  LMDBaseEdit, LMDCustomEdit, LMDEdit, LMDControl, LMDBaseControl,
  LMDBaseGraphicControl, LMDBaseLabel, LMDCustomLabel, LMDLabel, StdCtrls,
  LMDCustomButton, LMDButton, LMDGraphicControl,DateUtils;

type
  TfrmDialBackup = class(TForm)
    tmrStatus: TTimer;
    lblCompany: TLMDLabel;
    edtCompany: TLMDEdit;
    lblStore: TLMDLabel;
    lblRAS: TLMDLabel;
    lblIP: TLMDLabel;
    edtStore: TLMDEdit;
    edtRAS: TLMDEdit;
    edtIPAddress: TLMDEdit;
    lblPort: TLMDLabel;
    edtPort: TLMDEdit;
    edtTimeout: TLMDEdit;
    lblTimeoutIdle: TLMDLabel;
    pnlServiceStatus: TPanel;
    pnlDialConnection: TPanel;
    btnExit: TLMDButton;
    lblStatusAgelbl: TLMDLabel;
    lblStatusAge: TLMDLabel;
    procedure btnExitClick(Sender: TObject);
    procedure tmrStatusTimer(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure ServiceDisplay(Active: boolean);
    procedure DialDisplay(Active: boolean);
  private
    { Private declarations }
    StatusTime: TDateTime;
    procedure CheckStatus;
  public
    { Public declarations }
  end;

var
  frmDialBackup: TfrmDialBackup;

implementation

{$R *.dfm}

uses
  Ras,
  RasUtils,
  RasHelperClasses,
  WinSvc,
  StoreConfigurations,
  ActiveX,
  ServerEPSConstants;

//const
  //sOffOn: array[boolean] of string[3] = ('OFF','ON');
  //sOK: array[boolean] of string[4] = ('FAIL','OK');
  //REGISTRYMTX = 'REGISTRY.MTX';
  //LOG_NAMEONLY = 'MTX_DBC_Log';
  //LOG_EXTONLY = '.txt';
  //LOG_FILENAME = LOG_NAMEONLY + LOG_EXTONLY;

type
  TRasClass = class(TComponent)
  private
    FDialError: DWORD;
    FNowDialing: boolean;
    procedure RasDialerNotify(Sender: TObject; State: TRasConnState; ErrorCode: DWORD);
  public
    PhoneBook: TRasPhonebook;
    Dialer: TRasDialer;
    Index : integer;
    property DialError: DWORD read FDialError write FDialError;
    property NowDialing: boolean read FNowDialing write FNowDialing;
  end;

  TRasDialToHost = class
    FRasClass: TRasClass;
  private
    function GetRasState: TRasConnState;
  public
    constructor create;
    destructor destroy; override;
    function  IsRASConnected: boolean;
    procedure RasInit;
  end;

const
  STATUS_LATENCY = 15;   //seconds
  SETTINGS_XML = 'StoreConfigurations.xml';
  DEFAULT_PORT = 443;
  DEFAULT_ADDR = '127.0.0.1';
  DEFAULT_TIMEOUT = 300;  // seconds
  DEFAULT_COMPANY = 999;
  DEFAULT_STORE = 1;
  NOT_FOUND = -1;
  xCOLOR: array[boolean] of TColor = (clRed,clLime);

var
  RASDialToHost: TRasDialToHost;
  sRAS,IP: string;
  Port,Timeout: integer;

function ServiceRunning: boolean;
const
  SERVICENAME = 'DialBackupClient';
var
  scm,svc: SC_HANDLE;
  ServiceStatus: TServiceStatus;
begin
  result := false;
  scm := OpenSCManager(nil,nil,SERVICE_QUERY_STATUS);
  if scm > 0 then
    begin
    svc := OpenService(scm,SERVICENAME,SERVICE_QUERY_STATUS);
    if svc > 0 then
      begin
      if QueryServiceStatus(svc, ServiceStatus) then
        result := ServiceStatus.dwCurrentState = SERVICE_RUNNING;
      CloseServiceHandle(svc);
      end;
    CloseServiceHandle(scm);
    end;
end;

function GetIntKeyFromRegistry(root: hkey; dir, keyName: string; Default: integer): integer;
var
  reg: TRegistry;
begin
  result := NOT_FOUND;
  try
    reg := TRegistry.create;
    try
      reg.Access := KEY_EXECUTE;         // read-only access
      reg.rootkey := root;
      if (reg.openkey(dir, false)) then
        begin
        result := Reg.ReadInteger(keyName);
        Reg.CloseKey;
        end
      else
        result := Default;
    finally
      reg.free;
    end;
  except on e : Exception do ;
  end;
end;

function GetCompanyNumber: integer;
begin
  result := GetIntKeyFromRegistry(HKEY_LOCAL_MACHINE, SERVEREPS_REGISTRY_LOCATION, COMPANY_KEY, DEFAULT_COMPANY);
  if result < 0 then
    result := DEFAULT_COMPANY;
end;

function GetStoreNumber: integer;
begin
  result := GetIntKeyFromRegistry(HKEY_LOCAL_MACHINE, SERVEREPS_REGISTRY_LOCATION, STORE_KEY, DEFAULT_STORE);
  if result < 0 then
    result := DEFAULT_STORE;
end;

procedure GetStoreConfigurations;
var
  XML: IXMLStoreConfigurationsType;
  Filename: string;
begin
  Filename := ExtractFilePath(ParamStr(0)) + SETTINGS_XML;
  if FileExists(Filename) then
    begin
    CoInitialize(nil);         //CoInitializeEx(nil,COINIT_APARTMENTTHREADED);
    XML := LoadStoreConfigurations(Filename);
    IP := XML.DialBackupConfiguration.DBCIPAddress;
    if length(IP) = 0
      then IP := DEFAULT_ADDR;
    try
      Port := XML.DialBackupConfiguration.DBCPort;
    except on e: exception do
      Port := DEFAULT_PORT;
    end;
    try
      Timeout := XML.DialBackupConfiguration.DBCIdleTimeout;
    except on e: exception do
      Timeout := DEFAULT_TIMEOUT;
    end;
    sRAS := XML.DialBackupConfiguration.DBCRAS;
    CoUninitialize;
    end
  else
    begin
    IP := DEFAULT_ADDR;
    Port := DEFAULT_PORT;
    Timeout := DEFAULT_TIMEOUT;
    sRAS := '';
    end;
end;

procedure TfrmDialBackup.btnExitClick(Sender: TObject);
begin
  Close;
end;

procedure TRasClass.RasDialerNotify(Sender: TObject; State: TRasConnState; ErrorCode: DWORD);
begin
  FDialError := ErrorCode;
  FNowDialing := false;
  case State of
    RASCS_Connected:    ;
    RASCS_Disconnected: ;
  else    { could be an error or just a notification here }
    begin
    FNowDialing := false;
    //SM('RasDialerNotify: ' + RasConnStatusString(State, DialError) + ' RasState=' + IntToStr(State) + ' DialError=' + IntToStr(DialError));
    end;
  end;
end;

constructor TRasDialToHost.create;
begin
  FRasClass := Nil;
  RasInit;
end;

destructor TRasDialToHost.Destroy;
begin
  inherited;
end;

procedure TRasDialToHost.RasInit;
var
  i: integer;
  FoundRAS: boolean;
begin
  try
    if not assigned(FRasClass) then
      begin
      FRasClass := TRasClass.Create(nil);
      FRasClass.PhoneBook := TRasPhonebook.Create;
      FRasClass.Dialer := TRasDialer.Create;
      FRasClass.DialError := 0;
      FRasClass.Dialer.OnNotify := FRasClass.RasDialerNotify;
      FRasClass.Index := 0;
      end;

    with FRasClass.PhoneBook do
      for i := 0 to Count-1 do
        begin
        //SM(format('Scanning Phone Entry %d Name = %s  %s',[i,Items[FRasClass.Index].name,Items[FRasClass.Index].PhoneNumber]));
        FoundRAS := SameText(Items[i].Name,sRAS) or (sRAS = '');  // if RAS is blank, then just use FIRST entry in Phonebook
        if FoundRAS then
          begin
          FRasClass.Index := i;
          //SM(format('Phone Entry %d Name %s matches Config %s',[i,ProxyServer.RAS,Items[FRasClass.Index].name]));
          break;
          end;
        end;
    //if not FoundRAS then
    //  SM(format('Unable to locate RAS Name %s in Phonebook; using 1st entry',[RAS]));
    FRasClass.Dialer.Assign(FRasClass.PhoneBook.items[FRasClass.Index]);
    //SM(format('RAS Dial: %s -> %s',[FRasClass.PhoneBook.items[FRasClass.Index].name,FRasClass.PhoneBook.items[FRasClass.Index].PhoneNumber]));

  except on e: Exception do
    //SM('RasDial - ' + e.message);
  end;
end;

function TRasDialToHost.GetRasState: TRasConnState;
var aRasStatus: TRasConnStatus;
begin
  result := RASCS_Disconnected;
  aRasStatus.dwSize := sizeOf(aRasStatus);
  if assigned(FRasClass) and (FRasClass.Dialer.ConnHandle > 0) then    { then supposed to be connected }
    if (Ras.RasGetConnectStatus(FRasClass.Dialer.Connhandle, aRasStatus) = 0) then
      result := aRasStatus.rasconnstate;
end;

function TRasDialToHost.IsRASConnected: boolean;
begin
  result := GetRasState = RASCS_Connected;
end;

function RASStatus: boolean;
begin
  try
    result := Assigned(RasDialToHost) and RasDialToHost.IsRASConnected;
  except on e: exception do
    result := false;
  end;
end;

procedure TfrmDialBackup.ServiceDisplay(Active: boolean);
const
  sRUNNING: array[boolean] of string[30] = ('Service NOT Active','Service Active');
begin
  pnlServiceStatus.Caption := sRUNNING[Active];
  pnlServiceStatus.Color := xCOLOR[Active];
end;

procedure TfrmDialBackup.DialDisplay(Active: boolean);
const
  Connected: array[boolean] of string[30] = ('DIAL NOT Connected','DIAL Connected');
begin
  pnlDialConnection.Caption := Connected[Active];
  pnlDialConnection.Color := xCOLOR[Active];
end;

procedure TfrmDialBackup.CheckStatus;
const
  sPlural: array[boolean] of string[3] = ('s','');
var
  Elapsed,Remaining: integer;
  Running: boolean;
begin
  Elapsed := SecondsBetween(Now,StatusTime);
  Remaining := STATUS_LATENCY-Elapsed;
  lblStatusAge.Caption := format('%d second%s',[Remaining,sPlural[Remaining=1]]);
  if Elapsed >= STATUS_LATENCY then
    begin
    edtCompany.Text := format('%d',[GetCompanyNumber]);
    edtStore.Text := format('%d',[GetStoreNumber]);
    GetStoreConfigurations;
    edtRAS.Text := sRAS;
    edtIPAddress.Text := IP;
    edtPort.Text := format('%d',[Port]);
    edtTimeout.Text := format('%d',[Timeout]);
    Running := ServiceRunning;
    ServiceDisplay(Running);
    if Running then       // if service running then check dial connected
      DialDisplay(RASStatus);
    StatusTime := Now;
    end;
end;


procedure TfrmDialBackup.FormShow(Sender: TObject);
begin
  StatusTime := IncSecond(Now,-STATUS_LATENCY-1);
  RASDialToHost := TRASDialToHost.create;
  CheckStatus;
end;

procedure TfrmDialBackup.tmrStatusTimer(Sender: TObject);
begin
  CheckStatus;
end;

end.
