object frmProxyServer: TfrmProxyServer
  Left = 0
  Top = 0
  Caption = 'MTXEPS ProxyServer Test Platform'
  ClientHeight = 605
  ClientWidth = 683
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnClose = FormClose
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object LMDLabel3: TLMDLabel
    Left = 516
    Top = 28
    Width = 80
    Height = 15
    Bevel.Mode = bmCustom
    Options = []
    Caption = 'Timeout Hangup'
  end
  object LMDLabel4: TLMDLabel
    Left = 591
    Top = 47
    Width = 41
    Height = 15
    Bevel.Mode = bmCustom
    Options = []
    Caption = 'seconds'
  end
  object MemoLog: TMemo
    Left = 0
    Top = 96
    Width = 683
    Height = 509
    Align = alBottom
    ScrollBars = ssBoth
    TabOrder = 0
  end
  object btnProxyActivate: TLMDButton
    Left = 16
    Top = 8
    Width = 169
    Height = 25
    Caption = 'Proxy is OFF'
    TabOrder = 1
    OnClick = btnProxyActivateClick
  end
  object LMDGroupBox1: TLMDGroupBox
    Left = 208
    Top = 8
    Width = 201
    Height = 74
    Bevel.Mode = bmWindows
    Caption = 'Bindings'
    CaptionFont.Charset = DEFAULT_CHARSET
    CaptionFont.Color = clWindowText
    CaptionFont.Height = -11
    CaptionFont.Name = 'Tahoma'
    CaptionFont.Style = []
    TabOrder = 2
    object LMDLabel1: TLMDLabel
      Left = 26
      Top = 18
      Width = 12
      Height = 15
      Bevel.Mode = bmCustom
      Alignment = agCenterRight
      Options = []
      Caption = 'IP'
    end
    object LMDLabel2: TLMDLabel
      Left = 16
      Top = 41
      Width = 22
      Height = 15
      Bevel.Mode = bmCustom
      Alignment = agCenterRight
      Options = []
      Caption = 'Port'
    end
    object edtIP: TLMDEdit
      Left = 52
      Top = 16
      Width = 81
      Height = 21
      Bevel.Mode = bmWindows
      Caret.BlinkRate = 530
      TabOrder = 0
      CustomButtons = <>
      PasswordChar = #0
      Text = '***********'
    end
    object edtPort: TLMDMaskEdit
      Left = 52
      Top = 39
      Width = 47
      Height = 21
      Bevel.Mode = bmWindows
      Caret.BlinkRate = 530
      TabOrder = 1
      CurrencySettings.SystemDefaults = cmSystem
      CurrencySettings.PositiveFormatStr = #164'1,1'
      CurrencySettings.NegativeFormatStr = '('#164'1,1)'
      CurrencySettings.Symbol = '$'
      CustomButtons = <>
      MaskType = meInteger
      Text = '443'
      DateTime = 0.000000000000000000
    end
  end
  object btnDial: TLMDButton
    Left = 424
    Top = 8
    Width = 75
    Height = 25
    Caption = 'Dial'
    TabOrder = 3
    OnClick = btnDialClick
  end
  object btnPortActive: TLMDButton
    Left = 16
    Top = 39
    Width = 169
    Height = 25
    Caption = 'Port is OFF'
    TabOrder = 4
  end
  object btnHangup: TLMDButton
    Left = 424
    Top = 39
    Width = 75
    Height = 25
    Caption = 'Hangup'
    TabOrder = 5
    OnClick = btnHangupClick
  end
  object edtTimeout: TLMDMaskEdit
    Left = 528
    Top = 43
    Width = 57
    Height = 21
    Bevel.Mode = bmWindows
    Caret.BlinkRate = 530
    TabOrder = 6
    Alignment = taRightJustify
    CurrencySettings.SystemDefaults = cmSystem
    CurrencySettings.PositiveFormatStr = #164'1,1'
    CurrencySettings.NegativeFormatStr = '('#164'1,1)'
    CurrencySettings.Symbol = '$'
    CustomButtons = <>
    MaskType = meInteger
    Text = '0'
    DateTime = 0.000000000000000000
  end
  object IdHTTPProxyServer1: TIdHTTPProxyServer
    Bindings = <>
    DefaultPort = 443
    MaxConnections = 500
    OnConnect = IdHTTPProxyServer1Connect
    OnExecute = IdHTTPProxyServer1Execute
    CommandHandlers = <>
    ExceptionReply.Code = '500'
    ExceptionReply.Text.Strings = (
      'Unknown Internal Error')
    Greeting.Code = '200'
    Greeting.Text.Strings = (
      'Greetings from the MTXEPS Proxy Server')
    HelpReply.Code = '100'
    HelpReply.Text.Strings = (
      'Help follows')
    MaxConnectionReply.Code = '300'
    MaxConnectionReply.Text.Strings = (
      'Too many connections. Try again later.')
    ReplyTexts = <>
    ReplyUnknownCommand.Code = '400'
    OnBeforeCommandHandler = IdHTTPProxyServer1BeforeCommandHandler
    OnHTTPDocument = IdHTTPProxyServer1HTTPDocument
    Left = 624
    Top = 8
  end
  object IdMappedPort: TIdMappedPortTCP
    Bindings = <>
    DefaultPort = 0
    MappedPort = 0
    OnOutboundConnect = IdMappedPortOutboundConnect
    Left = 648
    Top = 56
  end
  object HangupTimer: TTimer
    Enabled = False
    Interval = 60000
    OnTimer = HangupTimerTimer
    Left = 640
    Top = 24
  end
end
