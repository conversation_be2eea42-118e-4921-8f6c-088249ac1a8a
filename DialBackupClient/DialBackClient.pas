
{*****************************************************************}
{                                                                 }
{                        XML Data Binding                         }
{                                                                 }
{         Generated on: 5/5/2010 11:10:26 AM                      }
{       Generated from: Z:\826.1\DialupProxy\DialBackClient.xml   }
{   Settings stored in: Z:\826.1\DialupProxy\DialBackClient.xdb   }
{                                                                 }
{*****************************************************************}

unit DialBackClient;

interface

uses xmldom, XMLDoc, XMLIntf;

type

{ Forward Decls }

  IXMLDialBackupConfigurationType = interface;

{ IXMLDialBackupConfigurationType }

  IXMLDialBackupConfigurationType = interface(IXMLNode)
    ['{3F54C571-D4EC-4566-BD5A-C6EA503BE35C}']
    { Property Accessors }
    function Get_DBCIPAddress: WideString;
    function Get_DBCPort: Integer;
    function Get_DBCIdleTimeout: Integer;
    function Get_DBCRAS: WideString;
    procedure Set_DBCIPAddress(Value: WideString);
    procedure Set_DBCPort(Value: Integer);
    procedure Set_DBCIdleTimeout(Value: Integer);
    procedure Set_DBCRAS(Value: WideString);
    { Methods & Properties }
    property DBCIPAddress: WideString read Get_DBCIPAddress write Set_DBCIPAddress;
    property DBCPort: Integer read Get_DBCPort write Set_DBCPort;
    property DBCIdleTimeout: Integer read Get_DBCIdleTimeout write Set_DBCIdleTimeout;
    property DBCRAS: WideString read Get_DBCRAS write Set_DBCRAS;
  end;

{ Forward Decls }

  TXMLDialBackupConfigurationType = class;

{ TXMLDialBackupConfigurationType }

  TXMLDialBackupConfigurationType = class(TXMLNode, IXMLDialBackupConfigurationType)
  protected
    { IXMLDialBackupConfigurationType }
    function Get_DBCIPAddress: WideString;
    function Get_DBCPort: Integer;
    function Get_DBCIdleTimeout: Integer;
    function Get_DBCRAS: WideString;
    procedure Set_DBCIPAddress(Value: WideString);
    procedure Set_DBCPort(Value: Integer);
    procedure Set_DBCIdleTimeout(Value: Integer);
    procedure Set_DBCRAS(Value: WideString);
  end;

{ Global Functions }

function GetDialBackupConfiguration(Doc: IXMLDocument): IXMLDialBackupConfigurationType;
function LoadDialBackupConfiguration(const FileName: WideString): IXMLDialBackupConfigurationType;
function NewDialBackupConfiguration: IXMLDialBackupConfigurationType;

const
  TargetNamespace = '';

implementation

{ Global Functions }

function GetDialBackupConfiguration(Doc: IXMLDocument): IXMLDialBackupConfigurationType;
begin
  Result := Doc.GetDocBinding('DialBackupConfiguration', TXMLDialBackupConfigurationType, TargetNamespace) as IXMLDialBackupConfigurationType;
end;

function LoadDialBackupConfiguration(const FileName: WideString): IXMLDialBackupConfigurationType;
begin
  Result := LoadXMLDocument(FileName).GetDocBinding('DialBackupConfiguration', TXMLDialBackupConfigurationType, TargetNamespace) as IXMLDialBackupConfigurationType;
end;

function NewDialBackupConfiguration: IXMLDialBackupConfigurationType;
begin
  Result := NewXMLDocument.GetDocBinding('DialBackupConfiguration', TXMLDialBackupConfigurationType, TargetNamespace) as IXMLDialBackupConfigurationType;
end;

{ TXMLDialBackupConfigurationType }

function TXMLDialBackupConfigurationType.Get_DBCIPAddress: WideString;
begin
  Result := ChildNodes['DBCIPAddress'].Text;
end;

procedure TXMLDialBackupConfigurationType.Set_DBCIPAddress(Value: WideString);
begin
  ChildNodes['DBCIPAddress'].NodeValue := Value;
end;

function TXMLDialBackupConfigurationType.Get_DBCPort: Integer;
begin
  Result := ChildNodes['DBCPort'].NodeValue;
end;

procedure TXMLDialBackupConfigurationType.Set_DBCPort(Value: Integer);
begin
  ChildNodes['DBCPort'].NodeValue := Value;
end;

function TXMLDialBackupConfigurationType.Get_DBCIdleTimeout: Integer;
begin
  Result := ChildNodes['DBCIdleTimeout'].NodeValue;
end;

procedure TXMLDialBackupConfigurationType.Set_DBCIdleTimeout(Value: Integer);
begin
  ChildNodes['DBCIdleTimeout'].NodeValue := Value;
end;

function TXMLDialBackupConfigurationType.Get_DBCRAS: WideString;
begin
  Result := ChildNodes['DBCRAS'].Text;
end;

procedure TXMLDialBackupConfigurationType.Set_DBCRAS(Value: WideString);
begin
  ChildNodes['DBCRAS'].NodeValue := Value;
end;

end.