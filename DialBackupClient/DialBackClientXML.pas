
{*****************************************************************}
{                                                                 }
{                        XML Data Binding                         }
{                                                                 }
{         Generated on: 5/3/2010 1:10:33 PM                       }
{       Generated from: Z:\826.1\DialupProxy\DialBackClient.xml   }
{   Settings stored in: Z:\826.1\DialupProxy\DialBackClient.xdb   }
{                                                                 }
{*****************************************************************}

unit DialBackClientXML;

interface

uses xmldom, XMLDoc, XMLIntf;

type

{ Forward Decls }

  IXMLDialBackClientType = interface;

{ IXMLDialBackClientType }

  IXMLDialBackClientType = interface(IXMLNode)
    ['{CBDC3598-3572-428D-98D9-522E2320250D}']
    { Property Accessors }
    function Get_Addr: WideString;
    function Get_Port: Integer;
    function Get_Timeout: Integer;
    function Get_Company: Integer;
    function Get_Store: Integer;
    procedure Set_Addr(Value: WideString);
    procedure Set_Port(Value: Integer);
    procedure Set_Timeout(Value: Integer);
    procedure Set_Company(Value: Integer);
    procedure Set_Store(Value: Integer);
    { Methods & Properties }
    property Addr: WideString read Get_Addr write Set_Addr;
    property Port: Integer read Get_Port write Set_Port;
    property Timeout: Integer read Get_Timeout write Set_Timeout;
    property Company: Integer read Get_Company write Set_Company;
    property Store: Integer read Get_Store write Set_Store;
  end;

{ Forward Decls }

  TXMLDialBackClientType = class;

{ TXMLDialBackClientType }

  TXMLDialBackClientType = class(TXMLNode, IXMLDialBackClientType)
  protected
    { IXMLDialBackClientType }
    function Get_Addr: WideString;
    function Get_Port: Integer;
    function Get_Timeout: Integer;
    function Get_Company: Integer;
    function Get_Store: Integer;
    procedure Set_Addr(Value: WideString);
    procedure Set_Port(Value: Integer);
    procedure Set_Timeout(Value: Integer);
    procedure Set_Company(Value: Integer);
    procedure Set_Store(Value: Integer);
  end;

{ Global Functions }

function GetDialBackClient(Doc: IXMLDocument): IXMLDialBackClientType;
function LoadDialBackClient(const FileName: WideString): IXMLDialBackClientType;
function NewDialBackClient: IXMLDialBackClientType;

const
  TargetNamespace = '';

implementation

{ Global Functions }

function GetDialBackClient(Doc: IXMLDocument): IXMLDialBackClientType;
begin
  Result := Doc.GetDocBinding('DialBackClient', TXMLDialBackClientType, TargetNamespace) as IXMLDialBackClientType;
end;

function LoadDialBackClient(const FileName: WideString): IXMLDialBackClientType;
begin
  Result := LoadXMLDocument(FileName).GetDocBinding('DialBackClient', TXMLDialBackClientType, TargetNamespace) as IXMLDialBackClientType;
end;

function NewDialBackClient: IXMLDialBackClientType;
begin
  Result := NewXMLDocument.GetDocBinding('DialBackClient', TXMLDialBackClientType, TargetNamespace) as IXMLDialBackClientType;
end;

{ TXMLDialBackClientType }

function TXMLDialBackClientType.Get_Addr: WideString;
begin
  Result := ChildNodes['Addr'].Text;
end;

procedure TXMLDialBackClientType.Set_Addr(Value: WideString);
begin
  ChildNodes['Addr'].NodeValue := Value;
end;

function TXMLDialBackClientType.Get_Port: Integer;
begin
  Result := ChildNodes['Port'].NodeValue;
end;

procedure TXMLDialBackClientType.Set_Port(Value: Integer);
begin
  ChildNodes['Port'].NodeValue := Value;
end;

function TXMLDialBackClientType.Get_Timeout: Integer;
begin
  Result := ChildNodes['Timeout'].NodeValue;
end;

procedure TXMLDialBackClientType.Set_Timeout(Value: Integer);
begin
  ChildNodes['Timeout'].NodeValue := Value;
end;

function TXMLDialBackClientType.Get_Company: Integer;
begin
  Result := ChildNodes['Company'].NodeValue;
end;

procedure TXMLDialBackClientType.Set_Company(Value: Integer);
begin
  ChildNodes['Company'].NodeValue := Value;
end;

function TXMLDialBackClientType.Get_Store: Integer;
begin
  Result := ChildNodes['Store'].NodeValue;
end;

procedure TXMLDialBackClientType.Set_Store(Value: Integer);
begin
  ChildNodes['Store'].NodeValue := Value;
end;

end.