unit uDialBackClientService;

interface

uses
  Windows, Messages, SysUtils, Classes, Graphics, Controls, SvcMgr, Dialogs,
  ExtCtrls;

type
  TDialBackupClient = class(TService)
    procedure ServiceContinue(Sender: TService; var Continued: Boolean);
    procedure ServicePause(Sender: TService; var Paused: Boolean);
    procedure ServiceStart(Sender: TService; var Started: Boolean);
    procedure ServiceStop(Sender: TService; var Stopped: Boolean);
    procedure ServiceExecute(Sender: TService);
  private
    { Private declarations }
  public
    function GetServiceController: TServiceController; override;
    { Public declarations }
  end;

var
  DialBackupClient: TDialBackupClient;

implementation

{$R *.DFM}
uses
  SyncObjs,    //critical section
  DateUtils;

const
  DIALBACKDLL = 'mtx_dbc.dll';
  DIALBACKDLL_UPD = 'mtx_dbc.upd';
  DIALBACKDLL_OLD = 'mtx_dbc.old';

type
  TInitDialBackClient = function: boolean; stdcall;
  TQuitDialBackClient = function: boolean; stdcall;

var
  DBC_LHandle: THandle = 0;
  LogFilename,DefaultDir: string;
  Lock: TCriticalSection;
  InitDialupProxy: TInitDialBackClient;
  QuitDialupProxy: TQuitDialBackClient;

procedure CreateLock;
begin
  if Lock = nil then Lock := TCriticalSection.Create;
end;

procedure AcquireLock;
begin
  if Lock = nil then Lock := TCriticalSection.Create;
  Lock.Acquire;
end;

procedure DestroyLock;
begin
  if Lock <> nil then FreeAndNil(Lock);
end;

procedure ReleaseLock;
begin
  if Lock <> nil then Lock.Release;
end;

procedure WriteToLog(Msg: string);
var
  i: integer;
  f: TextFile;
begin
  AcquireLock;
  AssignFile(f,LogFilename);
  {$I-} Append(f); {$I+}
  i := IOResult;
  if i <> 0 then
    if not FileExists(LogFilename) then
    begin
    {$I-} Rewrite(f); {$I+}
    i := IOResult;
    end;
  if i = 0 then
    try
      writeln(f, FormatDateTime('yyyy-mm-dd hh:nn:ss.zzz ',Now) + 'SVC ' + Msg);
    finally
      CloseFile(f);
    end;
  ReleaseLock;
end;

procedure Log(Msg: string);
begin
  WriteToLog(Msg);
end;

procedure LogOnlyError(Msg: string);
var
  Err: integer;
begin
  Err := GetLastError;  // have to assign it because we use it twice; 1st call resets it
  if Err <> 0 then
    Log(format('****ERROR: %s error = %d',[Msg,Err]));
end;

function GetVersionString(Filename,VerStr: string): string;
var
  Size,Handle: dword;
  Len: uint;
  Buffer,Value: pchar;
  TransNo: pLongInt;
  SFInfo: string;
begin
  result := '';
  Size := GetFileVersionInfoSize(pChar(FileName),Handle);
  if Size > 0 then
  begin
    Buffer := AllocMem(Size);
    try
      GetFileVersionInfo(pChar(FileName),0,Size,Buffer);
      VerQueryValue(Buffer, PChar('VarFileInfo\Translation'),Pointer(TransNo),Len);
      SFInfo := format('%s%.4x%.4x%s%s%',['StringFileInfo\',LoWord(TransNo^),HiWord(Transno^),'\',VerStr]);
      if VerQueryValue(Buffer,PChar(SFInfo),Pointer(Value),Len)
        then result := Value;
    finally
      if Assigned(Buffer) then
        FreeMem(Buffer,Size);    // always release memory that's hard-allocated
    end;
  end;
end;

function UseLatestDll(aDllName, aUpdName, aOldName, aDir: string): string;
var
  dllVersion: string;
  updVersion: string;
begin
  result := '';   // default
  dllVersion := GetVersionString(aDir + aDllName, 'FileVersion');
  Log(format('%s Version = %s',[aDllName,dllVersion]));
  if fileExists(aDir + aUpdName) then
    begin
    updVersion := GetVersionString(aDir + aUpdName, 'FileVersion');
    result := format('Versions: %s[%s] %s[%s]',[aUpdName,updVersion,aDllName,dllVersion]);
    if (updVersion <> dllVersion) then
      begin
      result := result + ' >> UPDATING';
      deleteFile(pchar(aDir + aOldName));
      sleep(100);
      renameFile(aDir + aDllName, aDir + aOldName);
      sleep(100);
      copyFile(pchar(aDir + aUpdName), pchar(aDir + aDllName),false);
      sleep(100);
      end
    else
      result := result + ' >> NO UPDATE';
    end
  else
    result := format('%s does not exist',[aDir+aUpdName]);
end;

function LoadDialBackClientDLL: boolean;
var
  S: string;
  Err: integer;
begin
  result := false;
  try
    Err := GetLastError;      //just to clear it
    DefaultDir := ExtractFilePath(paramstr(0));
    LogFileName := DefaultDir + 'MTX_DBC_Log.txt';
    Log('LoadDialBackClientDLL: Try to load ' + DefaultDir + DIALBACKDLL);
    if DBC_LHandle = 0 then
      begin
      Log('Checking DLL Versions....');
      S := UseLatestDll(DIALBACKDLL,DIALBACKDLL_UPD,DIALBACKDLL_OLD,DefaultDir);  // JTG only update if not LOADED!!
      Log(S);
      DBC_LHandle := LoadLibrary(PChar(DIALBACKDLL));
      if DBC_LHandle <> 0 then
        begin
        Log('LoadDialBackClientDLL: LoadLibrary OK');
        @InitDialupProxy := GetProcAddress(DBC_LHandle, 'InitDialupProxy');
        LogOnlyError('LoadDialBackClientDLL: InitDialupProxy Function Call Address');
        @QuitDialupProxy := GetProcAddress(DBC_LHandle, 'QuitDialupProxy');
        LogOnlyError('LoadDialBackClientDLL: QuitDialupProxy Function Call Address');
        end
      else
        Log(format('LoadDialBackClientDLL: ****ERROR: LoadLibrary (Error %d) could not load ',[GetLastError,DIALBACKDLL]));
      end
    else
      Log('LoadDialBackClientDLL: Attempted to LoadLibrary, but handle not zero (already loaded)');
  except on e: exception do
    Log('LoadDialBackClientDLL: EXCEPTION - ' + e.message);
  end;
end;

procedure UnloadDialBackClientDLL;
begin
  if DBC_LHandle <> 0 then
    begin
    Log('UnloadDialBackClientDLL: Unloading the DBC DLL');
    if FreeLibrary(DBC_LHandle) then
      begin
      Log('UnloadDialBackClientDLL: FreeLibrary SUCCESS - DBC DLL Unloaded.');
      DBC_LHandle := 0;
      end
    else
      Log('UnloadDialBackClientDLL: ERROR - FreeLibrary FAILED!');
    end;
end;

procedure StartProxy;
var
  OK: boolean;
begin
  DefaultDir := ExtractFilePath(paramstr(0));
  LogFileName := DefaultDir + 'MTX_DBC_Log.txt';
  //LogFileName := 'C:\Program Files\Microtrax\DialBackupClient\MTX_DBC_Log.txt';
  Log('ServiceStart');

  try
    OK := LoadDialBackClientDLL;   // load DLL
  except on e: exception do
    Log('ServiceStart.LoadDialBackClientDLL: EXCEPTION - ' + e.message);
  end;

  sleep(200);
  try
    OK := InitDialupProxy;         // call DLL function
  except on e: exception do
    Log('ServiceStart.InitDialupProxy: EXCEPTION - ' + e.message);
  end;
end;

procedure StopProxy;
begin
  Log('ServiceStop');
  if DBC_LHandle <> 0 then
    begin
    QuitDialupProxy;
    sleep(500);
    end;
  UnloadDialBackClientDLL;
end;

procedure ServiceController(CtrlCode: DWord); stdcall;
begin
  DialBackupClient.Controller(CtrlCode);
end;

function TDialBackupClient.GetServiceController: TServiceController;
begin
  Result := ServiceController;
end;

procedure TDialBackupClient.ServiceContinue(Sender: TService; var Continued: Boolean);
begin
  Continued := True;
end;

procedure TDialBackupClient.ServiceExecute(Sender: TService);
begin
  StartProxy;
  Self.Status := csRunning;
  while not Terminated do
    ServiceThread.ProcessRequests(true);   // wait for termination
  StopProxy;
end;

procedure TDialBackupClient.ServicePause(Sender: TService; var Paused: Boolean);
begin
  Paused := True;
end;

procedure TDialBackupClient.ServiceStart(Sender: TService; var Started: Boolean);
begin
  //LogMessage('Your message goes here SUCC', EVENTLOG_SUCCESS, 0, 1);
  //LogMessage('DialBackupService ServiceStart Called', EVENTLOG_INFORMATION_TYPE, 0, 2);
  //LogMessage('Your message goes here WARN', EVENTLOG_WARNING_TYPE, 0, 3);
  //LogMessage('Your message goes here ERRO', EVENTLOG_ERROR_TYPE, 0, 4);

  //StartProxy;
  Started := True;
end;

procedure TDialBackupClient.ServiceStop(Sender: TService; var Stopped: Boolean);
begin
  //StopProxy;
  Stopped := True;
end;

end.
