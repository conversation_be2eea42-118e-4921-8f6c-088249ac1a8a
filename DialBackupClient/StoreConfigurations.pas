
{**********************************************************************}
{                                                                      }
{                           XML Data Binding                           }
{                                                                      }
{         Generated on: 5/5/2010 1:32:08 PM                            }
{       Generated from: Z:\826.1\DialupProxy\StoreConfigurations.xml   }
{   Settings stored in: Z:\826.1\DialupProxy\StoreConfigurations.xdb   }
{                                                                      }
{**********************************************************************}

unit StoreConfigurations;

interface

uses xmldom, XMLDoc, XMLIntf;

type

{ Forward Decls }

  IXMLStoreConfigurationsType = interface;
  IXMLProcessingOptionsType = interface;
  IXMLPrintSettingsType = interface;
  IXMLFontStylesType = interface;
  IXMLReceiptTextsType = interface;
  IXMLReceiptTextType = interface;
  IXMLDialBackupConfigurationType = interface;

{ IXMLStoreConfigurationsType }

  IXMLStoreConfigurationsType = interface(IXMLNode)
    ['{FFB9940D-D1D5-4CD2-A5B9-D7DF1921C766}']
    { Property Accessors }
    function Get_Version: WideString;
    function Get_LastModified: WideString;
    function Get_ProcessingOptions: IXMLProcessingOptionsType;
    function Get_PrintSettings: IXMLPrintSettingsType;
    function Get_ReceiptTexts: IXMLReceiptTextsType;
    function Get_Hosts: WideString;
    function Get_DialBackupConfiguration: IXMLDialBackupConfigurationType;
    procedure Set_Version(Value: WideString);
    procedure Set_LastModified(Value: WideString);
    procedure Set_Hosts(Value: WideString);
    { Methods & Properties }
    property Version: WideString read Get_Version write Set_Version;
    property LastModified: WideString read Get_LastModified write Set_LastModified;
    property ProcessingOptions: IXMLProcessingOptionsType read Get_ProcessingOptions;
    property PrintSettings: IXMLPrintSettingsType read Get_PrintSettings;
    property ReceiptTexts: IXMLReceiptTextsType read Get_ReceiptTexts;
    property Hosts: WideString read Get_Hosts write Set_Hosts;
    property DialBackupConfiguration: IXMLDialBackupConfigurationType read Get_DialBackupConfiguration;
  end;

{ IXMLProcessingOptionsType }

  IXMLProcessingOptionsType = interface(IXMLNode)
    ['{0AA20E6E-043D-4485-B564-3CE76B2C8A1B}']
    { Property Accessors }
    function Get_PasswordExpirationDays: Integer;
    function Get_TruncateCardDataAtEOD: WideString;
    function Get_FtpStoreNumber: WideString;
    procedure Set_PasswordExpirationDays(Value: Integer);
    procedure Set_TruncateCardDataAtEOD(Value: WideString);
    procedure Set_FtpStoreNumber(Value: WideString);
    { Methods & Properties }
    property PasswordExpirationDays: Integer read Get_PasswordExpirationDays write Set_PasswordExpirationDays;
    property TruncateCardDataAtEOD: WideString read Get_TruncateCardDataAtEOD write Set_TruncateCardDataAtEOD;
    property FtpStoreNumber: WideString read Get_FtpStoreNumber write Set_FtpStoreNumber;
  end;

{ IXMLPrintSettingsType }

  IXMLPrintSettingsType = interface(IXMLNode)
    ['{9AB8D810-F183-4B05-B19A-14AE9642DBF7}']
    { Property Accessors }
    function Get_FontName: WideString;
    function Get_FontSize: Integer;
    function Get_FontStyles: IXMLFontStylesType;
    function Get_Orientation: WideString;
    function Get_NumberOfCopies: Integer;
    procedure Set_FontName(Value: WideString);
    procedure Set_FontSize(Value: Integer);
    procedure Set_Orientation(Value: WideString);
    procedure Set_NumberOfCopies(Value: Integer);
    { Methods & Properties }
    property FontName: WideString read Get_FontName write Set_FontName;
    property FontSize: Integer read Get_FontSize write Set_FontSize;
    property FontStyles: IXMLFontStylesType read Get_FontStyles;
    property Orientation: WideString read Get_Orientation write Set_Orientation;
    property NumberOfCopies: Integer read Get_NumberOfCopies write Set_NumberOfCopies;
  end;

{ IXMLFontStylesType }

  IXMLFontStylesType = interface(IXMLNode)
    ['{3C204E21-3553-42C6-9C8B-DDC7216B6B20}']
    { Property Accessors }
    function Get_Bold: WideString;
    function Get_Italic: WideString;
    function Get_Underline: WideString;
    function Get_StrikeOut: WideString;
    procedure Set_Bold(Value: WideString);
    procedure Set_Italic(Value: WideString);
    procedure Set_Underline(Value: WideString);
    procedure Set_StrikeOut(Value: WideString);
    { Methods & Properties }
    property Bold: WideString read Get_Bold write Set_Bold;
    property Italic: WideString read Get_Italic write Set_Italic;
    property Underline: WideString read Get_Underline write Set_Underline;
    property StrikeOut: WideString read Get_StrikeOut write Set_StrikeOut;
  end;

{ IXMLReceiptTextsType }

  IXMLReceiptTextsType = interface(IXMLNodeCollection)
    ['{A9671FA0-F2E0-424B-9F8A-AC324E1CC75A}']
    { Property Accessors }
    function Get_ReceiptText(Index: Integer): IXMLReceiptTextType;
    { Methods & Properties }
    function Add: IXMLReceiptTextType;
    function Insert(const Index: Integer): IXMLReceiptTextType;
    property ReceiptText[Index: Integer]: IXMLReceiptTextType read Get_ReceiptText; default;
  end;

{ IXMLReceiptTextType }

  IXMLReceiptTextType = interface(IXMLNode)
    ['{BF5C93D3-E87C-4352-B070-BA3A247558A0}']
    { Property Accessors }
    function Get_StoreNumber: Integer;
    function Get_ReceiptHeaderLine1: WideString;
    function Get_ReceiptHeaderLine2: WideString;
    function Get_ReceiptHeaderLine3: WideString;
    function Get_ReceiptHeaderLine4: WideString;
    function Get_ReceiptFooterLine1: WideString;
    function Get_ReceiptFooterLine2: WideString;
    function Get_CheckDepositLegend: WideString;
    function Get_CheckDepositBankName: WideString;
    function Get_CheckDepositAccountNumber: WideString;
    procedure Set_StoreNumber(Value: Integer);
    procedure Set_ReceiptHeaderLine1(Value: WideString);
    procedure Set_ReceiptHeaderLine2(Value: WideString);
    procedure Set_ReceiptHeaderLine3(Value: WideString);
    procedure Set_ReceiptHeaderLine4(Value: WideString);
    procedure Set_ReceiptFooterLine1(Value: WideString);
    procedure Set_ReceiptFooterLine2(Value: WideString);
    procedure Set_CheckDepositLegend(Value: WideString);
    procedure Set_CheckDepositBankName(Value: WideString);
    procedure Set_CheckDepositAccountNumber(Value: WideString);
    { Methods & Properties }
    property StoreNumber: Integer read Get_StoreNumber write Set_StoreNumber;
    property ReceiptHeaderLine1: WideString read Get_ReceiptHeaderLine1 write Set_ReceiptHeaderLine1;
    property ReceiptHeaderLine2: WideString read Get_ReceiptHeaderLine2 write Set_ReceiptHeaderLine2;
    property ReceiptHeaderLine3: WideString read Get_ReceiptHeaderLine3 write Set_ReceiptHeaderLine3;
    property ReceiptHeaderLine4: WideString read Get_ReceiptHeaderLine4 write Set_ReceiptHeaderLine4;
    property ReceiptFooterLine1: WideString read Get_ReceiptFooterLine1 write Set_ReceiptFooterLine1;
    property ReceiptFooterLine2: WideString read Get_ReceiptFooterLine2 write Set_ReceiptFooterLine2;
    property CheckDepositLegend: WideString read Get_CheckDepositLegend write Set_CheckDepositLegend;
    property CheckDepositBankName: WideString read Get_CheckDepositBankName write Set_CheckDepositBankName;
    property CheckDepositAccountNumber: WideString read Get_CheckDepositAccountNumber write Set_CheckDepositAccountNumber;
  end;

{ IXMLDialBackupConfigurationType }

  IXMLDialBackupConfigurationType = interface(IXMLNode)
    ['{DC9E9FD0-C629-4AF6-8584-287941D62001}']
    { Property Accessors }
    function Get_DBCIPAddress: WideString;
    function Get_DBCPort: Integer;
    function Get_DBCIdleTimeout: Integer;
    function Get_DBCRAS: WideString;
    procedure Set_DBCIPAddress(Value: WideString);
    procedure Set_DBCPort(Value: Integer);
    procedure Set_DBCIdleTimeout(Value: Integer);
    procedure Set_DBCRAS(Value: WideString);
    { Methods & Properties }
    property DBCIPAddress: WideString read Get_DBCIPAddress write Set_DBCIPAddress;
    property DBCPort: Integer read Get_DBCPort write Set_DBCPort;
    property DBCIdleTimeout: Integer read Get_DBCIdleTimeout write Set_DBCIdleTimeout;
    property DBCRAS: WideString read Get_DBCRAS write Set_DBCRAS;
  end;

{ Forward Decls }

  TXMLStoreConfigurationsType = class;
  TXMLProcessingOptionsType = class;
  TXMLPrintSettingsType = class;
  TXMLFontStylesType = class;
  TXMLReceiptTextsType = class;
  TXMLReceiptTextType = class;
  TXMLDialBackupConfigurationType = class;

{ TXMLStoreConfigurationsType }

  TXMLStoreConfigurationsType = class(TXMLNode, IXMLStoreConfigurationsType)
  protected
    { IXMLStoreConfigurationsType }
    function Get_Version: WideString;
    function Get_LastModified: WideString;
    function Get_ProcessingOptions: IXMLProcessingOptionsType;
    function Get_PrintSettings: IXMLPrintSettingsType;
    function Get_ReceiptTexts: IXMLReceiptTextsType;
    function Get_Hosts: WideString;
    function Get_DialBackupConfiguration: IXMLDialBackupConfigurationType;
    procedure Set_Version(Value: WideString);
    procedure Set_LastModified(Value: WideString);
    procedure Set_Hosts(Value: WideString);
  public
    procedure AfterConstruction; override;
  end;

{ TXMLProcessingOptionsType }

  TXMLProcessingOptionsType = class(TXMLNode, IXMLProcessingOptionsType)
  protected
    { IXMLProcessingOptionsType }
    function Get_PasswordExpirationDays: Integer;
    function Get_TruncateCardDataAtEOD: WideString;
    function Get_FtpStoreNumber: WideString;
    procedure Set_PasswordExpirationDays(Value: Integer);
    procedure Set_TruncateCardDataAtEOD(Value: WideString);
    procedure Set_FtpStoreNumber(Value: WideString);
  end;

{ TXMLPrintSettingsType }

  TXMLPrintSettingsType = class(TXMLNode, IXMLPrintSettingsType)
  protected
    { IXMLPrintSettingsType }
    function Get_FontName: WideString;
    function Get_FontSize: Integer;
    function Get_FontStyles: IXMLFontStylesType;
    function Get_Orientation: WideString;
    function Get_NumberOfCopies: Integer;
    procedure Set_FontName(Value: WideString);
    procedure Set_FontSize(Value: Integer);
    procedure Set_Orientation(Value: WideString);
    procedure Set_NumberOfCopies(Value: Integer);
  public
    procedure AfterConstruction; override;
  end;

{ TXMLFontStylesType }

  TXMLFontStylesType = class(TXMLNode, IXMLFontStylesType)
  protected
    { IXMLFontStylesType }
    function Get_Bold: WideString;
    function Get_Italic: WideString;
    function Get_Underline: WideString;
    function Get_StrikeOut: WideString;
    procedure Set_Bold(Value: WideString);
    procedure Set_Italic(Value: WideString);
    procedure Set_Underline(Value: WideString);
    procedure Set_StrikeOut(Value: WideString);
  end;

{ TXMLReceiptTextsType }

  TXMLReceiptTextsType = class(TXMLNodeCollection, IXMLReceiptTextsType)
  protected
    { IXMLReceiptTextsType }
    function Get_ReceiptText(Index: Integer): IXMLReceiptTextType;
    function Add: IXMLReceiptTextType;
    function Insert(const Index: Integer): IXMLReceiptTextType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLReceiptTextType }

  TXMLReceiptTextType = class(TXMLNode, IXMLReceiptTextType)
  protected
    { IXMLReceiptTextType }
    function Get_StoreNumber: Integer;
    function Get_ReceiptHeaderLine1: WideString;
    function Get_ReceiptHeaderLine2: WideString;
    function Get_ReceiptHeaderLine3: WideString;
    function Get_ReceiptHeaderLine4: WideString;
    function Get_ReceiptFooterLine1: WideString;
    function Get_ReceiptFooterLine2: WideString;
    function Get_CheckDepositLegend: WideString;
    function Get_CheckDepositBankName: WideString;
    function Get_CheckDepositAccountNumber: WideString;
    procedure Set_StoreNumber(Value: Integer);
    procedure Set_ReceiptHeaderLine1(Value: WideString);
    procedure Set_ReceiptHeaderLine2(Value: WideString);
    procedure Set_ReceiptHeaderLine3(Value: WideString);
    procedure Set_ReceiptHeaderLine4(Value: WideString);
    procedure Set_ReceiptFooterLine1(Value: WideString);
    procedure Set_ReceiptFooterLine2(Value: WideString);
    procedure Set_CheckDepositLegend(Value: WideString);
    procedure Set_CheckDepositBankName(Value: WideString);
    procedure Set_CheckDepositAccountNumber(Value: WideString);
  end;

{ TXMLDialBackupConfigurationType }

  TXMLDialBackupConfigurationType = class(TXMLNode, IXMLDialBackupConfigurationType)
  protected
    { IXMLDialBackupConfigurationType }
    function Get_DBCIPAddress: WideString;
    function Get_DBCPort: Integer;
    function Get_DBCIdleTimeout: Integer;
    function Get_DBCRAS: WideString;
    procedure Set_DBCIPAddress(Value: WideString);
    procedure Set_DBCPort(Value: Integer);
    procedure Set_DBCIdleTimeout(Value: Integer);
    procedure Set_DBCRAS(Value: WideString);
  end;

{ Global Functions }

function GetStoreConfigurations(Doc: IXMLDocument): IXMLStoreConfigurationsType;
function LoadStoreConfigurations(const FileName: WideString): IXMLStoreConfigurationsType;
function NewStoreConfigurations: IXMLStoreConfigurationsType;

const
  TargetNamespace = '';

implementation

{ Global Functions }

function GetStoreConfigurations(Doc: IXMLDocument): IXMLStoreConfigurationsType;
begin
  Result := Doc.GetDocBinding('StoreConfigurations', TXMLStoreConfigurationsType, TargetNamespace) as IXMLStoreConfigurationsType;
end;

function LoadStoreConfigurations(const FileName: WideString): IXMLStoreConfigurationsType;
begin
  Result := LoadXMLDocument(FileName).GetDocBinding('StoreConfigurations', TXMLStoreConfigurationsType, TargetNamespace) as IXMLStoreConfigurationsType;
end;

function NewStoreConfigurations: IXMLStoreConfigurationsType;
begin
  Result := NewXMLDocument.GetDocBinding('StoreConfigurations', TXMLStoreConfigurationsType, TargetNamespace) as IXMLStoreConfigurationsType;
end;

{ TXMLStoreConfigurationsType }

procedure TXMLStoreConfigurationsType.AfterConstruction;
begin
  RegisterChildNode('ProcessingOptions', TXMLProcessingOptionsType);
  RegisterChildNode('PrintSettings', TXMLPrintSettingsType);
  RegisterChildNode('ReceiptTexts', TXMLReceiptTextsType);
  RegisterChildNode('DialBackupConfiguration', TXMLDialBackupConfigurationType);
  inherited;
end;

function TXMLStoreConfigurationsType.Get_Version: WideString;
begin
  Result := AttributeNodes['Version'].Text;
end;

procedure TXMLStoreConfigurationsType.Set_Version(Value: WideString);
begin
  SetAttribute('Version', Value);
end;

function TXMLStoreConfigurationsType.Get_LastModified: WideString;
begin
  Result := AttributeNodes['LastModified'].Text;
end;

procedure TXMLStoreConfigurationsType.Set_LastModified(Value: WideString);
begin
  SetAttribute('LastModified', Value);
end;

function TXMLStoreConfigurationsType.Get_ProcessingOptions: IXMLProcessingOptionsType;
begin
  Result := ChildNodes['ProcessingOptions'] as IXMLProcessingOptionsType;
end;

function TXMLStoreConfigurationsType.Get_PrintSettings: IXMLPrintSettingsType;
begin
  Result := ChildNodes['PrintSettings'] as IXMLPrintSettingsType;
end;

function TXMLStoreConfigurationsType.Get_ReceiptTexts: IXMLReceiptTextsType;
begin
  Result := ChildNodes['ReceiptTexts'] as IXMLReceiptTextsType;
end;

function TXMLStoreConfigurationsType.Get_Hosts: WideString;
begin
  Result := ChildNodes['Hosts'].Text;
end;

procedure TXMLStoreConfigurationsType.Set_Hosts(Value: WideString);
begin
  ChildNodes['Hosts'].NodeValue := Value;
end;

function TXMLStoreConfigurationsType.Get_DialBackupConfiguration: IXMLDialBackupConfigurationType;
begin
  Result := ChildNodes['DialBackupConfiguration'] as IXMLDialBackupConfigurationType;
end;

{ TXMLProcessingOptionsType }

function TXMLProcessingOptionsType.Get_PasswordExpirationDays: Integer;
begin
  Result := ChildNodes['PasswordExpirationDays'].NodeValue;
end;

procedure TXMLProcessingOptionsType.Set_PasswordExpirationDays(Value: Integer);
begin
  ChildNodes['PasswordExpirationDays'].NodeValue := Value;
end;

function TXMLProcessingOptionsType.Get_TruncateCardDataAtEOD: WideString;
begin
  Result := ChildNodes['TruncateCardDataAtEOD'].Text;
end;

procedure TXMLProcessingOptionsType.Set_TruncateCardDataAtEOD(Value: WideString);
begin
  ChildNodes['TruncateCardDataAtEOD'].NodeValue := Value;
end;

function TXMLProcessingOptionsType.Get_FtpStoreNumber: WideString;
begin
  Result := ChildNodes['FtpStoreNumber'].Text;
end;

procedure TXMLProcessingOptionsType.Set_FtpStoreNumber(Value: WideString);
begin
  ChildNodes['FtpStoreNumber'].NodeValue := Value;
end;

{ TXMLPrintSettingsType }

procedure TXMLPrintSettingsType.AfterConstruction;
begin
  RegisterChildNode('FontStyles', TXMLFontStylesType);
  inherited;
end;

function TXMLPrintSettingsType.Get_FontName: WideString;
begin
  Result := ChildNodes['FontName'].Text;
end;

procedure TXMLPrintSettingsType.Set_FontName(Value: WideString);
begin
  ChildNodes['FontName'].NodeValue := Value;
end;

function TXMLPrintSettingsType.Get_FontSize: Integer;
begin
  Result := ChildNodes['FontSize'].NodeValue;
end;

procedure TXMLPrintSettingsType.Set_FontSize(Value: Integer);
begin
  ChildNodes['FontSize'].NodeValue := Value;
end;

function TXMLPrintSettingsType.Get_FontStyles: IXMLFontStylesType;
begin
  Result := ChildNodes['FontStyles'] as IXMLFontStylesType;
end;

function TXMLPrintSettingsType.Get_Orientation: WideString;
begin
  Result := ChildNodes['Orientation'].Text;
end;

procedure TXMLPrintSettingsType.Set_Orientation(Value: WideString);
begin
  ChildNodes['Orientation'].NodeValue := Value;
end;

function TXMLPrintSettingsType.Get_NumberOfCopies: Integer;
begin
  Result := ChildNodes['NumberOfCopies'].NodeValue;
end;

procedure TXMLPrintSettingsType.Set_NumberOfCopies(Value: Integer);
begin
  ChildNodes['NumberOfCopies'].NodeValue := Value;
end;

{ TXMLFontStylesType }

function TXMLFontStylesType.Get_Bold: WideString;
begin
  Result := AttributeNodes['Bold'].Text;
end;

procedure TXMLFontStylesType.Set_Bold(Value: WideString);
begin
  SetAttribute('Bold', Value);
end;

function TXMLFontStylesType.Get_Italic: WideString;
begin
  Result := AttributeNodes['Italic'].Text;
end;

procedure TXMLFontStylesType.Set_Italic(Value: WideString);
begin
  SetAttribute('Italic', Value);
end;

function TXMLFontStylesType.Get_Underline: WideString;
begin
  Result := AttributeNodes['Underline'].Text;
end;

procedure TXMLFontStylesType.Set_Underline(Value: WideString);
begin
  SetAttribute('Underline', Value);
end;

function TXMLFontStylesType.Get_StrikeOut: WideString;
begin
  Result := AttributeNodes['StrikeOut'].Text;
end;

procedure TXMLFontStylesType.Set_StrikeOut(Value: WideString);
begin
  SetAttribute('StrikeOut', Value);
end;

{ TXMLReceiptTextsType }

procedure TXMLReceiptTextsType.AfterConstruction;
begin
  RegisterChildNode('ReceiptText', TXMLReceiptTextType);
  ItemTag := 'ReceiptText';
  ItemInterface := IXMLReceiptTextType;
  inherited;
end;

function TXMLReceiptTextsType.Get_ReceiptText(Index: Integer): IXMLReceiptTextType;
begin
  Result := List[Index] as IXMLReceiptTextType;
end;

function TXMLReceiptTextsType.Add: IXMLReceiptTextType;
begin
  Result := AddItem(-1) as IXMLReceiptTextType;
end;

function TXMLReceiptTextsType.Insert(const Index: Integer): IXMLReceiptTextType;
begin
  Result := AddItem(Index) as IXMLReceiptTextType;
end;

{ TXMLReceiptTextType }

function TXMLReceiptTextType.Get_StoreNumber: Integer;
begin
  Result := AttributeNodes['StoreNumber'].NodeValue;
end;

procedure TXMLReceiptTextType.Set_StoreNumber(Value: Integer);
begin
  SetAttribute('StoreNumber', Value);
end;

function TXMLReceiptTextType.Get_ReceiptHeaderLine1: WideString;
begin
  Result := ChildNodes['ReceiptHeaderLine1'].Text;
end;

procedure TXMLReceiptTextType.Set_ReceiptHeaderLine1(Value: WideString);
begin
  ChildNodes['ReceiptHeaderLine1'].NodeValue := Value;
end;

function TXMLReceiptTextType.Get_ReceiptHeaderLine2: WideString;
begin
  Result := ChildNodes['ReceiptHeaderLine2'].Text;
end;

procedure TXMLReceiptTextType.Set_ReceiptHeaderLine2(Value: WideString);
begin
  ChildNodes['ReceiptHeaderLine2'].NodeValue := Value;
end;

function TXMLReceiptTextType.Get_ReceiptHeaderLine3: WideString;
begin
  Result := ChildNodes['ReceiptHeaderLine3'].Text;
end;

procedure TXMLReceiptTextType.Set_ReceiptHeaderLine3(Value: WideString);
begin
  ChildNodes['ReceiptHeaderLine3'].NodeValue := Value;
end;

function TXMLReceiptTextType.Get_ReceiptHeaderLine4: WideString;
begin
  Result := ChildNodes['ReceiptHeaderLine4'].Text;
end;

procedure TXMLReceiptTextType.Set_ReceiptHeaderLine4(Value: WideString);
begin
  ChildNodes['ReceiptHeaderLine4'].NodeValue := Value;
end;

function TXMLReceiptTextType.Get_ReceiptFooterLine1: WideString;
begin
  Result := ChildNodes['ReceiptFooterLine1'].Text;
end;

procedure TXMLReceiptTextType.Set_ReceiptFooterLine1(Value: WideString);
begin
  ChildNodes['ReceiptFooterLine1'].NodeValue := Value;
end;

function TXMLReceiptTextType.Get_ReceiptFooterLine2: WideString;
begin
  Result := ChildNodes['ReceiptFooterLine2'].Text;
end;

procedure TXMLReceiptTextType.Set_ReceiptFooterLine2(Value: WideString);
begin
  ChildNodes['ReceiptFooterLine2'].NodeValue := Value;
end;

function TXMLReceiptTextType.Get_CheckDepositLegend: WideString;
begin
  Result := ChildNodes['CheckDepositLegend'].Text;
end;

procedure TXMLReceiptTextType.Set_CheckDepositLegend(Value: WideString);
begin
  ChildNodes['CheckDepositLegend'].NodeValue := Value;
end;

function TXMLReceiptTextType.Get_CheckDepositBankName: WideString;
begin
  Result := ChildNodes['CheckDepositBankName'].Text;
end;

procedure TXMLReceiptTextType.Set_CheckDepositBankName(Value: WideString);
begin
  ChildNodes['CheckDepositBankName'].NodeValue := Value;
end;

function TXMLReceiptTextType.Get_CheckDepositAccountNumber: WideString;
begin
  Result := ChildNodes['CheckDepositAccountNumber'].Text;
end;

procedure TXMLReceiptTextType.Set_CheckDepositAccountNumber(Value: WideString);
begin
  ChildNodes['CheckDepositAccountNumber'].NodeValue := Value;
end;

{ TXMLDialBackupConfigurationType }

function TXMLDialBackupConfigurationType.Get_DBCIPAddress: WideString;
begin
  Result := ChildNodes['DBCIPAddress'].Text;
end;

procedure TXMLDialBackupConfigurationType.Set_DBCIPAddress(Value: WideString);
begin
  ChildNodes['DBCIPAddress'].NodeValue := Value;
end;

function TXMLDialBackupConfigurationType.Get_DBCPort: Integer;
begin
  Result := ChildNodes['DBCPort'].NodeValue;
end;

procedure TXMLDialBackupConfigurationType.Set_DBCPort(Value: Integer);
begin
  ChildNodes['DBCPort'].NodeValue := Value;
end;

function TXMLDialBackupConfigurationType.Get_DBCIdleTimeout: Integer;
begin
  Result := ChildNodes['DBCIdleTimeout'].NodeValue;
end;

procedure TXMLDialBackupConfigurationType.Set_DBCIdleTimeout(Value: Integer);
begin
  ChildNodes['DBCIdleTimeout'].NodeValue := Value;
end;

function TXMLDialBackupConfigurationType.Get_DBCRAS: WideString;
begin
  Result := ChildNodes['DBCRAS'].Text;
end;

procedure TXMLDialBackupConfigurationType.Set_DBCRAS(Value: WideString);
begin
  ChildNodes['DBCRAS'].NodeValue := Value;
end;

end.