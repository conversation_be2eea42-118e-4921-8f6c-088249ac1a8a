unit uProxyTest;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  IdHTTPProxyServer, IdMappedPortTCP, IdBaseComponent,
  IdComponent, IdTCPServer,IdContext, IdHeaderList,
  Dialogs, StdCtrls, IdCustomTCPServer, IdCmdTCPServer, LMDCustomControl,
  ProxyDialer,
  LMDCustomPanel, LMDCustomBevelPanel, LMDBaseEdit, LMDCustomEdit, LMDEdit,
  LMDCustomButton, LMDButton, LMDControl, LMDBaseControl, LMDBaseGraphicControl,
  LMDBaseLabel, LMDCustomLabel, LMDLabel, LMDCustomParentPanel,
  LMDCustomGroupBox, LMDGroupBox, LMDCustomMaskEdit, LMDMaskEdit, ExtCtrls;


type
  TProxyInfo = record
    PeerIP: string;
    IP: string;
    Port: integer;
  end;

  TfrmProxyServer = class(TForm)
    IdHTTPProxyServer1: TIdHTTPProxyServer;
    MemoLog: TMemo;
    IdMappedPort: TIdMappedPortTCP;
    btnProxyActivate: TLMDButton;
    LMDGroupBox1: TLMDGroupBox;
    edtIP: TLMDEdit;
    LMDLabel1: TLMDLabel;
    LMDLabel2: TLMDLabel;
    edtPort: TLMDMaskEdit;
    btnDial: TLMDButton;
    btnPortActive: TLMDButton;
    HangupTimer: TTimer;
    btnHangup: TLMDButton;
    LMDLabel3: TLMDLabel;
    LMDLabel4: TLMDLabel;
    edtTimeout: TLMDMaskEdit;
    procedure btnPortActiveClick(Sender: TObject);
    procedure IdHTTPProxyServer1Connect(AContext: TIdContext);
    procedure IdHTTPProxyServer1BeforeCommandHandler(ASender: TIdCmdTCPServer;
      var AData: string; AContext: TIdContext);
    procedure IdMappedPortOutboundConnect(AContext: TIdContext;
      AException: Exception);
    procedure IdHTTPProxyServer1HTTPDocument(ASender: TIdHTTPProxyServer;
      const ADocument: string; var VStream: TStream;
      const AHeaders: TIdHeaderList);
    procedure btnProxyActivateClick(Sender: TObject);
    procedure btnDialClick(Sender: TObject);
    procedure HangupTimerTimer(Sender: TObject);
    procedure btnHangupClick(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure IdHTTPProxyServer1Execute(AContext: TIdContext);
  private
    { Private declarations }
    RasDialToHost: TRasDialToHost;    // put this inside class later
    //ProxyInfo: TProxyInfo;
    PeerIP: string;
    IP: string;
    Port: integer;
    ManualDial: boolean;

    procedure Hangup;
    procedure Dial;
    procedure Transaction;
    procedure ResetHangupTimer;
  public
    { Public declarations }
  end;

procedure Log(Msg: string);
procedure InitDialupProxy;

var
  frmProxyServer: TfrmProxyServer;

implementation


uses
  SyncObjs,    //critical section
  DateUtils,DialBackClientXML;

{$R *.dfm}

//uses IdIOHandlerSocket,

const
  sOffOn: array[boolean] of string[3] = ('OFF','ON');
  HANGUP_TIME = 300;
  LOG_FILENAME = 'DialupProxyLog.txt';
  SETTINGS_XML = 'DialBackClient.xml';
  DEFAULT_PORT = 443;
  DEFAULT_ADDR = '***********';
  DEFAULT_TIMEOUT = 300;  // seconds
  DEFAULT_COMPANY = 999;
  DEFAULT_STORE = 1;

var
  LogFilename: string;
  Lock: TCriticalSection;
  IP: string;
  Port: integer;
  Timeout,Company,Store: integer;

procedure CreateLock;
begin
  if Lock = nil then Lock := TCriticalSection.Create;
end;

procedure AcquireLock;
begin
  if Lock = nil then Lock := TCriticalSection.Create;
  Lock.Acquire;
end;

procedure DestroyLock;
begin
  if Lock <> nil then FreeAndNil(Lock);
end;

procedure ReleaseLock;
begin
  if Lock <> nil then Lock.Release;
end;

{
procedure TCPServerConnect(AThread: TIdPeerThread);
begin
  PeerIP := AThread.Connection.Socket.Binding.PeerIP;
end;
}

procedure WriteToLog(Msg: string {; const NewJournal: boolean = false});
var
  i: integer;
  log: TextFile;
begin
  AcquireLock;
  //if NewJournal then CutOverToNewJournal;   this code is at end of file, commented out
  AssignFile(log,LogFilename);
  {$I-} Append(log); {$I+}
  i := IOResult;
  if i <> 0 then
    if not FileExists(LogFilename) then
    begin
    {$I-} Rewrite(log); {$I+}
    i := IOResult;
    end;
  if i = 0 then
    try
      writeln(log, FormatDateTime('yyyy-mm-dd hh:nn:ss.zzz ',Now) + Msg);
    finally
      CloseFile(log);
    end;
  ReleaseLock;
  Application.ProcessMessages;
end;

procedure Log(Msg: string);
begin
  //frmProxyServer.MemoLog.Lines.Add(FormatDateTime('yyyy-mm-dd hh:nn:ss.zzz ',Now) + Msg);   <-- use this for standalone EXE test
  WriteToLog(Msg);
end;

function PrintBinary(buffer: PChar; bufferSize: Integer): string;
var
  i: integer;
begin
  Result := '';
  for i := 0 to bufferSize - 1 do
    if ord(buffer[i]) < 32
      then result := format('%s[%2.2x]',[result,Ord(buffer[i])])
      else result := Result + buffer[i];
end;

function ProxyToggle: string;
begin
  if frmProxyServer.IdHTTPProxyServer1.Bindings.Count = 0 then
    frmProxyServer.IdHTTPProxyServer1.Bindings.Add.SetBinding(IP,Port);

  frmProxyServer.IdHTTPProxyServer1.Active := not frmProxyServer.IdHTTPProxyServer1.Active;
  result := 'Proxy is '+sOffOn[frmProxyServer.IdHTTPProxyServer1.Active];
  Log(format('%s on %s:%d',[result,IP,Port]));
end;

procedure InitDialupProxy;
var
  XML: IXMLDialBackClientType;
begin
  LogFilename :=  ExtractFilePath(Application.ExeName) + LOG_FILENAME;
  XML := LoadDialBackClient(ExtractFilePath(Application.ExeName) + SETTINGS_XML);
  IP := XML.Addr;
  if length(IP) = 0
    then IP := DEFAULT_ADDR;
  try Port := XML.Port;       except on e: exception do Port := DEFAULT_PORT; end;
  try Timeout := XML.Timeout; except on e: exception do Timeout := DEFAULT_TIMEOUT; end;
  try Company := XML.Company; except on e: exception do Company := DEFAULT_COMPANY; end;
  try Store := XML.Store;     except on e: exception do Store := DEFAULT_STORE; end;
  Log(format('Company %d, Store %d, Timeout %d seconds',[Company,Store,Timeout]));
  ProxyToggle;
end;

procedure TfrmProxyServer.btnProxyActivateClick(Sender: TObject);
var
  S: string;
begin
  IP := edtIP.Text;
  Port := edtPort.AsInteger;
  S := ProxyToggle;
  btnProxyActivate.Caption := S;
  Log(format('%s on %s:%d',[S,IP,Port]));
  ManualDial := false;
end;

function HangupTimeSeconds(sValue: string): integer;
begin
  result := StrToIntDef(sValue,HANGUP_TIME);
end;

procedure TfrmProxyServer.Hangup;
begin
  ManualDial := false;
  try
    if Assigned(RasDialToHost) then
      begin
      RasDialToHost.RasHangup(ManualDial);
      Log('Hangup: Releasing RasDialToHost now');
      FreeAndNil(RasDialToHost);
      end
    else
      Log('Hangup: Attempted to hangup but RAS Dialer already freed');
  except on e: exception do
    Log('Hangup: EXCEPTION - '+e.Message);
  end;
end;

procedure TfrmProxyServer.ResetHangupTimer;
begin
  HangupTimer.Enabled := false;
  HangupTimer.Interval := HangupTimeSeconds(edtTimeout.Text)*1000;
  HangupTimer.Enabled := true;
  Log('HangupTimer reset');
end;

procedure TfrmProxyServer.Dial;
begin
  try
    Log('Dial...');
    if not Assigned(RasDialToHost) then
      begin
      Log('Dial: Creating RasDialToHost...');
      RasDialToHost := TRasDialToHost.Create;
      sleep(100);
      end
    else
      Log('Dial: using existing RasDialToHost...');

    ResetHangupTimer;
    if Assigned(RasDialToHost) then
      begin
      if RasDialToHost.IsDialing then
        Log('Dial: we are currently dialing (trying to connect to RAS) Will not dial again now')
      else if RasDialToHost.IsRASConnected then
        Log('Dial: RasDialToHost Already Connected. Will not dial.')
      else
        begin
        RasDialToHost.RasDial;
        sleep(100);
        end;
      end
    else
      Log('Dial: Error creating RasDialToHost. Will not dial.');
  except on e: exception do
    Log('Dial Error: '+e.Message);
  end;
end;

procedure TfrmProxyServer.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  MemoLog.Lines.SaveToFile('ProxyTestLog.txt');
end;

procedure TfrmProxyServer.FormShow(Sender: TObject);
begin
  edtTimeout.Text := IntToStr(HANGUP_TIME);
  ManualDial := false;
end;

procedure TfrmProxyServer.Transaction;
begin
  try
    Log('Incoming connection/transaction...');
    if Assigned(RasDialToHost) then
      if RasDialToHost.IsRASConnected then
        begin
        Log('Incoming connection/transaction, but we are already RAS connected, so use existing connection and resetting Hangup timer');
        ResetHangupTimer;
        end
      else
        Dial
    else
      Dial;
  except on e: exception do
    Log('Transaction Error: '+e.Message);
  end;
end;

procedure TfrmProxyServer.HangupTimerTimer(Sender: TObject);
const
  MS_PER_MINUTE = 60000;
  MS_PER_SECOND = 1000;
begin
  HangupTimer.Enabled := false;
  if HangupTimer.Interval >= 120000
    then Log(format('Connection has been open for %d minutes without a connection/transaction',[HangupTimer.Interval div MS_PER_MINUTE]))
  else if HangupTimer.Interval >= 60000
    then Log(format('Connection has been open for %d minute without a connection/transaction',[HangupTimer.Interval div MS_PER_MINUTE]))
  else
    Log(format('Connection has been open for %d seconds without a connection/transaction',[HangupTimer.Interval div MS_PER_SECOND]));
  Hangup;
  sleep(500);
  Log('Hangup re-try...');
  Hangup;
end;

procedure TfrmProxyServer.btnDialClick(Sender: TObject);
begin
  ManualDial := true;
  Dial;
end;

procedure TfrmProxyServer.btnHangupClick(Sender: TObject);
begin
  Hangup;
end;

procedure TfrmProxyServer.btnPortActiveClick(Sender: TObject);
begin
  IdMappedPort.Active := not IdMappedPort.Active;
  btnPortActive.Caption := 'Port is '+sOffOn[IdMappedPort.Active];
end;

procedure TfrmProxyServer.IdHTTPProxyServer1BeforeCommandHandler(
  ASender: TIdCmdTCPServer; var AData: string; AContext: TIdContext);
begin
  Log(PeerIP + ' ' + AData);
end;

procedure TfrmProxyServer.IdHTTPProxyServer1Connect(AContext: TIdContext);
begin
  PeerIP := AContext.Connection.Socket.Binding.PeerIP;
  //Log('DATA = ' + AContext.Connection.Socket.AllData);
  if not ManualDial
    then Transaction;
end;

procedure TfrmProxyServer.IdHTTPProxyServer1Execute(AContext: TIdContext);
//var
//  lCmd: string;
begin
 {   // shows the usage of this event handler...
  lCmd := Trim(AContext.Connection.IOHandler.ReadLn);
  if AnsiSameText(lCmd, 'HELP') then
    begin
    AContext.Connection.IOHandler.WriteLn('HELP');
    AContext.Connection.IOHandler.WriteLn('QUIT');
    AContext.Connection.IOHandler.WriteLn('GETTIMESTAMP');
    AContext.Connection.IOHandler.WriteLn('');
    end
  else if AnsiSameText(lCmd, 'QUIT') then
    begin
    AContext.Connection.IOHandler.WriteLn('Goodbye...');
    AContext.Connection.IOHandler.WriteLn('');
    AContext.Connection.Disconnect;
    end
  else if AnsiSameText(lCmd, 'GETTIMESTAMP') then
    begin
    AContext.Connection.IOHandler.WriteLn(FormatDateTime(Now,'yyyy-mm-ddThh:nn:ss.zzz'));
    AContext.Connection.IOHandler.WriteLn('');
    end;
  }
  ManualDial := false;  // reset it on incoming transaction?
end;

procedure TfrmProxyServer.IdHTTPProxyServer1HTTPDocument(ASender: TIdHTTPProxyServer;
  const ADocument: string; var VStream: TStream; const AHeaders: TIdHeaderList);
begin
  //Log(ADocument);
end;

procedure TfrmProxyServer.IdMappedPortOutboundConnect(AContext: TIdContext;
  AException: Exception);
begin
  PeerIP := AContext.Connection.Socket.Binding.PeerIP;
end;

end.

{
  procedure ZipUp(S: string);
  var
    ZipCmd: string;
  begin
    arcName := S + dateStamp + '.zip';
    newRsLogName := S + dateStamp + '.txt';
    RenameFile(sExePath+ S + '.txt',sExePath+newRsLogName);
    zipCmd := 'zip.exe -j "' + sExePath + 'Archive\' + arcName + '" "' + sExePath + newRsLogName + '" "' + '"';
    EasyCreateProcessEx(zipCmd, tmpHandle, true, INFINITE); // YHJ-750
    SysUtils.DeleteFile(sExePath + newRsLogName);
  end;

  function DateTimeFromName(Filename: string): TDateTime;
  var
    YYYY,MM,DD,HH,NN,SS: word;
    i: integer;
    S: string;
  begin
    result := Now;
    for i := 1 to length(Filename) do
      if Filename[i] in ['0'..'9'] then
        begin
        S := copy(Filename,i,15);
        YYYY := StrToIntDef(copy(S,1,4),0);
        MM   := StrToIntDef(copy(S,5,2),0);
        DD   := StrToIntDef(copy(S,7,2),0);
        HH   := StrToIntDef(copy(S,10,2),0);
        NN   := StrToIntDef(copy(S,12,2),0);
        SS   := StrToIntDef(copy(S,14,2),0);
        LocalLog(format('Using %s from filename %s for the DateTime information (Year[%d] Mon[%d] Day[%d] %d:%d:%d',[S,Filename,YYYY,MM,DD,HH,NN,SS]));
        if not TryEncodeDateTime(YYYY,MM,DD,HH,NN,SS,0,result)
          then result := Now;
        break;
        end;
  end;

  procedure CutOverToNewJournal;
  var
    Primary: boolean;
    Filename: string;
    Age: integer;
  begin
    dirName := sExePath + 'Archive';
    if not DirectoryExists(dirName)
      then CreateDir(dirName);
    DateStamp := FormatDateTime('yyyymmdd-hhnnss',Now);
    if FindFirst(dirName + '\DialUpProxy*.zip', faAnyFile, sr) = 0 then   //delete old files
      try
        repeat
          Age := DaysBetween(Now,DateTimeFromName(sr.Name));
          Filename := dirName + '\' + sr.Name;
          if Age > Config.KeepArchives then
            DeleteFile(PChar(dirName + '\' + sr.Name));
        until FindNext(sr) <> 0;
      finally
        SysUtils.FindClose(sr);
      end;

    if Config.KeepArchives > 0 then
      then ZipUp('DialupProxy-')
      else SysUtils.DeleteFile(LogFilename);
  end;
}

//var
  //dirName, arcName, dateStamp: string;
  //Year, Month, Day: Word;
  //sr: TSearchRec;
  //tmpHandle: THandle;

