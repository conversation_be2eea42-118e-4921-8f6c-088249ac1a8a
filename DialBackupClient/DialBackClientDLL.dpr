library DialBackClientDLL;

{ Important note about DLL memory management: Share<PERSON><PERSON> must be the
  first unit in your library's USES clause AND your project's (select
  Project-View Source) USES clause if your DLL exports any procedures or
  functions that pass strings as parameters or function results. This
  applies to all strings passed to and from your DLL--even those that
  are nested in records and classes. ShareMem is the interface unit to
  the BORLNDMM.DLL shared memory manager, which must be deployed along
  with your DLL. To avoid using BORLNDMM.DLL, pass string information
  using PChar or ShortString parameters. }

uses
  SysUtils,
  Classes,
  FastShareMem,
  Types,
  SyncObjs,
  Rio,
  SOAPHTTPClient,
  DateUtils,
  XSBuiltIns,
  ActiveX,
  InvokeRegistry,
  OpConvert,
  uDialBackClient in 'uDialBackClient.pas',
  DialBackClient in 'DialBackClient.pas',
  StoreConfigurations in 'StoreConfigurations.pas';

{$R *.res}

procedure DLLEntryProc(EntryCode: integer);
begin
  {$IFDEF LOGGING}
  case EntryCode of
   DLL_PROCESS_ATTACH: Log('Process Attaching');
   DLL_PROCESS_DETACH: Log('Process Detaching');
   DLL_THREAD_ATTACH:  Log('Thread Attaching');
   DLL_THREAD_DETACH:  Log('Thread Detaching');
  end;
  {$ENDIF LOGGING}
end;

function InitDialupProxy: boolean; stdcall;
begin
  result := InitDialBackClient;
end;

function QuitDialupProxy: boolean; stdcall;
begin
  result := QuitDialBackClient;
end;

exports
  InitDialupProxy,
  QuitDialupProxy;

begin
  DLLProc := @DLLEntryProc;
  // DLLEntryProc(DLL_PROCESS_ATTACH);
  // DisableThreadLibraryCalls(hInstance);
  IsMultiThread := true;
end.
