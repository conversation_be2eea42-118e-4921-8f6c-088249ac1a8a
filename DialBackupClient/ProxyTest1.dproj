﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectGuid>{2f80312c-8e5d-4842-b2f4-4bc07e4e6072}</ProjectGuid>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <DCC_DCCCompiler>DCC32</DCC_DCCCompiler>
    <DCC_DependencyCheckOutputName>R:\ProxyTest1.exe</DCC_DependencyCheckOutputName>
    <MainSource>ProxyTest1.dpr</MainSource>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Version>7.0</Version>
    <DCC_DebugInformation>False</DCC_DebugInformation>
    <DCC_LocalDebugSymbols>False</DCC_LocalDebugSymbols>
    <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
    <DCC_Define>RELEASE</DCC_Define>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <Version>7.0</Version>
    <DCC_Define>DEBUG</DCC_Define>
    <DCC_UnitSearchPath>Z:\826.1\Common</DCC_UnitSearchPath>
    <DCC_ResourcePath>Z:\826.1\Common</DCC_ResourcePath>
    <DCC_ObjPath>Z:\826.1\Common</DCC_ObjPath>
    <DCC_IncludePath>Z:\826.1\Common</DCC_IncludePath>
    <DCC_ExeOutput>R:\</DCC_ExeOutput>
  </PropertyGroup>
  <ProjectExtensions>
    <Borland.Personality>Delphi.Personality</Borland.Personality>
    <Borland.ProjectType />
    <BorlandProject>
<BorlandProject><Delphi.Personality><Parameters><Parameters Name="UseLauncher">False</Parameters><Parameters Name="LoadAllSymbols">True</Parameters><Parameters Name="LoadUnspecifiedSymbols">False</Parameters></Parameters><VersionInfo><VersionInfo Name="IncludeVerInfo">True</VersionInfo><VersionInfo Name="AutoIncBuild">True</VersionInfo><VersionInfo Name="MajorVer">826</VersionInfo><VersionInfo Name="MinorVer">1</VersionInfo><VersionInfo Name="Release">0</VersionInfo><VersionInfo Name="Build">30</VersionInfo><VersionInfo Name="Debug">False</VersionInfo><VersionInfo Name="PreRelease">False</VersionInfo><VersionInfo Name="Special">False</VersionInfo><VersionInfo Name="Private">False</VersionInfo><VersionInfo Name="DLL">False</VersionInfo><VersionInfo Name="Locale">1033</VersionInfo><VersionInfo Name="CodePage">1252</VersionInfo></VersionInfo><VersionInfoKeys><VersionInfoKeys Name="CompanyName"></VersionInfoKeys><VersionInfoKeys Name="FileDescription"></VersionInfoKeys><VersionInfoKeys Name="FileVersion">826.1.0.30</VersionInfoKeys><VersionInfoKeys Name="InternalName"></VersionInfoKeys><VersionInfoKeys Name="LegalCopyright"></VersionInfoKeys><VersionInfoKeys Name="LegalTrademarks"></VersionInfoKeys><VersionInfoKeys Name="OriginalFilename"></VersionInfoKeys><VersionInfoKeys Name="ProductName"></VersionInfoKeys><VersionInfoKeys Name="ProductVersion">*******</VersionInfoKeys><VersionInfoKeys Name="Comments"></VersionInfoKeys></VersionInfoKeys><Source><Source Name="MainSource">ProxyTest1.dpr</Source></Source><Excluded_Packages>
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      <Excluded_Packages Name="$(BDS)\bin\bcboffice2k100.bpl">CodeGear C++Builder Office 2000 Servers Package</Excluded_Packages>
      <Excluded_Packages Name="$(BDS)\bin\bcbofficexp100.bpl">CodeGear C++Builder Office XP Servers Package</Excluded_Packages>
      <Excluded_Packages Name="$(BDS)\bin\dcloffice2k100.bpl">Microsoft Office 2000 Sample Automation Server Wrapper Components</Excluded_Packages>
      <Excluded_Packages Name="$(BDS)\bin\dclofficexp100.bpl">Microsoft Office XP Sample Automation Server Wrapper Components</Excluded_Packages>
      <Excluded_Packages Name="C:\DOCUMENTS AND SETTINGS\ALL USERS\DOCUMENTS\RAD STUDIO\5.0\bpl\dcllmdrtl9_110.bpl">LMD 2009 - Common Designtime Enhancements</Excluded_Packages>
      <Excluded_Packages Name="C:\DOCUMENTS AND SETTINGS\ALL USERS\DOCUMENTS\RAD STUDIO\5.0\bpl\dcllmdplugin1_110.bpl">LMD 2009 - Common PlugIn Components</Excluded_Packages>
      <Excluded_Packages Name="c:\documents and settings\all users\documents\rad studio\5.0\bpl\dcllmdrtlx9_110.bpl">LMD 2009 - Common Shared Components</Excluded_Packages>
      <Excluded_Packages Name="c:\documents and settings\all users\documents\rad studio\5.0\bpl\dcllmdcore9_110.bpl">LMD 2009 - LMD-Tools Core Components</Excluded_Packages>
    </Excluded_Packages></Delphi.Personality></BorlandProject></BorlandProject>
  </ProjectExtensions>
  <Import Project="$(MSBuildBinPath)\Borland.Delphi.Targets" />
  <ItemGroup>
    <DelphiCompile Include="ProxyTest1.dpr">
      <MainSource>MainSource</MainSource>
    </DelphiCompile>
    <DCCReference Include="..\Common\proxyDialer.pas" />
    <DCCReference Include="uDialBackClient.pas" />
    <DCCReference Include="uProxyTest.pas">
      <Form>frmProxyServer</Form>
    </DCCReference>
  </ItemGroup>
</Project>