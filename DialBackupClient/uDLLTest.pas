unit uDLLTest;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, LMDCustomButton, LMDButton;

type
  TfrmDLLTest = class(TForm)
    btnStartDLL: TLMDButton;
    btnQuitDLL: TLMDButton;
    btnTest: TLMDButton;
    procedure btnStartDLLClick(Sender: TObject);
    procedure btnQuitDLLClick(Sender: TObject);
    procedure btnTestClick(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  frmDLLTest: TfrmDLLTest;

implementation

{$R *.dfm}

uses
  ServerEPSConstants,
  uDialBackClient,
  SyncObjs,    //critical section
  DateUtils;

const
  DIALBACKDLL = 'MTX_DBC.dll';
  DIALBACKDLL_UPD = 'MTX_DBC.upd';
  DIALBACKDLL_OLD = 'MTX_DBC.old';

type
  TInitDialupProxy = function: boolean; stdcall;
  TQuitDialupProxy = function: boolean; stdcall;

var
  DBC_LHandle: THandle = 0;
  LogFilename,DefaultDir: string;
  Lock: TCriticalSection;
  InitDialupProxy: TInitDialupProxy;
  QuitDialupProxy: TQuitDialupProxy;

procedure CreateLock;
begin
  if Lock = nil then Lock := TCriticalSection.Create;
end;

procedure AcquireLock;
begin
  if Lock = nil then Lock := TCriticalSection.Create;
  Lock.Acquire;
end;

procedure DestroyLock;
begin
  if Lock <> nil then FreeAndNil(Lock);
end;

procedure ReleaseLock;
begin
  if Lock <> nil then Lock.Release;
end;

procedure WriteToLog(Msg: string {; const NewJournal: boolean = false});
var
  i: integer;
  log: TextFile;
begin
  AcquireLock;
  AssignFile(log,LogFilename);
  {$I-} Append(log); {$I+}
  i := IOResult;
  if i <> 0 then
    if not FileExists(LogFilename) then
    begin
    {$I-} Rewrite(log); {$I+}
    i := IOResult;
    end;
  if i = 0 then
    try
      writeln(log, FormatDateTime('yyyy-mm-dd hh:nn:ss.zzz ',Now) + 'EXE ' + Msg);
    finally
      CloseFile(log);
    end;
  ReleaseLock;
end;

procedure Log(Msg: string);
begin
  WriteToLog(Msg);
end;

function LogOnlyError(Msg: string): boolean;
var
  Err: integer;
begin
  Err := GetLastError;  // have to assign it because we use it twice; 1st call resets it
  result := Err = 0;
  if not result then
    Log(format('%s GetLastError = %d',[Msg,Err]));
end;

function GetVersionString(Filename,VerStr: string): string;
var
  Size,Handle: dword;
  Len: uint;
  Buffer,Value: pchar;
  TransNo: pLongInt;
  SFInfo: string;
begin
  result := '';
  Size := GetFileVersionInfoSize(pChar(FileName),Handle);
  if Size > 0 then
  begin
    Buffer := AllocMem(Size);
    try
      GetFileVersionInfo(pChar(FileName),0,Size,Buffer);
      VerQueryValue(Buffer, PChar('VarFileInfo\Translation'),Pointer(TransNo),Len);
      SFInfo := format('%s%.4x%.4x%s%s%',['StringFileInfo\',LoWord(TransNo^),HiWord(Transno^),'\',VerStr]);
      if VerQueryValue(Buffer,PChar(SFInfo),Pointer(Value),Len)
        then result := Value;
    finally
      if Assigned(Buffer) then
        FreeMem(Buffer,Size);    // always release memory that's hard-allocated
    end;
  end;
end;

function UseLatestDll(aDllName, aUpdName, aOldName, aDir: string): string;
var
  dllVersion: string;
  updVersion: string;
begin
  result := '';   // default
  dllVersion := GetVersionString(aDir + aDllName, 'FileVersion');
  Log(format('%s Version = %s',[aDllName,dllVersion]));
  if fileExists(aDir + aUpdName) then
    begin
    updVersion := GetVersionString(aDir + aUpdName, 'FileVersion');
    result := format('Versions: %s[%s] %s[%s]',[aUpdName,updVersion,aDllName,dllVersion]);
    if (updVersion <> dllVersion) then
      begin
      result := result + ' >> UPDATING';
      deleteFile(pchar(aDir + aOldName));
      sleep(100);
      renameFile(aDir + aDllName, aDir + aOldName);
      sleep(100);
      copyFile(pchar(aDir + aUpdName), pchar(aDir + aDllName),false);
      sleep(100);
      end
    else
      result := result + ' >> NO UPDATE';
    end
  else
    result := format('%s does not exist',[aDir+aUpdName]);
end;

function LoadDialBackClientDLL: boolean;
var
  S: string;
  Err: integer;
begin
  result := false;
  try
    Err := GetLastError;      //just to clear it
    LogFileName := 'MTX_DBC_Log.txt';
    DefaultDir := ExtractFilePath(paramstr(0));
    Log('LoadDialBackClientDLL: Try to load ' + DefaultDir + DIALBACKDLL);
    if DBC_LHandle = 0 then
      begin
      Log('Checking DLL Versions....');
      S := UseLatestDll(DIALBACKDLL,DIALBACKDLL_UPD,DIALBACKDLL_OLD,DefaultDir);  // JTG only update if not LOADED!!
      Log(S);
      DBC_LHandle := LoadLibrary(PChar(DIALBACKDLL));
      if DBC_LHandle <> 0 then
        begin
        Log('LoadDialBackClientDLL: LoadLibrary OK');
        @InitDialupProxy := GetProcAddress(DBC_LHandle, 'InitDialupProxy');
        result := LogOnlyError('LoadDialBackClientDLL: InitDialupProxy Function Call Address');
        @QuitDialupProxy := GetProcAddress(DBC_LHandle, 'QuitDialupProxy');
        LogOnlyError('LoadDialBackClientDLL: QuitDialupProxy Function Call Address');
        end
      else
        Log(format('LoadDialBackClientDLL: ****ERROR: LoadLibrary (Error %d) could not load ',[GetLastError,DIALBACKDLL]));
      end
    else
      Log('LoadDialBackClientDLL: Attempted to LoadLibrary, but handle not zero (already loaded)');
  except
    on e: exception do
      Log('LoadDialBackClientDLL: EXCEPTION - ' + e.message);
  end;
end;

procedure UnloadDialBackClientDLL;
begin
  if DBC_LHandle <> 0 then
    begin
    Log('UnloadDialBackClientDLL: Unloading the DBC DLL');
    if FreeLibrary(DBC_LHandle) then
      begin
      Log('UnloadDialBackClientDLL: FreeLibrary SUCCESS - DBC DLL Unloaded.');
      DBC_LHandle := 0;
      end
    else
      Log('UnloadDialBackClientDLL: ERROR - FreeLibrary FAILED!');
    end;
end;

procedure TfrmDLLTest.btnStartDLLClick(Sender: TObject);
var
  OK: boolean;
begin
  try
    OK := LoadDialBackClientDLL;   // load DLL
  except on e: exception do
    Log('StartDLL.LoadDialBackClientDLL: EXCEPTION - ' + e.message);
  end;

  try
    OK := InitDialupProxy;         // call DLL function
  except on e: exception do
    Log('StartDLL.InitDialupProxy: EXCEPTION - ' + e.message);
  end;
end;

function XMLData: string;
begin
  result :=
'<ConfigurationFileResponse ErrorCode="0" FileType="A03" FileName="StoreConfigurations.xml" FileSize="3344"><FileData>H4sIAAAAAAAEAO29B2AcSZYlJi9tynt/SvVK1+B0oQiAYBMk2JBAEOzBiM3mkuwdaUcjKasqgcplVmVdZhZAzO2dvPfee++999577733ujudTif33/8/XGZkAW'+
'z2zkrayZ4hgKrIHz9+fB8/Ih6/bqs6P6mW58XFus7aolo26U/mdUO/fPbR3njno/R51rRfVLPivMhn9NHO7s72zv3t3b10d//RvYNH9w8+OvqNkx97/LKupnnTFMuLL1cMBp/Sx1nTXFX17PTdqh'+
'D4T7Pr5ujhzuO7A1/xa2/q9XKatflJVs+eZm123J5++TS9K18+a1eM9ov1YpLX8imBiyFAaBXL9nXetvS5wn5WLdsX2SI/OqnWdUEAXuRXj+/aT22b18UP8qOdh/IV/+G+aq/LvEmfVCWR5MVH6V'+
'mblcWUf/1qOcvrsljm/Nfrti7e5l+uW/5L8f+Sel22POKjl4/v+n/y9zKuL89PqlWRN0c7RKvORzrgztAePy2y8kk2fbteBTMqUJ8+OTl7eTyb1USko92d8f3xg53x7u6Dx3eDb0zbl1XdHu3v3+'+
'Ov+Q8LZVbmb4pFXq3bo0+BXOcz0+7V8Wsi8WqV1+Nl3nIzfMS4b8D08at8mher9k3+rlV0vE9Sb+Y/+2iHec+1+HaeEfWfE/V3j9LweVKUpbzc+eLx3cjLA1D3ulAP7qfH9UW1zNbtjVD3hqDe86'+
'Ael0VTpT9Z5D9djdKT4/Th3qf3P90E9d4Q1P0Org/3H25/uru/vfvp7r3wmwjU/RDqs6pq43R98+3jF79X+vt8+VX67MtXcaj+ywNQQ7q+/vaXL1+evfg8/e7Zm2+nX73+XTdDNXQ9mefTt0/zVd'+
'UU7fP8Il/OjvSvVP6klyNt+i8/yZZvWRXglxS/mb6jrfoAjqfTak3qhLn0SP9KVV2FUMKmzO1mhGD3G/h/d2f3thLwxZvf+/Tl6/RsOSWNPgm++pEE/EgC/l8tAcEHYuq+XTXGOuDX9GWdnxfvPv'+
'vo1enJ7//m5GVKpnd9bj5xMtLW1yfojXrauQew3gfS5svzc1hv502kXzX5l+08r9EPzPjRC7LZ3Vb6No3iy3O4MW+q3yvPV9rsaHefTXjsK3nvWVlVs9dtVrewomTyYVc7H3otT5ezbjvzkddKu3'+
'hW1VfkRr3Op9Vy1jzJ26s8X+qH5F3o+7dqLMBfF4t1Sc7Z7GleZtdHOzS4zkfS7rtV/ZZI83vl14yo/zy+630prYnMp09Onp6dqJv0Y4+/yOvpPGPWOHr48KEvIMRD/rfS3jiUFsDJ/O3xup2/uV'+
'7l/mcn1Sz4+2l+nq1LcurgcPrfkSNcnF8/zSdFK/z15ZLnjBhg6KuhF3W2o28GnPD89wafvS4rlYjGYtPzeemzN3m9KJZZefaUXLpdpYz3obQCxC+yZXaxIEfzbHle9UgckAjNz4y37U0D1ED3Q3'+
'Ube5+fFO1170OmcO/TnypWU5/qFgTksnZQTtZ1nS+n15iio4N94qHgEyuARN9pfy71CwUafPX5F29oDpq8daQmtXiSNXOKDt5UrF8swetsmn/RXJAUP68ujn4fonb4iTR7lTfryaJoQ6lqMP9DX4'+
'UTGkyIx+qxye98THHBIquvgTb89tjn1t23X4oz3nvHfdx/BVqMFfm3i4ZY002UdvT5ibzXAdb7WDielBeCB9INu/cf3w0+s5RfNtkUYcJxWVZX+ezoRfDwVHSbyLsinApu/37/P2ikoJG+F8yRqr'+
'udnU93rAUIvoq9NKR2ZZy3aqvj//LVyfW0zA0Wu3uERfhhrP/gHQ7XBr93vE9uUNnOv6DZzi48veB/egabeZmVZEcBNP6dYp69zb97dsK80jyvpllZOmYJ9N0xGzGif+drmRo2hWQztLHjt3m15E'+
'zBF9kFAm5Pcl7iDQo1z148ra6WZZU54/DyyRvWiypbnsJ79uYlGaP624HCow/7IkAf9owOffasKPPgZX0Pvpp72TkPX/jaCPR/fUWDmD19HuhqFSsmYERI+5/zJ2cvz6vaaAoKtpf51LXAoHSuvP'+
'7btsxhKV6Rg+aBI4aZ5m9y5DeUhexXxiOlcbMMnpPeJAV29No5q92vghdZ78jwXCaizqe740X7Ll8142m1cD56rPEQOMwQkhef7t3fiUEw3/feF6IG2OwNYdNtOwBsEJfw697bYEaPPf1BdJnP+6'+
'7HhP534Nt27iyJptvIF29fOI4DK77Krr44O3nllACkdd2sYCFJXe+S6AefuFbPKG9Xos19aWP+di1gChe5B8Z+4Np8uXzz5Tt6AgR+/5Nqsfj9X7z6qbPw4y+yYvn7v4ZQVcvwG1KU8S/AytFvAl'+
'HiWSpaJ7xC/dr3cpgObbUKPjh79RPB319csAOGnGbwOeH33HeO9DPNqXU/flG92Qs+40CG9eDuwOdhe2icxdmyCCHjA5DQ8cWXr/DZ67w8D1hJ5T/6HQBJUg+euZlZ94lHLDZ3r6d1V9H+/qSvwb'+
'7P6mqBhgHsp8VlEZ2scISYrfATma7wM8xX+AlNWOc1An2vBzr8RECHnwF0+AmB7jTJKYivKSZ8XlzMWziTkU+9iQ+k9Oj3+X3I6eF/XsiLne899Cg0Iyuw8JQIz0mZE97Li8AZZu6ATaQ56H94Rg'+
'mZYEgvrdkJh4ZVA8xiu3raZWuyexd1tjhzWguf0rQ/LZwTi49OX52Q6jNk0b+USer8F72eV6tX+S9SXuSAyr5OLV+/fm7/VLv3Mrs+XWaTEq4jRQ+9D7uNJT5zOLGPg9Bi3dAgJpyy7tlPv9WLqv'+
'3yba8FvfvO9Bl+2PUvaBj3np6+BrbmV/mCdfklzd3ZsmmLdg1EPBdEXDvOWojXBQiRT4Pm1kXzGrvPpKnxllildOKOF+S2UlD/ivQVIfaTWblGgoISH5HP5Y1N3q8Jk5q42zrUGICFX+RXVh8mOf'+
'Tk93mO5JDLDdEH33Bu6Pf5UW7oh50bciKk3tDR7t69/fufuhVGI9guHXT0EElI96dtwKmEN6f8rZdWiGSJjk6OuVXvc3nj/0u5o1DP//8rUfTw4OBWiaKjnU9ZDsMPg7Ybc0dHzLnuT6X7z1Yi6f'+
'd5n0TS0YP7Livo8fsXuZ9Xwgge3r8fZFZNSr6bbEL+wYEMGv78ST/B9/p96Hnx+7xH9mlvZ28HGSf8lN+Rx/lR7smKy/+Pc09Q4ZFPtfnPUT4KSAV/Gyn6/3p2StLDv48IZ+RreW8gP2Xhbkg6bW'+
'ijuZxYi06yaLjJBhg/ygj9KCMUfvz/oYzQ3o8yQh7obzYjZJ7/XyeENN31NRJCmkjBb/LxjxJDPz8TQ/IbPqX8BGJj0UbrmqeuOfp/ALT799MYLQAA</FileData></ConfigurationFileResp'+
'onse>';
end;

procedure TfrmDLLTest.btnTestClick(Sender: TObject);
var
  Files: NeededFileTypeArray;
  i,XMLError: integer;
begin
  i := uDialBackClient.XML2ConfigurationFile(XMLData,Files,XMLError);
end;

procedure TfrmDLLTest.btnQuitDLLClick(Sender: TObject);
begin
  Log('QuitDLL: Calling QuitDialupProxy...');
  if DBC_LHandle <> 0 then
    begin
    QuitDialupProxy;
    sleep(500);
    UnloadDialBackClientDLL;
    end
  else
    Log('QuitDLL: ' + DIALBACKDLL + ' already unloaded');
  Log('QuitDLL: Exiting program');
  Close;
end;

end.
