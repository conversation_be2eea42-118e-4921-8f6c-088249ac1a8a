unit proxyDialer;
{
This unit will dial a RAS connection.  The method that does this is RasDial and it uses Index which is set from
the config file to determine which RAS definition in the windows system RAS setup to use.  Before calling RasDial
directly, call IsRASConnected:

  If IsRASConnected = true no need to call RASDial, you can make the call you want from the app that is using this one.
  If IsRASConnected = false and IsDialing = false, then you can call RasDial.  This could take some time to complete
    and doesn't block, so you have to call IsRASConnected repeatedly (but not more ofter than every 200 msecs) to
    determine when you are connected.  Note that you might get an error on the RAS dial instead of a connection, so
    you can check this with the RasError property.

  When you are done you can call the RasHangup method or leave the line open.
}
interface

uses
   Windows,
   SysUtils,
   DateUtils,
   classes,
   ServerEPSHelperLogger,
   Ras,
   RasUtils,
   RasHelperClasses;

type
  TRasClass = class(TComponent)

  private
    FDialError: DWORD;
    FNowDialing: boolean;
    procedure RasDialerNotify(Sender: TObject; State: TRasConnState; ErrorCode: DWORD);
  public
    PhoneBook: TRasPhonebook;
    Dialer: TRasDialer;
    Index : integer;
    property DialError: DWORD read FDialError write FDialError;
    property NowDialing: boolean read FNowDialing write FNowDialing;
  end;

  TRasDialToHost = class
    FRasClass: TRasClass;

  private
    function GetIsDialing: boolean;
    function GetRasError: DWORD;
    function GetRasState: TRasConnState;

  public
    constructor create;
    destructor destroy; override;
    function  IsRASConnected: boolean;
    procedure RasDial;
    //procedure RasHangup;
    procedure RasHangup(ManualDial: boolean);
    property  RasError: DWORD read GetRasError;
    property  IsDialing: boolean read GetIsDialing;
  end;

implementation

uses
  uDialBackClient;
  //uProxyTest;

const
  HANGUPWAITSECS = 30;
  IDLETIMEMINUTES = 10;

procedure SM(aMsg: string);
begin
  //uProxyTest.Log('ProxyDialer: ' + aMsg);
  uDialBackClient.Log('ProxyDialer: ' + aMsg);
end;

procedure TRasClass.RasDialerNotify(Sender: TObject; State: TRasConnState; ErrorCode: DWORD);
begin
  FDialError := ErrorCode;
  FNowDialing := false;
  case State of
    RASCS_Connected:    SM('RAS Connection established');
    RASCS_Disconnected: SM('RAS NO Connection established');
    else    { could be an error or just a notification here }
    begin
      FNowDialing := false;
      SM('RasDialerNotify: ' + RasConnStatusString(State, DialError) + ' RasState=' + IntToStr(State) + ' DialError=' + IntToStr(DialError));
    end;
  end;
end;    { TRasClass.RasDialerNotify }

// TRasDialToHost methods

constructor TRasDialToHost.create;
begin
  FRasClass := Nil;
end;

destructor TRasDialToHost.Destroy;
begin
  inherited;
  //RasHangup;
end;

(*
procedure TRasDialToHost.RasDial;
begin
  try
    if assigned(FRasClass) then
      RasHangup;
    FRasClass := TRasClass.Create(nil);
    FRasClass.PhoneBook := TRasPhonebook.Create;
    FRasClass.Dialer := TRasDialer.Create;
    FRasClass.DialError := 0;
    FRasClass.Dialer.OnNotify := FRasClass.RasDialerNotify;
    FRasClass.Index := 0; //   strToIntDef(trim(DSHostBuf.Host_Main_Session), 0);
    FRasClass.Dialer.Assign(FRasClass.PhoneBook.items[FRasClass.Index]);
    sm('RAS Dial: ' + FRasClass.PhoneBook.items[FRasClass.Index].name + ' ' +
      FRasClass.PhoneBook.items[FRasClass.Index].PhoneNumber);
    FRasClass.Dialer.Dial;
    FRasClass.NowDialing := true;
  except
    on e: Exception do
      SM('Try..Except:  RasDial - ' + e.message);
  end;
end;
*)

procedure TRasDialToHost.RasDial;
var
  i: integer;
  FoundRAS: boolean;
begin
  try
    //if assigned(FRasClass) then
    //  RasHangup;
    if not assigned(FRasClass) then
      begin
      FRasClass := TRasClass.Create(nil);
      FRasClass.PhoneBook := TRasPhonebook.Create;
      FRasClass.Dialer := TRasDialer.Create;
      FRasClass.DialError := 0;
      FRasClass.Dialer.OnNotify := FRasClass.RasDialerNotify;
      FRasClass.Index := 0; //   strToIntDef(trim(DSHostBuf.Host_Main_Session), 0);
      end;

    with FRasClass.PhoneBook do
      for i := 0 to Count-1 do
        begin
        SM(format('Scanning Phone Entry %d Name = %s  %s',[i,Items[FRasClass.Index].name,Items[FRasClass.Index].PhoneNumber]));
        FoundRAS := SameText(Items[i].Name,ProxyServer.RAS) or (ProxyServer.RAS = '');  // if RAS is blank, then just use FIRST entry in Phonebook
        if FoundRAS then
          begin
          FRasClass.Index := i;
          SM(format('Phone Entry %d Name %s matches Config %s',[i,ProxyServer.RAS,Items[FRasClass.Index].name]));
          break;
          end;
        end;
    if not FoundRAS then
      SM(format('Unable to locate RAS Name %s in Phonebook; using 1st entry',[ProxyServer.RAS]));
    FRasClass.Dialer.Assign(FRasClass.PhoneBook.items[FRasClass.Index]);
    SM(format('RAS Dial: %s -> %s',[FRasClass.PhoneBook.items[FRasClass.Index].name,FRasClass.PhoneBook.items[FRasClass.Index].PhoneNumber]));
    FRasClass.Dialer.Dial;
    FRasClass.NowDialing := true;
  except on e: Exception do
    SM('RasDial - ' + e.message);
  end;
end;

function TRasDialToHost.GetIsDialing: boolean;
begin
  if assigned(FRasClass)
    then result := FRasClass.NowDialing
    else result := false;
end;

function TRasDialToHost.GetRasError: DWORD;
begin
  if assigned(FRasClass)
    then result := FRasClass.DialError
    else result := 0;
end;

function TRasDialToHost.GetRasState: TRasConnState;
var aRasStatus: TRasConnStatus;
begin
  result := RASCS_Disconnected;
  aRasStatus.dwSize := sizeOf(aRasStatus);
  if assigned(FRasClass) and (FRasClass.Dialer.ConnHandle > 0) then    { then supposed to be connected }
    if (Ras.RasGetConnectStatus(FRasClass.Dialer.Connhandle, aRasStatus) = 0) then
      result := aRasStatus.rasconnstate;
end;   { GetRasState }

function  CheckIfTimeToHangup: boolean;
begin
  result := false;
end;

function TRasDialToHost.IsRASConnected: boolean;
begin
  result := GetRasState = RASCS_Connected;
end;

(*
procedure TRasDialToHost.RasHangup;
var HangUpTimer: TDateTime;
    aRasState: TRasConnState;
    aRasStatus: TRasConnStatus;
begin
  if assigned(FRasClass) then
  begin
    try
      sm('RAS Hang Up: ' + FRasClass.PhoneBook[FRasClass.Index].name);
      FRasClass.Dialer.HangUp;
    except
      on E: Exception do
        sm(e.message);
    end;
    aRasStatus.dwSize := sizeOf(aRasStatus);
    aRasState := RASCS_Connected;
    HangUpTimer := now;           { wait time for hang up }
    while (aRasState <> RASCS_Disconnected) and (SecondsBetween(now, HangUpTimer) >= HANGUPWAITSECS) do
    begin
      if (Ras.RasGetConnectStatus(FRasClass.Dialer.Connhandle,aRasStatus) = 0) then
        aRasState := aRasStatus.rasconnstate;
      sleep(10);
    end;
    FRasClass.PhoneBook.Free;
    FRasClass.Dialer.Free;
    FreeAndNil(FRasClass);
  end;
end;    { RasHangup }
*)

procedure TRasDialToHost.RasHangup(ManualDial: boolean);
var
  HangUpTimer: TDateTime;
  aRasState: TRasConnState;
  aRasStatus: TRasConnStatus;
  i: integer;
  Res: DWORD;

  procedure FreeDialerAndPhoneBook;
  begin
    SM('FreeDialerAndPhoneBook');
    FRasClass.PhoneBook.Free;
    FRasClass.Dialer.Free;
    FreeAndNil(FRasClass);
    sleep(250);
  end;

begin
  if assigned(FRasClass) then
    begin
    try
      SM('RASHangUp: ' + FRasClass.PhoneBook[FRasClass.Index].name);
      FRasClass.Dialer.HangUp;
      if ManualDial then
        begin
        FreeDialerAndPhoneBook;
        exit;
        end;
    except on e: exception do
      begin
      FreeDialerAndPhoneBook;
      exit;
      end;
    end;

    //aRasStatus.dwSize := sizeOf(aRasStatus);
    //aRasState := RASCS_Connected;
    HangUpTimer := now;           { wait time for hang up }
    i := 0;
    try
      while IsRASConnected and (SecondsBetween(Now,HangUpTimer) <= HANGUPWAITSECS) do
        begin
        if i mod 10 = 0 then    // only show this log line once a second...
          begin
          SM('Waiting for RAS Status to show DISCONNECTED...');
          FRasClass.Dialer.HangUp;   // try calling it again... but only about once a second...
          end;
        sleep(100);
        inc(i);
        end;
      if IsRASConnected
        then SM(format('RAS failed to DISCONNECT after %d seconds of trying. Sorry.',[HANGUPWAITSECS]))
        else SM('RAS DISCONNECTED OK...');

      {
      while (aRasState <> RASCS_Disconnected) and (SecondsBetween(Now,HangUpTimer) <= HANGUPWAITSECS) do
        begin
        Res := Ras.RasGetConnectStatus(FRasClass.Dialer.Connhandle,aRasStatus);
        if Res = ERROR_INVALID_HANDLE then
          begin
          SM('RAS DISCONNECTED OK...');
          exit;
          end
        else if Res = 0 then
          aRasState := aRasStatus.rasconnstate;
        if i mod 20 = 0 then    // only show this log line once a second...
          SM('Waiting for RAS Status to show DISCONNECTED...');
        sleep(50);
        inc(i);
        end;
      if aRasState = RASCS_Disconnected
        then SM('RAS DISCONNECTED OK...')
        else SM(format('RAS failed to DISCONNECT after %d seconds of trying. Sorry.',[HANGUPWAITSECS]));
      }
    finally
      FreeDialerAndPhoneBook;
    end;
    end;
end;
end.
