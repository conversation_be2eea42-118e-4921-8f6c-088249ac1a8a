{$I OpenEPS_DEF.inc}

unit Compression;

interface

uses Types, Classes, MTX_Types;

type
  TMTXZip = class
  private
  public
    class function Uncompress(const S: String): String; overload;
    class function Uncompress(const S: AnsiString): AnsiString; overload;
    class function Compress(const S: String): String; overload;
    class function Compress(const S: AnsiString): AnsiString; overload;
    class function UnZip(Filename: String): boolean;  // fully qualified file name
    class function Zip(const aFilename: String; const ZipName: String = ''): String; overload;
    class function Zip(const aFilename: TStrings; const ZipName: String = ''): String; overload;
    class function AddToZip(ZipFileName: String; FileList: String): integer;
    class function ExtractFromZip(ZipFileName: String; FileList: String;StrictDelimiter:boolean = false): boolean; overload;
    class function ExtractFromZip(ZipFileName, DestPath: String; FileList: String;StrictDelimiter:boolean = false): boolean; overload;
    class function CheckFileSizeGreaterThan(FileName: String; MinFileSize: Int64): boolean;
    class function ExtractToStream(const aFilename, ZipName: string; out aStream: TStream): boolean;
    class function GetZippedFileNames(const ZipName: string; FileList: TStrings): boolean;
    class function UnzipAllFiles(const ZipName, DestDir: string): boolean;
    class function ZipContainsFile(const aFilename, ZipName: string): boolean;
  end;

implementation

uses
  FinalizationLog,
  {$IFDEF MSWINDOWS}
    Windows,
  {$ENDIF MSWINDOWS}
  SysUtils,StrUtils,DateUtils,
  System.Zip,
  System.ZLib,
  GeneralUtilities;

class function TMTXZip.Uncompress(const S: String): String;
var
  InStream: TStream;
  OutStream: TStringStream;
  zStream: TZDecompressionStream;
begin
  Result := '';
  try
    InStream := TStringStream.Create(S);
    OutStream := TStringStream.Create('');
    try
      zStream := TZDecompressionStream.Create(InStream, 15 + 16);
      OutStream.CopyFrom(zStream, 0);
    finally
      zStream.Free;                                // Need to do this here to flush the buffer into OutStream
    end;
    Result := OutStream.DataString;
  finally
    FreeAndNil(InStream);
    FreeAndNil(OutStream);
  end;
end;

class function TMTXZip.Uncompress(const S: AnsiString): AnsiString;
var
  InStream: TStream;
  OutStream: TStringStream;
  zStream: TZDecompressionStream;
begin
  Result := '';
  try
    InStream := TStringStream.Create(S);
    OutStream := TStringStream.Create('');
    try
      zStream := TZDecompressionStream.Create(InStream, 15 + 16);
      OutStream.CopyFrom(zStream, 0);
    finally
      zStream.Free;                                // Need to do this here to flush the buffer into OutStream
    end;
    Result := TMTXString(OutStream.DataString);
  finally
    FreeAndNil(InStream);
    FreeAndNil(OutStream);
  end;
end;

class function TMTXZip.Compress(const S: String): String;
var
  InStream: TStream;
  OutStream: TStringStream;
  zStream: TZCompressionStream;
begin
  Result := '';
  InStream := TStringStream.Create(S);
  try
    OutStream := TStringStream.Create;               // zcNone, zcFastest, zcDefault, zcMax
    zStream := TZCompressionStream.Create(OutStream, zcMax, 15 + 16);
    try
      zStream.CopyFrom(InStream, InStream.Size);
    finally
      zStream.Free;                                  // Need to do this here to flush the buffer into OutStream
    end;
    Result := OutStream.DataString;
    // Result := Copy(Result, 1, Length(Result) - 8);   // Delete last 8 characters to make it match old result
    // Result[9] := AnsiChar(1);                        // Replace compression level with old value. Should be 2 or 4.
  finally
    FreeAndNil(OutStream);
    FreeAndNil(InStream);
  end;
end;

class function TMTXZip.Compress(const S: AnsiString): AnsiString;
var
  InStream: TStream;
  OutStream: TStringStream;
  zStream: TZCompressionStream;
begin
  Result := '';
  InStream := TStringStream.Create(S);
  try
    OutStream := TStringStream.Create;               // zcNone, zcFastest, zcDefault, zcMax
    zStream := TZCompressionStream.Create(OutStream, zcMax, 15 + 16);
    try
      zStream.CopyFrom(InStream, InStream.Size);
    finally
      zStream.Free;                                  // Need to do this here to flush the buffer into OutStream
    end;
    Result := OutStream.DataString;
    // Result := Copy(Result, 1, Length(Result) - 8);   // Delete last 8 characters to make it match old result
    // Result[9] := AnsiChar(1);                        // Replace compression level with old value. Should be 2 or 4.
  finally
    FreeAndNil(OutStream);
    FreeAndNil(InStream);
  end;
end;

class function TMTXZip.UnZip(Filename: String): boolean;   // fully qualified file name
begin
  result := false;
  try
    try
      if pos(':',Filename) = 0 then
        Filename := ExtractFileDrive(GetCurrentDir) + Filename;  // copy drive letter if none exists
      if CheckFileSizeGreaterThan(FileName, 0) then
      begin
        TZipFile.ExtractZipFile(Filename, ExtractFileDir(Filename));
        result := true;
      end
      else
        Result := False;
    finally
    end;
  except on e: exception do
    raise;
  end;
end;

class function TMTXZip.Zip(const aFilename: String; const ZipName: String = ''): String;
var
  Z: TZipFile;
begin
  result := '';
  try
    Z := TZipFile.Create;
    try
      if ZipName = ''
        then result := ChangeFileExt(aFilename,'.zip')
        else result := ZipName;
      Z.Open(result, zmWrite);
      Z.Add(aFileName);
      Z.Close;
    finally
      Z.Free;
    end;
  except
    on e: exception do
      result := '';
  end;
end;

class function TMTXZip.Zip(const aFilename: TStrings; const ZipName: String = ''): String;
var
  Z: TZipFile;
  FileToAdd: String;
  i: integer;
begin
  result := '';
  try
    Z := TZipFile.Create;
    try
      if ZipName = ''
        then result := ChangeFileExt(aFileName[0],'.zip')
        else result := ZipName;
      Z.Open(result, zmWrite);
      for i := 0 to aFileName.Count - 1 do
      begin
        FileToAdd := aFileName[i];
        if FileExists(FileToAdd) then
          Z.Add(FileToAdd);
      end;
      Z.Close;
    finally
      Z.Free;
    end;
  except
    on e: exception do
      result := '';
  end;
end;

class function TMTXZip.AddToZip(ZipFileName: String; FileList: String): integer;
var
  Z             : TZipFile;
  tmpList       : TStringList;
  i, count      : integer;
  tmpStr        : string;
  FileToAdd     : String;
  HasError      : boolean;
begin
  result := -1;
  Z := TZipFile.Create;
  if Pos(':', ZipFileName) = 0 then
    ZipFileName := Copy(GetCurrentDir,1,2) + ZipFileName;
  try
    if FileExists(ZipFileName) then
      Z.Open(ZipFileName, zmReadWrite)
    else
      Z.Open(ZipFileName, zmWrite);
  except
    result := 1;
    Exit;
  end;
  result := 2;
  try
    tmpList := TStringList.Create;
    tmpList.Delimiter := ',';
    result := 4;
    try
      tmpList.DelimitedText := FileList;
      HasError := false;
      for i := 0 to tmpList.Count - 1 do
      begin
        FileToAdd := tmpList[i];
        begin
          try
            if Pos(':', FileToAdd) = 0 then
            begin
              if FileExists(Copy(GetCurrentDir,1,2) + FileToAdd) then
                Z.Add(ExtractFileName(FileToAdd),   Copy(GetCurrentDir,1,2) + FileToAdd);
            end
            else
            begin
              if FileExists(FileToAdd) then
                Z.Add(ExtractFileName(FileToAdd), FileToAdd);
            end;
          except
            HasError := true;
            result := 5;
            Break;
          end;
        end;
      end;
      result := 6;
      Count := Z.FileCount;
      try
        Z.Close;
        result := 7;
      except
        HasError := true;
        result := 8;
      end;
      if NOT HasError then
        result := 0;
    finally
      FreeAndNil(tmpList);
      Z.Free;
      if (Count = 0) and FileExists(ZipFileName) then
        DeleteFile(ZipFileName);
    end;
  except
    ;
  end;
end;

class function TMTXZip.ExtractFromZip(ZipFileName: String; FileList: String;StrictDelimiter:boolean = false): boolean;
var
  Z: TZipFile;
  tmpList: TStringList;
  i: integer;
  FileToGet: String;
begin
  result := false;
  if Pos(':', ZipFileName) = 0 then
    ZipFileName := Copy(GetCurrentDir,1,2) + ZipFileName;
  if NOT FileExists(ZipFileName) then
    Exit;
  Z := TZipFile.Create;
  tmpList := TStringList.Create;
  tmpList.Delimiter := ',';
  tmpList.StrictDelimiter := StrictDelimiter;
  try
    Z.Open(ZipFileName, zmRead);
    tmpList.DelimitedText := FileList;
    for i := 0 to tmpList.Count - 1 do
    begin
      FileToGet := ExtractFileName(tmpList[i]);
      Z.Extract(FileToGet);
    end;
    Z.Close;
    result := true;
  finally
    FreeAndNil(tmpList);
    Z.Free;
  end;
end;

class function TMTXZip.ExtractFromZip(ZipFileName, DestPath: String; FileList: String; StrictDelimiter: boolean = false): boolean;
var
  Z: TZipFile;
  tmpList: TStringList;
  i: integer;
  FileToGet: String;
begin
  result := false;
  if Pos(':', ZipFileName) = 0 then
    ZipFileName := Copy(GetCurrentDir,1,2) + ZipFileName;
  if NOT FileExists(ZipFileName) then
    Exit;
  DestPath := IncludeTrailingPathDelimiter(DestPath);
  Z := TZipFile.Create;
  tmpList := TStringList.Create;
  tmpList.Delimiter := ',';
  tmpList.StrictDelimiter := StrictDelimiter;
  try
    Z.Open(ZipFileName, zmRead);
    tmpList.DelimitedText := FileList;
    for i := 0 to tmpList.Count - 1 do
    begin
      FileToGet := ExtractFileName(tmpList[i]);
      Z.Extract(FileToGet, DestPath);
    end;
    Z.Close;
    result := true;
  finally
    FreeAndNil(tmpList);
    Z.Free;
  end;
end;

class function TMTXZip.CheckFileSizeGreaterThan(FileName: String; MinFileSize: Int64): boolean;
var
  f: file of byte;
begin
  Result := False;
  AssignFile(f, Filename);
  try
    Reset(f);
    try
      Result := FileSize(f) > MinFileSize;
    finally
      CloseFile(f);
    end;
  except
  end;
end;

class function TMTXZip.ExtractToStream(const aFilename, ZipName: string; out aStream: TStream): boolean;
var
  Z: TZipFile;
  LocalHeader: TZipHeader;
  fName: string;
  TmpStream: TStream;
begin
  Result :=False ;
  Z := TZipFile.Create;
  try
    TmpStream := TMemoryStream.Create;
    FName := ExtractFileName(aFileName);
    Z.Open(ZipName, zmRead);
    Z.Read(FName, TmpStream, LocalHeader);
    TmpStream.Position := 0;
    aStream.CopyFrom(TmpStream, TmpStream.Size);
    TmpStream.Free;
    aStream.Position := 0;
    Result := True;
    Z.Close;
  finally
    Z.Free;
  end;
end;

class function TMTXZip.GetZippedFileNames(const ZipName: string; FileList: TStrings): boolean;
var
  Z: TZipFile;
  TmpFileList: TArray<string>;
  Str: string;
begin
  Z := TZipFile.Create;
  try
    Z.Open(ZipName, zmRead);
    TmpFileList := Z.FileNames;
    FileList.Clear;
    for Str in TmpFileList do
      FileList.Add(Str);
    Result := True;
    Z.Close;
  except
    Result := False;
  end;
  Z.Free;
end;

class function TMTXZip.UnzipAllFiles(const ZipName, DestDir: string): boolean;
begin
  try
    TZipFile.ExtractZipFile(ZipName, DestDir);
    Result := True;
  except
    Result := False;
  end;
end;

class function TMTXZip.ZipContainsFile(const aFilename, ZipName: string):  boolean;
var
  Z: TZipFile;
begin
  Result := False;
  Z := TZipFile.Create;
  try
    Z.Open(ZipName, zmRead);
    Result := Z.IndexOf(ExtractFileName(aFilename)) >= 0;
    Z.Close;
  except
    Result := False;
  end;
  Z.Free;
end;

initialization
  ExtendedLog('Compression Initialization');
finalization
  ExtendedLog('Compression Finalization');


end.

