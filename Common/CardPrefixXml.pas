
{*******************************************************************}
{                                                                   }
{                      Delphi XML Data Binding                      }
{                                                                   }
{         Generated on: 1/5/2012 12:45:39 PM                        }
{       Generated from: C:\Program Files\MicroTrax\EPS\Prefix.xml   }
{                                                                   }
{*******************************************************************}
unit CardPrefixXml;

interface

uses
  FinalizationLog,
  xmldom, XMLDoc, XMLIntf,
  SysUtils,
  MTX_Constants,
  MTX_Lib,
  UXMLCommon; // DEV-32564

type

{ Forward Decls }

  IXMLCardPrefixType = interface;
  IXMLTenderType = interface;
  IXMLTenderTypeList = interface;
  IXMLPrefixType = interface;
  IXMLDebitNetworkIDTableType = interface;

{ IXMLCardPrefixType }

  IXMLCardPrefixType = interface(IXMLNode)
    ['{3B1CE0B9-B721-46EB-86F8-587FC0FF30ED}']
    { Property Accessors }
    function Get_Version: WideString;
    function Get_LastModified: WideString;
    function Get_Tender: IXMLTenderTypeList;
    function Get_DebitNetworkIDTable: IXMLDebitNetworkIDTableType;
    procedure Set_Version(Value: WideString);
    procedure Set_LastModified(Value: WideString);
    { Methods & Properties }
    property Version: WideString read Get_Version write Set_Version;
    property LastModified: WideString read Get_LastModified write Set_LastModified;
    property Tender: IXMLTenderTypeList read Get_Tender;
    property DebitNetworkIDTable: IXMLDebitNetworkIDTableType read Get_DebitNetworkIDTable;
    function SaveToXML(FileName: string): boolean; // DEV-32564
  end;

{ IXMLTenderType }

  IXMLTenderType = interface(IXMLNodeCollection)
    ['{03415C16-4364-428B-89AD-82D69290150A}']
    { Property Accessors }
    function Get_Type_: WideString;
    function Get_Prefix(Index: Integer): IXMLPrefixType;
    procedure Set_Type_(Value: WideString);
    { Methods & Properties }
    function Add: IXMLPrefixType;
    function Insert(const Index: Integer): IXMLPrefixType;
    property Type_: WideString read Get_Type_ write Set_Type_;
    property Prefix[Index: Integer]: IXMLPrefixType read Get_Prefix; default;
  end;

{ IXMLTenderTypeList }

  IXMLTenderTypeList = interface(IXMLNodeCollection)
    ['{FA305B20-B840-42BB-B3C5-B6377E05E93B}']
    { Methods & Properties }
    function Add: IXMLTenderType;
    function Insert(const Index: Integer): IXMLTenderType;
    function Get_Item(Index: Integer): IXMLTenderType;
    property Items[Index: Integer]: IXMLTenderType read Get_Item; default;
  end;

{ IXMLPrefixType }

  IXMLPrefixType = interface(IXMLNode)
    ['{C24EAE8F-8EE7-4708-876E-D3E54262A407}']
    { Property Accessors }
    function Get_Data: WideString;
    function Get_CardLen: WideString;
    function Get_CardCode: WideString;
    function Get_DraftCapture: WideString;
    function Get_AutoTenderExcluded: WideString;
    function Get_FSACode: WideString;
    function Get_ProgramID: WideString; // DEV-32564
    function Get_StateCode: WideString; // DEV-32564
    procedure Set_Data(Value: WideString);
    procedure Set_CardLen(Value: WideString);
    procedure Set_CardCode(Value: WideString);
    procedure Set_DraftCapture(Value: WideString);
    procedure Set_AutoTenderExcluded(Value: WideString);
    procedure Set_FSACode(Value: WideString);
    procedure Set_ProgramID(Value: WideString); // DEV-32564
    procedure Set_StateCode(Value: WideString); // DEV-32564
    { Methods & Properties }
    property Data: WideString read Get_Data write Set_Data;
    property CardLen: WideString read Get_CardLen write Set_CardLen;
    property CardCode: WideString read Get_CardCode write Set_CardCode;
    property DraftCapture: WideString read Get_DraftCapture write Set_DraftCapture;
    property AutoTenderExcluded: WideString read Get_AutoTenderExcluded write Set_AutoTenderExcluded;
    property FSACode: WideString read Get_FSACode write Set_FSACode;
    property ProgramID: WideString read Get_ProgramID write Set_ProgramID; // DEV-32564
    property StateCode: WideString read Get_StateCode write Set_StateCode; // DEV-32564
  end;

{ IXMLDebitNetworkIDTableType }

  IXMLDebitNetworkIDTableType = interface(IXMLNodeCollection)
    ['{5454AA64-E493-40C6-B72B-9C2752DB7288}']
    { Property Accessors }
    function Get_AllowedDebitNetwork(Index: Integer): WideString;
    { Methods & Properties }
    function Add(const AllowedDebitNetwork: WideString): IXMLNode;
    function Insert(const Index: Integer; const AllowedDebitNetwork: WideString): IXMLNode;
    property AllowedDebitNetwork[Index: Integer]: WideString read Get_AllowedDebitNetwork; default;
  end;

{ Forward Decls }

  TXMLCardPrefixType = class;
  TXMLTenderType = class;
  TXMLTenderTypeList = class;
  TXMLPrefixType = class;
  TXMLDebitNetworkIDTableType = class;

{ TXMLCardPrefixType }

  TXMLCardPrefixType = class(TXMLNode, IXMLCardPrefixType)
  private
    FTender: IXMLTenderTypeList;
  protected
    { IXMLCardPrefixType }
    function Get_Version: WideString;
    function Get_LastModified: WideString;
    function Get_Tender: IXMLTenderTypeList;
    function Get_DebitNetworkIDTable: IXMLDebitNetworkIDTableType;
    procedure Set_Version(Value: WideString);
    procedure Set_LastModified(Value: WideString);
  public
    procedure AfterConstruction; override;
    function SaveToXML(FileName: string): boolean; // DEV-32564
  end;

{ TXMLTenderType }

  TXMLTenderType = class(TXMLNodeCollection, IXMLTenderType)
  protected
    { IXMLTenderType }
    function Get_Type_: WideString;
    function Get_Prefix(Index: Integer): IXMLPrefixType;
    procedure Set_Type_(Value: WideString);
    function Add: IXMLPrefixType;
    function Insert(const Index: Integer): IXMLPrefixType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLTenderTypeList }

  TXMLTenderTypeList = class(TXMLNodeCollection, IXMLTenderTypeList)
  protected
    { IXMLTenderTypeList }
    function Add: IXMLTenderType;
    function Insert(const Index: Integer): IXMLTenderType;
    function Get_Item(Index: Integer): IXMLTenderType;
  end;

{ TXMLPrefixType }

  TXMLPrefixType = class(TXMLNode, IXMLPrefixType)
  protected
    { IXMLPrefixType }
    function Get_Data: WideString;
    function Get_CardLen: WideString;
    function Get_CardCode: WideString;
    function Get_DraftCapture: WideString;
    function Get_AutoTenderExcluded: WideString;
    function Get_FSACode: WideString;
    function Get_ProgramID: WideString; // DEV-32564
    function Get_StateCode: WideString; // DEV-32564
    procedure Set_Data(Value: WideString);
    procedure Set_CardLen(Value: WideString);
    procedure Set_CardCode(Value: WideString);
    procedure Set_DraftCapture(Value: WideString);
    procedure Set_AutoTenderExcluded(Value: WideString);
    procedure Set_FSACode(Value: WideString);
    procedure Set_ProgramID(Value: WideString); // DEV-32564
    procedure Set_StateCode(Value: WideString); // DEV-32564    
  end;

{ TXMLDebitNetworkIDTableType }

  TXMLDebitNetworkIDTableType = class(TXMLNodeCollection, IXMLDebitNetworkIDTableType)
  protected
    { IXMLDebitNetworkIDTableType }
    function Get_AllowedDebitNetwork(Index: Integer): WideString;
    function Add(const AllowedDebitNetwork: WideString): IXMLNode;
    function Insert(const Index: Integer; const AllowedDebitNetwork: WideString): IXMLNode;
  public
    procedure AfterConstruction; override;
  end;

{ Global Functions }

function GetCardPrefix(Doc: IXMLDocument): IXMLCardPrefixType;
function LoadCardPrefix(const FileName: WideString): IXMLCardPrefixType;
function NewCardPrefix: IXMLCardPrefixType;

implementation

{ Global Functions }

function GetCardPrefix(Doc: IXMLDocument): IXMLCardPrefixType;
begin
  Result := Doc.GetDocBinding('CardPrefix', TXMLCardPrefixType) as IXMLCardPrefixType;
end;
function LoadCardPrefix(const FileName: WideString): IXMLCardPrefixType;
begin
  Result := LoadXMLDocument(FileName).GetDocBinding('CardPrefix', TXMLCardPrefixType) as IXMLCardPrefixType;
end;

function NewCardPrefix: IXMLCardPrefixType;
begin
  Result := NewXMLDocument.GetDocBinding('CardPrefix', TXMLCardPrefixType) as IXMLCardPrefixType;
end;

{ TXMLCardPrefixType }

procedure TXMLCardPrefixType.AfterConstruction;
begin
  RegisterChildNode('Tender', TXMLTenderType);
  RegisterChildNode('DebitNetworkIDTable', TXMLDebitNetworkIDTableType);
  FTender := CreateCollection(TXMLTenderTypeList, IXMLTenderType, 'Tender') as IXMLTenderTypeList;
  inherited;
end;

function TXMLCardPrefixType.Get_Version: WideString;
begin
  Result := AttributeNodes['Version'].Text;
end;

procedure TXMLCardPrefixType.Set_Version(Value: WideString);
begin
  SetAttribute('Version', Value);
end;

function TXMLCardPrefixType.Get_LastModified: WideString;
begin
  Result := AttributeNodes['LastModified'].Text;
end;

procedure TXMLCardPrefixType.Set_LastModified(Value: WideString);
begin
  SetAttribute('LastModified', Value);
end;

function TXMLCardPrefixType.Get_Tender: IXMLTenderTypeList;
begin
  Result := FTender;
end;

function TXMLCardPrefixType.Get_DebitNetworkIDTable: IXMLDebitNetworkIDTableType;
begin
  Result := ChildNodes['DebitNetworkIDTable'] as IXMLDebitNetworkIDTableType;
end;

{ TXMLTenderType }

procedure TXMLTenderType.AfterConstruction;
begin
  RegisterChildNode('Prefix', TXMLPrefixType);
  ItemTag := 'Prefix';
  ItemInterface := IXMLPrefixType;
  inherited;
end;

function TXMLTenderType.Get_Type_: WideString;
begin
  Result := AttributeNodes['Type'].Text;
end;

procedure TXMLTenderType.Set_Type_(Value: WideString);
begin
  SetAttribute('Type', Value);
end;

function TXMLTenderType.Get_Prefix(Index: Integer): IXMLPrefixType;
begin
  Result := List[Index] as IXMLPrefixType;
end;

function TXMLTenderType.Add: IXMLPrefixType;
begin
  Result := AddItem(-1) as IXMLPrefixType;
end;

function TXMLTenderType.Insert(const Index: Integer): IXMLPrefixType;
begin
  Result := AddItem(Index) as IXMLPrefixType;
end;


{ TXMLTenderTypeList }

function TXMLTenderTypeList.Add: IXMLTenderType;
begin
  Result := AddItem(-1) as IXMLTenderType;
end;

function TXMLTenderTypeList.Insert(const Index: Integer): IXMLTenderType;
begin
  Result := AddItem(Index) as IXMLTenderType;
end;

function TXMLTenderTypeList.Get_Item(Index: Integer): IXMLTenderType;
begin
  Result := List[Index] as IXMLTenderType;
end;

{ TXMLPrefixType }

function TXMLPrefixType.Get_Data: WideString;
begin
  Result := AttributeNodes['Data'].Text;
end;

procedure TXMLPrefixType.Set_Data(Value: WideString);
begin
  SetAttribute('Data', Value);
end;

function TXMLPrefixType.Get_CardLen: WideString;
begin
  Result := AttributeNodes['CardLen'].Text;
end;

procedure TXMLPrefixType.Set_CardLen(Value: WideString);
begin
  SetAttribute('CardLen', Value);
end;

function TXMLPrefixType.Get_CardCode: WideString;
begin
  Result := AttributeNodes['CardCode'].Text;
end;

procedure TXMLPrefixType.Set_CardCode(Value: WideString);
begin
  SetAttribute('CardCode', Value);
end;

function TXMLPrefixType.Get_DraftCapture: WideString;
begin
  Result := AttributeNodes['DraftCapture'].Text;
end;

procedure TXMLPrefixType.Set_DraftCapture(Value: WideString);
begin
  SetAttribute('DraftCapture', Value);
end;

function TXMLPrefixType.Get_AutoTenderExcluded: WideString;
begin
  Result := AttributeNodes['AutoTenderExcluded'].Text;
end;

procedure TXMLPrefixType.Set_AutoTenderExcluded(Value: WideString);
begin
  SetAttribute('AutoTenderExcluded', Value);
end;

function TXMLPrefixType.Get_FSACode: WideString;
begin
  Result := AttributeNodes['FSACode'].Text;
end;

procedure TXMLPrefixType.Set_FSACode(Value: WideString);
begin
  SetAttribute('FSACode', Value);
end;

function TXMLPrefixType.Get_ProgramID: WideString; // DEV-32564
begin
  Result := AttributeNodes['ProgramID'].Text;
end;

procedure TXMLPrefixType.Set_ProgramID(Value: WideString); // DEV-32564
begin
  SetAttribute('ProgramID', Value);
end;

function TXMLPrefixType.Get_StateCode: WideString; // DEV-32564
begin
  Result := AttributeNodes['StateCode'].Text;
end;

procedure TXMLPrefixType.Set_StateCode(Value: WideString); // DEV-32564
begin
  SetAttribute('StateCode', Value);
end;

{ TXMLDebitNetworkIDTableType }

procedure TXMLDebitNetworkIDTableType.AfterConstruction;
begin
  ItemTag := 'AllowedDebitNetwork';
  ItemInterface := IXMLNode;
  inherited;
end;

function TXMLDebitNetworkIDTableType.Get_AllowedDebitNetwork(Index: Integer): WideString;
begin
  Result := List[Index].Text;
end;

function TXMLDebitNetworkIDTableType.Add(const AllowedDebitNetwork: WideString): IXMLNode;
begin
  Result := AddItem(-1);
  Result.NodeValue := AllowedDebitNetwork;
end;


function TXMLDebitNetworkIDTableType.Insert(const Index: Integer; const AllowedDebitNetwork: WideString): IXMLNode;
begin
  Result := AddItem(Index);
  Result.NodeValue := AllowedDebitNetwork;
end;

function TXMLCardPrefixType.SaveToXML(FileName: string): Boolean; // DEV-32564
var
  Root, N1, N2, NTenders: TXMLParserNode;
  I, J: Integer;
  tmpTender: IXMLTenderType;
  tmpPrefix: IXMLPrefixType;
  tmpDebitNetworkIDTable: IXMLDebitNetworkIDTableType;
begin
  Root := TXMLParserNode.Create(nil);
  try
    Root.Name := 'CardPrefix';
    Root.Attr.Values['Version'] := FormatFloat(FORMAT_XML_VERSION, GetValidXMLVersion(xfCardPrefix));
    Root.Attr.Values['LastModified'] := FormatDateTime(FORMAT_LASTMODIFIED, Now);

    //NTenders := Root.AddChild('Tenders'); // DEV-12503
    NTenders := Root;
    for i := 0 to FTender.Count -1 do
    begin
      tmpTender := FTender.Items[i];
      N1 := NTenders.AddChild('Tender'); //Root.AddChild('Tender'); // DEV-12503
      N1.Attr.Values['Type'] := tmpTender.Type_;
      for j := 0 to tmpTender.Count -1 do
      begin
        tmpPrefix := tmpTender.Prefix[j];
        N2 := N1.AddChild('Prefix');
        N2.Attr.Values['Data']      := tmpPrefix.Data;
        N2.Attr.Values['CardLen']   := tmpPrefix.CardLen;
        N2.Attr.Values['CardCode']  := tmpPrefix.CardCode;
        N2.Attr.Values['FSACode']  := tmpPrefix.FSACode;
        N2.Attr.Values['ProgramID'] := tmpPrefix.ProgramID;
        N2.Attr.Values['StateCode']  := tmpPrefix.StateCode;
        if (tmpPrefix.AutoTenderExcluded = '') then
        begin
          if SameText(tmpTender.Type_, 'Check') or
             SameText(tmpTender.Type_, 'PhoneCard') or
             SameText(tmpTender.Type_, 'Wireless') then
            N2.Attr.Values['AutoTenderExcluded'] := 'Y'
          else
            N2.Attr.Values['AutoTenderExcluded'] := 'N';
        end
        else
          N2.Attr.Values['AutoTenderExcluded'] := tmpPrefix.AutoTenderExcluded;
      end;
    end;

    tmpDebitNetworkIDTable := Get_DebitNetworkIDTable;
    N1 := Root.AddChild('DebitNetworkIDTable');
    for i := 0 to tmpDebitNetworkIDTable.Count -1 do
    begin
      N2 := N1.AddChild('AllowedDebitNetwork');
      N2.Text := Trim(tmpDebitNetworkIDTable.AllowedDebitNetwork[i]);
    end;
    Root.SaveToFile(FileName);
    result := true;
  finally
    Root.Free;
  end;
end;

initialization
  ExtendedLog('CardPrefixXml Initialization');
finalization
  ExtendedLog('CardPrefixXml Finalization');

end.

