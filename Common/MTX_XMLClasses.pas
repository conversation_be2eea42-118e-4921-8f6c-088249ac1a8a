// (c) MTXEPS, Inc. 1988-2008
unit MTX_XMLClasses;
{
v825.0 12-11-07 JMR-A  DEV-3854 Remove EOD start time (lanes are considered up when engine is up)
}

interface

{$UNDEF GUIJR_OR_UPDATE}
{$IFDEF GUIJR}
  {$DEFINE GUIJR_OR_UPDATE}
{$ENDIF}
{$IFDEF UPDATE}
  {$DEFINE GUIJR_OR_UPDATE}
{$ENDIF}

uses
  SysUtils, Classes, DB, DBClient, Variants,
  {$IFNDEF LINUX}
  Forms,
  ActiveX,
  {$ENDIF}
  StrUtils,
  XMLIntf,
  MTX_Constants,
  UXMLCommon,
  CashiersXML,
  //ManagersXML,
  LanesXML,
  //ReportGroupsXML,
  StoreConfigurationsXML,
  CardProcessingProfileXML,
  UpdateXMLConfigXML,
  BinXml,
  CardPrefixXml, // DEV-32564
  EMVParmsXml, // DOEP-38362
  EMVCapkXml, // DOEP-38883
  WinEpsStatusXML;

const
  { keep inc to get field length constant }
  {$I PROCESS.INC}
  {$I HOST.INC}
//  {$I LANE.INC}
  //{$I RPTINFO.INC}
  //{$I REPORTGP.INC}
  dummy = 0;

type
  TdXMLTables = class(TDataModule)
    cHost: TClientDataSet;
    cProc: TClientDataSet;
    cProcDSProcAuto: TStringField;
    cProcDSProcAoff: TStringField;
    cProcDSProcTrainID: TStringField;
    cProcDSProcStopHH: TStringField;
    cProcDSProcStopMM: TStringField;
    cProcDSProcArchDays: TStringField;
    cProcDSProcCutHH: TStringField;
    cProcDSProcCutMM: TStringField;
    cProcDSProcTrackCkr: TStringField;
    cProcDSProcLPort: TStringField;
    cProcDSProcBaud: TStringField;
    cProcDSProcLBits: TStringField;
    cProcDSProcLParity: TStringField;
    cProcDSProcLStop: TStringField;
    cProcDSProcLIRQ: TStringField;
    cProcDSProcTermIPport: TStringField;
    cProcDSProcPrintLog: TStringField;
    cProcDSProcPrintTrx: TStringField;
    cProcDSProcPrintRpt: TStringField;
    cProcDSProcTermTrace: TStringField;
    cProcDSProcFormFeedUnused: TStringField;
    cProcDSProcDupAcctOnly: TStringField;
    cProcDSProc490type: TStringField;
    cProcDSProcReportDone: TStringField;
    cProcDSProcHide: TStringField;
    cProcDSProcDisableAlarm: TStringField;
    cProcDSProcCmdMsgIPportUnused: TStringField;
    cProcDSProcExportTranData: TStringField;
    cProcDSProcOnlineVerify: TStringField;
    cProcDSProcOnlineConvert: TStringField;
    cProcDSProcOfflineChoice: TStringField;
    cProcDSProcOfflineConvert: TStringField;
    cProcDSProcEncryption: TStringField;
    cProcDSProcJournalDaysPOSUnused: TStringField;
    cProcDSProcJournalDaysEPSUnused: TStringField;
    cProcDSProcUseJournalService: TStringField;
    cProcDSProcJournalPort: TStringField;
    cProcDSProcSuspendJournaling: TStringField;
    cProcDSProcPumpIncrement: TStringField;
    cProcDSProcSigLineYN: TStringField;
    cProcDSProcSigLineAmt: TStringField;
    cProcDSProcPrinterNorm: TStringField;
    cProcDSProcPumpAllowedTenders: TStringField;
    cProcDSProcPumpDefaultTender: TStringField;
    cProcDSProcSSLPort: TStringField;
    cProcDSProcRemoveOfflinesFromActlogAtEOD: TStringField;
    procedure DataModuleCreate(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

  TXMLProcess = class(TXMLConfiguration)
  private
  public
    fileName: string;
    Version: string;
    LastModified: string;
    constructor Create; overload;
    constructor Create(aFileName: string); overload;
    destructor Destroy; override;
    function CreateXML: Boolean;
    function GetRecord(var Buf: DSProcRec): Boolean;
    function SetRecord(Buf: DSProcRec; KeepRemovedFields: boolean=false): Boolean;
    function ValidateVersion: boolean;
  end;

  THostSearchOption = (soSuffix, {soHostName, }soSeq);

  TXMLStoreConfigurations = class
  private
    procedure SetVars;
  public
    FileName    : string;
    Active      : boolean;
    XMLObj              : TXMLStoreConfigurationsType;
    { easy access to the elements under XMLObj }
    ProcessingOptions   : TXMLProcessingOptionsType;
    PrintSettings       : TXMLPrintSettingsType;
    ReceiptTexts        : TXMLReceiptTextsType;
    Hosts               : StoreConfigurationsXML.TXMLHostsType;
    DialBackupConfiguration : TXMLDialBackupConfigurationType;
    constructor Create; overload;
    constructor Create(aFileName: string); overload;
    destructor Destroy; override;
    function LoadFromFile(aFileName: string): boolean;
    function SaveToFile(aFileName: string): boolean;
    function ValidateVersion: boolean;
    function FindHost(SearchStr: string; SearchOption: THostSearchOption): StoreConfigurationsXML.TXMLHostType;
    procedure CopyHost(aSourceHost, aTargetHost: StoreConfigurationsXML.TXMLHostType);
    function AddHost: StoreConfigurationsXML.TXMLHostType;
    function OnNewHost(AHost: StoreConfigurationsXML.TXMLHostType): boolean;
    function FindReceiptText(storeNumber: string): TXMLReceiptTextType;
    function AddReceiptText: TXMLReceiptTextType;
  end;

  TXMLCardProcessingProfile = class
  private
  public
    FileName    : string;
    Active      : boolean;
    XMLObj      : TXMLCardProcessingProfileType;
    Cards       : TXMLCardProcessingProfileType;
    constructor Create; overload;
    constructor Create(aFileName: string); overload;
    destructor Destroy; override;
    function LoadFromFile(aFileName: string): boolean;
    function SaveToFile(aFileName: string): boolean;
    function ValidateVersion: boolean;
    function FindFCT(CardCode: string): TXMLCardType;
  end;

  TXMLCashiers = class
  private
  public
    FileName    : string;
    Active      : boolean;
    XMLObj      : IXMLCashiersType;
    Cashiers    : IXMLCashiersType;
    constructor Create; overload;
    constructor Create(aFileName: string); overload;
    function LoadFromFile(aFileName: string): boolean;
    function SaveToFile(aFileName: string): boolean;
    function ValidateVersion: boolean;
    function FindCashier(CashierNumber: Integer): IXMLCashierType;
    function DeleteCashier(CashierNumber: Integer): boolean;
    function AddCashier: IXMLCashierType;
  end;

//  TXMLManagers = class
//  private
//  public
//    FileName    : string;
//    Active      : boolean;
//    XMLObj      : IXMLManagersType;
//    Managers    : IXMLManagersType;
//    constructor Create; overload;
//    constructor Create(aFileName: string); overload;
//    function LoadFromFile(aFileName: string): boolean;
//    function SaveToFile(aFileName: string): boolean;
//    function ValidateVersion: boolean;
//    function FindManager(ManagerNumber: Integer): IXMLManagerType;
//    function DeleteManager(ManagerNumber: Integer): boolean;
//    function AddManager: IXMLManagerType;
//  end;

  TLaneSearchOption = (soLaneNumber, soTermType);

  TXMLLanes = class
  private
  public
    FileName    : string;
    Active      : boolean;
    XMLObj      : LanesXML.TXMLLanesType;
    Lanes       : LanesXML.TXMLLanesType;
    constructor Create; overload;
    constructor Create(aFileName: string); overload;
    destructor Destroy; override;
    function LoadFromFile(aFileName: string): boolean;
    function SaveToFile(aFileName: string): boolean;
    function ValidateVersion: boolean;
    function FindLane(SearchValue: Variant; SearchOption: TLaneSearchOption): LanesXML.TXMLLaneType;
    function AddLane: LanesXML.TXMLLaneType;
    function DeleteLane(LaneNumber: Integer): boolean;
    function GetLaneType(laneNumber: integer): string;
  end;

//  TReportGroupKind = (rkStoreSummary, rkLaneSummary, rkCheckerSummary, rkCheckerSignOff);

//  TXMLReportGroups = class
//  private
//    procedure SetVars;
//  public
//    FileName    : string;
//    Active      : boolean;
//    XMLObj      : IXMLReportGroupsType;
//    StoreSummaryGroupsList   : IXMLStoreSummaryGroupsListType;
//    LaneSummaryGroupsList    : IXMLLaneSummaryGroupsListType;
//    CheckerSummaryGroupsList : IXMLCheckerSummaryGroupsListType;
//    CheckerSignOffGroupsList : IXMLCheckerSignOffGroupsListType;
//    HostSettlementGroupsList : IXMLHostSettlementGroupsListType;
//    ReportHeaderText         : IXMLReportHeaderTextType;
//    ReportPrintControls      : IXMLReportPrintControlsType;
//    NumberOfEachReportToPrint: IXMLNumberOfEachReportToPrintType;
//    constructor Create; overload;
//    constructor Create(aFileName: string); overload;
//    function LoadFromFile(aFileName: string): boolean;
//    function SaveToFile(aFileName: string): boolean;
//    function ValidateVersion: boolean;
//    function OnNewReportGroups: boolean;
//  end;

  // AFR, 4/23/2007, DEV-1688
  TXMLWinEpsStatus = class
  private
  public
    FileName    : string;
    Active      : boolean;
    XMLObj      : IXMLWinEpsStatusType;
    constructor Create; overload;
    constructor Create(aFileName: string); overload;
    function LoadFromFile(aFileName: string): boolean;
    function SaveToFile(aFileName: string): boolean;
    function FindLane(SearchValue: Variant; SearchOption: TLaneSearchOption): IXMLLaneStatusType;
    function DeleteLane(LaneNumber: Integer): boolean;
    function LoadLane(xmlstring: string): IXMLLaneStatusType;
  end;

  TCPPList = class
  private
    FActive: boolean;
    procedure ClearItems;
  public
    Items: TStringList;
    constructor Create(populate: boolean);
    destructor Destroy; override;
    function PopulateCPPStringList: Boolean;
    function FindCPP(laneNumber: integer): TXMLCardProcessingProfile;
    property Active: boolean read FActive;
  end;

  TUpdateActionType = (uaUnknown, uaUpdate, uaDelete, uaInsert);

  TXMLUpdateXMLConfig = class
  private
  public
    FileName    : string;
    Active      : boolean;
    XMLObj      : IXMLUpdateXMLConfigType;
    Actions     : IXMLActionsType;
    constructor Create; overload;
    constructor Create(aFileName: string); overload;
    function LoadFromFile(aFileName: string): boolean;
    function SaveToFile(aFileName: string): boolean;
    function ValidateVersion: boolean;
    function FindAction(aXMLPath: string; aActionType: TUpdateActionType): IXMLActionType;
    function AddAction: IXMLActionType;
    function DeleteAction(aXMLPath: string; aActionType: TUpdateActionType): boolean;
  end;

  TXMLBinUpdateMsgToSEPS = class(TXMLConfiguration)
  private
    FCompany: string;
    FStore: string;
    FBinVersion: string;
    FAppVersion: string;
    FActKey: string;
    FIPAddress: string;
    FPrevBinFile: string;
    FUpdateStatus: string; // 0=update avail; 1=no update; 2=busy, request again in wait time secs
    FDataSize: string;
    FWaitTime: string;
    function GetWaitTime: integer;
    function GetDataSize: integer;
  public
    constructor Create(aCompany, aStore, aBinVersion,
      aAppVersion, aActKey, aIPAddress, aPrevBinFile: string);
    function MakeXMLReqMsg: string;
    procedure ParseSEPSMsg(aXMLMsg: string);
    function MakeXMLAckMsg(aSizeReceived: string): string;
    property BinVersion: string read FBinVersion write FBinversion;
    property UpdateStatus: string read FUpdateStatus;
    property DataSize: integer read GetDataSize;
    property WaitTime: integer read GetWaitTime;
  end;

  TXMLBinPrefixes = class // DOEP-16415
  private
  public
    FileName    : string;
    Active      : boolean;
    XMLObj      : IXMLServerEPS_BinFileType;
    DebitBin    : IXMLBinType;
    FSABin      : IXMLBinType;
    PrepaidBin  : IXMLBinType;
    constructor Create; overload;
    constructor Create(aFileName: string); overload;
    destructor Destroy; override;
    function LoadFromFile(aFileName: string): boolean;
    function SaveToFile(aFileName: string): boolean;
    procedure SetVars;
  end;

  TXMLCardPrefix = class // DEV-32564
  private
  public
    FileName    : string;
    Active      : boolean;
    XMLObj      : IXMLCardPrefixType;
    Tenders     : IXMLTenderTypeList;
    DebitNetworkIDTable: IXMLDebitNetworkIDTableType;
    constructor Create; overload;
    constructor Create(aFileName: string); overload;
    destructor Destroy; override;
    function LoadFromFile(aFileName: string): boolean;
    function SaveToFile(aFileName: string): boolean;
    procedure SetVars;
    function FindTender(TenderType: string): IXMLTenderType;
    function FindPrefix(TenderType: string; Data: string; CardLen: string; CardCode: string): IXMLPrefixType;
    function AddPrefix(TenderType: string; Data: string; CardLen: string; CardCode: string; FSACode: string=''): IXMLPrefixType;
    function DeletePrefix(TenderType: string; Data: string; CardLen: string; CardCode: string): Boolean;
  end;

  TXMLEMVParms = class // DOEP-38362
  private
  public
    FileName    : string;
    Active      : boolean;
    XMLObj      : IXMLEMVParmsType;
    constructor Create; overload;
    constructor Create(aFileName: string); overload;
    destructor Destroy; override;
    function LoadFromFile(aFileName: string): boolean;
  end;

  TXMLEMVCapK = class // DOEP-38883
  private
  public
    FileName    : string;
    Active      : boolean;
    XMLObj      : IXMLEmvCapK;
    constructor Create; overload;
    constructor Create(aFileName: string); overload;
    destructor Destroy; override;
    function LoadFromFile(aFileName: string): boolean;
  end;

{ global functions to load xml file }
function LoadStoreConfigurationsXML(fileName: string): boolean;
function LoadCardProcessingProfileXML(fileName: string): boolean;
//function LoadCashiersXML(fileName: string): boolean;
//function LoadManagersXML(fileName: string): boolean;
function LoadLanesXML(fileName: string): boolean;
//function LoadReportGroupsXML(fileName: string): boolean;
function LoadCardPrefixXML(fileName: string): boolean; // DEV-32564
function LoadEMVParmsXML(fileName: string): Boolean; // DOEP-38362
procedure LoadHostXMLFiles;
procedure UnloadHostXMLFiles;
function ValidateCashier(cashier: string): Boolean; // XE: Remove WinEPS - copied from Cashstuf to remove Cashstuf from OpenEPS

const
  UpdateActionTypes: array [0..3] of string = ('', 'Update', 'Delete', 'Insert');

var
  dXMLTables: TdXMLTables;
  { ----------------------------------------------------------------------------
    XML* :      keep one instance of xml object
    *Buf :      for easy conversion by replacing DS*Buf to *Buf
                should be removed next time refactoring (*.Buf, HostArray)
  ---------------------------------------------------------------------------- }
  { StoreConfigurations.xml }
  XMLStoreConfigurations: TXMLStoreConfigurations = nil;
//  RcptBuf               : TXMLReceiptTextType = nil;
  HostArray             : StoreConfigurationsXML.TXMLHostsType = nil;
  HostBuf               : StoreConfigurationsXML.TXMLHostType = nil;
  tmpHostBuf            : StoreConfigurationsXML.TXMLHostType = nil;
  DLLTruncAcct          : array[1..10] of string[HostTruncateAcctNoL];    //TODO Maybe make this '10' a CONST
  DLLHostPO             : String[HostPONumberUsedL] = '';
  { CardProcessingProfile.xml }
  XMLCardProcessingProfile: TXMLCardProcessingProfile = nil;
  FCTBuf                : TXMLCardType = nil; // moved from fct.pas
  { Cashiers.xml }
  XMLCashiers           : TXMLCashiers = nil;
  CashBuf               : IXMLCashierType = nil;
  { Managers.xml }
//  XMLManagers           : TXMLManagers = nil;
//  MgrBuf                : IXMLManagerType = nil;
  { Lanes.xml }
  XMLLanes              : TXMLLanes = nil;
  LaneBuf               : LanesXML.TXMLLaneType = nil;
  { ReportGroups.xml }
//  XMLReportGroups       : TXMLReportGroups = nil;
//  RptInfoBuf            : IXMLReportGroupsType = nil; // replaced by ReportHeaderText, ReportPrintControls, NumberOfEachReportToPrint
  { WinEpsStatus.xml }
  XMLWinEPSStatus       : TXMLWinEpsStatus = nil;
  WinEPSStatusBuf       : IXMLWinEpsStatusType = nil;
  { LaneStatus.xml }
  XMLBinPrefixes        : TXMLBinPrefixes = nil;

  XMLCardPrefix         : TXMLCardPrefix = nil;
  XMLEMVParms           : TXMLEMVParms = nil; // DOEP-38362
  XMLEMVCapK            : TXMLEMVCapK = nil; // DOEP-38883

  CPPList:         TCPPList = nil; { used by engine & host dlls to handle multiple card processing profiles }

  XMLUpdateXMLConfig    : TXMLUpdateXMLConfig = nil;
  UpdateXMLActions      : TXMLActionsType = nil;

implementation

uses
  {$IFDEF MSWINDOWS}
  Windows,
  {$ENDIF}
  FinalizationLog,
  MTXEncryptionUtils,
  MTX_Lib;

{$R *.dfm}

var
  isPrinted: Boolean = false;

function LoadStoreConfigurationsXML(fileName: string): boolean;
begin
  result := false;
  try
    SM('MTX_XMLClasses.LoadStoreConfigurationsXML');
    if trim(fileName) = '' then fileName := StoreConfigurationsXML_;
    if Assigned(XMLStoreConfigurations) then
      FreeAndNil(XMLStoreConfigurations);
    XMLStoreConfigurations := TXMLStoreConfigurations.Create(fileName);
    result := Assigned(XMLStoreConfigurations) and XMLStoreConfigurations.Active;
  except
    on e: exception do
      SM('****TRY..EXCEPT: MTX_XMLClasses.LoadStoreConfigurationsXML : ' + fileName + ' - ' + e.message);
  end;
end;

function LoadCardProcessingProfileXML(fileName: string): boolean;
begin
  result := false;
  try
    if trim(fileName) = '' then fileName := CardProcessingProfileXML_;
    if Assigned(XMLCardProcessingProfile) then
      FreeAndNil(XMLCardProcessingProfile);
    XMLCardProcessingProfile := TXMLCardProcessingProfile.Create(fileName);
    result := XMLCardProcessingProfile.Active;
  except
    on e: exception do
      SM('****TRY..EXCEPT: MTX_XMLClasses.LoadCardProcessingProfileXML : ' + fileName + ' - ' + e.message);
  end;
end;

//function LoadCashiersXML(fileName: string): boolean;
//begin
//  result := false;
//  try
//    if trim(fileName) = '' then fileName := CashiersXML_;
//    if Assigned(XMLCashiers) then
//      FreeAndNil(XMLCashiers);
//    XMLCashiers := TXMLCashiers.Create(fileName);
//    result := XMLCashiers.Active;
//  except
//    on e: exception do
//      SM('****TRY..EXCEPT: MTX_XMLClasses.LoadCashiersXML : ' + fileName + ' - ' + e.message);
//  end;
//end;

//function LoadManagersXML(fileName: string): boolean;
//begin
//  result := false;
//  try
//    if trim(fileName) = '' then fileName := ManagersXML_;
//    if Assigned(XMLManagers) then
//      FreeAndNil(XMLManagers);
//    XMLManagers := TXMLManagers.Create(fileName);
//    result := XMLManagers.Active;
//  except
//    on e: exception do
//      SM('****TRY..EXCEPT: MTX_XMLClasses.LoadManagersXML : ' + fileName + ' - ' + e.message);
//  end;
//end;

function LoadLanesXML(fileName: string): boolean;
begin
  result := false;
  try
    if trim(fileName) = '' then fileName := LanesXML_;
    if Assigned(XMLLanes) then
      FreeAndNil(XMLLanes);
    XMLLanes := TXMLLanes.Create(fileName);
    result := XMLLanes.Active;
  except
    on e: exception do
      SM('****TRY..EXCEPT: MTX_XMLClasses.LoadLanesXML : ' + fileName + ' - ' + e.message);
  end;
end;

//function LoadReportGroupsXML(fileName: string): boolean;
//begin
//  result := false;
//  try
//    if trim(fileName) = '' then fileName := ReportGroupsXML_;
//    if Assigned(XMLReportGroups) then
//      FreeAndNil(XMLReportGroups);
//    XMLReportGroups := TXMLReportGroups.Create(fileName);
//    result := XMLReportGroups.Active;
//  except
//    on e: exception do
//      SM('****TRY..EXCEPT: MTX_XMLClasses.ReportGroupsXML : ' + fileName + ' - ' + e.message);
//  end;
//end;

function LoadCardPrefixXML(fileName: string): boolean; // DEV-32564
begin
  result := false;
  try
    if trim(fileName) = '' then fileName := CARDPREFIX_FILENAME;
    if Assigned(XMLCardPrefix) then
      FreeAndNil(XMLCardPrefix);
    XMLCardPrefix := TXMLCardPrefix.Create(fileName);
    result := XMLCardPrefix.Active;
  except
    on e: exception do
      SM('****TRY..EXCEPT: MTX_XMLClasses.LoadCardPrefixXML : ' + fileName + ' - ' + e.message);
  end;
end;

function LoadEMVParmsXML(fileName: string): Boolean; // DOEP-38362
begin
  Result := False;
  try
    if Trim(fileName) = '' then
      fileName := EMV_PARMS_FILENAME;

    if Assigned(XMLEMVParms) then
      FreeAndNil(XMLEMVParms);

    XMLEMVParms := TXMLEMVParms.Create(fileName);
    Result := XMLEMVParms.Active;
  except
    on e: exception do
      SM('****TRY..EXCEPT: MTX_XMLClasses.LoadCardPrefixXML : ' + fileName + ' - ' + e.message);
  end;
end;

function LoadEMVCapKXML(fileName: string): Boolean; // DOEP-38883
begin
  Result := False;
  try
    if Trim(fileName) = '' then
      fileName := EMV_CAPK_FILENAME;

    if Assigned(XMLEMVCapK) then
      FreeAndNil(XMLEMVCapK);

    XMLEMVCapK := TXMLEMVCapK.Create(fileName);
    Result := XMLEMVCapK.Active;
  except
    on e: exception do
      SM('****TRY..EXCEPT: MTX_XMLClasses.LoadCardPrefixXML : ' + fileName + ' - ' + e.message);
  end;
end;

{ ---------------------------------------------------------------------------- }

procedure TdXMLTables.DataModuleCreate(Sender: TObject);
begin
  cHost.LogChanges := False;
  cProc.LogChanges := False;
end;

{ ---------------------------------------------------------------------------- }

constructor TXMLProcess.Create;
begin
  Create(ProcessXML_);
end;

constructor TXMLProcess.Create(aFileName: string);
var
  tmpStr: string;
begin
  try
    inherited Create;
    if ExtractFileDir(aFileName) = '' then
      FileName := Trim(DefaultDir + aFileName)
    else
      FileName := Trim(aFileName);
    if not FileExists(FileName) then
    begin
      tmpStr := FileName + ' does not exist!' + #13 + 'Default process.xml will be created.'; //Consult technical support please.';
      SM('WARNING: ' + tmpStr);
      CreateXML;
    end;
    SM('TXMLProcess.Create FileName=' + FileName);
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLProcess.Create ' + e.message);
  end;
end;

destructor TXMLProcess.Destroy;
begin
  ;
  inherited;
end;

function TXMLProcess.CreateXML: Boolean;
begin
  result := false;
  try
    FillChar(DSProcBuf, SizeOf(DSProcBuf), #0);
    with DSProcBuf do
    begin
      DSProcAuto       := 'N';
      DSProcAOff       := '15';
      DSProcTrainID    := '758134653';
      DSProcStopHH     := '23';
      DSProcStopMM     := '59';
      DSProcArchDays   := IntToStr(MAX_DAYS_TO_STORE_LOGS);  { This is used for: # of days to keep journal files on EPS}
      DSProcCutHH      := '23';
      DSProcCutMM      := '59';
      DSProcTrackCkr   := 'Y';
      DSProcLPort      := '3';
      DSProcBaud       := '096';
      DSProcLBits      := '7';
      DSProcLParity    := 'E';
      DSProcLStop      := '1';
      DSProcLIRQ       := '5';
      DSProcTermIPport := RPad(WinEPSPortDefault, DSProcTermIPportL);
      DSProcPrintLog   := 'N';
      DSProcPrintTrx   := 'N';
      DSProcPrintRpt   := 'N';
      DSProcPumpAllowedTenders := 'YYNNNNNNYNNN';
      DSProcPumpDefaultTender  := 'Debit';
      DSProcTruncateCardDataAtEOD := 'N';
      ExcludedTenders := '';
      DSProcSSLPort := IntToStr(WINEPS_SSL_LANE_PORT); // DEV-7810
    end;
    SetRecord(DSProcBuf);
    result := true;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLTable.CreateXML ' + e.message);
  end;
end;

function TXMLProcess.GetRecord(var Buf: DSProcRec): Boolean;
var
  I: Integer;
  N1: TXMLParserNode;
  LoadProblem: boolean;       //JTG DEV-32180
begin
  result := false;                            // JTG move this to first line, so if we exit on the next line, result won't be random
  LoadProblem := true;       //JTG DEV-32180
  try
    if not FileExists(fileName)
      then exit;        // >>>>>>>>>>>>>>  EXIT HERE >>>>>>>>>>>>>>>>>>>>
    try
      FXMLParser.LoadFromFile(fileName);                                          // 828.5
      FXMLParser.StartScan;                                                       // 828.5
      ScanElement(nil);

      if Root.Name <> 'Process' then Exit;
      Version := Root.Attr.Values['Version'];
      DEFAULT_XML_VERSION := ConvertToDecimalSeparator(DEFAULT_XML_VERSION);  //33046 so it'll work with non-decimal point separator
      if Version = '' then Version := DEFAULT_XML_VERSION;
      LastModified := Root.Attr.Values['LastModified'];
      FillChar(Buf, SizeOf(Buf), #0);           // JTG 32180 - don't blank out Buf unless we're able to read the XML file
      LoadProblem := false;      //JTG DEV-32180, if it A/V's AFTER this then it's a different A/V, not related to simply reading the file
      for I := 0 to Root.Children.Count - 1 do
      begin
        N1 := Root.Children[I];
        if SameText(N1.Name, 'DSProcAuto') then Buf.DSProcAuto  := N1.Text
        else if SameText(N1.Name, 'DSProcAoff') then Buf.DSProcAoff  := N1.Text
        else if SameText(N1.Name, 'DSProcStopHH') then Buf.DSProcStopHH  := N1.Text
        else if SameText(N1.Name, 'DSProcStopMM') then Buf.DSProcStopMM  := N1.Text
        else if SameText(N1.Name, 'DSProcArchDays') then Buf.DSProcArchDays  := N1.Text
        else if SameText(N1.Name, 'DSProcCutHH') then Buf.DSProcCutHH  := N1.Text
        else if SameText(N1.Name, 'DSProcCutMM') then Buf.DSProcCutMM  := N1.Text
        else if SameText(N1.Name, 'DSProcTrackCkr') then Buf.DSProcTrackCkr  := N1.Text
        else if SameText(N1.Name, 'DSProcLPort') then Buf.DSProcLPort  := N1.Text
        else if SameText(N1.Name, 'DSProcBaud') then Buf.DSProcBaud  := N1.Text
        else if SameText(N1.Name, 'DSProcLBits') then Buf.DSProcLBits  := N1.Text
        else if SameText(N1.Name, 'DSProcLParity') then Buf.DSProcLParity  := N1.Text
        else if SameText(N1.Name, 'DSProcLStop') then Buf.DSProcLStop  := N1.Text
        else if SameText(N1.Name, 'DSProcLIRQ') then Buf.DSProcLIRQ  := N1.Text
        else if SameText(N1.Name, 'DSProcTermIPport') then Buf.DSProcTermIPport  := N1.Text
        else if SameText(N1.Name, 'DSProcPrintLog') then Buf.DSProcPrintLog  := N1.Text
        else if SameText(N1.Name, 'DSProcPrintTrx') then Buf.DSProcPrintTrx  := N1.Text
        else if SameText(N1.Name, 'DSProcPrintRpt') then Buf.DSProcPrintRpt  := N1.Text
        else if SameText(N1.Name, 'DSProcTermTrace') then Buf.DSProcTermTrace  := N1.Text
        else if SameText(N1.Name, 'DSProcFormFeedUnused') then Buf.DSProcFormFeedUnused  := N1.Text
        else if SameText(N1.Name, 'DSProcDupAcctOnly') then Buf.DSProcDupAcctOnly  := N1.Text
        else if SameText(N1.Name, 'DSProc490type') then Buf.DSProc490type  := N1.Text
        else if SameText(N1.Name, 'DSProcReportDone') then Buf.DSProcReportDone  := N1.Text
        else if SameText(N1.Name, 'DSProcHide') then Buf.DSProcHide  := N1.Text
        else if SameText(N1.Name, 'DSProcDisableAlarm') then Buf.DSProcDisableAlarm  := N1.Text
        else if SameText(N1.Name, 'DSProcCmdMsgIPportUnused') then Buf.DSProcCmdMsgIPportUnused  := N1.Text
        else if SameText(N1.Name, 'DSProcExportTranData') then Buf.DSProcExportTranData  := N1.Text
        else if SameText(N1.Name, 'DSProcOnlineVerify') then Buf.DSProcOnlineVerify  := N1.Text
        else if SameText(N1.Name, 'DSProcOnlineConvert') then Buf.DSProcOnlineConvert  := N1.Text
        else if SameText(N1.Name, 'DSProcOfflineChoice') then Buf.DSProcOfflineChoice  := N1.Text
        else if SameText(N1.Name, 'DSProcOfflineConvert') then Buf.DSProcOfflineConvert  := N1.Text
        else if SameText(N1.Name, 'DSProcEncryption') then Buf.DSProcEncryption  := N1.Text
        else if SameText(N1.Name, 'DSProcJournalDaysPOSUnused') then Buf.DSProcJournalDaysPOSUnused  := N1.Text
        else if SameText(N1.Name, 'DSProcJournalDaysEPSUnused') then Buf.DSProcJournalDaysEPSUnused  := N1.Text
        else if SameText(N1.Name, 'DSProcPumpIncrement') then Buf.DSProcPumpIncrement  := N1.Text
// for Dev 5964, move these to CPP
        else if (Length(N1.Text) > 0) and SameText(N1.Name, 'DSProcSigLineYN') then Buf.DSProcSigLineYN := N1.Text[1]
        else if SameText(N1.Name, 'DSProcSigLineAmt') then Buf.DSProcSigLineAmt := N1.Text
        else if SameText(N1.Name, 'DSProcPrinterNorm') then Buf.DSProcPrinterNorm := N1.Text
        else if SameText(N1.Name, 'DSProcPumpAllowedTenders') then Buf.DSProcPumpAllowedTenders := N1.Text
        else if SameText(N1.Name, 'DSProcPumpDefaultTender') then Buf.DSProcPumpDefaultTender:= N1.Text
        else if SameText(N1.Name, 'DSProcTruncateCardDataAtEOD') then
        begin
          Buf.DSProcTruncateCardDataAtEOD := N1.Text;
          Buf.ExcludedTenders := N1.Attr.Values['ExcludedTenders'];
        end
        else if SameText(N1.Name, 'DSProcSSLPort') then Buf.DSProcSSLPort := N1.Text // DEV-7810
        else if SameText(N1.Name, 'DSProcFtpCSVFile') then Buf.DSProcFtpCSVFile := N1.Text
        else if SameText(N1.Name, 'DSProcCSVFtpServer') then Buf.DSProcCSVFtpServer := N1.Text
        else if SameText(N1.Name, 'DSProcCSVFtpUserName') then Buf.DSProcCSVFtpUserName := N1.Text
        else if SameText(N1.Name, 'DSProcCSVFtpPassword') then Buf.DSProcCSVFtpPassword := N1.Text
        else if SameText(N1.Name, 'DSProcCSVFtpHostDir') then Buf.DSProcCSVFtpHostDir := N1.Text
        else if SameText(N1.Name, 'DSProcOfflineBlackhawkReceiptVerbiage') then Buf.DSProcOfflineBlackhawkReceiptVerbiage := N1.Text // DEV-11973
        //else if SameText(N1.Name, 'DSProcOfflineBlackhawkReceiptVerbiageLang2') then Buf.DSProcOfflineBlackhawkReceiptVerbiageLang2 := N1.Text
        //else if SameText(N1.Name, 'DSProcOfflineBlackhawkReceiptVerbiageLang3') then Buf.DSProcOfflineBlackhawkReceiptVerbiageLang3 := N1.Text
        else if SameText(N1.Name, 'DSProcRemoveOfflinesFromActlogAtEOD') then Buf.DSProcRemoveOfflinesFromActlogAtEOD := N1.Text
        else if SameText(N1.Name, 'DSProcDeclineBusinessCardPONoReq') then Buf.DSProcDeclineBusinessCardPONoReq := N1.Text
        else if SameText(N1.Name, 'DoNotRetainCustomerName') then Buf.DoNotRetainCustomerName := N1.Text
      end;
      ValidateVersion;
    finally
      FreeAndNil(Root);
    end;
    result := true;
  except
    on e: exception do
      if LoadProblem      //JTG DEV-32180
        then SM('MTX_XMLClasses.TXMLProcess.LoadFromXML: using previous settings because we are unable to read XML data from file '+Filename) //JTG DEV-32177
        else SM('****TRY..EXCEPT: TXMLProcess.LoadFromXML ' + e.message);
  end;
end;

function TXMLProcess.SetRecord(Buf: DSProcRec; KeepRemovedFields: boolean=false): Boolean;
var
  Root, N1: TXMLParserNode;
begin
  result := False;
  try
    Root := TXMLParserNode.Create(nil);
    try
      Root.Name := 'Process';
      Root.Attr.Values['Version'] := Version;
      Root.Attr.Values['LastModified'] := FormatDateTime(FORMAT_LASTMODIFIED, Now);
      with Buf do
      begin
        N1 := Root.AddChild('DSProcAuto');                  N1.Text := DSProcAuto;
        N1 := Root.AddChild('DSProcAoff');                  N1.Text := DSProcAoff;
        N1 := Root.AddChild('DSProcStopHH');                N1.Text := DSProcStopHH;
        N1 := Root.AddChild('DSProcStopMM');                N1.Text := DSProcStopMM;
        N1 := Root.AddChild('DSProcArchDays');              N1.Text := DSProcArchDays;
        N1 := Root.AddChild('DSProcCutHH');                 N1.Text := DSProcCutHH;
        N1 := Root.AddChild('DSProcCutMM');                 N1.Text := DSProcCutMM;
        N1 := Root.AddChild('DSProcTrackCkr');              N1.Text := DSProcTrackCkr;
        N1 := Root.AddChild('DSProcLPort');                 N1.Text := DSProcLPort;
        N1 := Root.AddChild('DSProcBaud');                  N1.Text := DSProcBaud;
        N1 := Root.AddChild('DSProcLBits');                 N1.Text := DSProcLBits;
        N1 := Root.AddChild('DSProcLParity');               N1.Text := DSProcLParity;
        N1 := Root.AddChild('DSProcLStop');                 N1.Text := DSProcLStop;
        N1 := Root.AddChild('DSProcLIRQ');                  N1.Text := DSProcLIRQ;
        N1 := Root.AddChild('DSProcTermIPport');            N1.Text := DSProcTermIPport;
        N1 := Root.AddChild('DSProcPrintLog');              N1.Text := DSProcPrintLog;
        N1 := Root.AddChild('DSProcPrintTrx');              N1.Text := DSProcPrintTrx;
        N1 := Root.AddChild('DSProcPrintRpt');              N1.Text := DSProcPrintRpt;
        N1 := Root.AddChild('DSProcTermTrace');             N1.Text := DSProcTermTrace;
        N1 := Root.AddChild('DSProcFormFeedUnused');        N1.Text := DSProcFormFeedUnused;
        N1 := Root.AddChild('DSProcDupAcctOnly');           N1.Text := DSProcDupAcctOnly;
        N1 := Root.AddChild('DSProc490type');               N1.Text := DSProc490type;
        N1 := Root.AddChild('DSProcReportDone');            N1.Text := DSProcReportDone;
        N1 := Root.AddChild('DSProcHide');                  N1.Text := DSProcHide;
        N1 := Root.AddChild('DSProcDisableAlarm');          N1.Text := DSProcDisableAlarm;
        N1 := Root.AddChild('DSProcCmdMsgIPportUnused');    N1.Text := DSProcCmdMsgIPportUnused;
        N1 := Root.AddChild('DSProcExportTranData');        N1.Text := DSProcExportTranData;
        N1 := Root.AddChild('DSProcOnlineVerify');          N1.Text := DSProcOnlineVerify;
        N1 := Root.AddChild('DSProcOnlineConvert');         N1.Text := DSProcOnlineConvert;
        N1 := Root.AddChild('DSProcOfflineChoice');         N1.Text := DSProcOfflineChoice;
        N1 := Root.AddChild('DSProcOfflineConvert');        N1.Text := DSProcOfflineConvert;
        N1 := Root.AddChild('DSProcEncryption');            N1.Text := DSProcEncryption;
        N1 := Root.AddChild('DSProcJournalDaysPOSUnused');  N1.Text := DSProcJournalDaysPOSUnused;
        N1 := Root.AddChild('DSProcJournalDaysEPSUnused');  N1.Text := DSProcJournalDaysEPSUnused;
        N1 := Root.AddChild('DSProcPumpIncrement');         N1.Text := DSProcPumpIncrement;
// for Dev 5964, move these to CPP
        {$IFDEF GUIJR_OR_UPDATE}
        {$IFDEF UPDATE}if KeepRemovedFields then {$ENDIF} // only for update, ConMan should create nodes
        begin
          N1 := Root.AddChild('DSProcSigLineYN');             N1.Text := DSProcSigLineYN;
          N1 := Root.AddChild('DSProcSigLineAmt');            N1.Text := DSProcSigLineAmt;
        end;
        {$ENDIF}
        N1 := Root.AddChild('DSProcPrinterNorm');           N1.Text := DSProcPrinterNorm;
        N1 := Root.AddChild('DSProcPumpAllowedTenders');    N1.Text := DSProcPumpAllowedTenders;
        N1 := Root.AddChild('DSProcPumpDefaultTender');     N1.Text := DSProcPumpDefaultTender;
        N1 := Root.AddChild('DSProcTruncateCardDataAtEOD');
                N1.Text := DSProcTruncateCardDataAtEOD;
                N1.Attr.Values['ExcludedTenders'] := Buf.ExcludedTenders; // DEV-18499
        N1 := Root.AddChild('DSProcSSLPort'); N1.Text := DSProcSSLPort; // DEV-7810
        N1 := Root.AddChild('DSProcFtpCSVFile'); N1.Text := DSProcFtpCSVFile;
        N1 := Root.AddChild('DSProcCSVFtpServer'); N1.Text := DSProcCSVFtpServer;
        N1 := Root.AddChild('DSProcCSVFtpUserName'); N1.Text := DSProcCSVFtpUserName;
        N1 := Root.AddChild('DSProcCSVFtpPassword'); N1.Text := DSProcCSVFtpPassword;
        N1 := Root.AddChild('DSProcCSVFtpHostDir'); N1.Text := DSProcCSVFtpHostDir;
        N1 := Root.AddChild('DSProcOfflineBlackhawkReceiptVerbiage'); N1.Text := DSProcOfflineBlackhawkReceiptVerbiage; // DEV-11973
        N1 := Root.AddChild('DSProcRemoveOfflinesFromActlogAtEOD'); N1.Text := DSProcRemoveOfflinesFromActlogAtEOD;
        N1 := Root.AddChild('DSProcDeclineBusinessCardPONoReq'); N1.Text := DSProcDeclineBusinessCardPONoReq;
        N1 := Root.AddChild('DoNotRetainCustomerName'); N1.Text := DoNotRetainCustomerName;
      end;
      if not FileExists(fileName) then
      begin
        SM('WARNING! FileName does not exist - ' + fileName);
        fileName := Trim(DefaultDir + ProcessXML_);
      end;
      Root.SaveToFile(fileName);
      result := True;
    finally
      Root.Free;
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLProcess.SaveToXML ' + e.message);
  end;
end;

function TXMLProcess.ValidateVersion: boolean;
var
  ver: Double;
  sVer: string;      // 33046
begin
  // TODO: need to convert XMLObj to double
  ver := GetValidXMLVersion(xfProcess);
  sVer := ConvertToDecimalSeparator(Version);   // 33046
  result := StrToFloatDef(sVer, 0) = ver;
  if not result then
    SM(Format('%s%s Version (%s) is %s valid (should be %.1f)',
        [iif(not result, '***WARNING ',''), ExtractFileName(fileName), sVer, iif(not result, 'not', ''), ver]));
end;

{ ---------------------------------------------------------------------------- }

constructor TXMLStoreConfigurations.Create;
begin
  if Assigned(XMLObj) then
    FreeAndNil(XMLObj);
  XMLObj := NewStoreConfigurations;
  if Assigned(XMLObj) then
  begin
    //XMLObj.OwnerDocument.Options := XMLObj.OwnerDocument.Options + [doNodeAutoCreate, doNodeAutoIndent];
    SetVars;
    //Active := XMLObj.OwnerDocument.Active;
  end;
  Active := Assigned(XMLObj);
end;

constructor TXMLStoreConfigurations.Create(aFileName: string);
begin
  try
    SM('XMLStoreConfigurations.Create');
    if Trim(aFileName) <> '' then
      LoadFromFile(aFileName);
    if not Active then
    begin
      SM('ERROR! TXMLHost.Create - Fail to load ' + FileName);
{$IFNDEF MTXEPSDLL} // DOEP-57142: OpenEPS should stop processing, how about engine?
      Create;
{$ENDIF}
      Exit;
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLStoreConfigurations.Create ' + e.message);
  end;
end;

destructor TXMLStoreConfigurations.Destroy;
begin
  FreeAndNil(XMLObj);
  { these are only pointers to parts of XMLObj }
  ProcessingOptions := nil;
  PrintSettings := nil;
  ReceiptTexts := nil;
  Hosts := nil;
  DialBackupConfiguration := nil;

//  RcptBuf := nil;
  HostArray := nil;
  HostBuf := nil;
  inherited;
end;

function TXMLStoreConfigurations.LoadFromFile(aFileName: string): boolean;
var
  tmpStr: string;
begin
  result := false;
  try
    if ExtractFileDir(aFileName) = '' then
      FileName := Trim(DefaultDir + aFileName)
    else
      FileName := Trim(aFileName);
    if not FileExists(FileName) then
    begin
      tmpStr := FileName + ' does not exist!' + #13 + 'Consult technical support please.';
      SM('WARNING: ' + tmpStr);
    end;
    if Assigned(XMLObj) then
      FreeAndNil(XMLObj);
    XMLObj := LoadStoreConfigurations(FileName);
    Active := Assigned(XMLObj);
    if Assigned(XMLObj) then
    begin
      SetVars;
      if XMLObj.Version = '' then
        XMLObj.Version := DEFAULT_XML_VERSION;
      ValidateVersion;
    end;
    result := Active;
    SM('TXMLStoreConfigurations.LoadFromFile - Active=' + TrueFalse(result) + ' / FileName=' + FileName);
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLStoreConfigurations.LoadFromFile: ' + FileName + ' - ' + e.message);
  end;
end;

function TXMLStoreConfigurations.SaveToFile(aFileName: string): boolean;
begin
  result := false;
  try
    if not Active then
    begin
      SM('WARNING! TXMLStoreConfigurations.SaveToFile: Active = False');
      Exit;
    end;
    if ExtractFileDir(aFileName) = '' then
      aFileName := Trim(DefaultDir + aFileName)
    else
      aFileName := Trim(aFileName);
    XMLObj.LastModified := FormatDateTime(FORMAT_LASTMODIFIED, Now);
    XMLObj.Version := FormatFloat(FORMAT_XML_VERSION, GetValidXMLVersion(xfStoreConfigurations));
    XMLObj.SaveToFile(aFileName);
    SM('TXMLStoreConfigurations.SaveToFile: saved to ' + aFileName);
    result := true;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLStoreConfigurations.SaveToFile: ' + FileName + ' - ' + e.message);
  end;
end;

procedure TXMLStoreConfigurations.SetVars;
begin
  if not Assigned(XMLObj) then Exit;
  ProcessingOptions    := XMLObj.ProcessingOptions;
  PrintSettings        := XMLObj.PrintSettings;
  ReceiptTexts         := XMLObj.ReceiptTexts;
  Hosts                := XMLObj.Hosts;
  DialBackupConfiguration := XMLObj.DialBackupConfiguration;
  { set global var }
//  RcptBuf              := FindReceiptText(IntToStr(DEFAULT_STORE_NUMBER));
  HostArray            := XMLObj.Hosts;
end;

function TXMLStoreConfigurations.ValidateVersion: boolean;
var
  ver: Double;
  sVer: string;   // 33046
begin
  // TODO: need to convert XMLObj to double
  ver := GetValidXMLVersion(xfStoreConfigurations);   //JTG ver is correctly a double regardless of language
  sVer := ConvertToDecimalSeparator(XMLObj.Version);     // 33046
  if StrToFloatDef(sVer,0) = 0 then                      // JTG 32650/33130 - bad version string for the current decimal separator.. fix it up
    sVer := DoubleStrCorrectedForDecimalSeparator(sVer);
  result := StrToFloatDef(sVer, 0) = ver;
  if not result then
    SM(Format('%s%s Version (%s) is %s valid (should be %.1f)',
        [iif(not result, '***WARNING ',''), ExtractFileName(fileName), sVer, iif(not result, 'not', ''), ver]));
end;

function TXMLStoreConfigurations.FindHost(SearchStr: string; SearchOption: THostSearchOption): StoreConfigurationsXML.TXMLHostType;
var
  I: integer;
begin
  result := nil;
  for I := 0 to XMLObj.Hosts.Count -1 do
    case SearchOption of
      soSuffix:
        if SameText(Trim(XMLObj.Hosts[I].Suffix), SearchStr) then
        begin
          result := XMLObj.Hosts[I];
          Exit;
        end;
      {
      soHostName:
        if SameText(Copy(XMLObj.Hosts[I].Name, 1, 3), SearchStr) then
        begin
          result := XMLObj.Hosts[I];
          Exit;
        end;
      }
      soSeq:
        //if XMLObj.Hosts[I].SeqNo = StrToIntDef(SearchStr,-1) then
        if I = StrToIntDef(SearchStr,-1) then
        begin
          result := XMLObj.Hosts[I];
          Exit;
        end;
    end;
end;

function TXMLStoreConfigurations.AddHost: StoreConfigurationsXML.TXMLHostType;
begin
  result := XMLObj.Hosts.Add;
  if Assigned(result) then OnNewHost(result);
end;

function TXMLStoreConfigurations.OnNewHost(AHost: StoreConfigurationsXML.TXMLHostType): boolean;
begin
  result := false;
  if not Assigned(AHost) then exit;
  try
    (*
    if (HostName = '') then
      HostName         := String_(HostNameL, ' ');
    HostPollChar       := String_(HostPollCharL, ' ');
    HostPollIntvl      := '03';
    HostSuspendCt      := '01';
    HostFatalCt        := '05';
    HostResumeCt       := '01';
    HostOnTOxxxx       := String_(HostOnTOL, ' ');
    HostLocal          := String_(HostLocalL, ' ');
    HostOffTO          := '04';
    Host_SSCP_ID       := String_(Host_SSCP_IDL, '0');
    Host_Is_EBCDIC     := String_(Host_Is_EBCDICL, ' ');
    Host_Comm_NRZI     := String_(Host_Comm_NRZIL, ' ');
    Host_Main_Session  := String_(Host_Main_SessionL, ' ');
    Host_TOR_Session   := String_(Host_TOR_SessionL, ' ');
    Host_Offl_Session  := String_(Host_Offl_SessionL, ' ');
    HostMerchantID     := String_(HostMerchantIDL, ' ');
    HostPassword       := String_(HostPasswordL, ' ');
    HostCkAuthType     := String_(HostCkAuthTypeL, ' ');
    HostCkCCode        := String_(HostCkCCodeL, ' ');
    HostDefaultState   := String_(HostDefaultStateL, ' ');
    HostOnDbCk         := String_(HostOnDbCkL, ' ');
    HostOffDbCk        := String_(HostOffDbCkL, ' ');
    HostPort           := String_(HostPortL, ' ');
    HostBaud           := String_(HostBaudL, ' ');
    HostBits           := String_(HostBitsL, ' ');
    HostParity         := String_(HostParityL, ' ');
    HostStop           := String_(HostStopL, ' ');
    HostIRQ            := String_(HostIRQL, ' ');
    HostStoreTermID    := String_(HostStoreTermIDL, ' ');
    HostMsgSecCode     := String_(HostMsgSecCodeL, ' ');
    HostMgtInfoData    := String_(HostMgtInfoDataL, ' ');
    HostSendMgtInfo    := String_(HostSendMgtInfoL, ' ');
  // JMR-C      HostMerchType      := '5411';
    HostInstIDCode     := String_(HostInstIDCodeL, ' ');
    HostMerchName      := String_(HostMerchNameL, ' ');
    HostMerchAddress   := String_(HostMerchAddressL, ' ');
    HostMerchCity      := String_(HostMerchCityL, ' ');
    HostMerchState     := String_(HostMerchStateL, ' ');
    HostZipCode        := String_(HostZipCodeL, ' ');
    HostCountry        := String_(HostCountryL, ' ');
    HostCurrencyCode   := '840';
    HostStateCode      := String_(HostStateCodeL, ' ');
    HostCountyCode     := String_(HostCountyCodeL, ' ');
    HostOffsetHrsGMT   := String_(HostOffsetHrsGMTL, ' ');
    HostClerkLane      := String_(HostClerkLaneL, ' ');
    HostTORLate        := String_(HostTORLateL, ' ');
    HostTORTimeout     := String_(HostTORTimeoutL, ' ');
    HostTORNoT2        := String_(HostTORNoT2L, ' ');
    Host_Trace_Data    := 'Y';                        { TSL-E }
    HostOffResubmit    := String_(HostOffResubmitL, ' ');
    HostTerminalType   := String_(HostTerminalTypeL, ' ');
    HostMerchantNum    := String_(HostMerchantNumL, ' ');
    HostTerminalNum    := String_(HostTerminalNumL, ' ');
    HostRetryPhone1    := String_(HostRetryPhone1L, ' ');
    HostPhone1         := String_(HostPhone1L, ' ');
    HostLogon1         := String_(HostLogon1L, ' ');
    HostRetryPhone2    := String_(HostRetryPhone2L, ' ');
    HostPhone2         := String_(HostPhone2L, ' ');
    HostLogon2         := String_(HostLogon2L, ' ');
    HostRetryPhone3    := String_(HostRetryPhone3L, ' ');
    HostPhone3         := String_(HostPhone3L, ' ');
    HostLogon3         := String_(HostLogon3L, ' ');
    HostModemInit      := String_(HostModemInitL, ' ');
    HostHoldTime       := '00015';
    HostInitMain       := String_(HostInitSelfL, ' ');
    HostInitTOR        := String_(HostInitSelfL, ' ');
    HostInitOffline    := String_(HostInitSelfL, ' ');
    HostConfigNum      := String_(HostConfigNumL, ' ');
    HostSecondScrName  := String_(HostSecondScrNameL, ' ');
    HostHealthMsgInt   := '0300';
    For I := Low(HostTransactions) to High(HostTransactions) do  { TRI-A - This is important }
      HostTransactions[I] := 'N';
    For I := 1 To 10 do
      HostTimeout[I] := '45';
    HostOffFwdDelay    := '00060';  { secs before starting queue after comm up }
    HostOffPaceDelay   := '00015';    { delay between sending transactions }
    HostTORWaitInt     := '00120';    { how often to check TOR queue }
    HostTrxWaitInt     := '00600';    { how often to check ofline queue }
    HostSuffix         := String_(HostSuffixL, ' ');
    HostPort2          := String_(HostPortL, ' ');
    HostBaud2          := String_(HostBaudL, ' ');
    HostBits2          := String_(HostBitsL, ' ');
    HostParity2        := String_(HostParityL, ' ');
    HostStop2          := String_(HostStopL, ' ');
    HostIRQ2           := String_(HostIRQL, ' ');

    HostPort3          := String_(HostPortL, ' ');
    HostBaud3          := String_(HostBaudL, ' ');
    HostBits3          := String_(HostBitsL, ' ');
    HostParity3        := String_(HostParityL, ' ');
    HostStop3          := String_(HostStopL, ' ');
    HostIRQ3           := String_(HostIRQL, ' ');
    HostSetForDayLight := 'Y';
    HostSaveCheckInfo  := String_(HostSaveCheckInfoL, ' ');
    HostPartialBINDll  := String_(HostPartialBINDllL, ' ');   { TSL-F }
    HostACHStateCodes  := String_(HostACHStateCodesL, ' ');
    *)
    if (AHost.Prefix = '') then
      AHost.Prefix           := '';
    //HostPollChar       := String_(HostPollCharL, ' ');
    AHost.RetryCounter      := '03';
    AHost.HostSuspendCt      := '01';
    AHost.HostFatalCt        := '05';
    AHost.HostResumeCt       := '01';
    AHost.HostOnTOxxxx       := '';
    AHost.OfflineProcessing  := '';
    AHost.SimulatedDelay          := '04';
    AHost.WorkingKey         := String_(Host_SSCP_IDL, '0');
    AHost.UseEBCDIC     := String_(Host_Is_EBCDICL, ' ');
    AHost.Host_Comm_NRZI     := String_(Host_Comm_NRZIL, ' ');
    AHost.Host_Main_Session  := String_(Host_Main_SessionL, ' ');
    AHost.Host_TOR_Session   := String_(Host_TOR_SessionL, ' ');
    AHost.Host_Offl_Session  := String_(Host_Offl_SessionL, ' ');
    AHost.MerchantNum     := String_(HostMerchantIDL, ' ');
    AHost.Password       := String_(HostPasswordL, ' ');
    AHost.ChkAuthType     := String_(HostCkAuthTypeL, ' ');
    AHost.ChkCode        := String_(HostCkCCodeL, ' ');
    AHost.ChkDefaultStateCode   := String_(HostDefaultStateL, ' ');
    AHost.VerifyDebitPrefixOnline         := String_(HostOnDbCkL, ' ');
    AHost.VerifyDebitPrefixOffline        := String_(HostOffDbCkL, ' ');
    AHost.HostPort           := String_(HostPortL, ' ');
    AHost.LXHostSlotNumbers           := String_(HostBaudL, ' ');
    AHost.HostBits           := String_(HostBitsL, ' ');
    AHost.HostParity         := String_(HostParityL, ' ');
    AHost.HostStop           := String_(HostStopL, ' ');
    AHost.HostIRQ            := String_(HostIRQL, ' ');
    AHost.StoreNumber    := String_(HostStoreTermIDL, ' ');
    AHost.TerminalID     := String_(HostMsgSecCodeL, ' ');
    AHost.HostMgtInfoData    := String_(HostMgtInfoDataL, ' ');
    AHost.HostManagmentInfo    := String_(HostSendMgtInfoL, ' ');
    //JMR-C      HostMerchType      := '5411';
    AHost.MerchantType := '';
    AHost.HostID     := String_(HostInstIDCodeL, ' ');
    AHost.MerchantName      := String_(HostMerchNameL, ' ');
    AHost.MerchantAddress   := String_(HostMerchAddressL, ' ');
    AHost.MerchantCity      := String_(HostMerchCityL, ' ');
    AHost.MerchantState     := String_(HostMerchStateL, ' ');
    AHost.MerchantZipcode        := String_(HostZipCodeL, ' ');
    AHost.MerchantCountry        := String_(HostCountryL, ' ');
    AHost.CurrencyCode   := '840';
    AHost.NumericStateCode      := String_(HostStateCodeL, ' ');
    AHost.NumericCountryCode     := String_(HostCountyCodeL, ' ');
    AHost.GMTOffset   := String_(HostOffsetHrsGMTL, ' ');
    AHost.SendCashierToHost      := String_(HostClerkLaneL, ' ');
    AHost.HostTORLate        := String_(HostTORLateL, ' ');
    AHost.HostTORTimeout     := String_(HostTORTimeoutL, ' ');
    AHost.HostTORNoT2        := String_(HostTORNoT2L, ' ');
    AHost.TraceMsgsToLog    := 'Y';                        { TSL-E }
    AHost.ResubmitOfflineForwards    := String_(HostOffResubmitL, ' ');
    AHost.TerminalType   := String_(HostTerminalTypeL, ' ');
    AHost.MerchantNumber    := String_(HostMerchantNumL, ' ');
    AHost.TerminalNumber    := String_(HostTerminalNumL, ' ');
    AHost.HostRetryPhone1    := String_(HostRetryPhone1L, ' ');
    AHost.PrimaryHostPort         := String_(HostPhone1L, ' ');
    AHost.PrimaryHostIPAddress         := String_(HostLogon1L, ' ');
    AHost.HostRetryPhone2    := String_(HostRetryPhone2L, ' ');
    AHost.BackupHostPort         := String_(HostPhone2L, ' ');
    AHost.BackupHostIPAddress         := String_(HostLogon2L, ' ');
    AHost.KeepCheckHistory    := String_(HostRetryPhone3L, ' ');
    AHost.BackupGCHostIP         := String_(HostPhone3L, ' ');
    AHost.PrimaryGCHostIP         := String_(HostLogon3L, ' '); // **************************
    AHost.HostModemInit      := String_(HostModemInitL, ' ');
    AHost.LXHostTimout       := '00015';
    AHost.HostInitMain       := String_(HostInitSelfL, ' ');
    AHost.TORInitSelfName        := String_(HostInitSelfL, ' ');
    AHost.OfflineInitSelfName    := String_(HostInitSelfL, ' ');
    AHost.OfflineInitSelfName      := String_(HostConfigNumL, ' ');
    AHost.HostSecondScrName  := String_(HostSecondScrNameL, ' ');
    AHost.HealthMessageInterval   := '0300';
    {
    For I := Low(HostTransactions) to High(HostTransactions) do
      HostTransactions[I] := 'N';
    }
    AHost.TransactionAllowed := DupeString('N', MaxRealTenders);
    {
    For I := 1 To 10 do
      HostTimeout[I] := '45';
    }
    AHost.OnlineTimout := DupeString('45', 11); // DEV-10078 was 10
    AHost.HostTruncateAcctNo := DupeString('Y', 11); // DEV-10078
    AHost.OfflineForwardDelay    := '00060';  { secs before starting queue after comm up }
    AHost.OfflineForwardSecondsBetweenForwards   := '00015';    { delay between sending transactions }
    AHost.TORCycleDelay     := '00120';    { how often to check TOR queue }
    AHost.OfflineForwardCycleDelay     := '00600';    { how often to check ofline queue }
    AHost.Suffix         := String_(HostSuffixL, ' ');
    AHost.HostPort2          := String_(HostPortL, ' ');
    AHost.LXHostSlotNumAndTimout          := String_(HostBaudL, ' ');
    AHost.HostBits2          := String_(HostBitsL, ' ');
    AHost.HostParity2        := String_(HostParityL, ' ');
    AHost.HostStop2          := String_(HostStopL, ' ');
    AHost.HostIRQ2           := String_(HostIRQL, ' ');
    AHost.HostPort3          := String_(HostPortL, ' ');
    AHost.LXHostOnlineStartingSlotNum          := String_(HostBaudL, ' ');
    AHost.HostBits3          := String_(HostBitsL, ' ');
    AHost.HostParity3        := String_(HostParityL, ' ');
    AHost.HostStop3          := String_(HostStopL, ' ');
    AHost.HostIRQ3           := String_(HostIRQL, ' ');
    AHost.HostSetForDayLight := 'N'; // DEV-7993
    AHost.PhoneCardMagStripe  := String_(HostSaveCheckInfoL, ' ');
    AHost.PartialBINDownload  := String_(HostPartialBINDllL, ' ');   { TSL-F }
    AHost.PBTHostMerchantID  := String_(HostACHStateCodesL, ' ');
    AHost.DynamicCurrConv := 'N'; // TFS-9730
    result := true;
  except
    ;
  end;
end;

procedure TXMLStoreConfigurations.CopyHost(aSourceHost, aTargetHost: StoreConfigurationsXML.TXMLHostType); // DEV-12505
begin
  if NOT Assigned(aSourceHost) or NOT Assigned(aTargetHost) then
    Exit;

  //aTargetHost.Prefix := aSourceHost.Prefix;
  //aTargetHost.Suffix := aSourceHost.Suffix;
  aTargetHost.RetryCounter := aSourceHost.RetryCounter;
  aTargetHost.OfflineProcessing := aSourceHost.OfflineProcessing;
  aTargetHost.SimulatedDelay := aSourceHost.SimulatedDelay;
  aTargetHost.WorkingKey := aSourceHost.WorkingKey;
  aTargetHost.UseEBCDIC := aSourceHost.UseEBCDIC;
  aTargetHost.MerchantNum := aSourceHost.MerchantNum;
  aTargetHost.Password := aSourceHost.Password;
  aTargetHost.ChkAuthType := aSourceHost.ChkAuthType;
  aTargetHost.ChkCode := aSourceHost.ChkCode;
  aTargetHost.ChkDefaultStateCode := aSourceHost.ChkDefaultStateCode;
  aTargetHost.VerifyDebitPrefixOnline := aSourceHost.VerifyDebitPrefixOnline;
  aTargetHost.VerifyDebitPrefixOffline := aSourceHost.VerifyDebitPrefixOffline;
  aTargetHost.LXHostSlotNumbers := aSourceHost.LXHostSlotNumbers;
  aTargetHost.StoreNumber := aSourceHost.StoreNumber;
  aTargetHost.TerminalID := aSourceHost.TerminalID;
  aTargetHost.HostManagmentInfo := aSourceHost.HostManagmentInfo;
  aTargetHost.MerchantType := aSourceHost.MerchantType;
  aTargetHost.HostID := aSourceHost.HostID;
  aTargetHost.MerchantName := aSourceHost.MerchantName;
  aTargetHost.MerchantAddress := aSourceHost.MerchantAddress;
  aTargetHost.MerchantCity := aSourceHost.MerchantCity;
  aTargetHost.MerchantState := aSourceHost.MerchantState;
  aTargetHost.MerchantZipcode := aSourceHost.MerchantZipcode;
  aTargetHost.MerchantCountry := aSourceHost.MerchantCountry;
  aTargetHost.CurrencyCode := aSourceHost.CurrencyCode;
  aTargetHost.NumericStateCode := aSourceHost.NumericStateCode;
  aTargetHost.NumericCountryCode := aSourceHost.NumericCountryCode;
  aTargetHost.GMTOffset := aSourceHost.GMTOffset;
  aTargetHost.SendCashierToHost := aSourceHost.SendCashierToHost;
  aTargetHost.TraceMsgsToLog := aSourceHost.TraceMsgsToLog;
  aTargetHost.ResubmitOfflineForwards := aSourceHost.ResubmitOfflineForwards;
  aTargetHost.TerminalType := aSourceHost.TerminalType;
  aTargetHost.MerchantNumber := aSourceHost.MerchantNumber;
  aTargetHost.TerminalNumber := aSourceHost.TerminalNumber;
  aTargetHost.PrimaryHostPort := aSourceHost.PrimaryHostPort;
  aTargetHost.PrimaryHostIPAddress := aSourceHost.PrimaryHostIPAddress;
  aTargetHost.BackupHostPort := aSourceHost.BackupHostPort;
  aTargetHost.BackupHostIPAddress := aSourceHost.BackupHostIPAddress;
  aTargetHost.KeepCheckHistory := aSourceHost.KeepCheckHistory;
  aTargetHost.PrimaryGCHostIP := aSourceHost.PrimaryGCHostIP;
  aTargetHost.BackupGCHostIP := aSourceHost.BackupGCHostIP;
  aTargetHost.LXHostTimout := aSourceHost.LXHostTimout;
  aTargetHost.TransactionAllowed := aSourceHost.TransactionAllowed;
  aTargetHost.OnlineTimout := aSourceHost.OnlineTimout;
  aTargetHost.OfflineForwardDelay := aSourceHost.OfflineForwardDelay;
  aTargetHost.OfflineForwardSecondsBetweenForwards := aSourceHost.OfflineForwardSecondsBetweenForwards;
  aTargetHost.TORCycleDelay := aSourceHost.TORCycleDelay;
  aTargetHost.OfflineForwardCycleDelay := aSourceHost.OfflineForwardCycleDelay;
  aTargetHost.SendHealthMessage := aSourceHost.SendHealthMessage;
  aTargetHost.HealthMessageInterval := aSourceHost.HealthMessageInterval;
  aTargetHost.TakeWICChecksLocally := aSourceHost.TakeWICChecksLocally;
  aTargetHost.LXHostSlotNumAndTimout := aSourceHost.LXHostSlotNumAndTimout;
  aTargetHost.LXHostOnlineStartingSlotNum := aSourceHost.LXHostOnlineStartingSlotNum;
  aTargetHost.PhoneCardMagStripe := aSourceHost.PhoneCardMagStripe;
  aTargetHost.PartialBINDownload := aSourceHost.PartialBINDownload;
  aTargetHost.PBTHostMerchantID := aSourceHost.PBTHostMerchantID;
  aTargetHost.FTPUserHName := aSourceHost.FTPUserHName;
  aTargetHost.FTPIPAddress := aSourceHost.FTPIPAddress;
  aTargetHost.FTPPassword := aSourceHost.FTPPassword;
  aTargetHost.FTPFileName := aSourceHost.FTPFileName;
  aTargetHost.AddressLines := aSourceHost.AddressLines;
  aTargetHost.ProcessingMode := aSourceHost.ProcessingMode;
  aTargetHost.SendSwipedDLInfo := aSourceHost.SendSwipedDLInfo;
  aTargetHost.PrimaryLocalPort := aSourceHost.PrimaryLocalPort;
  aTargetHost.BackupLocalPort := aSourceHost.BackupLocalPort;
  aTargetHost.LocalIPforTerminalConnect := aSourceHost.LocalIPforTerminalConnect;
  aTargetHost.FTPInterval := aSourceHost.FTPInterval;
  aTargetHost.SettlementReport := aSourceHost.SettlementReport;
  aTargetHost.ForceTenderOffline := aSourceHost.ForceTenderOffline;
  aTargetHost.ReceiptFileTransferType := aSourceHost.ReceiptFileTransferType;
  aTargetHost.ReceiptHostPrimaryIPAddress := aSourceHost.ReceiptHostPrimaryIPAddress;
  aTargetHost.ReceiptHostPrimaryFTPPort := aSourceHost.ReceiptHostPrimaryFTPPort;
  aTargetHost.ReceiptHostBackupIPAddress := aSourceHost.ReceiptHostBackupIPAddress;
  aTargetHost.ReceiptHostBackupFTPPort := aSourceHost.ReceiptHostBackupFTPPort;
  aTargetHost.ReceiptHostUserID := aSourceHost.ReceiptHostUserID;
  aTargetHost.ReceiptHostPassword := aSourceHost.ReceiptHostPassword;
  aTargetHost.ReceiptHostFileName := aSourceHost.ReceiptHostFileName;
  aTargetHost.ReceiptHostFTPPath := aSourceHost.ReceiptHostFTPPath;
  aTargetHost.TruncateAcctNo := aSourceHost.TruncateAcctNo;
  aTargetHost.SendRawMICR := aSourceHost.SendRawMICR;
  aTargetHost.HostSuspendCt := aSourceHost.HostSuspendCt;
  aTargetHost.HostFatalCt := aSourceHost.HostFatalCt;
  aTargetHost.HostResumeCt := aSourceHost.HostResumeCt;
  aTargetHost.HostOnTOxxxx := aSourceHost.HostOnTOxxxx;
  aTargetHost.Host_Comm_NRZI := aSourceHost.Host_Comm_NRZI;
  aTargetHost.Host_Main_Session := aSourceHost.Host_Main_Session;
  aTargetHost.Host_TOR_Session := aSourceHost.Host_TOR_Session;
  aTargetHost.Host_Offl_Session := aSourceHost.Host_Offl_Session;
  aTargetHost.HostPort := aSourceHost.HostPort;
  aTargetHost.HostBits := aSourceHost.HostBits;
  aTargetHost.HostParity := aSourceHost.HostParity;
  aTargetHost.HostStop := aSourceHost.HostStop;
  aTargetHost.HostIRQ := aSourceHost.HostIRQ;
  aTargetHost.HostMgtInfoData := aSourceHost.HostMgtInfoData;
  aTargetHost.HostTORLate := aSourceHost.HostTORLate;
  aTargetHost.HostTORTimeout := aSourceHost.HostTORTimeout;
  aTargetHost.HostTORNoT2 := aSourceHost.HostTORNoT2;
  aTargetHost.HostRetryPhone1 := aSourceHost.HostRetryPhone1;
  aTargetHost.HostRetryPhone2 := aSourceHost.HostRetryPhone2;
  aTargetHost.HostModemInit := aSourceHost.HostModemInit;
  aTargetHost.HostInitMain := aSourceHost.HostInitMain;
  aTargetHost.TORInitSelfName := aSourceHost.TORInitSelfName;
  aTargetHost.OfflineInitSelfName := aSourceHost.OfflineInitSelfName;
  aTargetHost.HostConfigNum := aSourceHost.HostConfigNum;
  aTargetHost.HostSecondScrName := aSourceHost.HostSecondScrName;
  aTargetHost.FTP_BINFileFromHost := aSourceHost.FTP_BINFileFromHost;
  aTargetHost.HostDivision := aSourceHost.HostDivision;
  aTargetHost.HostPort2 := aSourceHost.HostPort2;
  aTargetHost.HostBits2 := aSourceHost.HostBits2;
  aTargetHost.HostParity2 := aSourceHost.HostParity2;
  aTargetHost.HostStop2 := aSourceHost.HostStop2;
  aTargetHost.HostIRQ2 := aSourceHost.HostIRQ2;
  aTargetHost.HostPort3 := aSourceHost.HostPort3;
  aTargetHost.HostBits3 := aSourceHost.HostBits3;
  aTargetHost.HostParity3 := aSourceHost.HostParity3;
  aTargetHost.HostStop3 := aSourceHost.HostStop3;
  aTargetHost.HostIRQ3 := aSourceHost.HostIRQ3;
  aTargetHost.HostSetForDayLight := aSourceHost.HostSetForDayLight;
  aTargetHost.HostTruncateAcctNo := aSourceHost.HostTruncateAcctNo;
  aTargetHost.HostPlatformID := aSourceHost.HostPlatformID;
  aTargetHost.HostClearingCode := aSourceHost.HostClearingCode;
  aTargetHost.HostMerchBIN := aSourceHost.HostMerchBIN;
  aTargetHost.HostMerchICA := aSourceHost.HostMerchICA;
  aTargetHost.HostIPLocalPort3 := aSourceHost.HostIPLocalPort3;
  aTargetHost.HostLastBINFtpDate := aSourceHost.HostLastBINFtpDate;
  aTargetHost.HostProgramId := aSourceHost.HostProgramId;
  aTargetHost.HostFTPDir := aSourceHost.HostFTPDir;
  aTargetHost.HostERCUse := aSourceHost.HostERCUse;
  aTargetHost.FreqShopReqOfflineDebit := aSourceHost.FreqShopReqOfflineDebit;
  aTargetHost.UseSSL := aSourceHost.UseSSL;
  aTargetHost.ConnectPayEnabled := aSourceHost.ConnectPayEnabled;
  aTargetHost.ConnectPayPrefix := aSourceHost.ConnectPayPrefix;
  aTargetHost.HealthStatusProbationInterval := aSourceHost.HealthStatusProbationInterval;
  aTargetHost.HealthStatusNotOkInterval := aSourceHost.HealthStatusNotOkInterval;
  aTargetHost.ProxyEnabled := aSourceHost.ProxyEnabled;
  aTargetHost.ProxyAddress := aSourceHost.ProxyAddress;

  aTargetHost.Use3DES := aSourceHost.Use3DES;
  aTargetHost.ReceivingInstituionId := aSourceHost.ReceivingInstituionId;
  aTargetHost.ForwardAllChecksOnline := aSourceHost.ForwardAllChecksOnline;
  aTargetHost.ForwardOtherChecks := aSourceHost.ForwardOtherChecks;
  aTargetHost.ForwardWICChecks := aSourceHost.ForwardWICChecks;
  aTargetHost.MerchantPhoneNumber := aSourceHost.MerchantPhoneNumber;
  aTargetHost.UseOtherHostForOfflineProcessing := aSourceHost.UseOtherHostForOfflineProcessing;
  aTargetHost.NumOfDaysToKeepOffline := aSourceHost.NumOfDaysToKeepOffline;
  aTargetHost.NetworkRoutingValue := aSourceHost.NetworkRoutingValue;

  aTargetHost.FloodStartTime := aSourceHost.FloodStartTime;
  aTargetHost.FloodEndTime := aSourceHost.FloodEndTime;
  aTargetHost.FloodOfflineForwardSecondsBetweenForwards := aSourceHost.FloodOfflineForwardSecondsBetweenForwards;
  aTargetHost.OfflineForwardSecondsBetweenResubmits := aSourceHost.OfflineForwardSecondsBetweenResubmits;

  aTargetHost.DynamicCurrConv := aSourceHost.DynamicCurrConv; // TFS-9730
end;

function TXMLStoreConfigurations.FindReceiptText(storeNumber: string): TXMLReceiptTextType;
var
  i: integer;
begin
  result := nil;
  {
  if Trim(storeNumber) = '' then
    storeNumber := DEFAULT_STORE_NUMBER;
  }
  for I := 0 to XMLObj.ReceiptTexts.Count -1 do
    //if SameText(Trim(XMLObj.ReceiptTexts[i].storeNumber), storeNumber) then
    if StrToIntDef(XMLObj.ReceiptTexts[i].storeNumber, DEFAULT_STORE_NUMBER)
        = StrToIntDef(storeNumber, DEFAULT_STORE_NUMBER) then
    begin
      result := XMLObj.ReceiptTexts[I];
      Exit;
    end;
end;

function TXMLStoreConfigurations.AddReceiptText: TXMLReceiptTextType;
begin
  result := XMLObj.ReceiptTexts.Add;
  //if Assigned(result) then OnNewReceiptTexts(result);
end;

{ ---------------------------------------------------------------------------- }

constructor TXMLCardProcessingProfile.Create;
begin
  if Assigned(XMLObj) then
    FreeAndNil(XMLObj);
  XMLObj := NewCardProcessingProfile;
  if Assigned(XMLObj) then
  begin
    //XMLObj.OwnerDocument.Options := XMLObj.OwnerDocument.Options + [doNodeAutoCreate, doNodeAutoIndent];
    Cards := XMLObj;
  end;
  Active := Assigned(XMLObj);
end;

constructor TXMLCardProcessingProfile.Create(aFileName: string);
begin
  try
    if Trim(aFileName) <> '' then
      LoadFromFile(aFileName);
    if not Active then
    begin
      SM('ERROR! TXMLCardProcessingProfile.Create - Fail to load ' + FileName);
{$IFNDEF MTXEPSDLL} // DOEP-57142: OpenEPS should stop processing, how about engine?
      Create;
{$ENDIF}
      Exit;
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLCardProcessingProfile.Create ' + e.message);
  end;
end;

destructor TXMLCardProcessingProfile.Destroy;
begin
  Cards := nil;
  if Assigned(XMLObj) then
    FreeAndNil(XMLObj);
  inherited;
end;

function TXMLCardProcessingProfile.LoadFromFile(aFileName: string): boolean;
var
  tmpStr: string;
begin
  result := false;
  try
    if ExtractFileDir(aFileName) = '' then
    begin
      {$IFDEF MTXEPSDLL}
      FileName := Trim(DefaultDir + aFileName);
      {$ELSE} // ENGINE, GUI
      FileName := Trim(DefaultDir + CONFIG_DIR + CARDPROFILES_DIR + aFileName);
      {$ENDIF}
    end
    else
      FileName := Trim(aFileName);
    if not FileExists(FileName) then
    begin
      tmpStr := FileName + ' does not exist!' + #13 + 'Consult technical support please.';
      SM('WARNING: ' + tmpStr);
    end;
    if Assigned(XMLObj) then
      FreeAndNil(XMLObj);
    XMLObj := LoadCardProcessingProfile(FileName);
    Active := Assigned(XMLObj);
    if Assigned(XMLObj) then
    begin
      //XMLObj.OwnerDocument.Options := XMLObj.OwnerDocument.Options + [doNodeAutoCreate, doNodeAutoIndent];
      Cards := XMLObj;
      //result := XMLObj.OwnerDocument.Active;
      if XMLObj.Version = '' then XMLObj.Version := DEFAULT_XML_VERSION;
      ValidateVersion;
    end;
    result := Active;
    SM('TXMLCardProcessingProfile.LoadFromFile - Active=' + TrueFalse(result) + '/ FileName=' + FileName);
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLCardProcessingProfile.LoadFromFile: ' + FileName + ' - ' + e.message);
  end;
end;

function TXMLCardProcessingProfile.SaveToFile(aFileName: string): boolean;
begin
  result := false;
  try
    if not Active then
    begin
      SM('WARNING! TXMLCardProcessingProfile.SaveToFile: Active = False');
      Exit;
    end;
    if ExtractFileDir(aFileName) = '' then
    begin
      {$IFDEF MTXEPSDLL}
      aFileName := Trim(DefaultDir + aFileName);
      {$ELSE}
      aFileName := Trim(DefaultDir + CONFIG_DIR + CARDPROFILES_DIR + aFileName);
      {$ENDIF}
    end
    else
      aFileName := Trim(aFileName);
    XMLObj.LastModified := FormatDateTime(FORMAT_LASTMODIFIED, Now);
    XMLObj.Version := FormatFloat(FORMAT_XML_VERSION, GetValidXMLVersion(xfCardProcessingProfile));
    //XMLObj.OwnerDocument.SaveToFile(aFileName);
    XMLObj.SaveToFile(aFileName);
    SM('TXMLCardProcessingProfile.SaveToFile: saved to ' + aFileName);
    result := true;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLCardProcessingProfile.SaveToFile: ' + FileName + ' - ' + e.message);
  end;
end;

function TXMLCardProcessingProfile.ValidateVersion: boolean;
var
  ver: Double;
  sVer: string;                                           // 33046
begin
  // TODO: need to convert XMLObj to double
  ver := GetValidXMLVersion(xfCardProcessingProfile);
  sVer := ConvertToDecimalSeparator(XMLObj.Version);     // 33046
  result := StrToFloatDef(sVer,0) = ver;                 // 33046
  if not result then
    SM(Format('%s%s Version (%s) is %s valid (should be %.1f)',
        [iif(not result, '***WARNING ',''), ExtractFileName(fileName), sVer, iif(not result, 'not', ''), ver]));
end;

function TXMLCardProcessingProfile.FindFCT(CardCode: string): TXMLCardType;
var
  I: integer;
begin
  result := nil;
  for I := 0 to XMLObj.Count -1 do
    if SameText(XMLObj.Card[I].Code, CardCode) then
    begin
      result := XMLObj.Card[I];
      Exit;
    end;
end;

{ ---------------------------------------------------------------------------- }

constructor TXMLCashiers.Create;
begin
  XMLObj := NewCashiers;
  if Assigned(XMLObj) then
  begin
    XMLObj.OwnerDocument.Options := XMLObj.OwnerDocument.Options + [doNodeAutoCreate, doNodeAutoIndent];
    Cashiers := XMLObj;
    Active := XMLObj.OwnerDocument.Active;
  end;
end;

constructor TXMLCashiers.Create(aFileName: string);
begin
  try
    if Trim(aFileName) <> '' then
      Active := LoadFromFile(aFileName);
    if not Active then
    begin
      SM('ERROR! TXMLCashiers.Create - Fail to load ' + FileName);
      Create;
      Exit;
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLCashiers.Create ' + e.message);
  end;
end;

function TXMLCashiers.LoadFromFile(aFileName: string): boolean;
begin
  result := false;
  try
    if ExtractFileDir(aFileName) = '' then
      FileName := Trim(DefaultDir + aFileName)
    else
      FileName := Trim(aFileName);
    if not FileExists(FileName) then
    begin
      SM('WARNING: ' + FileName + ' does not exist!' + #13 + 'Consult technical support please.');
      XMLObj := NewCashiers;
    end
    else
      XMLObj := LoadCashiers(FileName);
    if Assigned(XMLObj) then
    begin
      XMLObj.OwnerDocument.Options := XMLObj.OwnerDocument.Options + [doNodeAutoCreate, doNodeAutoIndent];
      Cashiers := XMLObj;
      result := XMLObj.OwnerDocument.Active;
      if result then
      begin
        if XMLObj.OwnerDocument.Version = '' then
          XMLObj.OwnerDocument.Version := DEFAULT_XML_VERSION;
        ValidateVersion;
      end;
      SM('TXMLCashiers.LoadFromFile - ' + FileName);
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLCashiers.LoadFromFile: ' + FileName + ' - ' + e.message);
  end;
end;

function TXMLCashiers.SaveToFile(aFileName: string): boolean;
begin
  result := false;
  try
    if not Active then
    begin
      SM('WARNING! TXMLCashiers.SaveToFile: Active = False');
      Exit;
    end;
    if ExtractFileDir(aFileName) = '' then
      aFileName := Trim(DefaultDir + aFileName)
    else
      aFileName := Trim(aFileName);
    XMLObj.LastModified := FormatDateTime(FORMAT_LASTMODIFIED, Now);
    XMLObj.Version := FormatFloat(FORMAT_XML_VERSION, GetValidXMLVersion(xfCashiers));
    XMLObj.OwnerDocument.SaveToFile(aFileName);
    SM('TXMLCashiers.SaveToFile: saved to ' + aFileName);
    result := true;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLCashiers.SaveToFile: ' + FileName + ' - ' + e.message);
  end;
end;

function TXMLCashiers.ValidateVersion: boolean;
var
  ver: Double;
  sVer: string;
begin
  // TODO: need to convert XMLObj to double
  ver := GetValidXMLVersion(xfCashiers);
  sVer := ConvertToDecimalSeparator(XMLObj.Version);     // 33046
  result := StrToFloatDef(sVer, 0) = ver;
  if not result then
    SM(Format('%s%s Version (%s) is %s valid (should be %.1f)',
        [iif(not result, '***WARNING ',''), ExtractFileName(fileName), sVer, iif(not result, 'not', ''), ver]));
end;

function TXMLCashiers.FindCashier(CashierNumber: Integer): IXMLCashierType;
var
  i: integer;
begin
  result := nil;
  for i := 0 to XMLObj.Count-1 do
    if XMLObj.Cashier[I].Number = CashierNumber then
    begin
      result := XMLObj.Cashier[i];
      exit;
    end;
end;

function TXMLCashiers.DeleteCashier(CashierNumber: Integer): boolean;
var
  i: integer;
begin
  result := false;
  for i := 0 to XMLObj.Count-1 do
    if XMLObj.Cashier[i].Number = CashierNumber then
    begin
      XMLObj.Delete(i);
      result := true;
      exit;
    end;
end;

function TXMLCashiers.AddCashier: IXMLCashierType;
begin
  result := XMLObj.Add;
end;

function ValidateCashier(cashier: string): Boolean; // XE: Remove WinEPS - copied from Cashstuf to remove Cashstuf from OpenEPS
begin
  result := false;
  try
    if Assigned(XMLCashiers) then
      CashBuf := XMLCashiers.FindCashier(StrToIntDef(cashier, -1));
    if Assigned(CashBuf) then
    begin
      SM('ValidateCashier: Found Cashier >' + cashier + '<');
      result := true;
    end;

    if not result then
      SM('**** WARNING! UNABLE TO LOCATE CASHIER ' + Cashier);
  except
    on e: exception do
      SM('Try..Except: CashiersXML.ValidateCashier - ' + e.message);
  end;
end;

{ ---------------------------------------------------------------------------- }

//constructor TXMLManagers.Create;
//begin
//  XMLObj := NewManagers;
//  if Assigned(XMLObj) then
//  begin
//    XMLObj.OwnerDocument.Options := XMLObj.OwnerDocument.Options + [doNodeAutoCreate, doNodeAutoIndent];
//    Managers := XMLObj;
//    Active := XMLObj.OwnerDocument.Active;
//  end;
//end;
//
//constructor TXMLManagers.Create(aFileName: string);
//begin
//  try
//    if Trim(aFileName) <> '' then
//      Active := LoadFromFile(aFileName);
//    if not Active then
//    begin
//      SM('ERROR! TXMLManagers.Create - Fail to load ' + FileName);
//      Create;
//      Exit;
//    end;
//  except
//    on e: exception do
//      SM('****TRY..EXCEPT: TXMLManagers.Create ' + e.message);
//  end;
//end;
//
//function TXMLManagers.LoadFromFile(aFileName: string): boolean;
//begin
//  result := false;
//  try
//    if ExtractFileDir(aFileName) = '' then
//      FileName := Trim(DefaultDir + aFileName)
//    else
//      FileName := Trim(aFileName);
//    if not FileExists(FileName) then
//      SM('WARNING: TXMLManagers.LoadFromFile - ' + FileName + ' does not exist!' + #13 +
//         'Consult technical support please.');
//    XMLObj := LoadManagers(FileName);
//    if Assigned(XMLObj) then
//    begin
//      XMLObj.OwnerDocument.Options := XMLObj.OwnerDocument.Options + [doNodeAutoCreate, doNodeAutoIndent];
//      Managers := XMLObj;
//      result := XMLObj.OwnerDocument.Active;
//      if result then
//      begin
//        if XMLObj.OwnerDocument.Version = '' then
//          XMLObj.OwnerDocument.Version := DEFAULT_XML_VERSION;
//        ValidateVersion;
//      end;
//      SM('TXMLManagers.LoadFromFile - ' + FileName);
//    end;
//  except
//    on e: exception do
//      SM('****TRY..EXCEPT: TXMLManagers.LoadFromFile: ' + FileName + ' - ' + e.message);
//  end;
//end;
//
//function TXMLManagers.SaveToFile(aFileName: string): boolean;
//begin
//  result := false;
//  try
//    if not Active then
//    begin
//      SM('WARNING! TXMLManagers.SaveToFile: Active = False');
//      Exit;
//    end;
//    if ExtractFileDir(aFileName) = '' then
//      aFileName := Trim(DefaultDir + aFileName)
//    else
//      aFileName := Trim(aFileName);
//    XMLObj.LastModified := FormatDateTime(FORMAT_LASTMODIFIED, Now);
//    XMLObj.Version := FormatFloat(FORMAT_XML_VERSION, GetValidXMLVersion(xfManagers));
//    XMLObj.OwnerDocument.SaveToFile(aFileName);
//    SM('TXMLManagers.SaveToFile: saved to ' + aFileName);
//    result := true;
//  except
//    on e: exception do
//      SM('****TRY..EXCEPT: TXMLManagers.SaveToFile: ' + FileName + ' - ' + e.message);
//  end;
//end;
//
//function TXMLManagers.ValidateVersion: boolean;
//var
//  ver: Double;
//  sVer: string;                    // 33046
//begin
//  // TODO: need to convert XMLObj to double
//  ver := GetValidXMLVersion(xfManagers);
//  sVer := ConvertToDecimalSeparator(XMLObj.Version);     // 33046
//  result := StrToFloatDef(sVer, 0) = ver;
//  if not result then
//    SM(Format('%s%s Version (%s) is %s valid (should be %.1f)',
//        [iif(not result, '***WARNING ',''), ExtractFileName(fileName), sVer, iif(not result, 'not', ''), ver]));
//end;
//
//function TXMLManagers.FindManager(ManagerNumber: Integer): IXMLManagerType;
//var
//  I: integer;
//begin
//  result := nil;
//  for I := 0 to XMLObj.Count -1 do
//    if XMLObj.Manager[I].Number = ManagerNumber then
//    begin
//      result := XMLObj.Manager[I];
//      Exit;
//    end;
//end;
//
//function TXMLManagers.DeleteManager(ManagerNumber: Integer): boolean;
//var
//  i: integer;
//begin
//  result := false;
//  for i := 0 to XMLObj.Count-1 do
//    if XMLObj.Manager[i].Number = ManagerNumber then
//    begin
//      XMLObj.Delete(i);
//      result := true;
//      exit;
//    end;
//end;
//
//function TXMLManagers.AddManager: IXMLManagerType;
//begin
//  result := XMLObj.Add;
//end;

{ ---------------------------------------------------------------------------- }

constructor TXMLLanes.Create;
begin
  XMLObj := LanesXML.NewLanes;
  if Assigned(XMLObj) then
  begin
    Lanes := XMLObj;
  end;
  Active := Assigned(XMLObj);
end;

constructor TXMLLanes.Create(aFileName: string);
begin
  try
    inherited Create;
    if Trim(aFileName) <> '' then
      Active := LoadFromFile(aFileName);
    if not Active then
    begin
      SM('ERROR! TXMLLanes.Create - Fail to load ' + FileName);
{$IFNDEF MTXEPSDLL} // DOEP-57142: OpenEPS should stop processing, how about engine?
      Create;
{$ENDIF}
      Exit;
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLLanes.Create ' + e.message);
  end;
end;

destructor TXMLLanes.Destroy;
begin
  XMLObj.Free;
  Lanes := nil;
  inherited;
end;

function TXMLLanes.LoadFromFile(aFileName: string): boolean;
begin
  result := false;
  try
    if ExtractFileDir(aFileName) = '' then
      FileName := Trim(DefaultDir + aFileName)
    else
      FileName := Trim(aFileName);
    if not FileExists(FileName) then
      SM('WARNING: TXMLLanes.LoadFromFile - ' + FileName + ' does not exist!' + #13 +
         'Consult technical support please.');
    XMLObj := LoadLanes(FileName);
    if Assigned(XMLObj) then
    begin
      Lanes := XMLObj;
      Active := true;
      result := Active;
      if XMLObj.Version = '' then
        XMLObj.Version := DEFAULT_XML_VERSION;
      ValidateVersion;
      SM('TXMLLanes.LoadFromFile - ' + FileName);
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLLanes.LoadFromFile: ' + FileName + ' - ' + e.message);
  end;
end;

function TXMLLanes.SaveToFile(aFileName: string): boolean;
begin
  result := false;
  try
    if not Active then
    begin
      SM('WARNING! TXMLLanes.SaveToFile: Active = False');
      Exit;
    end;
    if ExtractFileDir(aFileName) = '' then
      aFileName := Trim(DefaultDir + aFileName)
    else
      aFileName := Trim(aFileName);
    XMLObj.LastModified := FormatDateTime(FORMAT_LASTMODIFIED, Now);
    XMLObj.Version := FormatFloat(FORMAT_XML_VERSION, GetValidXMLVersion(xfLanes));
    XMLObj.SaveToFile(aFileName);
    SM('TXMLLanes.SaveToFile: saved to ' + aFileName);
    result := true;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLLanes.SaveToFile: ' + FileName + ' - ' + e.message);
  end;
end;

function TXMLLanes.ValidateVersion: boolean;
var
  ver: Double;
  sVer: string;              // 33046
begin
  // TODO: need to convert XMLObj to double
  ver := GetValidXMLVersion(xfLanes);
  sVer := ConvertToDecimalSeparator(XMLObj.Version);     // 33046
  result := StrToFloatDef(sVer, 0) = ver;
  if not result then
    SM(Format('%s%s Version (%s) is %s valid (should be %.1f)',
        [iif(not result, '***WARNING ',''), ExtractFileName(fileName), sVer, iif(not result, 'not', ''), ver]));
end;

function TXMLLanes.FindLane(SearchValue: Variant; SearchOption: TLaneSearchOption): LanesXML.TXMLLaneType;
var
  I: integer;
begin
  result := nil;
  if not Assigned(Lanes) then
    exit;

  try
    for I := 0 to Lanes.Count -1 do
      case SearchOption of
        soLaneNumber:
          begin
          if StrToIntDef(Lanes.Lane[I].Number, -1) = StrToInt(VarToStrDef(SearchValue, '-1')) then
          begin
            result := Lanes.Lane[I];
            Exit;
          end;
          end;
        soTermType:
          if SameText(Lanes.Lane[I].TermType, VarToStrDef(SearchValue, '')) then
          begin
            result := Lanes.Lane[I];
            Exit;
          end;
      end;

    if NOT Assigned(result) then
      case SearchOption of
        soLaneNumber: SM(Format('****WARNING: TXMLLanes.FindLane - FAILED TO FIND LANE for LaneNumber >%s<', [VarToStrDef(SearchValue, '')]));
        soTermType: SM(Format('****WARNING: TXMLLanes.FindLane - FAILED TO FIND LANE for TermType >%s<', [VarToStrDef(SearchValue, '')]))
      end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: MTX_XMLClasses.TTXMLLanes.FindLane ' + e.message);
  end;
end;

function TXMLLanes.AddLane: LanesXML.TXMLLaneType;
begin
  result := XMLObj.Add;
end;

function TXMLLanes.DeleteLane(LaneNumber: Integer): boolean;
var
  i: integer;
begin
  result := false;
  for i := 0 to XMLObj.Count-1 do
    if StrToIntDef(XMLObj.Lane[i].Number, -1) = LaneNumber then
    begin
      XMLObj.Delete(i);
      result := true;
      exit;
    end;
end;

function TXMLLanes.GetLaneType(laneNumber: integer): string;
var
  i: integer;
begin
  result := '';
  for i := 0 to XMLObj.Count-1 do
    if StrToIntDef(XMLObj.Lane[i].Number, -1) = LaneNumber then
    begin
      result := XMLObj.Lane[i].LaneType;
      exit;
    end;
end;

{ ---------------------------------------------------------------------------- }

//constructor TXMLReportGroups.Create;
//begin
//  XMLObj := NewReportGroups;
//  if Assigned(XMLObj) then
//  begin
//    XMLObj.OwnerDocument.Options := XMLObj.OwnerDocument.Options + [doNodeAutoCreate, doNodeAutoIndent];
//    SetVars;
//    Active := XMLObj.OwnerDocument.Active;
//  end;
//end;
//
//constructor TXMLReportGroups.Create(aFileName: string);
//begin
//  try
//    inherited Create;
//    if Trim(aFileName) <> '' then
//      Active := LoadFromFile(aFileName);
//    if not Active then
//    begin
//      SM('ERROR! TXMLReportGroups.Create - Fail to load ' + FileName);
//      Create;
//      Exit;
//    end;
//    //XMLObj.OwnerDocument.Options := XMLObj.OwnerDocument.Options + [doNodeAutoCreate, doNodeAutoIndent];
//  except
//    on e: exception do
//      SM('****TRY..EXCEPT: TXMLReportGroups.Create ' + e.message);
//  end;
//end;
//
//function TXMLReportGroups.LoadFromFile(aFileName: string): boolean;
//begin
//  result := false;
//  try
//    if ExtractFileDir(aFileName) = '' then
//      FileName := Trim(DefaultDir + aFileName)
//    else
//      FileName := Trim(aFileName);
//    if not FileExists(FileName) then
//      SM('WARNING: TXMLReportGroups.LoadFromFile - ' + FileName + ' does not exist!' + #13 +
//         'Consult technical support please.');
//    XMLObj := LoadReportGroups(FileName);
//    if Assigned(XMLObj) then
//    begin
//      XMLObj.OwnerDocument.Options := XMLObj.OwnerDocument.Options + [doNodeAutoCreate, doNodeAutoIndent];
//      SetVars;
//      result := XMLObj.OwnerDocument.Active;
//      if result then
//      begin
//        if XMLObj.OwnerDocument.Version = '' then
//          XMLObj.OwnerDocument.Version := DEFAULT_XML_VERSION;
//        ValidateVersion;
//      end;
//      SM('TXMLReportGroups.LoadFromFile - ' + FileName);
//    end;
//  except
//    on e: exception do
//      SM('****TRY..EXCEPT: TXMLReportGroups.LoadFromFile: ' + FileName + ' - ' + e.message);
//  end;
//end;
//
//function TXMLReportGroups.SaveToFile(aFileName: string): boolean;
//begin
//  result := false;
//  try
//    if not Active then
//    begin
//      SM('WARNING! TXMLReportGroups.SaveToFile: Active = False');
//      Exit;
//    end;
//    if ExtractFileDir(aFileName) = '' then
//      aFileName := Trim(DefaultDir + aFileName)
//    else
//      aFileName := Trim(aFileName);
//    XMLObj.LastModified := FormatDateTime(FORMAT_LASTMODIFIED, Now);
//    XMLObj.Version := FormatFloat(FORMAT_XML_VERSION, GetValidXMLVersion(xfReportGroups));
//    XMLObj.OwnerDocument.SaveToFile(aFileName);
//    SM('TXMLReportGroups.SaveToFile: saved to ' + aFileName);
//    result := true;
//  except
//    on e: exception do
//      SM('****TRY..EXCEPT: TXMLReportGroups.SaveToFile: ' + FileName + ' - ' + e.message);
//  end;
//end;
//
//procedure TXMLReportGroups.SetVars;
//begin
//  if not Assigned(XMLObj) then Exit;
//  RptInfoBuf := XMLObj;
//  StoreSummaryGroupsList   := XMLObj.StoreSummaryGroupsList;
//  LaneSummaryGroupsList    := XMLObj.LaneSummaryGroupsList;
//  CheckerSummaryGroupsList := XMLObj.CheckerSummaryGroupsList;
//  CheckerSignOffGroupsList := XMLObj.CheckerSignOffGroupsList;
//  HostSettlementGroupsList := XMLObj.HostSettlementGroupsList;
//  {set global vars }
//  ReportHeaderText         := XMLObj.ReportHeaderText;
//  ReportPrintControls      := XMLObj.ReportPrintControls;
//  NumberOfEachReportToPrint:= XMLObj.NumberOfEachReportToPrint;
//end;
//
//function TXMLReportGroups.ValidateVersion: boolean;
//var
//  ver: Double;
//  sVer: string;
//begin
//  // TODO: need to convert XMLObj to double
//  ver := GetValidXMLVersion(xfReportGroups);
//  sVer := ConvertToDecimalSeparator(XMLObj.Version);     // 33046
//  result := StrToFloatDef(sVer, 0) = ver;
//  if not result then
//    SM(Format('%s%s Version (%s) is %s valid (should be %.1f)',
//        [iif(not result, '***WARNING ',''), ExtractFileName(fileName), sVer, iif(not result, 'not', ''), ver]));
//end;
//
//function TXMLReportGroups.OnNewReportGroups: boolean;
//begin
//  result := false;
//  try
//    { moved from ntfilini }
//    ReportHeaderText.Line1 := RPad('Your Store Name Here', DSRptInfoLine1L-1);
//    ReportHeaderText.Line2 := RPad('Your Store Address Here', DSRptInfoLine1L-1);
//    ReportHeaderText.Line3 := RPad('Your City, ST ZIP-Suffix', DSRptInfoLine1L-1);
//    ReportHeaderText.Line4 := RPad('Your Phone Number Here', DSRptInfoLine1L-1);
//
//    ReportPrintControls.CheckerIDLengthShown    := '9';
//    ReportPrintControls.ClearCheckerTotalsAt  := 'D';
//    ReportPrintControls.SortCheckerReportBy := '#';
//    ReportPrintControls.ManagerIDLengthShown    := '9';
//    ReportPrintControls.PrintFormfeedAtEnd  := '1';
//
//    NumberOfEachReportToPrint.HostSettlement := 0;
//    NumberOfEachReportToPrint.SalesSummary_StoreTotals := 0;
//    NumberOfEachReportToPrint.SalesSummary_LaneTotals := 0;
//    NumberOfEachReportToPrint.SalesSummary_CheckerTotals := 0;
//    NumberOfEachReportToPrint.VoidedTransactions := 0;
//    NumberOfEachReportToPrint.OfflineActivity := 0;
//    NumberOfEachReportToPrint.OfflineTransactions_ForwardPending := 0;
//    NumberOfEachReportToPrint.OfflineTransactions_ForwardAppr_Rejected := 0;
//    NumberOfEachReportToPrint.ApprovedTransactions := 0;
//    NumberOfEachReportToPrint.DeclinedTransactions := 0;
//    NumberOfEachReportToPrint.DeclinedTransactions_OfflineNotAllowed := 0; // DEV-11199
//    NumberOfEachReportToPrint.TransactionResponseSummary := 0;
//    NumberOfEachReportToPrint.CreditToDebit_Successful := 0;
//    NumberOfEachReportToPrint.CreditToDebit_Unsuccessful := 0;
//    NumberOfEachReportToPrint.InfoMessages := 0;
//    NumberOfEachReportToPrint.TroubleMessages := 0;
//    NumberOfEachReportToPrint.PreAuth_AuthCompletion := 0;
//    NumberOfEachReportToPrint.DetailedCashierReport := 0;
//    NumberOfEachReportToPrint.DetailedLaneReport := 0;
//    NumberOfEachReportToPrint.DetailedTrainingCashierReport := 0;
//    result := true;
//  except
//    ;
//  end;
//end;

{ ---------------------------------------------------------------------------- }

constructor TXMLWinEpsStatus.Create;
begin
  XMLObj := NewWinEpsStatus;
  {
  if Assigned(XMLObj) then
  begin
    XMLObj.OwnerDocument.Options := XMLObj.OwnerDocument.Options + [doNodeAutoCreate, doNodeAutoIndent];
  end;
  }
  Active := Assigned(XMLObj);
end;

constructor TXMLWinEpsStatus.Create(aFileName: string);
begin
  try
    if Trim(aFileName) <> '' then
      Active := LoadFromFile(aFileName);
    if not Active then
    begin
      SM('ERROR! TXMLWinEpsStatus.Create - Fail to load ' + FileName);
      Create;
      Exit;
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLWinEpsStatus.Create ' + e.message);
  end;
end;

function TXMLWinEpsStatus.LoadFromFile(aFileName: string): boolean;
begin

  result := false;
  try
    if ExtractFileDir(aFileName) = '' then
      FileName := Trim(DefaultDir + aFileName)
    else
      FileName := Trim(aFileName);
    if not FileExists(FileName) then
      SM('WARNING: TXMLWinEpsStatus.LoadFromFile - ' + FileName + ' does not exist!' + #13 +
         'Consult technical support please.');
    //if Assigned(XMLObj) then FreeAndNil(XMLObj);
    XMLObj := LoadWinEpsStatus(FileName);
    if Assigned(XMLObj) then
    begin
      XMLObj.OwnerDocument.Options := XMLObj.OwnerDocument.Options + [doNodeAutoCreate, doNodeAutoIndent];
      result := XMLObj.OwnerDocument.Active;
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLWinEpsStatus.LoadFromFile ' + e.message);
  end;

end;

function TXMLWinEpsStatus.SaveToFile(aFileName: string): boolean;
begin
  result := false;
  try
    if not Active then
    begin
      SM('WARNING! TXMLWinEpsStatus.SaveToFile: Active = False');
      Exit;
    end;
    if ExtractFileDir(aFileName) = '' then
      aFileName := Trim(DefaultDir + aFileName)
    else
      aFileName := Trim(aFileName);
    {$IFNDEF TEST}
    XMLObj.LastModified := FormatDateTime(FORMAT_LASTMODIFIED, Now);
    XMLObj.Version := FormatFloat(FORMAT_XML_VERSION, GetValidXMLVersion(xfWinEPSStatus));
    {$ENDIF TEST}
    XMLObj.OwnerDocument.SaveToFile(aFileName);

    SM('TXMLWinEpsStatus.SaveToFile: saved to ' + aFileName);
    //SM('   LastDebitBINUpdate = ' + XMLObj.LocalMachine.LastDebitBINUpdate);
    result := true;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLWinEpsStatus.SaveToFile ' + e.message);
  end;
end;


function TXMLWinEpsStatus.FindLane(SearchValue: Variant; SearchOption: TLaneSearchOption): IXMLLaneStatusType;
var
  I: integer;
begin
  result := nil;
  for I := 0 to (XMLObj.Lanes.Count - 1)  do
    case SearchOption of
      soLaneNumber:
        if StrToIntDef(inttostr(XMLObj.Lanes.Lane[I].Number), -1) =
           StrToInt(VarToStrDef(SearchValue, '-1')) then
        begin
          result := XMLObj.Lanes.Lane[I];
          Exit;
        end;
      soTermType:
        if SameText(XMLObj.Lanes.Lane[I].LaneType, VarToStrDef(SearchValue, '')) then
        begin
          result := XMLObj.Lanes.Lane[I];
          Exit;
        end;
    end;
end;

function TXMLWinEpsStatus.DeleteLane(LaneNumber: Integer): boolean;
var
  i: integer;
begin
  result := false;
  for i := 0 to (XMLObj.Lanes.Count-1) do
    if XMLObj.Lanes.Lane[i].Number = LaneNumber then
    begin
      XMLObj.Lanes.Delete(i);
      result := true;
      exit;
    end;
end;

function TXMLWinEPSStatus.LoadLane(xmlstring: string): IXMLLaneStatusType;
begin
  result := LoadLaneStatus(xmlstring);
end;

{ ---------------------------------------------------------------------------- }

constructor TCPPList.Create(populate: boolean);
begin
  Items := TStringList.Create;
  Items.CaseSensitive := false;
  Items.Duplicates := dupIgnore;
  {$IFDEF MTXEPSDLL}
  //if not Assigned(XMLCardProcessingProfile) then
    FActive := LoadCardProcessingProfileXML(DefaultDir + CardProcessingProfilesFileName);
  {$ELSE}
   FActive := LoadLanesXML(LanesXML_);
  {$ENDIF}
  if populate then
    FActive := PopulateCPPStringList;
end;

destructor TCPPList.Destroy;
begin
  try
    ClearItems;
    Items.Free;
  except
    ;
  end;
//  FreeAndNil(Items);
end;

procedure TCPPList.ClearItems;
begin
  // must free objects explicitly
  while Items.Count > 0 do
  begin
     try
       if Assigned(Items.Objects[0]) then
         Items.Objects[0].Free;
     except
       ;
     end;
     Items.Delete(0);
  end;
end;

function TCPPList.PopulateCPPStringList: Boolean;
{$IFNDEF MTXEPSDLL}
var
  i: integer;
  CPP: TXMLCardProcessingProfile;
  CPPFileName: string;
{$ENDIF}
begin
  result := false;
  SM('PopulateCPPStringList');
  try
    ClearItems;
    {$IFDEF MTXEPSDLL}
    LoadCardProcessingProfileXML(DefaultDir + CardProcessingProfilesFileName); // reload CPP
    {$ELSE}
    for i := 0 to XMLLanes.Lanes.Count -1 do
    begin
      CPPFileName := XMLLanes.Lanes[i].CardProcessingProfileXMLFileName;
      if Items.IndexOf(CPPFileName) = -1 then
      begin
        SM('Adding ' + CPPFileName + ' to CPPList.');
        if (Trim(CPPFileName) <> '') then
          CPP := TXMLCardProcessingProfile.Create(CPPFileName)
        else
          CPP := nil;
        if Assigned(CPP) then
          Items.AddObject(CPPFileName, CPP);
      end;
    end;
    {$ENDIF}
    result := true;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TCPPList.PopulateCPPStringList: ' + e.message);
  end;
end;

function TCPPList.FindCPP(laneNumber: integer): TXMLCardProcessingProfile;
{$IFNDEF MTXEPSDLL} // [Hint] MTX_XMLClasses.pas(2709): Variable 'CPPFileName' is declared but never used in 'TCPPList.FindCPP'
var
  idx: integer;
  aLane: LanesXML.TXMLLaneType;
  CPPFileName: string;
{$ENDIF}
begin
  result := nil;
  try
    {$IFDEF MTXEPSDLL}
    result := XMLCardProcessingProfile;
    {$ELSE} // engine & host dlls
    aLane := XMLLanes.FindLane(laneNumber, soLaneNumber);
    if Assigned(aLane) then
    begin
      CPPFileName := aLane.CardProcessingProfileXMLFileName;
      idx := Items.IndexOf(CPPFileName);
      if idx >= 0 then
        result := TXMLCardProcessingProfile(Items.Objects[idx]);
    end
    else
      SM('FindCPP for lane >' + inttostr(laneNumber) + '< not found!');
    {$ENDIF}
  except
    on e: exception do
      SM('****TRY..EXCEPT: TCPPList.FindCPP: ' + e.message);
  end;
end;

procedure LoadHostXMLFiles;
begin
  try
    SM('LoadHostXMLFiles');
//   JMR: store config is read at init and 'START' (on-the-fly reload)
    if FileExists(StoreConfigurationsXML_) then
      LoadStoreConfigurationsXML(StoreConfigurationsXML_);
    if Assigned(CPPList) then
      FreeAndNil(CPPList);
    CPPList := TCPPList.Create(true); // CPPList loads Lanes.xml, no need to do it again.
    SM('SecCodeVer=' + MTXEncryptionUtils.GetMTXEncryptionFileVersion); // not the perfect place but best place w/o updating host code - initSwitch_XXX will call LoadHostXMLFiles
  except
    on E: Exception do
      SM('MTX_XMLClasses.LoadHostXMLFiles exception.  ' + E.Message);
  end;
end;

procedure UnLoadHostXMLFiles;
begin
  try
    SM('UnLoadHostXMLFiles');
    if Assigned(XMLStoreConfigurations) then FreeAndNil(XMLStoreConfigurations);
    if Assigned(XMLLanes) then FreeAndNil(XMLLanes);
    if Assigned(CPPList) then FreeAndNil(CPPList);
  except
    on E: Exception do
      SM('MTX_XMLClasses.UnLoadHostXMLFiles exception.  ' + E.Message);
  end;
end;

{//$ENDIF}

{ ---------------------------------------------------------------------------- }

constructor TXMLUpdateXMLConfig.Create;
begin
  XMLObj := NewUpdateXMLConfig;
  if Assigned(XMLObj) then
  begin

    XMLObj.OwnerDocument.Options := XMLObj.OwnerDocument.Options + [doNodeAutoCreate, doNodeAutoIndent];
    Actions := XMLObj.Actions;
    Active := XMLObj.OwnerDocument.Active;
  end;
end;

constructor TXMLUpdateXMLConfig.Create(aFileName: string);
begin
  try
    if Trim(aFileName) <> '' then
      Active := LoadFromFile(aFileName);
    if not Active then
    begin
      SM('ERROR! TXMLUpdateXMLConfig.Create - Fail to load ' + FileName);
      //Create;
      Exit;
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLUpdateXMLConfig.Create ' + e.message);
  end;
end;

function TXMLUpdateXMLConfig.LoadFromFile(aFileName: string): boolean;
begin
  result := false;
  try
    if ExtractFileDir(aFileName) = '' then
      FileName := Trim(DefaultDir + aFileName)
    else
      FileName := Trim(aFileName);
    if not FileExists(FileName) then
    begin
      SM('WARNING: ' + FileName + ' does not exist!' + #13 + 'Consult technical support please.');
      XMLObj := NewUpdateXMLConfig;
    end
    else
      XMLObj := LoadUpdateXMLConfig(FileName);
    if Assigned(XMLObj) and (XMLObj.OwnerDocument.DocumentElement.NodeName = 'UpdateXMLConfig') then
    begin
      XMLObj.OwnerDocument.Options := XMLObj.OwnerDocument.Options + [doNodeAutoCreate, doNodeAutoIndent];
      Actions := XMLObj.Actions;
      result := XMLObj.OwnerDocument.Active;
      if result then
      begin
        if XMLObj.OwnerDocument.Version = '' then
          XMLObj.OwnerDocument.Version := DEFAULT_XML_VERSION;
        ValidateVersion;
      end;
      SM('TXMLUpdateXMLConfig.LoadFromFile - ' + FileName);
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLUpdateXMLConfig.LoadFromFile: ' + FileName + ' - ' + e.message);
  end;
end;

function TXMLUpdateXMLConfig.SaveToFile(aFileName: string): boolean;
begin
  result := false;
  try
    if not Active then
    begin
      SM('WARNING! TXMLUpdateXMLConfig.SaveToFile: Active = False');
      Exit;
    end;
    if ExtractFileDir(aFileName) = '' then
      aFileName := Trim(DefaultDir + aFileName)
    else
      aFileName := Trim(aFileName);
    XMLObj.LastModified := FormatDateTime(FORMAT_LASTMODIFIED, Now);
    XMLObj.Version := FormatFloat(FORMAT_XML_VERSION, GetValidXMLVersion(xfUpdateXMLConfig));
    XMLObj.OwnerDocument.SaveToFile(aFileName);
    SM('TXMLUpdateXMLConfig.SaveToFile: saved to ' + aFileName);
    result := true;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLUpdateXMLConfig.SaveToFile: ' + FileName + ' - ' + e.message);
  end;
end;

function TXMLUpdateXMLConfig.ValidateVersion: boolean;
var
  ver: Double;
  sVer: string; // 33046
begin
  // TODO: need to convert XMLObj to double
  ver := GetValidXMLVersion(xfUpdateXMLConfig);
  sVer := ConvertToDecimalSeparator(XMLObj.Version);     // 33046
  result := StrToFloatDef(sVer, 0) = ver;
  if not result then
    SM(Format('%s%s Version (%s) is %s valid (should be %.1f)',
        [iif(not result, '***WARNING ',''), ExtractFileName(fileName), sVer, iif(not result, 'not', ''), ver]));
end;

function TXMLUpdateXMLConfig.FindAction(aXMLPath: string; aActionType: TUpdateActionType): IXMLActionType;
var
  i: integer;
begin
  result := nil;
  for i := 0 to Actions.Count-1 do
    if SameText(Actions.Action[i].XMLPath, aXMLPath)
        and (Actions.Action[i].Type_ = UpdateActionTypes[Ord(aActionType)]) then
    begin
      result := Actions.Action[i];
      exit;
    end;
end;

function TXMLUpdateXMLConfig.DeleteAction(aXMLPath: string; aActionType: TUpdateActionType): boolean;
var
  i: integer;
begin
  result := false;
  for i := 0 to Actions.Count-1 do
    if SameText(Actions.Action[i].XMLPath, aXMLPath)
        and (Actions.Action[i].Type_ = UpdateActionTypes[Ord(aActionType)]) then
    begin
      Actions.Delete(i);
      result := true;
      exit;
    end;
end;

function TXMLUpdateXMLConfig.AddAction: IXMLActionType;
begin
  result := Actions.Add;
end;

// ------------------ TXMLBinUpdateMsgToSEPS

constructor TXMLBinUpdateMsgToSEPS.Create(aCompany, aStore, aBinVersion,
  aAppVersion, aActKey, aIPAddress, aPrevBinFile: string);
begin
  inherited Create;
  FCompany := aCompany;
  FStore := aStore;
  FBinVersion := aBinVersion;
  FAppVersion := aAppVersion;
  FActKey := aActKey;
  FIPAddress := aIPAddress;
  FPrevBinFile := aPrevBinFile;
  FUpdateStatus := '';
  FBinVersion := '';
  FDataSize := '';
  FWaitTime := '';
end;

function TXMLBinUpdateMsgToSEPS.GetWaitTime: integer;
begin
  result := strToIntDef(FWaitTime, 0);
end;

function TXMLBinUpdateMsgToSEPS.GetDataSize: integer;
begin
  result := strToIntDef(FDataSize, 0);
end;

function TXMLBinUpdateMsgToSEPS.MakeXMLReqMsg: string;
var
  Root, N1: TXMLParserNode;
begin
  try
    Root := TXMLParserNode.Create(nil);
    try
      Root.Name := 'BinReqRsp';
      Root.Attr.Values['version'] := SEPSBINVERSION;
      Root.Attr.Values['requestType'] := SEPSFULLBINDLL;
      N1 := Root.AddChild('Company');
      N1.text := FCompany;
      N1 := Root.AddChild('Store');
      N1.text := FStore;
      N1 := Root.AddChild('BinVersion');
      N1.text := FBinVersion;
      N1 := Root.AddChild('Client');
      N1.Attr.Values['version'] := FAppVersion;
      N1.Attr.Values['actKey'] := FActKey;
      N1.Attr.Values['hostAddress'] := FIPAddress;
      N1.Attr.Values['prevBinFile'] := FPrevBinFile;
      result := Root.GetXMLStr;
    finally
      Root.Free;
    end;
  except
    on e: Exception do
      SM('****Try..Except: TXMLBinUpdateMsgToSEPS.MakeXMLReqMsg ' + e.message);
  end;
end;

function TXMLBinUpdateMsgToSEPS.MakeXMLAckMsg(aSizeReceived: string): string;
var Root: TXMLParserNode;
begin
  try
    Root := TXMLParserNode.Create(nil);
    try
      Root.Name := 'BinReqRsp';
      Root.Attr.Values['version'] := SEPSBINVERSION;
      Root.Attr.Values['requestType'] := SEPSBINACK;
      Root.Attr.Values['fileSize'] := aSizeReceived;
      result := Root.GetXMLStr;
    finally
      Root.Free;
    end;
  except
    on e: Exception do
      sm('****Try..Except: TXMLBinUpdateMstToSEPS.MakeXMLAckMsg ' + e.message);
  end;
end;

procedure TXMLBinUpdateMsgToSEPS.ParseSEPSMsg(aXMLMsg: string);
begin
  try
    try
      FXMLParser.LoadFromBuffer(PAnsiChar(AnsiString(aXMLMsg)));                  // 828.5
      FXMLParser.StartScan;                                       // 828.5
      ScanElement(nil);
      if (Root.Name = 'BinReqRsp') then
      begin
        FUpdateStatus := Root.Attr.Values['status'];
        FBinVersion := Root.Attr.Values['binVersion'];
        FDataSize := Root.Attr.Values['dataSize'];
        FWaitTime := Root.Attr.Values['wait'];
      end;
    finally
      FreeAndNil(Root);
    end;
  except
    on e: Exception do
      sm('****Try..Except: TXMLBinUpdateMstToSEPS.ParseSEPSMsg ' + e.message);
  end;
end;

{ ---------------------------------------------------------------------------- }

constructor TXMLBinPrefixes.Create;
begin
  //if Assigned(XMLObj) then
  //  FreeAndNil(XMLObj);
  XMLObj := NewServerEPS_BinFile;
  if Assigned(XMLObj) then
  begin
    //XMLObj.OwnerDocument.Options := XMLObj.OwnerDocument.Options + [doNodeAutoCreate, doNodeAutoIndent];
    SetVars;
    //Active := XMLObj.OwnerDocument.Active;
  end;
  Active := Assigned(XMLObj);
end;

constructor TXMLBinPrefixes.Create(aFileName: string);
begin
  try
//    SM('TXMLBinPrefixes.Create');
    if Trim(aFileName) <> '' then
      LoadFromFile(aFileName);
    if not Active then
    begin
      SM('ERROR! TXMLBinPrefixes.Create - Fail to load ' + FileName);
      Create;
      Exit;
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLBinPrefixes.Create ' + e.message);
  end;
end;

destructor TXMLBinPrefixes.Destroy;
begin
  {
  FreeAndNil(XMLObj);
  DebitBin := nil;
  FSABin := nil;
  PrepaidBin := nil;
  }
  inherited;
end;

function TXMLBinPrefixes.LoadFromFile(aFileName: string): boolean;
var
  tmpStr: string;
begin
  result := false;
  try
    if ExtractFileDir(aFileName) = '' then
      FileName := Trim(DefaultDir + aFileName)
    else
      FileName := Trim(aFileName);
    if not FileExists(FileName) then
    begin
      tmpStr := FileName + ' does not exist!' + #13 + 'Consult technical support please.';
      SM('WARNING: ' + tmpStr);
      XMLObj := NewServerEPS_BinFile;
    end
    else
      XMLObj := LoadServerEPS_BinFile(FileName);
//    if Assigned(XMLObj) then
//      FreeAndNil(XMLObj);
    Active := Assigned(XMLObj);
    if Assigned(XMLObj) then
    begin
      SetVars;
      //if XMLObj.Version = '' then
      //  XMLObj.Version := DEFAULT_XML_VERSION;
      //ValidateVersion;
    end;
    result := Active;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLBinPrefixes.LoadFromFile: ' + FileName + ' - ' + e.message);
  end;
end;

function TXMLBinPrefixes.SaveToFile(aFileName: string): boolean;
begin
  result := false;
  try
    if not Active then
    begin
      SM('WARNING! TXMLBinPrefixes.SaveToFile: Active = False');
      Exit;
    end;
    if ExtractFileDir(aFileName) = '' then
      aFileName := Trim(DefaultDir + aFileName)
    else
      aFileName := Trim(aFileName);
    //XMLObj.LastModified := FormatDateTime(FORMAT_LASTMODIFIED, Now);
    //XMLObj.Version := FormatFloat(FORMAT_XML_VERSION, GetValidXMLVersion(xfBinPrefixes));
    XMLObj.OwnerDocument.SaveToFile(aFileName);
//    SM('TXMLBinPrefixes.SaveToFile: saved to ' + aFileName);
    result := true;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLBinPrefixes.SaveToFile: ' + FileName + ' - ' + e.message);
  end;
end;

procedure TXMLBinPrefixes.SetVars;
begin
  if not Assigned(XMLObj) then Exit;
  DebitBin := XMLObj.DebitBin;
  FSABin := XMLObj.FSABin;
  PrepaidBin := XMLObj.PrepaidBin;
end;

constructor TXMLCardPrefix.Create;
begin
  XMLObj := NewCardPrefix;
  if Assigned(XMLObj) then
    SetVars;
  Active := Assigned(XMLObj);
end;

constructor TXMLCardPrefix.Create(aFileName: string);
begin
  try
    if Trim(aFileName) <> '' then
      LoadFromFile(aFileName);
    if not Active then
    begin
      SM('ERROR! TXMLCardPrefix.Create - Fail to load ' + FileName);
{$IFNDEF MTXEPSDLL} // DOEP-57142: OpenEPS should stop processing, how about engine?
      Create;
{$ENDIF}
      Exit;
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLCardPrefix.Create ' + e.message);
  end;
end;

destructor TXMLCardPrefix.Destroy;
begin
  ;
  inherited;
end;

function TXMLCardPrefix.LoadFromFile(aFileName: string): boolean;
var
  tmpStr: string;
begin
  result := false;
  try
    if ExtractFileDir(aFileName) = '' then
      FileName := Trim(DefaultDir + aFileName)
    else
      FileName := Trim(aFileName);
    if not FileExists(FileName) then
    begin
      tmpStr := FileName + ' does not exist!' + #13 + 'Consult technical support please.';
      SM('WARNING: ' + tmpStr);
      XMLObj := NewCardPrefix;
    end
    else
      XMLObj := LoadCardPrefix(FileName);
    Active := Assigned(XMLObj);
    if Assigned(XMLObj) then
      SetVars;
    result := Active;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLCardPrefix.LoadFromFile: ' + FileName + ' - ' + e.message);
  end;
end;

function TXMLCardPrefix.SaveToFile(aFileName: string): boolean;
begin
  result := false;
  try
    if not Active then
    begin
      SM('WARNING! TXMLCardPrefix.SaveToFile: Active = False');
      Exit;
    end;
    if ExtractFileDir(aFileName) = '' then
      aFileName := Trim(DefaultDir + aFileName)
    else
      aFileName := Trim(aFileName);
    //XMLObj.OwnerDocument.SaveToFile(aFileName);
    XMLObj.SaveToXML(aFileName);
    result := true;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLCardPrefix.SaveToFile: ' + FileName + ' - ' + e.message);
  end;
end;

function TXMLCardPrefix.FindTender(TenderType: string): IXMLTenderType;
var
  i: integer;
  tmpTender: IXMLTenderType;
begin
  result := nil;
  for i := 0 to Tenders.Count -1 do
  begin
    tmpTender := Tenders.Items[i];
    if SameText(tmpTender.Type_, TenderType) then
    begin
      result := tmpTender;
      Exit;
    end;
  end;
end;

function TXMLCardPrefix.FindPrefix(TenderType: string; Data: string; CardLen: string; CardCode: string): IXMLPrefixType;
var
  j: integer;
  tmpTender: IXMLTenderType;
  tmpPrefix: IXMLPrefixType;
begin
  result := nil;
  tmpTender := FindTender(TenderType);
  if Assigned(tmpTender) then
  begin
    for j := 0 to tmpTender.Count -1 do
    begin
      tmpPrefix := tmpTender.Prefix[j];
      if SameText(tmpPrefix.Data, Data) and
         SameText(tmpPrefix.CardLen, CardLen) and
         SameText(tmpPrefix.CardCode, CardCode) then
      begin
        result := tmpPrefix;
        exit;
      end;
    end;
  end;
end;

function TXMLCardPrefix.DeletePrefix(TenderType: string; Data: string; CardLen: string; CardCode: string): Boolean;
var
  j: integer;
  tmpTender: IXMLTenderType;
  tmpPrefix: IXMLPrefixType;
begin
  result := false;
  tmpTender := FindTender(TenderType);
  if Assigned(tmpTender) then
  begin
    for j := 0 to tmpTender.Count -1 do
    begin
      tmpPrefix := tmpTender.Prefix[j];
      if SameText(tmpPrefix.Data, Data) and
         SameText(tmpPrefix.CardLen, CardLen) and
         SameText(tmpPrefix.CardCode, CardCode) then
      begin
        tmpTender.Delete(j);
        result := true;
        exit;
      end;
    end;
  end;
end;

function TXMLCardPrefix.AddPrefix(TenderType: string; Data: string; CardLen: string; CardCode: string; FSACode: string=''): IXMLPrefixType;
var
  tmpTender: IXMLTenderType;
  tmpPrefix: IXMLPrefixType;
begin
  result := nil;
  tmpTender := FindTender(TenderType);
  if NOT Assigned(FindPrefix(TenderType, Data, CardLen, CardCode)) then
  begin
    // if not found, create new tender
    if NOT Assigned(tmpTender) then
    begin
      tmpTender := XMLCardPrefix.Tenders.Add;
      tmpTender.Type_ := TenderType;
    end;
    tmpPrefix := tmpTender.Add;
    tmpPrefix.Data := Data;
    tmpPrefix.CardLen := CardLen;
    tmpPrefix.CardCode := CardCode;
    if FSACode <> '' then
      tmpPrefix.FSACode := FSACode;
    result := tmpPrefix;
  end;
end;

procedure TXMLCardPrefix.SetVars;
begin
  if not Assigned(XMLObj) then Exit;
  Tenders := XMLObj.Tender;
  DebitNetworkIDTable := XMLObj.DebitNetworkIDTable;
end;

{ ---------------------------------------------------------------------------- }


{ ---------------------------------------------------------------------------- }

{ TXMLEMVParms }

constructor TXMLEMVParms.Create;
begin
  XMLObj := NewEMVParms;
  Active := Assigned(XMLObj);
end;

constructor TXMLEMVParms.Create(aFileName: string);
begin
  try
    if Trim(aFileName) <> '' then
      LoadFromFile(aFileName);
    if not Active then
    begin
      SM('ERROR! TXMLEMVParms.Create - Fail to load ' + FileName);
      Create;
      Exit;
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLEMVParms.Create ' + e.message);
  end;
end;

destructor TXMLEMVParms.Destroy;
begin
  inherited;
end;

function TXMLEMVParms.LoadFromFile(aFileName: string): boolean;
var
  tmpStr: string;
begin
  Result := False;
  try
    if ExtractFileDir(aFileName) = '' then
      FileName := Trim(DefaultDir + aFileName)
    else
      FileName := Trim(aFileName);

    if not FileExists(FileName) then
    begin
      tmpStr := FileName + ' does not exist!' + #13 + 'Consult technical support please.';
      SM('WARNING: ' + tmpStr);
      XMLObj := NewEMVParms;
    end
    else
      XMLObj := LoadEMVParms(FileName);

    Active := Assigned(XMLObj);
    Result := Active;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLEMVParms.LoadFromFile: ' + FileName + ' - ' + e.message);
  end;
end;

{ TXMLEMVCapK }

constructor TXMLEMVCapK.Create;
begin
  XMLObj := NewEmvCapK;
  Active := Assigned(XMLObj);
end;

constructor TXMLEMVCapK.Create(aFileName: string);
begin
  try
    if Trim(aFileName) <> '' then
      LoadFromFile(aFileName);
    if not Active then
    begin
      SM('ERROR! TXMLEMVCapK.Create - Fail to load ' + FileName);
      Create;
      Exit;
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLEMVCapK.Create ' + e.message);
  end;
end;

destructor TXMLEMVCapK.Destroy;
begin
  inherited;
end;

function TXMLEMVCapK.LoadFromFile(aFileName: string): boolean;
var
  tmpStr: string;
begin
  Result := False;
  try
    if ExtractFileDir(aFileName) = '' then
      FileName := Trim(DefaultDir + aFileName)
    else
      FileName := Trim(aFileName);

    if not FileExists(FileName) then
    begin
      tmpStr := FileName + ' does not exist!' + #13 + 'Consult technical support please.';
      SM('WARNING: ' + tmpStr);
      XMLObj := NewEmvCapK;
    end
    else
      XMLObj := LoadEmvCapK(FileName);

    Active := Assigned(XMLObj);
    Result := Active;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLEMVCapK.LoadFromFile: ' + FileName + ' - ' + e.message);
  end;
end;

{$IFDEF GUIJR}

initialization
  xLog('MTX_XMLClasses initialization BEGIN');
//  SM('----------------------- MTX_XMLClasses initialization');
  {$IFNDEF LINUX}CoInitialize(nil);{$ENDIF}
  xLog('MTX_XMLClasses initialization END');

finalization
  xLog('MTX_XMLClasses finalization BEGIN');
  try
    try    //JTG need the try..except so that VT2 doesn't crash on exit
      try
        if Assigned(CPPList) then FreeAndNil(CPPList);
        xLog('MTX_XMLClasses CPPList Free OK');
      except on e: exception do
        xLog('MTX_XMLClasses: Unable to Free/Nil CPPList: EXCEPTION - ' + e.message);
      end;
      {$IFDEF TEST} Exit; {$ENDIF}
      try
        if Assigned(XMLStoreConfigurations) then FreeAndNil(XMLStoreConfigurations);       
        xLog('MTX_XMLClasses XMLStoreConfigurations Free OK');
      except on e: exception do
        xLog('MTX_XMLClasses: Unable to Free/Nil XMLStoreConfigurations: EXCEPTION - ' + e.message);
      end;

      try
        if Assigned(XMLCashiers) then FreeAndNil(XMLCashiers);
        xLog('MTX_XMLClasses XMLCashiers Free OK');
      except on e: exception do
        xLog('MTX_XMLClasses: Unable to Free/Nil XMLCashiers: EXCEPTION - ' + e.message);
      end;

      try
        if Assigned(XMLManagers) then FreeAndNil(XMLManagers);
        xLog('MTX_XMLClasses XMLManagers Free OK');
      except on e: exception do
        xLog('MTX_XMLClasses: Unable to Free/Nil XMLManagers: EXCEPTION - ' + e.message);
      end;

      try
        if Assigned(XMLReportGroups) then FreeAndNil(XMLReportGroups);
        xLog('MTX_XMLClasses XMLReportGroups Free OK');
      except on e: exception do
        xLog('MTX_XMLClasses: Unable to Free/Nil XMLReportGroups: EXCEPTION - ' + e.message);
      end;

      try
        if Assigned(XMLBinPrefixes) then FreeAndNil(XMLBinPrefixes);
        xLog('MTX_XMLClasses XMLBinPrefixes Free OK');
      except on e: exception do
        xLog('MTX_XMLClasses: Unable to Free/Nil XMLBinPrefixes: EXCEPTION - ' + e.message);
      end;

      try
        if Assigned(XMLCardProcessingProfile) then FreeAndNil(XMLCardProcessingProfile);   
        xLog('MTX_XMLClasses XMLCardProcessingProfile Free OK');
      except on e: exception do
        xLog('MTX_XMLClasses: Unable to Free/Nil XMLCardProcessingProfile: EXCEPTION - ' + e.message);
      end;

      // TODO: what about XMLCardPrefix         : TXMLCardPrefix;?
      // TODO: what about XMLEMVParms           : TXMLEMVParms;?

    except on e: exception do
      xLog('MTX_XMLClasses EXCEPTION - ' + e.message);
      //SM('----------------------- MTX_XMLClasses EXCEPTION');
    end;
  finally
    {$IFNDEF LINUX}
    xLog('MTX_XMLClasses CoUninitialize...');
    CoUninitialize;
    {$ENDIF}
  end;
  xLog('MTX_XMLClasses finalization END');
  
{$ELSE}

/// This is extracted due to compiler error on closure in initialization.
procedure MtxXmlClassesInitialization;
begin
  ExtendedLog('MTX_XMLClasses Initialization',
    procedure
    begin
{$IFNDEF LINUX}
      CoInitialize(nil);
{$ENDIF}
    end
    );
end;

initialization

MtxXmlClassesInitialization;

finalization

ExtendedLog('MTX_XMLClasses Finalization',
  procedure
  begin
    ExtendedLog('MTX_XMLClasses Finalization CPPList',
      procedure
      begin
        if Assigned(CPPList) then
          FreeAndNil(CPPList);
      end
      );
    ExtendedLog('MTX_XMLClasses Finalization XMLStoreConfigurations',
      procedure
      begin
        if Assigned(XMLStoreConfigurations) then
          FreeAndNil(XMLStoreConfigurations);
      end
      );
//    ExtendedLog('MTX_XMLClasses Finalization XMLCashiers',
//      procedure
//      begin
//        if Assigned(XMLCashiers) then
//          FreeAndNil(XMLCashiers);
//      end
//      );
//    ExtendedLog('MTX_XMLClasses Finalization XMLManagers',
//      procedure
//      begin
//        if Assigned(XMLManagers) then
//          FreeAndNil(XMLManagers);
//      end
//      );
//    ExtendedLog('MTX_XMLClasses Finalization XMLReportGroups',
//      procedure
//      begin
//        if Assigned(XMLReportGroups) then
//          FreeAndNil(XMLReportGroups);
//      end
//      );
    ExtendedLog('MTX_XMLClasses Finalization XMLBinPrefixes',
      procedure
      begin
        if Assigned(XMLBinPrefixes) then
          FreeAndNil(XMLBinPrefixes);
      end
      );
    ExtendedLog('MTX_XMLClasses Finalization XMLCardProcessingProfile',
      procedure
      begin
        if Assigned(XMLCardProcessingProfile) then
          FreeAndNil(XMLCardProcessingProfile);
      end
      );
    // TODO: what about XMLCardPrefix         : TXMLCardPrefix;?
    // TODO: what about XMLEMVParms           : TXMLEMVParms;?
{$IFNDEF LINUX}
    ExtendedLog('MTX_XMLClasses Finalization CoUninitialize',
      procedure
      begin
        CoUninitialize
      end
      );
{$ENDIF}
  end
  );

{$ENDIF GUIJR}

end.
