// (c) MTXEPS, Inc. 1988-2008
{*
v823.0 (patch) 03-12-08 JMR-B Load global XMLResponseCodes only once instead of with every trx.
v824.0 08-10-07 TSL-J Use OpenEPS dir if rsp code file not in WinEPS
v815.3 04-28-05 TSL-I Add HmapRspLongRspCode and HMapRspCashLines5x40LongRspCode
v815.1 01-03-05 TSL-H Only show "cust bal showing" on line 1 of cashier msg,
                      print error message if can't open respcodeFile
v815.0 11-09-04 TSL-G Map ~b ~d in cashLines5x40
v814.2 09-17-04 TSL-F Set MTXRspCode Here intead of each host.
v814 05-05-04 TRI-A  Use XML
v810 04-02-03 JMR-A  Free StringLists.
v810 01-30-03 TSL-E  Change Press Cancel to Cancel/clear in ebt bal
v809 01-30-03 TSL-D  use tStrings - no limit to RspCodes, add fHMapRspHostMsg
v809 07-25-02 TSL-C  Change 'NO' to 'ND'
v808 07-01-02 JMR    Cleanup
v808 03-20-03 TSL-? Change SendMessage(AnyToLst to SM( for one EXE
v807 07-25-01 TSL-B Fix bug in msg for Gift card SHOWAPPROVAL#
v807 05-24-01 TSL-A Add function ReplaceField for ~d and ~b in terminal messages
v807 04-05-01 JMR    Remove VIRTUALPASCAL conditional defines
--------------------Rel 807-----------------------------------------------------
 ** 04/30/98 JGS    put suffix on message about not being able to open...
 ** 02/19/98 JGS    Get rid of DOS in uses
 ** 09/28/97 JGS    Put in a suffix so we can have multiple ones for multi host
 ** 12/11/96 JGS    Chang qty of rsps
 ** 11/21/96 JGS    Change variable names so as not to collide with Map_IRSP
*************************************************************************** }
unit Map_HRsp;

{ ************************************************************************* }
interface
{ ************************************************************************* }

uses
     FinalizationLog,
     Sysutils,
     MTX_Lib,
     MTX_Constants,
     MdMsg,
     UResponseCodes,
    {$IFDEF MTXEPSDLL}
     DllTypes,
    {$ENDIF}
     UXMLCommon;

var
  HMapSuffix: string[3] = 'MTX';

procedure HMapRsp(var InRec : MDMsgRec);
procedure HMapRspHostMsg(var InRec : MDMsgRec; hostMsg   : string255);
function fHMapRspHostMsg(var InRec : MDMsgRec; hostMsg   : string255): boolean;
function HMapRspCashLines5x40(InRec : MDMsgRec; var LineCollection : TLineCollection5; HostMsg: string255): Boolean;
//procedure HMapRspLongRspCode(var InRec : MDMsgRec); // XE: Remove WinEPS - not for OpenEPS
//procedure HMapRspCashLines5x40LongRspCode(InRec : MDMsgRec; var LineCollection : TLineCollection5; HostMsg : string255); // XE: Remove WinEPS - not for OpenEPS
{$IFDEF MTXEPSDLL}
procedure GetExtCustomerDisplay(var aExtPrimeCustDisp, aExtAltCustDisp: String40); // DOEP-71599
procedure SetExtCustomerDisplay(const aExtPrimeCustDisp, aExtAltCustDisp: String40); // TFS-7950
{$ENDIF}

{ ************************************************************************* }
implementation
{ ************************************************************************* }

//uses XPIBaseTerm;                                // TFS-14462

var
  hfound: Boolean;
  UseLongSwRspCode : boolean = false;
  {$IFDEF MTXEPSDLL}
  ExtPrimeCustDisp, ExtAltCustDisp: String40; // DOEP-71599
  {$ENDIF}

procedure HMapRsp(var InRec : MDMsgRec);
var
  rc: TResponseCodes;
  fileLocation: string;
  rcode: TResponseCode;
  SwRspCodeToMap : string;

  procedure MakeND;
  begin
    with InRec do
    begin
      TermRspCode   := 'ND';
      CashPadDisp   := 'DECLINED        ';
      PrimeCustDisp := 'DECLINED            ';
      AltCustDisp   := CONTACTBANK;
    end;
  end;

  procedure UseRespCode(RespCode: TResponseCode);
  begin
    with InRec do
    begin
//      SM('+++DEBUG-JMR: Map_HRsp.HMapRsp.UseRespCode CashierLines1x16 = >' + RespCode.CashierLines1x16.Line1 + '<');
      TermRspCode := RespCode.TerminalAction;
      // if (TermRspCode = 'NP') and Assigned(XPIMXTerminal) and XPIMXTerminal.IsCardEntryChipCard then     // TFS-14462    // TFS-14560 now need NP
      //   TermRspCode := 'ND';                                                                             // TFS-14462    // TFS-14560
      If (TermRspCode[1] = 'A') Then   { TSL-F }
        MTXRspCode := TrxAppWCap;
      CashPadDisp := replaceField(InRec, RespCode.CashierLines1x16.Line1,sizeOf(CashPadDisp) - 1, tCashpad1); { TSL-A }
//      SM('+++DEBUG-JMR: Map_HRsp.HMapRsp.UseRespCode CashPadDisp = >' + CashPadDisp + '<');
      if RespCode.CustomerLines.Count > 0 then
      begin
        if (languageID > 0) then   // languageID is 1 based, but if not set could be zero, customer lines are zero based
          dec(languageID);
        PrimeCustDisp := replaceField(InRec, RespCode.CustomerLines[languageID].Line1, sizeOf(PrimeCustDisp) - 1, tCust1); { TSL-A }
        AltCustDisp := replaceField(InRec, RespCode.CustomerLines[languageID].Line2, sizeOf(AltCustDisp) - 1, tCust2); { TSL-A }
        {$IFDEF MTXEPSDLL}
        ExtPrimeCustDisp := replaceField(InRec, RespCode.CustomerLines[languageID].Line1, sizeOf(ExtPrimeCustDisp) - 1, tCust1); // DOEP-71599
        ExtAltCustDisp := replaceField(InRec, RespCode.CustomerLines[languageID].Line2, sizeOf(ExtAltCustDisp) - 1, tCust2); // DOEP-71599
        {$ENDIF}
      end;
    end;
  end;

begin   { HMapRsp }
  try
//    SM('+++DEBUG-JMR: Map_HRsp.HMapRsp');
    InRec.MTXRspCode := TrxDecGen;   { initialize TSL-F }
    hfound := False;
    {$IFDEF MTXEPSDLL}
      fileLocation := DefaultDir;
      HMapSuffix := 'EPS';
    {$ELSE}
      fileLocation := WinEPSDir + CONFIG_DIR + RESPONSECODES_DIR;
      {$IFDEF FUEL}
      HMapSuffix := 'EPS';
      {$ENDIF FUEL}
    {$ENDIF}
    if UseLongSwRspCode then
      SwRspCodeToMap := InRec.LongSwRspCode
    else
      SwRspCodeToMap := InRec.SwRspCode;

    rc := nil;
    try // JMR-B
      if not Assigned(XMLResponseCodesList) then
        XMLResponseCodesList := TResponseCodesList.Create(fileLocation);
      rc := XMLResponseCodesList.FindResponseCodes(HMapSuffix);
    except
      SM('Exception in HMapRsp creating/loading XMLResponseCodesList from ' + fileLocation);
    end;

    if Assigned(rc) then
    begin
      rcode := rc.LocateByResponseCodeAndCardCode(SwRspCodeToMap, Trim(InRec.CardProcID));
      if rcode <> nil then
      begin
        UseRespCode(rcode);
        hfound := True;
      end
      else
      begin
        rcode := rc.LocateByResponseCodeAndCardCode(SwRspCodeToMap, DEFAULT_CARDCODE);
        if rcode <> nil then
        begin
          UseRespCode(rcode);
          hfound := True;
        end
        else
        begin
          rcode := rc.LocateByResponseCodeAndCardCode(DEFAULT_RESPCODE, Trim(InRec.CardProcID));
          if rcode <> nil then
          begin
            UseRespCode(rcode);
            hfound := True;
          end
          else
          begin
            rcode := rc.LocateByResponseCodeAndCardCode(DEFAULT_RESPCODE, DEFAULT_CARDCODE);
            if rcode <> nil then
            begin
              UseRespCode(rcode);
              hfound := True;
            end
            else
              MakeND;
          end;
        end;
      end;
    end
    else
    begin
      sm('****ERROR:  could not open response code file ' +
        HMapSuffix + ' - can not map response code >' + SwRspCodeToMap + '<');
      MakeND;
    end;
    Inrec.MTXRspCodeN := strToIntDef(InRec.MTXRspCode, 0);
//    SM('+++DEBUG-JMR: Map_HRsp.HMapRsp InRec.CashPadDisp = >' + InRec.CashPadDisp + '<');
  except on e:exception do
    SM('Exception in HMapRsp. ' + e.Message);
  end;
end;   { HMapRsp }

(*  HMapRspHostMsg maps the hostMsg returned from the host to any display msg in
    the host response code table that has 'USE HOST MSG' as its value.
    It also maps any EBT decline msg from the host to the cashpad message.
*)
procedure HMapRspHostMsg(var InRec : MDMsgRec;
                         hostMsg   : string255);
var
  i : integer;
begin
//  SM('===DEBUG-JMR: Map_HRsp.HMapRspHostMsg = >' + hostMsg + '< InRec.CashPadDisp = ' + InRec.CashPadDisp);
  HMapRsp(InRec);
//  SM('===DEBUG-JMR: Map_HRsp.HMapRspHostMsg after HMapRsp = >' + hostMsg + '< InRec.CashPadDisp = ' + InRec.CashPadDisp);
  for i := 1 to length(hostMsg) do
    if (ord(hostMsg[i]) < 32) then
      hostMsg[i] := ' ';

  with InRec do
  begin
    if (trim(CashPadDisp) = USEHOSTMSG)                                or
       ((hostMsg <> '') and (InRec.ReqCodeN in EBT_Set) and (InRec.TermRspCode[1] <> 'A')) then  // DEV-8000
      CashPadDisp := rpad(hostMsg, sizeof(CashPadDisp) - 1);
    if (trim(PrimeCustDisp) = USEHOSTMSG) then
      PrimeCustDisp := rpad(hostMsg, sizeof(PrimeCustDisp) - 1);
    if (trim(AltCustDisp) = USEHOSTMSG) then
      AltCustDisp := rpad(hostMsg, sizeof(AltCustDisp) - 1);
  end;
end;

function fHMapRspHostMsg(var InRec : MDMsgRec;
                         hostMsg   : string255): boolean;
begin
//  SM('===DEBUG-JMR: fHMapRspHostMsg = ' + hostMsg);
  HMapRspHostMsg(InRec, hostMsg);
  result := hfound;
end;

function HMapRspCashLines5x40(InRec          : MDMsgRec;
                              var LineCollection : TLineCollection5;
                              HostMsg        : string255): Boolean;
var
  rc: TResponseCodes;
  fileLocation: string;
  rcode: TResponseCode;
  SwRspCodeToMap : string;

  function repUseHostMsg(inStr: string): string;
  begin
    with inRec do
    begin
      if (trim(inStr) = USEHOSTMSG) and
         (Trim(hostMsg) <> '')      then
        result := hostMsg
      else
        result := inStr;
    end;
  end;   { repUseHostMsg }

begin
  try
//    SM('===DEBUG-JMR: Map_HRsp.HMapRspCashLines5x40 = >' + hostMsg + '<');
    {$IFDEF MTXEPSDLL}
      fileLocation := DefaultDir;
    {$ELSE}
      fileLocation := WinEPSDir + CONFIG_DIR + RESPONSECODES_DIR;
    {$ENDIF}

    rc := nil;
    try //JMR-B
      if not Assigned(XMLResponseCodesList) then
        XMLResponseCodesList := TResponseCodesList.Create(fileLocation);
      rc := XMLResponseCodesList.FindResponseCodes(InRec.HostSuffixCode);
    except
      SM('Exception in HMapRspCashLines5x40 creating/loading XMLResponseCodesList from ' + fileLocation);
    end;

    if Assigned(rc) then
    begin
      if UseLongSwRspCode then
        SwRspCodeToMap := InRec.LongSwRspCode
      else
        SwRspCodeToMap := InRec.SwRspCode;
      rcode := rc.LocateByResponseCodeAndCardCode(SwRspCodeToMap, Trim(InRec.CardProcID));
      if rcode <> nil then
      begin
        LineCollection.Assign(rcode.CashierLines5x40);
        Result := True;
      end
      else
      begin
        rcode := rc.LocateByResponseCodeAndCardCode(SwRspCodeToMap, DEFAULT_CARDCODE);
        if rcode <> nil then
        begin
          LineCollection.Assign(rcode.CashierLines5x40);
          Result := True;
        end
        else
        begin
          rcode := rc.LocateByResponseCodeAndCardCode(DEFAULT_RESPCODE, Trim(InRec.CardProcID));
          if rcode <> nil then
          begin
            LineCollection.Assign(rcode.CashierLines5x40);
            Result := True;
          end
          else
          begin
            rcode := rc.LocateByResponseCodeAndCardCode(DEFAULT_RESPCODE, DEFAULT_CARDCODE);
            if rcode <> nil then
            begin
              LineCollection.Assign(rcode.CashierLines5x40);
              Result := True;
            end
            else
              Result := False;
          end;
        end;
      end;
    end
    else
      result := false;

    if Result then
    with LineCollection do
    begin
      Line1 := replaceField(InRec, Line1, SizeOf(Line1) - 1, tCashpad1);
      Line2 := replaceField(InRec, Line2, SizeOf(Line2) - 1, tCashpad2);
      Line3 := replaceField(InRec, Line3, SizeOf(Line3) - 1, tCashpad2);
      Line4 := replaceField(InRec, Line4, SizeOf(Line4) - 1, tCashpad2);
      Line5 := replaceField(InRec, Line5, SizeOf(Line5) - 1, tCashpad2);
      Line1 := repUseHostMsg(Line1);
      Line2 := repUseHostMsg(Line2);
      Line3 := repUseHostMsg(Line3);
      Line4 := repUseHostMsg(Line4);
      Line5 := repUseHostMsg(Line5);
    end;
  except
    on e:exception do
    begin
      result := false;
      SM('Exception in HMapRspCashLines5x40. ' + e.Message);
    end;
  end;
end;

(* // XE: Remove WinEPS - not for OpenEPS
procedure HMapRspLongRspCode(var InRec : MDMsgRec);
begin
//  SM('===DEBUG-JMR: HMapRspLongRspCode = ' + InRec.HostTextMsg);
  UseLongSwRspCode := true;
  if (trim(InRec.HostTextMsg) <> '') then
    HMapRspHostMsg(InRec, InRec.HostTextMsg)
  else
    HMapRsp(InRec);
  UseLongSwRspCode := false;
end;    { HMapRspWfName }
*)

(* // XE: Remove WinEPS - not for OpenEPS
procedure HMapRspCashLines5x40LongRspCode(InRec : MDMsgRec; var LineCollection : TLineCollection5; HostMsg : string255);
begin
//  SM('===DEBUG-JMR: HMapRspCashLines5x40LongRspCode = ' + HostMsg);
  UseLongSwRspCode := true;
  HMapRspCashLines5x40(inRec, LineCollection, HostMsg);
  UseLongSwRspCode := false;
end;   { HMapCashLines5x40WfName }
*)
{$IFDEF MTXEPSDLL}
procedure GetExtCustomerDisplay(var aExtPrimeCustDisp, aExtAltCustDisp: String40); // DOEP-71599
begin
  aExtPrimeCustDisp := ExtPrimeCustDisp;
  aExtAltCustDisp := ExtAltCustDisp;
end;

procedure SetExtCustomerDisplay(const aExtPrimeCustDisp, aExtAltCustDisp: String40); // TFS-7950
begin
  ExtPrimeCustDisp := aExtPrimeCustDisp;
  ExtAltCustDisp := aExtAltCustDisp;
end;
{$ENDIF}

initialization
  ExtendedLog('Map_HRsp Initialization');
finalization
  ExtendedLog('Map_HRsp Finalization');

end.
