{ LANERCPT.INC                                                         }
{                                                                      }
{ Transaction receipt file, 2 for each terminal CUSTXX.EFT DRAWRXX.EFT }
(*
v814.0 02-23-04 TSL-I Add Str_PreActivate
v809.2 12-12-02 Add line for WinEPS Seq#
v809 08-14-12 TSL-H Add tip amount for all receipts
 05-03-03  TSL-01 Add DeActive
 10-30-00  TSL-G Changes for Lynk
 10-20-00  TSL-F Add PIN Used, beginbal for Gift card
 08-15-00  TSL-E Add Str_Activate
 07-18-00  TSL-D Add Str_Balance
 07-07-99  TSL-C Add Bashas PO# to receipt if used and not blank
 05-08-98  TSL-B Add Forced to description of tran type
 06-11-97  JGS-B Move last 4 items in large receipt 1 left, too long
 06-08-97  JGS-A Remove double wide char from do not dispense line
 05-16-97  TSL-A Changes to reduce receipt size for 150 & other stuff
*)
{$A-}

  Change_Ribbon_Color          = Char(18);
  DoubleWide                   = Chr($1E);

  Str_Blank20      : String[20] = '                    ';
  Str_EBTFood      : String[09] = 'EBT Food ';
  Str_SNAP         : String[09] = 'SNAP     '; // DOEP-71726, TFS ID 7752, Should be 'SNAP' for Target rather than 'EBT Food'
  Str_Cash         : String[09] = 'EBT Cash ';
  Str_Checking     : String[09] = 'Checking ';
  Str_Savings      : String[09] = 'Savings  ';
  Str_Return       : String[09] = 'Return   ';
  Str_VoidRetrn    : String[09] = 'VoidRetrn';   { TSL-G }
  Str_Recharge     : String[09] = 'Recharge ';   { TSL-E }
  Str_VoidRechg    : String[09] = 'VoidRechg';
  Str_PreAuth      : String[09] = 'PreAuth  ';
  Str_VoidPreAuth  : string[09] = 'VoidPreAu';
  Str_Purchase     : String[09] = 'Purchase ';
  Str_VoidPurch    : String[09] = 'VoidPurch';   { TSL-G }
  Str_Balance      : String[09] = 'Balance  ';   { TSL-D }
  Str_Activate     : String[09] = 'Activate ';   { TSL-E }
  Str_PreActivate  : String[09] = 'PreActive';
  Str_ReActivate   : String[09] = 'ReActive '; // DOEP-66588 
  Str_Deactivation : String[09] = 'DeActive ';   { TSL-01 }
  Str_Refresh      : String[09] = 'Refresh  ';
  Str_Voice        : String[09] = 'FrcPurch ';
  Str_BeginBal     : String[09] = 'BeginBal ';   { TSL-F }
  Str_EndBal       : String[09] = 'End Bal  ';   { TSL-F }
  Str_BalRemain    : String[10] = 'Bal Remain';  // CPCLIENTS-14925
  Str_Total        : String[17] = 'Total            ';
  Str_ExpDate      : String[13] = 'Exp Date   / ';
  Str_Case         : String[4]  = 'Case';
  Str_Declined     : String[8]  = 'DECL    ';
  Str_Approved     : String[8]  = 'APPROVED';
  Str_AvailBal     : String[19] = 'Available Balance $';
  Str_PinUsed      : String[8]  = 'PIN Used';           { TSL-F }
  Str_SubTotal     : String[9]  = 'SubTotal ';
  Str_MPS_EBT_Purhcase: String[20] = 'EBT Food Purchase'; // CPCLIENTS-14663
  Str_BlankAmountLine: String[8]  = '________';

  L_Str_Declined0  : String[22] = {Change_Ribbon_Color +} ' ---  DECLINED  ---';
  L_Str_Declined1  : String[36] = {Change_Ribbon_Color +} '**DO NOT USE AS PROOF OF PURCHASE**';
  L_Str_Declined2  : String[36] = {Change_Ribbon_Color +} '';
  L_Str_Declined3  : String[36] = {Change_Ribbon_Color +} '';
  L_Str_Declined4  : String[36] = {Change_Ribbon_Color +} '';

  L_Str_Training   : String[38] = 'Training Receipt -- Not Valid Purchase';

  L_Str_BalInq1    : String[36] = ' **DO NOT USE AS PROOF OF PURCHASE**';   { TSL-D }
  L_Str_BalInq2    : String[36] = ' ';

  { ----------------------------------------------------------------------- }
  {   Definitions For Large Regular Receipts                                }
  { ----------------------------------------------------------------------- }

  L_Rcpt_Map_Lines   = 17; //CPCLIENTS-1422
  L_Rcpt_Line_Len    = 38{40};
  L_Max_Name_Len     = 29;

  L_Receipt_A  :  Array[1..L_Rcpt_Map_Lines] of String[L_Rcpt_Line_Len] = ( //CPCLIENTS-1422

  {            1         2         3         Actual  }
  {   12345678901234567890123456789012345678 Position}
  {           1         2         3          RcptBuf }
  {   23456789012345678901234567890123456789 Position}

     '                                       ', { 01 }
     '                 #                     ', { 02 }
     ' Auth #          Account Type: primary ', { 03 }
     ' Lane #          Cashier #             ', { 04 }
     ' xx/xx/xx hh:mm  Ref/Seq #             ', { 05 }   { TSL-G }
     ' EPS Sequence    #                     ', { 06 }
     ' Purchase   $      .                   ', { 07 }
     ' Cashback   $      .                   ', { 08 }
     ' Merch Fee  $      .                   ', { 09 }
     ' Tip        $      .                   ', { 10 }
     '              --------                 ', { 11 }
     ' Total      $      .                   ', { 12 }
     '                                       ', { 13 }
     '                                       ', { 14 }
     '                                       ', { 15 }
     ' Signature:___________________________ ', { 16 }
     ' :                                     ');{ 17 }

  L_Receipt_B  :  Array[1..L_Rcpt_Map_Lines] of String[L_Rcpt_Line_Len] = (

  {            1         2         3         Actual  }
  {   12345678901234567890123456789012345678 Position}
  {           1         2         3          RcptBuf }
  {   23456789012345678901234567890123456789 Position}

     '                                      ', { 01 }
     '                 #                    ', { 02 }
     ' Auth #          Payment from primary ', { 03 }
     ' Lane #          Cashier #            ', { 04 }
     ' xx/xx/xx hh:mm  Ref/Seq #            ', { 05 }   { TSL-G }
     ' EPS Sequence    #                    ', { 06 }
     ' Purchase   $      .                  ', { 07 }
     ' Cashback   $      .                  ', { 08 }
     ' Merch Fee  $      .                  ', { 09 }
     ' Tip        $      .                  ', { 10 }
     '              --------                ', { 11 }
     ' Total      $      .                  ', { 12 }
     '                                      ', { 13 }
     '                                      ', { 14 }
     '                                      ', { 15 }
     ' Signature:___________________________', { 16 }
     ' :                                    ');{ 17 }


  L_Credit_Verbage_Lines = 3;
  L_Credit_Verbage : Array[1..L_Credit_Verbage_Lines] of String[L_Rcpt_Line_Len] = (
     '   I AGREE TO PAY ABOVE TOTAL AMOUNT  ',
     '   ACCORDING TO CARD ISSUER AGREEMENT ',
     '(MERCHANT AGREEMENT IF CREDIT VOUCHER)');

  L_Defs : Array[1..45] of Byte = ( //CPCLIENTS-1422
  { L_Rcpt_Trx_Type_Line   }  1, // CPCLIENTS-5230
  { L_Rcpt_Trx_Type_Pos    } 15,
  { L_Rcpt_Purch_Amt_Line  }  7,
  { L_Rcpt_Purch_Amt_Pos   } 15,
  { L_Rcpt_Cashb_Amt_Line  }  8,
  { L_Rcpt_Cashb_Amt_Pos   } 15,
  { L_Rcpt_Fee_Amt_Line    }  9,
  { L_Rcpt_Fee_Amt_Pos     } 15,
  { L_Rcpt_Tip_Amt_Line    } 10,
  { L_Rcpt_Tip_Amt_Pos     } 15,
  { L_Rcpt_Underscore_Line } 11,
  { L_Rcpt_Total_Amt_Line  } 12,
  { L_Rcpt_Amt_Desc_Pos    }  2,
  { L_Rcpt_Total_Amt_Pos   } 15,
  { L_Rcpt_Pin_Used_Line   }  1,
  { L_Rcpt_Pin_Used_Pos    } 30,
  { L_Rcpt_Card_Type_Line  }  2,
  { L_Rcpt_Card_Type_Pos   }  2,
  { L_Rcpt_Acct_Line       }  2,
  { L_Rcpt_Acct_Pos        } 20,
  { L_Rcpt_Auth_Line       }  3,
  { L_Rcpt_Auth_Pos        }  9,
  { L_Rcpt_From_Line       }  3,
  { L_Rcpt_From_Where_Pos  } 31,
  { L_Rcpt_From_Pos        } 18,
  { L_Rcpt_Exp_Date_Line   }  3,
  { L_Rcpt_Exp_Date_Pos    } 18,
  { L_Rcpt_Lane_Line       }  4,
  { L_Rcpt_Lane_Pos        }  9,
  { L_Rcpt_Cashier_Line    }  4,
  { L_Rcpt_Cashier_Pos     } 28,
  { L_Rcpt_Date_Line       }  5,
  { L_Rcpt_Date_Pos        }  2,
  { L_Rcpt_Time_Line       }  5,
  { L_Rcpt_Time_Pos        } 11,
  { L_Rcpt_Ref_Line        }  5,
  { L_Rcpt_Ref_Pos         } 28,
  { L_Rcpt_WinEPSSeq_Line  }  6,
  { L_Rcpt_WinEPSSeq_Pos   } 20,
  (* // YHJ-286
  { L_Rcpt_MerchantNum_Line} 13,                                                { YHJ-286 < }
  { L_Rcpt_MerchantNum_Pos }  7,
  { L_Rcpt_TerminalNum_Pos } 19,
  { L_Rcpt_CardProcID_Pos }  26,                                                { YHJ-286 > }
  *)
  { L_Rcpt_Blank_To_Sig_1  } 13,
  { L_Rcpt_Blank_To_Sig_2  } 14,
  { L_Rcpt_Blank_To_Sig_3  } 15,
  { L_Rcpt_Sig_Line        } 16,
  { L_Rcpt_Name_Line       } 17,
  { L_Rcpt_Name_Pos        } 12);

  L_Defs_B : Array[1..45] of Byte = (

  { L_Rcpt_Trx_Type_Line   }  1, // CPCLIENTS-5230
  { L_Rcpt_Trx_Type_Pos    } 15,
  { L_Rcpt_Purch_Amt_Line  }  7,
  { L_Rcpt_Purch_Amt_Pos   } 15,
  { L_Rcpt_Cashb_Amt_Line  }  8,
  { L_Rcpt_Cashb_Amt_Pos   } 15,
  { L_Rcpt_Fee_Amt_Line    }  9,
  { L_Rcpt_Fee_Amt_Pos     } 15,
  { L_Rcpt_Tip_Amt_Line    } 10,
  { L_Rcpt_Tip_Amt_Pos     } 15,
  { L_Rcpt_Underscore_Line } 11,
  { L_Rcpt_Total_Amt_Line  } 12,
  { L_Rcpt_Amt_Desc_Pos    }  2,
  { L_Rcpt_Total_Amt_Pos   } 15,
  { L_Rcpt_Pin_Used_Line   }  1,
  { L_Rcpt_Pin_Used_Pos    } 30,
  { L_Rcpt_Card_Type_Line  }  2,
  { L_Rcpt_Card_Type_Pos   }  2,
  { L_Rcpt_Acct_Line       }  2,
  { L_Rcpt_Acct_Pos        } 20,
  { L_Rcpt_Auth_Line       }  3,
  { L_Rcpt_Auth_Pos        }  9,
  { L_Rcpt_From_Line       }  3,
  { L_Rcpt_From_Where_Pos  } 31,
  { L_Rcpt_From_Pos        } 18,
  { L_Rcpt_Exp_Date_Line   }  3,
  { L_Rcpt_Exp_Date_Pos    } 18,
  { L_Rcpt_Lane_Line       }  4,
  { L_Rcpt_Lane_Pos        }  9,
  { L_Rcpt_Cashier_Line    }  4,
  { L_Rcpt_Cashier_Pos     } 28,
  { L_Rcpt_Date_Line       }  5,
  { L_Rcpt_Date_Pos        }  2,
  { L_Rcpt_Time_Line       }  5,
  { L_Rcpt_Time_Pos        } 11,
  { L_Rcpt_Ref_Line        }  5,
  { L_Rcpt_Ref_Pos         } 28,
  { L_Rcpt_WinEPSSeq_Line  }  6,
  { L_Rcpt_WinEPSSeq_Pos   } 20,
  (* // YHJ-286
  { L_Rcpt_MerchantNum_Line} 13,                                                { YHJ-286 < }
  { L_Rcpt_MerchantNum_Pos }  7,
  { L_Rcpt_TerminalNum_Pos } 19,
  { L_Rcpt_CardProcID_Pos }  26,                                                { YHJ-286 > }
  *)
  { L_Rcpt_Blank_To_Sig_1  } 13,
  { L_Rcpt_Blank_To_Sig_2  } 14,
  { L_Rcpt_Blank_To_Sig_3  } 15,
  { L_Rcpt_Sig_Line        } 16,
  { L_Rcpt_Name_Line       } 17,
  { L_Rcpt_Name_Pos        } 12);


  BashasPO_Pos           = 2;   // TSL-C
  L_EBT_Map_Lines        = 19;

  L_EBT_Rcpt    :  Array[1..L_EBT_Map_Lines] of String[L_Rcpt_Line_Len] = (

  {            1         2         3         Actual  }
  {   12345678901234567890123456789012345678 Position}
  {           1         2         3          RcptBuf }
  {   23456789012345678901234567890123456789 Position}
     ' Card_Type_Desc                       ', { 01 }
     ' Card #                               ', { 02 }
     ' Beginning Balance $                  ', { 03 }
     ' Purchase  Amount  $       .          ', { 04 }
     ' Cashback  Amount  $       .          ', { 05 }
     ' Merch Fee Amount  $       .          ', { 06 }
     ' Tip       Amount  $       .          ', { 07}
     '                    ----------        ', { 08 }
     ' EBT Cash  Balance $                  ', { 09 }
     '                                      ', { 10 }
     ' Other     Balance $       .          ', { 11 }
     '                                      ', { 12 }
     ' Auth #                               ', { 13 }
     ' Lane #           Cashier #           ', { 14 }
     ' xx/xx/xx hh:mm   Ref/Seq #           ', { 15 }
     ' EPS Sequence     #                   ', { 16 }
     ' ******* DO NOT DISPENSE CASH ******* ', { 17 }
     ' Signature:___________________________', { 18 }
     '                                      ');{ 19 } //CPCLIENTS-19133 Removed unwanted ':' from EBT receipt

  L_EBT_Defs : Array[1..48] of Byte = (

  { L_EBT_Card_Type_Line   }  1,
  { L_EBT_Card_Type_Pos    }  2,
  { L_EBT_Acct_Line        }  2,
  { L_EBT_Case_Pos         }  2,
  { L_EBT_Acct_Pos         }  9,
  { L_EBT_Pin_Used_Line    }  2,        { TSL-F }
  { L_EBT_Pin_Used_Pos     } 30,
  { L_EBT_Begin_Bal_Line   }  3,
  { L_EBT_Begin_Bal_Pos    } 23,
  { L_EBT_Purch_Amt_Line   }  4,
  { L_EBT_Purch_Amt_Pos    } 23,
  { L_EBT_Purch_Type_Pos   }  2,
  { L_EBT_CashB_Amt_Line   }  5,
  { L_EBT_CashB_Amt_Pos    } 23,
  { L_EBT_Fee_Amt_Line     }  6,
  { L_EBT_Fee_Amt_Pos      } 23,
  { L_EBT_Tip_Amt_Line     }  7,
  { L_EBT_Tip_Amt_Pos      } 23,
  { L_EBT_Underscore_Line  }  8,
  { L_EBT_End_Bal_Line     }  9,
  { L_EBT_End_Bal_Hdr_Pos  }  2,
  { L_EBT_End_Bal_Pos      } 23,
  { L_EBT_Other_Bal_Line   } 11,
  { L_EBT_Other_Bal_Hdr_Pos}  2,
  { L_EBT_Other_Bal_Pos    } 23,
  { L_EBT_Auth_Line        } 13,
  { L_EBT_Auth_Pos         }  9,
  { L_EBT_Exp_Date_Line    } 13,
  { L_EBT_Exp_Date_Pos     } 19,
  { L_EBT_Lane_Line        } 14,
  { L_EBT_Lane_Pos         }  9,
  { L_EBT_Cashier_Line     } 14,
  { L_EBT_Cashier_Pos      } 29,
  { L_EBT_Date_Line        } 15,
  { L_EBT_Date_Pos         }  2,
  { L_EBT_Time_Line        } 15,
  { L_EBT_Time_Pos         } 11,
  { L_EBT_Ref_Line         } 15,
  { L_EBT_Ref_Pos          } 28,         { TSL-G }
  { L_EBT_WinEPSSeq_Line   } 16,
  { L_EBT_WinEPSSeq_Pos    } 21,
  { L_EBT_No_Disp_Cash_1   } 17,
  { L_EBT_No_Disp_Cash_2   } 17,
  { L_EBT_No_Disp_Cash_3   } 17,
  { L_EBT_No_Disp_Cash_4   } 17,
  { L_EBT_Sig_Line         } 18,
  { L_EBT_Name_Line        } 19,
  { L_EBT_Name_Pos         } 13);


  { SMALL RECEIPTS }

  S_Rcpt_Map_Lines   = 21;
  S_Rcpt_Line_Len    = 21;
  S_Max_Name_Len     = 16;

  S_Str_Declined0  : String[21] = {Change_Ribbon_Color +} ' ---  DECLINED  ---';
  S_Str_Declined1  : String[21] = {Change_Ribbon_Color +} ' *******************';
  S_Str_Declined2  : String[21] = {Change_Ribbon_Color +} ' *  DO NOT USE AS  *';
  S_Str_Declined3  : String[21] = {Change_Ribbon_Color +} ' *PROOF OF PURCHASE*';
  S_Str_Declined4  : String[21] = {Change_Ribbon_Color +} ' *******************';
  S_Str_Training   : String[20] = '- Training Receipt -';

  S_Str_BalInq1    : String[21] = ' *  DO NOT USE AS  *';    { TSL-D }
  S_Str_BalInq2    : String[21] = ' *PROOF OF PURCHASE*';    { TSL-D }

  S_Receipt_A  :  Array[1..S_Rcpt_Map_Lines] of String[L_Rcpt_Line_Len] = ( //CPCLIENTS-1422
                                               { Note: ^ is not S_ so we can move entire }
                                               {       Array...                          }
  {            1         2         3         4 Actual  }
  {   1234567890123456789012345678901234567890 Position}
     '                     ', { 01 }
     '                     ', { 02 }
     ' Card #              ', { 03 }
     '                     ', { 04 }
     ' Pmt From Primary    ', { 05 }
     ' Auth #              ', { 06 }
     ' Ref/Seq#            ', { 07 }
     ' Lane #              ', { 08 }
     ' Chkr #              ', { 09 }
     ' xx/xx/xx hh:mm      ', { 10 }
     ' WinEPS Seq #        ', { 11 }
     ' Purchase $      .   ', { 12 }
     ' Cashback $      .   ', { 13 }
     ' Merch Fee$      .   ', { 14 }
     ' Tip      $      .   ', { 15 }
     '            -------- ', { 16 }
     ' Total    $      .   ', { 17 }
     '                     ', { 18 }
     '                     ', { 19 }
     ' Sig:________________', { 20 }
     '                     ');{ 21 }


  S_Receipt_B  :  Array[1..S_Rcpt_Map_Lines] of String[L_Rcpt_Line_Len] = (
                                               { Note: ^ is not S_ so we can move entire }
                                               {       Array...                          }
  {            1         2         3         4 Actual  }
  {   1234567890123456789012345678901234567890 Position}
     '                     ', { 01 }
     '                     ', { 02 }
     ' Card #              ', { 03 }
     '                     ', { 04 }
     ' Pmt From Primary    ', { 05 }
     ' Auth #              ', { 06 }
     ' Ref/Seq#            ', { 07 }
     ' Lane #              ', { 08 }
     ' Chkr #              ', { 09 }
     ' xx/xx/xx hh:mm      ', { 10 }
     ' WinEPS Seq #        ', { 11 }
     ' Purchase $      .   ', { 12 }
     ' Cashback $      .   ', { 13 }
     ' Merch Fee$      .   ', { 14 }
     ' Tip      $      .   ', { 15 }
     '            -------- ', { 16 }
     ' Total    $      .   ', { 17 }
     '                     ', { 18 }
     '                     ', { 19 }
     ' Sig:________________', { 20 }
     '                     ');{ 21 }


  S_Credit_Verbage_Lines = 6;
  S_Credit_Verbage : Array[1..S_Credit_Verbage_Lines] of String[S_Rcpt_Line_Len] = (
     ' I AGREE TO PAY ABOVE',
     ' TOTAL AMOUNT ACCORD-',
     ' ING TO CARD ISSUER  ',
     ' AGREEMENT (MERCHANT ',
     ' AGREEMENT IF CREDIT ',
     ' VOUCHER)            ');

  { ----------------------------------------------------------------------- }
  {   Definitions For Small Regular Receipt                                 }
  { ----------------------------------------------------------------------- }

  S_Defs : Array[1..42] of Byte = ( //CPCLIENTS-1422

  { S_Rcpt_Purch_Amt_Line  } 12,
  { S_Rcpt_Purch_Amt_Pos   } 13,
  { S_Rcpt_Cashb_Amt_Line  } 13,
  { S_Rcpt_Cashb_Amt_Pos   } 13,
  { S_Rcpt_Fee_Amt_Line    } 14,
  { S_Rcpt_Fee_Amt_Pos     } 13,
  { S_Rcpt_Tip_Amt_Line    } 15,
  { S_Rcpt_Tip_Amt_Pos     } 13,
  { S_Rcpt_Underscore_Line } 16,
  { S_Rcpt_Total_Amt_Line  } 17,
  { S_Rcpt_Amt_Desc_Pos    }  2,
  { S_Rcpt_Total_Amt_Pos   } 13,
  { S_Rcpt_Pin_Used_Line   }  1,     { TSL-F }
  { S_Rcpt_Pin_Used_Pos    } 13,
  { S_Rcpt_Card_Type_Line  }  2,
  { S_Rcpt_Card_Type_Pos   }  2,
  { S_Rcpt_Acct_Line       }  4,
  { S_Rcpt_Acct_Pos        }  2,
  { S_Rcpt_Auth_Line       }  6,
  { S_Rcpt_Auth_Pos        }  9,
  { S_Rcpt_From_Line       }  5,
  { S_Rcpt_From_Where_Pos  } 11,
  { S_Rcpt_From_Pos        }  2,
  { S_Rcpt_Exp_Date_Line   }  5,
  { S_Rcpt_Exp_Date_Pos    }  2,
  { S_Rcpt_Lane_Line       }  8,
  { S_Rcpt_Lane_Pos        }  9,
  { S_Rcpt_Cashier_Line    }  9,
  { S_Rcpt_Cashier_Pos     }  9,
  { S_Rcpt_Date_Line       } 10,
  { S_Rcpt_Date_Pos        }  2,
  { S_Rcpt_Time_Line       } 10,
  { S_Rcpt_Time_Pos        } 11,
  { S_Rcpt_Ref_Line        }  7,
  { S_Rcpt_Ref_Pos         } 11,         { TSL-G }
  { S_Rcpt_WinEPSSeq_Line  } 11,
  { S_Rcpt_WinEPSSeq_Pos   } 15,
  { S_Rcpt_Blank_To_Sig_1  } 18,
  { S_Rcpt_Blank_To_Sig_2  } 19,
  { S_Rcpt_Sig_Line        } 20,
  { S_Rcpt_Name_Line       } 21,
  { S_Rcpt_Name_Pos        }  6);


  S_Defs_B : Array[1..42] of Byte = (

  { S_Rcpt_Purch_Amt_Line  } 12,
  { S_Rcpt_Purch_Amt_Pos   } 13,
  { S_Rcpt_Cashb_Amt_Line  } 13,
  { S_Rcpt_Cashb_Amt_Pos   } 13,
  { S_Rcpt_Fee_Amt_Line    } 14,
  { S_Rcpt_Fee_Amt_Pos     } 13,
  { S_Rcpt_Tip_Amt_Line    } 15,
  { S_Rcpt_Tip_Amt_Pos     } 13,
  { S_Rcpt_Underscore_Line } 16,
  { S_Rcpt_Total_Amt_Line  } 17,
  { S_Rcpt_Amt_Desc_Pos    }  2,
  { S_Rcpt_Total_Amt_Pos   } 13,
  { S_Rcpt_Pin_Used_Line   }  1,     { TSL-F }
  { S_Rcpt_Pin_Used_Pos    } 13,
  { S_Rcpt_Card_Type_Line  }  2,
  { S_Rcpt_Card_Type_Pos   }  2,
  { S_Rcpt_Acct_Line       }  4,
  { S_Rcpt_Acct_Pos        }  2,
  { S_Rcpt_Auth_Line       }  6,
  { S_Rcpt_Auth_Pos        }  9,
  { S_Rcpt_From_Line       }  5,
  { S_Rcpt_From_Where_Pos  } 11,
  { S_Rcpt_From_Pos        }  2,
  { S_Rcpt_Exp_Date_Line   }  5,
  { S_Rcpt_Exp_Date_Pos    }  2,
  { S_Rcpt_Lane_Line       }  8,
  { S_Rcpt_Lane_Pos        }  9,
  { S_Rcpt_Cashier_Line    }  9,
  { S_Rcpt_Cashier_Pos     }  9,
  { S_Rcpt_Date_Line       } 10,
  { S_Rcpt_Date_Pos        }  2,
  { S_Rcpt_Time_Line       } 10,
  { S_Rcpt_Time_Pos        } 11,
  { S_Rcpt_Ref_Line        }  7,
  { S_Rcpt_Ref_Pos         } 11,         { TSL-G }
  { S_Rcpt_WinEPSSeq_Line  } 11,
  { S_Rcpt_WinEPSSeq_Pos   } 15,
  { S_Rcpt_Blank_To_Sig_1  } 18,
  { S_Rcpt_Blank_To_Sig_2  } 19,
  { S_Rcpt_Sig_Line        } 20,
  { S_Rcpt_Name_Line       } 21,
  { S_Rcpt_Name_Pos        }  6);

  S_EBT_Map_Lines            = 24;

  S_EBT_Rcpt    :  Array[1..S_EBT_Map_Lines] of String[L_Rcpt_Line_Len] = (

  {            1         2         3         4 Actual  }
  {   1234567890123456789012345678901234567890 Position}
     ' C/T_desc            ', { 01 }
     ' #                   ', { 02 }
     ' Beg Bal   $      .  ', { 03 }
     ' Purchase  $      .  ', { 04 }
     ' Cashback  $      .  ', { 05 }
     ' Merch Fee $      .  ', { 06 }
     ' Tip       $      .  ', { 07 }
     '             --------', { 08 }
     ' End   Bal $      .  ', { 09 }
     '                     ', { 10 }
     ' Other Bal $      .  ', { 11 }
     '                     ', { 12 }
     ' Exp Date 00/00      ', { 13 }
     ' Auth #              ', { 14 }
     ' Ref/Seq#            ', { 15 }
     ' Lane #              ', { 16 }
     ' Chkr #              ', { 17 }
     ' xx/xx/xx hh:mm      ', { 18 }
     ' WinEPS Seq #        ', { 19 }
     ' ***** DO  NOT ***** ', { 20 }
     ' *  DISPENSE CASH  * ', { 21 }
     ' ******************* ', { 22 }
     ' Sig:________________', { 23 }
     '                     ');{ 24 }

  S_EBT_Defs : Array[1..47] of Byte = (

  { S_EBT_Card_Type_Line   }  1,
  { S_EBT_Card_Type_Pos    }  2,
  { S_EBT_Acct_Line        }  2,
  { S_EBT_Case_Pos         }  2,
  { S_EBT_Acct_Pos         }  4,
  { S_EBT_Pin_Used_Line    } 11,     { TSL-F }
  { S_EBT_Pin_Used_Pos     }  2,
  { S_EBT_Begin_Bal_Line   }  3,
  { S_EBT_Begin_Bal_Pos    } 14,
  { S_EBT_Purch_Amt_Line   }  4,
  { S_EBT_Purch_Amt_Pos    } 14,
  { S_EBT_Purch_Type_Pos   }  2,
  { S_EBT_CashB_Amt_Line   }  5,
  { S_EBT_CashB_Amt_Pos    } 14,
  { S_EBT_Fee_Amt_Line     }  6,
  { S_EBT_Fee_Amt_Pos      } 14,
  { S_EBT_Tip_Amt_Line     }  7,
  { S_EBT_Tip_Amt_Pos      } 14,
  { S_EBT_Underscore_Line  }  8,
  { S_EBT_End_Bal_Line     }  9,
  { S_EBT_End_Bal_Hdr_Pos  }  2,
  { S_EBT_End_Bal_Pos      } 14,
  { S_EBT_Other_Bal_Line   } 11,
  { S_EBT_Other_Bal_Hdr_Pos } 2,
  { S_EBT_Other_Bal_Pos    } 14,
  { S_EBT_Auth_Line        } 14,
  { S_EBT_Auth_Pos         }  9,
  { S_EBT_Exp_Date_Line    } 13,
  { S_EBT_Exp_Date_Pos     } 11,
  { S_EBT_Lane_Line        } 16,
  { S_EBT_Lane_Pos         }  9,
  { S_EBT_Cashier_Line     } 17,
  { S_EBT_Cashier_Pos      }  9,
  { S_EBT_Date_Line        } 18,
  { S_EBT_Date_Pos         }  2,
  { S_EBT_Time_Line        } 18,
  { S_EBT_Time_Pos         } 11,
  { S_EBT_Ref_Line         } 15,
  { S_EBT_Ref_Pos          } 11,        { TSL-G }
  { S_EBT_WinEPSSeq_Line   } 19,
  { S_EBT_WinEPSSeq_Pos    } 15,
  { S_EBT_No_Disp_Cash_1   } 20,
  { S_EBT_No_Disp_Cash_2   } 21,
  { S_EBT_No_Disp_Cash_3   } 22,
  { S_EBT_Sig_Line         } 23,
  { S_EBT_Name_Line        } 24,
  { S_EBT_Name_Pos         }  6);

  Len_Str_Declined4 = 36;
  Len_Str_Training = 38;  

Type
  Rcpt_Layout = Record
           Rcpt_Trx_Type_Line, // CPCLIENTS-5230
           Rcpt_Trx_Type_Pos,
           Rcpt_Purch_Amt_Line,
           Rcpt_Purch_Amt_Pos,
           Rcpt_Cashb_Amt_Line,
           Rcpt_Cashb_Amt_Pos,
           Rcpt_Fee_Amt_Line,
           Rcpt_Fee_Amt_Pos,
           Rcpt_Tip_Amt_Line,
           Rcpt_Tip_Amt_Pos,
           Rcpt_Underscore_Line,
           Rcpt_Total_Amt_Line,
           Rcpt_Amt_Desc_Pos,
           Rcpt_Total_Amt_Pos,
           Rcpt_Pin_Used_Line,            { TSL-F }
           Rcpt_Pin_Used_Pos,
           Rcpt_Card_Type_Line,
           Rcpt_Card_Type_Pos,
           Rcpt_Acct_Line,
           Rcpt_Acct_Pos,
           Rcpt_Auth_Line,
           Rcpt_Auth_Pos,
           Rcpt_From_Line,
           Rcpt_From_Where_Pos,
           Rcpt_From_Pos,
           Rcpt_Exp_Date_Line,
           Rcpt_Exp_Date_Pos,
           Rcpt_Lane_Line,
           Rcpt_Lane_Pos,
           Rcpt_Cashier_Line,
           Rcpt_Cashier_Pos,
           Rcpt_Date_Line,
           Rcpt_Date_Pos,
           Rcpt_Time_Line,
           Rcpt_Time_Pos,
           Rcpt_Ref_Line,
           Rcpt_Ref_Pos,
           Rcpt_WinEPSSeq_Line,
           Rcpt_WinEPSSeq_Pos,
           { YHJ-286 begin }
           { // YHJ-286
           Rcpt_MerchantNum_Line,
           Rcpt_MerchantNum_Pos,
           Rcpt_TerminalNum_Pos,
           Rcpt_CardProcID_Pos,
           }
           { YHJ-285 end }
           Rcpt_Blank_To_Sig_1,
           Rcpt_Blank_To_Sig_2,
           Rcpt_Blank_To_Sig_3,
           Rcpt_Sig_Line,
           Rcpt_Name_Line,
           Rcpt_Name_Pos   : Byte;
      End;

  EBT_Rcpt_Layout = Record
           EBT_Card_Type_Line,
           EBT_Card_Type_Pos,
           EBT_Acct_Line,
           EBT_Case_Pos,
           EBT_Acct_Pos,
           EBT_Pin_Used_Line,          { TSL-F }
           EBT_Pin_Used_Pos,
           EBT_Begin_Bal_Line,
           EBT_Begin_Bal_Pos,
           EBT_Purch_Amt_Line,
           EBT_Purch_Amt_Pos,
           EBT_Purch_Type_Pos,
           EBT_CashB_Amt_Line,
           EBT_CashB_Amt_Pos,
           EBT_Fee_Amt_Line,
           EBT_Fee_Amt_Pos,
           EBT_Tip_Amt_Line,
           EBT_Tip_Amt_Pos,
           EBT_Underscore_Line,
           EBT_End_Bal_Line,
           EBT_End_Bal_Hdr_Pos,
           EBT_End_Bal_Pos,
           EBT_Other_Bal_Line,
           EBT_Other_Bal_Hdr_Pos,
           EBT_Other_Bal_Pos,
           EBT_Auth_Line,
           EBT_Auth_Pos,
           EBT_Exp_Date_Line,
           EBT_Exp_Date_Pos,
           EBT_Lane_Line,
           EBT_Lane_Pos,
           EBT_Cashier_Line,
           EBT_Cashier_Pos,
           EBT_Date_Line,
           EBT_Date_Pos,
           EBT_Time_Line,
           EBT_Time_Pos,
           EBT_Ref_Line,
           EBT_Ref_Pos,
           EBT_WinEPSSeq_Line,
           EBT_WinEPSSeq_Pos,
           EBT_No_Disp_Cash_1,
           EBT_No_Disp_Cash_2,
           EBT_No_Disp_Cash_3,
           EBT_No_Disp_Cash_4,
           EBT_Sig_Line,
           EBT_Name_Line,
           EBT_Name_Pos     : Byte;
      End;

  Rcpt_Record = Record
        Rcpt_Line : String[38];   { JGS-10/23/97 Stater Problem }
      End;

(* // remove hints
Var
  EBT_Map_Lines,
  Rcpt_Map_Lines,
  Rcpt_Line_Len,
  Max_Name_Len   : Byte;

  Defs           : Rcpt_Layout;
  EBT_Defs       : EBT_Rcpt_Layout;

  Receipt_A      : Array[1..S_Rcpt_Map_Lines] of String[L_Rcpt_Line_Len];
  EBT_Rcpt       : Array[1..S_EBT_Map_Lines]  of String[L_Rcpt_Line_Len];

  Str_Declined0,              { Change_Ribbon_Color + ' ---  DECLINED  ---'  }
  Str_Declined1,              { Change_Ribbon_Color + ' *******************' }
  Str_Declined2,              { Change_Ribbon_Color + ' *  DO NOT USE AS  *' }
  Str_Declined3,              { Change_Ribbon_Color + ' *PROOF OF PURCHASE*' }
  Str_Declined4  : String[Len_Str_Declined4];{ Change_Ribbon_Color + ' *******************' } // was String[36]

  Str_Training   : String[Len_Str_Training];{ 'Training Receipt -- Not Valid Purchase'                } // was String[38]

  {$IFNDEF MTXEPSDLL} // [Hint] LANERCPT.INC(550): Variable 'Rcpt_File' is declared but never used in 'receiptClass'
  Rcpt_File      : File of Rcpt_Record;
  Rcpt_Buf       : Rcpt_Record;  
  {$ENDIF MTXEPSDLL}

Const
*)
