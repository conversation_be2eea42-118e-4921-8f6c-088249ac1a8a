
{**********************************************************************}
{                                                                      }
{                       Delphi XML Data Binding                        }
{                                                                      }
{         Generated on: 6/23/2014 12:35:06 PM                          }
{       Generated from: C:\dev\projects\828.3\Common\packinglist.xml   }
{   Settings stored in: C:\dev\projects\828.3\Common\packinglist.xdb   }
{                                                                      }
{**********************************************************************}
unit PackingListXML;

interface

uses xmldom, XMLDoc, XMLIntf;

type

{ Forward Decls }

  IXMLPackingListType = interface;
  IXMLFileGroupsType = interface;
  IXMLFileGroupType = interface;
  IXMLFileType = interface;

{ IXMLPackingListType }

  IXMLPackingListType = interface(IXMLNode)
    ['{3CE40EB0-C634-4163-BACF-7204B9401ADB}']
    { Property Accessors }
    function Get_FileGroups: IXMLFileGroupsType;
    { Methods & Properties }
    property FileGroups: IXMLFileGroupsType read Get_FileGroups;
  end;

{ IXMLFileGroupsType }

  IXMLFileGroupsType = interface(IXMLNodeCollection)
    ['{3BCD6618-F888-40E3-AF59-A844B1D72926}']
    { Property Accessors }
    function Get_FileGroup(Index: Integer): IXMLFileGroupType;
    { Methods & Properties }
    function Add: IXMLFileGroupType;
    function Insert(const Index: Integer): IXMLFileGroupType;
    property FileGroup[Index: Integer]: IXMLFileGroupType read Get_FileGroup; default;
  end;

{ IXMLFileGroupType }

  IXMLFileGroupType = interface(IXMLNodeCollection)
    ['{B240C850-BA8C-4572-87D9-22B872D1A412}']
    { Property Accessors }
    function Get_Number: Integer;
    function Get_File_(Index: Integer): IXMLFileType;
    procedure Set_Number(Value: Integer);
    { Methods & Properties }
    function Add: IXMLFileType;
    function Insert(const Index: Integer): IXMLFileType;
    property Number: Integer read Get_Number write Set_Number;
    property File_[Index: Integer]: IXMLFileType read Get_File_; default;
  end;

{ IXMLFileType }

  IXMLFileType = interface(IXMLNode)
    ['{8DB2269A-CF39-4E43-8F47-5B9915442B69}']
    { Property Accessors }
    function Get_Number: Integer;
    function Get_ParameterName: WideString;
    function Get_ParameterValue: WideString;
    function Get_FileName: WideString;
    function Get_ParamaterName: WideString;
    procedure Set_Number(Value: Integer);
    procedure Set_ParameterName(Value: WideString);
    procedure Set_ParameterValue(Value: WideString);
    procedure Set_FileName(Value: WideString);
    procedure Set_ParamaterName(Value: WideString);
    { Methods & Properties }
    property Number: Integer read Get_Number write Set_Number;
    property ParameterName: WideString read Get_ParameterName write Set_ParameterName;
    property ParameterValue: WideString read Get_ParameterValue write Set_ParameterValue;
    property FileName: WideString read Get_FileName write Set_FileName;
    property ParamaterName: WideString read Get_ParamaterName write Set_ParamaterName;
  end;

{ Forward Decls }

  TXMLPackingListType = class;
  TXMLFileGroupsType = class;
  TXMLFileGroupType = class;
  TXMLFileType = class;

{ TXMLPackingListType }

  TXMLPackingListType = class(TXMLNode, IXMLPackingListType)
  protected
    { IXMLPackingListType }
    function Get_FileGroups: IXMLFileGroupsType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLFileGroupsType }

  TXMLFileGroupsType = class(TXMLNodeCollection, IXMLFileGroupsType)
  protected
    { IXMLFileGroupsType }
    function Get_FileGroup(Index: Integer): IXMLFileGroupType;
    function Add: IXMLFileGroupType;
    function Insert(const Index: Integer): IXMLFileGroupType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLFileGroupType }

  TXMLFileGroupType = class(TXMLNodeCollection, IXMLFileGroupType)
  protected
    { IXMLFileGroupType }
    function Get_Number: Integer;
    function Get_File_(Index: Integer): IXMLFileType;
    procedure Set_Number(Value: Integer);
    function Add: IXMLFileType;
    function Insert(const Index: Integer): IXMLFileType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLFileType }

  TXMLFileType = class(TXMLNode, IXMLFileType)
  protected
    { IXMLFileType }
    function Get_Number: Integer;
    function Get_ParameterName: WideString;
    function Get_ParameterValue: WideString;
    function Get_FileName: WideString;
    function Get_ParamaterName: WideString;
    procedure Set_Number(Value: Integer);
    procedure Set_ParameterName(Value: WideString);
    procedure Set_ParameterValue(Value: WideString);
    procedure Set_FileName(Value: WideString);
    procedure Set_ParamaterName(Value: WideString);
  end;

{ Global Functions }

function GetPackingList(Doc: IXMLDocument): IXMLPackingListType;
function LoadPackingList(const FileName: WideString): IXMLPackingListType;
function NewPackingList: IXMLPackingListType;

implementation

{ Global Functions }

function GetPackingList(Doc: IXMLDocument): IXMLPackingListType;
begin
  Result := Doc.GetDocBinding('PackingList', TXMLPackingListType) as IXMLPackingListType;
end;
function LoadPackingList(const FileName: WideString): IXMLPackingListType;
begin
  Result := LoadXMLDocument(FileName).GetDocBinding('PackingList', TXMLPackingListType) as IXMLPackingListType;
end;

function NewPackingList: IXMLPackingListType;
begin
  Result := NewXMLDocument.GetDocBinding('PackingList', TXMLPackingListType) as IXMLPackingListType;
end;

{ TXMLPackingListType }

procedure TXMLPackingListType.AfterConstruction;
begin
  RegisterChildNode('FileGroups', TXMLFileGroupsType);
  inherited;
end;

function TXMLPackingListType.Get_FileGroups: IXMLFileGroupsType;
begin
  Result := ChildNodes['FileGroups'] as IXMLFileGroupsType;
end;

{ TXMLFileGroupsType }

procedure TXMLFileGroupsType.AfterConstruction;
begin
  RegisterChildNode('FileGroup', TXMLFileGroupType);
  ItemTag := 'FileGroup';
  ItemInterface := IXMLFileGroupType;
  inherited;
end;

function TXMLFileGroupsType.Get_FileGroup(Index: Integer): IXMLFileGroupType;
begin
  Result := List[Index] as IXMLFileGroupType;
end;

function TXMLFileGroupsType.Add: IXMLFileGroupType;
begin
  Result := AddItem(-1) as IXMLFileGroupType;
end;

function TXMLFileGroupsType.Insert(const Index: Integer): IXMLFileGroupType;
begin
  Result := AddItem(Index) as IXMLFileGroupType;
end;


{ TXMLFileGroupType }

procedure TXMLFileGroupType.AfterConstruction;
begin
  RegisterChildNode('File', TXMLFileType);
  ItemTag := 'File';
  ItemInterface := IXMLFileType;
  inherited;
end;

function TXMLFileGroupType.Get_Number: Integer;
begin
  Result := AttributeNodes['Number'].NodeValue;
end;

procedure TXMLFileGroupType.Set_Number(Value: Integer);
begin
  SetAttribute('Number', Value);
end;

function TXMLFileGroupType.Get_File_(Index: Integer): IXMLFileType;
begin
  Result := List[Index] as IXMLFileType;
end;

function TXMLFileGroupType.Add: IXMLFileType;
begin
  Result := AddItem(-1) as IXMLFileType;
end;

function TXMLFileGroupType.Insert(const Index: Integer): IXMLFileType;
begin
  Result := AddItem(Index) as IXMLFileType;
end;


{ TXMLFileType }

function TXMLFileType.Get_Number: Integer;
begin
  Result := AttributeNodes['Number'].NodeValue;
end;

procedure TXMLFileType.Set_Number(Value: Integer);
begin
  SetAttribute('Number', Value);
end;

function TXMLFileType.Get_ParameterName: WideString;
begin
  Result := AttributeNodes['ParameterName'].Text;
end;

procedure TXMLFileType.Set_ParameterName(Value: WideString);
begin
  SetAttribute('ParameterName', Value);
end;

function TXMLFileType.Get_ParameterValue: WideString;
begin
  Result := AttributeNodes['ParameterValue'].Text;
end;

procedure TXMLFileType.Set_ParameterValue(Value: WideString);
begin
  SetAttribute('ParameterValue', Value);
end;

function TXMLFileType.Get_FileName: WideString;
begin
  Result := AttributeNodes['FileName'].Text;
end;

procedure TXMLFileType.Set_FileName(Value: WideString);
begin
  SetAttribute('FileName', Value);
end;

function TXMLFileType.Get_ParamaterName: WideString;
begin
  Result := AttributeNodes['ParamaterName'].Text;
end;

procedure TXMLFileType.Set_ParamaterName(Value: WideString);
begin
  SetAttribute('ParamaterName', Value);
end;

end.
