// (c) MTXEPS, Inc. 1988-2008
unit CardProcessingProfileXML;
(*
v824.0 08-14-07 DEV-5885 ConnectPay - GUI, update program, RPT

Version History
--------------------------------------------------------------------------------
1.0     DEV-4656 Convert EFT files to XML - Phase 1 & 2
2.0     DEV-5885 ConnectPay - GUI, update program, RPT
        ConnectPayEnabled       string /YN
        ConnectPayPrefix        string /10 digit numeric chars
1.0     2007.10.31 Rollback DEV-5885
*)

interface

uses
  FinalizationLog,
  Classes, SysUtils, IdGlobal, Variants;

type

{ Forward Decls }
  TXMLCardProcessingProfileType = class;
  TXMLCardType = class;
  TXMLReportingGroupsType = class;
  TXMLProcessingFlagsType = class;
  TXMLCashbackType = class;
  TXMLExpirationDateType = class;
  TXMLLocalAuthFlagsType = class;
  TXMLPurchaseType = class;
  TXMLReturnType = class;
  TXMLForceType = class;
  TXMLPreAuthType = class;
  TXMLActivateType = class;
  TXMLHoldTabType = class;              // CPCLIENTS-1932
  TXMLReleaseTabType = class;           // CPCLIENTS-1932
  TXMLOnlineLimitsType = class;
  TXMLPreauthAmountType = class;
  TXMLOfflineLimitsType = class;

{ TXMLCardProcessingProfileType }
  TXMLCardProcessingProfileType = class(TCollection)
  private
    FVersion: string;
    FLastModified: string;
    FCard: TXMLCardType;
    {$HINTS OFF}
    function GetVersion: string;
    function GetLastModified: string;
    function GetCard(Index: Integer): TXMLCardType;
    procedure SetVersion(Value: string);
    procedure SetLastModified(Value: string);
    {$HINTS ON}
  public
    property Version: string read FVersion write FVersion;
    property LastModified: string read FLastModified write FLastModified;
    property Card[Index: Integer]: TXMLCardType read GetCard; default;
    constructor Create(ItemClass: TCollectionItemClass);
    destructor Destroy; override;
    function Add: TXMLCardType;
    function LoadFromFile(aFileName: string): boolean;
    function SaveToFile(aFileName: string): boolean;
  end;

{ TXMLCardType }
  TXMLCardType = class(TCollectionItem)
  private
    FCode: string;
    FCardName: string;
    FFleetType: Integer;
    FReportingGroups: TXMLReportingGroupsType;
    FProcessingFlags: TXMLProcessingFlagsType;
    FLocalAuthFlags: TXMLLocalAuthFlagsType;
    FOnlineLimits: TXMLOnlineLimitsType;
    FOfflineLimits: TXMLOfflineLimitsType;
  protected
    { TXMLCardType }
    function GetCode: string;
    function GetCardName: string;
    function GetFleetType: Integer;
    function GetReportingGroups: TXMLReportingGroupsType;
    function GetProcessingFlags: TXMLProcessingFlagsType;
    function GetLocalAuthFlags: TXMLLocalAuthFlagsType;
    function GetOnlineLimits: TXMLOnlineLimitsType;
    function GetOfflineLimits: TXMLOfflineLimitsType;
    procedure SetCode(Value: string);
    procedure SetCardName(Value: string);
    procedure SetFleetType(Value: Integer);
  public
    property Code: string read GetCode write SetCode;
    property CardName: string read GetCardName write SetCardName;
    property FleetType: Integer read GetFleetType write SetFleetType;
    property ReportingGroups: TXMLReportingGroupsType read GetReportingGroups;
    property ProcessingFlags: TXMLProcessingFlagsType read GetProcessingFlags;
    property LocalAuthFlags: TXMLLocalAuthFlagsType read GetLocalAuthFlags;
    property OnlineLimits: TXMLOnlineLimitsType read GetOnlineLimits;
    property OfflineLimits: TXMLOfflineLimitsType read GetOfflineLimits;
    constructor Create(Collection: TCollection); override;
    destructor Destroy; override;
  end;

{ TXMLReportingGroupsType }

  TXMLReportingGroupsType = class
  private
    FHostSettlement: string;
    FStoreSummary: string;
    FLaneSummary: string;
    FCheckerSummary: string;
    FCheckerSignOff: string;
    function GetHostSettlement: string;
    function GetStoreSummary: string;
    function GetLaneSummary: string;
    function GetCheckerSummary: string;
    function GetCheckerSignOff: string;
    procedure SetHostSettlement(Value: string);
    procedure SetStoreSummary(Value: string);
    procedure SetLaneSummary(Value: string);
    procedure SetCheckerSummary(Value: string);
    procedure SetCheckerSignOff(Value: string);    
  public
    property HostSettlement: string read GetHostSettlement write SetHostSettlement;
    property StoreSummary: string read GetStoreSummary write SetStoreSummary;
    property LaneSummary: string read GetLaneSummary write SetLaneSummary;
    property CheckerSummary: string read GetCheckerSummary write SetCheckerSummary;
    property CheckerSignOff: string read GetCheckerSignOff write SetCheckerSignOff;
  end;

{ TXMLSkipSignatureCaptureType }
  TXMLSkipSignatureCaptureType = class // DEV-5964
  private
  public
    Skip: string; // DEV-5964: was boolean
    SkipOffline: boolean;
    PromptOnManual: string; // DEV-20085
    Amount: integer;
  end;

{ TXMLProcessingFlagsType }

  TXMLProcessingFlagsType = class
  private
    FManualEntryAllowed: string;
    FPromptForZipCode: string;
    FPromptForCVV2: string;
    FPromptForSecurityCode: string;
    FPromptForPO: string;           //JTG Dev 6418
    FPromptForTaxAmount: string;
    FMod10Check: string;
    FPINRequired: string;
    FCustomerNameOnReceipt: string;
    FCashback: TXMLCashbackType;
    FExpirationDate: TXMLExpirationDateType;
    FSkipSigLine: boolean;          // JTG Dev 5964
    FSkipSigLineAmount: integer;    // JTG Dev 5964
    FStandInForPrePaidCredit: string; // DEV-9575
    FDiscountType: string;
    FDiscountAmount: string;
    FPromptForOdometer: string;
    FAllowRetryOnVerifyLast4: string;
    FPromptForTokenPin: string; // DOEP-72732
    FPromptForPinOnManual: string; //TFS-14049
    fSuppressBalance: string;   // TFS-114195/114196/114465
    FAllowBarTab: boolean;      // CPCLIENTS-1940
    FTipLineOnReceipt: Boolean; // CPCLIENTS-1942

    function GetManualEntryAllowed: string;
    function GetPromptForZipCode: string;
    function GetPromptForCVV2: string;
    function GetPromptForSecurityCode: string;
    function GetPromptForPO: string;              //JTG Dev 6418
    function GetMod10Check: string;
    function GetPINRequired: string;
    function GetCustomerNameOnReceipt: string;
    function GetCashback: TXMLCashbackType;
    function GetExpirationDate: TXMLExpirationDateType;
    {$HINTS OFF}
    function GetSkipSigLine: boolean;           // JTG Dev 5964
    function GetSkipSigLineAmount: integer;     // JTG Dev 5964
    {$HINTS ON}

    procedure SetManualEntryAllowed(Value: string);
    procedure SetPromptForZipCode(Value: string);
    procedure SetPromptForCVV2(Value: string);
    procedure SetPromptForSecurityCode(Value: string);
    procedure SetPromptForPO(Value: string);       //JTG Dev 6418
    procedure SetMod10Check(Value: string);
    procedure SetPINRequired(Value: string);
    procedure SetCustomerNameOnReceipt(Value: string);
    {$HINTS OFF}
    procedure SetSkipSigLine(Value: boolean);       // JTG Dev 5964
    procedure SetSkipSigLineAmount(Value: integer); // JTG Dev 5964
    {$HINTS ON}
  public
    DoNotAllowPartialApproval: string; // DEV-10666
    DeclineCashOnly: string; // DEV-12509
    //SkipSigOffline: string; // DEV-5964
    SkipSignatureCapture: TXMLSkipSignatureCaptureType; // DEV-5964
    ContactlessEMV: string;
    PaymentOnAccount: string; // DOEP-33255
    AllowFallback: string;
    PrintMinimalReceipt: string;
    property StandInForPrePaidCredit: string read FStandInForPrePaidCredit write FStandInForPrePaidCredit; // DEV-9575
    property ManualEntryAllowed: string read GetManualEntryAllowed write SetManualEntryAllowed;
    property PromptForZipCode: string read GetPromptForZipCode write SetPromptForZipCode;
    property PromptForCVV2: string read GetPromptForCVV2 write SetPromptForCVV2;
    property PromptForSecurityCode: string read GetPromptForSecurityCode write SetPromptForSecurityCode;
    property PromptForPO: string read GetPromptForPO write SetPromptForPO;   //JTG Dev 6418
    property PromptForTaxAmount: string read FPromptForTaxAmount write FPromptForTaxAmount; 
    property Mod10Check: string read GetMod10Check write SetMod10Check;
    property PINRequired: string read GetPINRequired write SetPINRequired;
    property CustomerNameOnReceipt: string read GetCustomerNameOnReceipt write SetCustomerNameOnReceipt;
    property Cashback: TXMLCashbackType read GetCashback;
    property ExpirationDate: TXMLExpirationDateType read GetExpirationDate;
    property PromptForOdometer: string read FPromptForOdometer write FPromptForOdometer;
    //property SkipSigLine: boolean read GetSkipSigLine write SetSkipSigLine;           // JTG Dev 5964
    //property SkipSigLineAmount: integer read GetSkipSigLineAmount write SetSkipSigLineAmount;  // JTG Dev 5964
    property DiscountType: string read FDiscountType write FDiscountType;
    property DiscountAmount: string read FDiscountAmount write FDiscountAmount;
    property AllowRetryOnVerifyLast4: string read FAllowRetryOnVerifyLast4 write FAllowRetryOnVerifyLast4;
    property PromptForTokenPin: string read FPromptForTokenPin write FPromptForTokenPin;  // DOEP-72732
    property PromptForPinOnManual: string read FPromptForPinOnManual write FPromptForPinOnManual; // TFS-14049
    property SuppressBalance: string read fSuppressBalance write fSuppressBalance;  // TFS-114195/114196//114465
    property AllowBarTab: boolean read FAllowBarTab write FAllowBarTab;                 // CPCLIENTS-1940
    property TipLineOnReceipt: Boolean read FTipLineOnReceipt write FTipLineOnReceipt;  // CPCLIENTS-1942
    constructor Create;
    destructor Destroy; override;
  end;

{ TXMLCashbackType }

  TXMLCashbackType = class
  private
    FAllowed: string;
    FCashOnManual: string;
    function GetAllowed: string;
    function GetCashOnManual: string;
    procedure SetAllowed(Value: string);
    procedure SetCashOnManual(Value: string);
  public
    AllowMaxCashBack: string; // DEV-10250
    property Allowed: string read GetAllowed write SetAllowed;
    property CashOnManual: string read GetCashOnManual write SetCashOnManual; 
  end;

{ TXMLExpirationDateType }

  TXMLExpirationDateType = class
  private
    FRequired: string;
    FSendOnlineOnly: string;
    function GetRequired: string;
    function GetSendOnlineOnly: string;
    procedure SetRequired(Value: string);
    procedure SetSendOnlineOnly(Value: string);    
  public
    property Required: string read GetRequired write SetRequired;
    property SendOnlineOnly: string read GetSendOnlineOnly write SetSendOnlineOnly;  
  end;

{ TXMLLocalAuthFlagsType }

  TXMLLocalAuthFlagsType = class
  private
    FPurchase: TXMLPurchaseType;
    FReturn: TXMLReturnType;
    FForce: TXMLForceType;
    FPreAuth: TXMLPreAuthType;
    FActivate: TXMLActivateType;
    FHoldTab: TXMLHoldTabType;                    // CPCLIENTS-1932
    FReleaseTab: TXMLReleaseTabType;              // CPCLIENTS-1932
    function GetPurchase: TXMLPurchaseType;
    function GetReturn: TXMLReturnType;
    function GetForce: TXMLForceType;
    function GetPreAuth: TXMLPreAuthType;
    function GetActivate: TXMLActivateType;
    function GetHoldTab: TXMLHoldTabType;         // CPCLIENTS-1932
    function GetReleaseTab: TXMLReleaseTabType;   // CPCLIENTS-1932
  public
    property Purchase: TXMLPurchaseType read GetPurchase;
    property Return: TXMLReturnType read GetReturn;
    property Force: TXMLForceType read GetForce;
    property PreAuth: TXMLPreAuthType read GetPreAuth;
    property Activate: TXMLActivateType read GetActivate;
    property HoldTab: TXMLHoldTabType read GetHoldTab;              // CPCLIENTS-1932
    property ReleaseTab: TXMLReleaseTabType read GetReleaseTab;     // CPCLIENTS-1932
    constructor Create;
    destructor Destroy; override;
  end;

{ TXMLPurchaseType }

  TXMLPurchaseType = class
  private
    FOffline: string;
    FOfflineVoid: string;
    function GetOffline: string;
    function GetOfflineVoid: string;
    procedure SetOffline(Value: string);
    procedure SetOfflineVoid(Value: string);
  public
    property Offline: string read GetOffline write SetOffline;
    property OfflineVoid: string read GetOfflineVoid write SetOfflineVoid;
  end;

{ TXMLReturnType }

  TXMLReturnType = class
  private
    FOffline: string;
    FOfflineVoid: string;
    function GetOffline: string;
    function GetOfflineVoid: string;
    procedure SetOffline(Value: string);
    procedure SetOfflineVoid(Value: string);    
  public
    property Offline: string read GetOffline write SetOffline;
    property OfflineVoid: string read GetOfflineVoid write SetOfflineVoid;
  end;

{ TXMLForceType }

  TXMLForceType = class
  private
    FOffline: string;
    FOfflineVoid: string;
    function GetOffline: string;
    function GetOfflineVoid: string;
    procedure SetOffline(Value: string);
    procedure SetOfflineVoid(Value: string);    
  public
    property Offline: string read GetOffline write SetOffline;
    property OfflineVoid: string read GetOfflineVoid write SetOfflineVoid;
  end;

{ TXMLPreAuthType }

  TXMLPreAuthType = class
  private
    FOffline: string;
    FOfflineVoid: string;
    function GetOffline: string;
    function GetOfflineVoid: string;
    procedure SetOffline(Value: string);
    procedure SetOfflineVoid(Value: string);    
  public
    property Offline: string read GetOffline write SetOffline;
    property OfflineVoid: string read GetOfflineVoid write SetOfflineVoid;
  end;

{ TXMLActivateType }

  TXMLActivateType = class
  private
    FOffline: string;
    FOfflineVoid: string;
    function GetOffline: string;
    function GetOfflineVoid: string;
    procedure SetOffline(Value: string);
    procedure SetOfflineVoid(Value: string);
  public
    property Offline: string read GetOffline write SetOffline;
    property OfflineVoid: string read GetOfflineVoid write SetOfflineVoid;
  end;

{ TXMLHoldTabType }

  TXMLHoldTabType = class                                  // CPCLIENTS-1932
  private
    FOffline: string;
    FOfflineVoid: string;
    function GetOffline: string;
    function GetOfflineVoid: string;
    procedure SetOffline(Value: string);
    procedure SetOfflineVoid(Value: string);
  public
    property Offline: string read GetOffline write SetOffline;
    property OfflineVoid: string read GetOfflineVoid write SetOfflineVoid;
  end;

{ TXMLReleaseTabType }

  TXMLReleaseTabType = class                               // CPCLIENTS-1932
  private
    FOffline: string;
    FOfflineVoid: string;
    function GetOffline: string;
    function GetOfflineVoid: string;
    procedure SetOffline(Value: string);
    procedure SetOfflineVoid(Value: string);
  public
    property Offline: string read GetOffline write SetOffline;
    property OfflineVoid: string read GetOfflineVoid write SetOfflineVoid;
  end;

{ TXMLOnlineLimitsType }

  TXMLOnlineLimitsType = class
  private
    FMaxTotalAmount: Integer;
    FMaxCashback: Integer;
    FMinTotalAmount: Integer;
    FMinCashback: Integer;
    FMinCreditToDebit: Integer;
    FNoCVMAmount: Integer;                                 // MCD TFS-7513
    FPreauthAmount: TXMLPreauthAmountType;
    function GetMaxTotalAmount: Integer;
    function GetMaxCashback: Integer;
    function GetMinTotalAmount: Integer;
    function GetMinCashback: Integer;
    function GetMinCreditToDebit: Integer;
    function GetPreauthAmount: TXMLPreauthAmountType;
    procedure SetMaxTotalAmount(Value: Integer);
    procedure SetMaxCashback(Value: Integer);
    procedure SetMinTotalAmount(Value: Integer);
    procedure SetMinCashback(Value: Integer);
    procedure SetMinCreditToDebit(Value: Integer);
    procedure SetNoCVMAmount(const Value: Integer);        // MCD TFS-7513
  public
    property MaxTotalAmount: Integer read GetMaxTotalAmount write SetMaxTotalAmount;
    property MaxCashback: Integer read GetMaxCashback write SetMaxCashback;
    property MinTotalAmount: Integer read GetMinTotalAmount write SetMinTotalAmount;
    property MinCashback: Integer read GetMinCashback write SetMinCashback;
    property MinCreditToDebit: Integer read GetMinCreditToDebit write SetMinCreditToDebit;
    property NoCVMAmount: Integer read FNoCVMAmount write SetNoCVMAmount;                         // MCD TFS-7513
    property PreauthAmount: TXMLPreauthAmountType read GetPreauthAmount;
    constructor Create;
    destructor Destroy; override;
  end;

{ TXMLPreauthAmountType }

  TXMLPreauthAmountType = class
  private
    FAmountToHost: Integer;
    FAmountToPump: Integer;
    function GetAmountToHost: Integer;
    function GetAmountToPump: Integer;
    procedure SetAmountToHost(Value: Integer);
    procedure SetAmountToPump(Value: Integer);    
  public
    property AmountToHost: Integer read GetAmountToHost write SetAmountToHost;
    property AmountToPump: Integer read GetAmountToPump write SetAmountToPump;
  end;

  { TXMLAmountByEntryType }

  TXMLAmountByEntryType = class
  private
    FSwipeAmount,
    FFallbackToSwipeAmount,
    FManualAmount,
    FChipAmount,
    FBarcodeAmount,
    FRFIDAmount,
    FEMVContactlessAmount: Integer;
    function GetSwipeAmount: Integer;
    function GetFallbackToSwipeAmount: Integer;
    function GetManualAmount: Integer;
    function GetChipAmount: Integer;
    function GetBarcodeAmount: Integer;
    function GetRFIDAmount: Integer;
    function GetEMVContactlessAmount: Integer;
    procedure SetSwipeAmount(Value: Integer);
    procedure SetFallbackToSwipeAmount(Value: Integer);
    procedure SetManualAmount(Value: Integer);
    procedure SetChipAmount(Value: Integer);
    procedure SetBarcodeAmount(Value: Integer);
    procedure SetRFIDAmount(Value: Integer);
    procedure SetEMVContactlessAmount(Value: Integer);
  public
    property SwipeAmount: Integer read GetSwipeAmount write SetSwipeAmount;
    property FallbackToSwipeAmount: Integer read GetFallbackToSwipeAmount write SetFallbackToSwipeAmount;
    property ManualAmount: Integer read GetManualAmount write SetManualAmount;
    property ChipAmount: Integer read GetChipAmount write SetChipAmount;
    property BarcodeAmount: Integer read GetBarcodeAmount write SetBarcodeAmount;
    property RFIDAmount: Integer read GetRFIDAmount write SetRFIDAmount;
    property EMVContactlessAmount: Integer read GetEMVContactlessAmount write SetEMVContactlessAmount;
  end;

{ TXMLOfflineLimitsType }

  TXMLOfflineLimitsType = class
  private
    FMaxTotalAmount: Integer;
    FMaxCashback: Integer;
    FMinCreditToDebit: Integer;
    FPreauthAmount: TXMLPreauthAmountType;
    FUseEntryType: Boolean;
    FMaxTotalAmountByEntryType,
    FMaxVoiceAuthLimitByEntryType: TXMLAmountByEntryType;
    function GetMaxTotalAmount: Integer;
    function GetMaxCashback: Integer;
    function GetMinCreditToDebit: Integer;
    function GetPreauthAmount: TXMLPreauthAmountType;
    function GetMaxTotalAmountByEntryType: TXMLAmountByEntryType;
    function GetMaxVoiceAuthLimitByEntryType: TXMLAmountByEntryType;
    procedure SetMaxTotalAmount(Value: Integer);
    procedure SetMaxCashback(Value: Integer);
    procedure SetMinCreditToDebit(Value: Integer);
  public
    property MaxTotalAmount: Integer read GetMaxTotalAmount write SetMaxTotalAmount;
    property MaxCashback: Integer read GetMaxCashback write SetMaxCashback;
    property MinCreditToDebit: Integer read GetMinCreditToDebit write SetMinCreditToDebit;
    property PreauthAmount: TXMLPreauthAmountType read GetPreauthAmount;
    property UseEntryType: Boolean read FUseEntryType write FUseEntryType;
    property MaxTotalAmountByEntryType: TXMLAmountByEntryType read GetMaxTotalAmountByEntryType;
    property MaxVoiceAuthLimitByEntryType: TXMLAmountByEntryType read GetMaxVoiceAuthLimitByEntryType;
    constructor Create;
    destructor Destroy; override;
  end;

const
  CPP_DISCOUNT_AMOUNT_LENGTH = 9;

{ Global Functions } // do we still need?
//function GetCardProcessingProfile(Doc: TXMLDocument): TXMLCardProcessingProfileType;
function LoadCardProcessingProfile(const FileName: string): TXMLCardProcessingProfileType;
function NewCardProcessingProfile: TXMLCardProcessingProfileType;

implementation

uses
  MTX_Constants,
  MTX_Lib,
  UXMLCommon,
  MTX_XMLClasses;

const
  _CardProcessingProfile = 'CardProcessingProfile';
  _Version = 'Version';
  _LastModified = 'LastModified';
  { Card }
  _Card                 = 'Card';
  _Code                 = 'Code';
  _CardName             = 'CardName';
  _FleetType            = 'FleetType';
  _ReportingGroups      = 'ReportingGroups';
  { HostSettlement }
  _HostSettlement       = 'HostSettlement';
  _StoreSummary         = 'StoreSummary';
  _LaneSummary          = 'LaneSummary';
  _CheckerSummary       = 'CheckerSummary';
  _CheckerSignOff       = 'CheckerSignOff';
  { ProcessingFlags }
  _ProcessingFlags      = 'ProcessingFlags';
  _ManualEntryAllowed   = 'ManualEntryAllowed';
  _PromptForZipCode     = 'PromptForZipCode';
  _PromptForCVV2        = 'PromptForCVV2';
  _PromptForSecurityCode= 'PromptForSecurityCode';
  _PromptForPO          = 'PromptForPO';
  _PromptForTaxAmount   = 'PromptForTaxAmount';
  _Mod10Check           = 'Mod10Check';
  _PINRequired          = 'PINRequired';
  _CustomerNameOnReceipt = 'CustomerNameOnReceipt';
  _Cashback             = 'Cashback';
  _Allowed              = 'Allowed';
  _CashOnManual         = 'CashOnManual';
  _AllowMaxCashBack     = 'AllowMaxCashBack'; // DEV-10250
  _ExpirationDate       = 'ExpirationDate';
  _Required             = 'Required';
  _SendOnlineOnly       = 'SendOnlineOnly';
  _PromptForOdometer    = 'PromptForOdometer';
  _DoNotAllowPartialApproval = 'DoNotAllowPartialApproval'; // DEV-10666

  _SkipSignatureCapture = 'SkipSignatureCapture'; // DEV-5964 <
  _Skip                 = 'Skip';
  _SkipOffline          = 'SkipOffline';
  _Amount               = 'Amount'; // DEV-5964 >
  _PromptOnManual       = 'PromptOnManual';
  _DeclineCashOnly      = 'DeclineCashOnly'; // DEV-12509
  _StandInForPrePaidCredit = 'StandInForPrePaidCredit'; // DEV-9575
  _PaymentOnAccount     = 'PaymentOnAccount'; // DOEP-33255
  _ContactlessEMV       = 'ContactlessEMV';
  _AllowFallback        = 'AllowFallback';
  _MinimalActivationReceipt = 'MinimalActivationReceipt';
  _AllowRetryOnVerifyLast4 = 'AllowRetryOnVerifyLast4';
  _DiscountType         = 'DiscountType'; // DOEP-45145
  _DiscountAmount       = 'DiscountAmount';
  _PromptForTokenPin    = 'PromptForTokenPin'; //DOEP-72732
  _PromptForPinOnManual = 'PromptForPinOnManual'; // TFS-14049
  _SuppressBalance       = 'SuppressBalance';      // TFS-114195/114196//114465
  _AllowBarTab          = 'AllowBarTab';      // CPCLIENTS-1940
  _TipLineOnReceipt     = 'TipLineOnReceipt'; // CPCLIENTS-1942

  { LocalAuthFlags }
  _LocalAuthFlags       = 'LocalAuthFlags';
  _Purchase             = 'Purchase';
  _Offline              = 'Offline';
  _OfflineVoid          = 'OfflineVoid';
  _Return               = 'Return';
  _Force                = 'Force';
  _PreAuth              = 'PreAuth';
  _Activate             = 'Activate';
  _HoldTab              = 'Hold';      // CPCLIENTS-1932
  _ReleaseTab           = 'Release';   // CPCLIENTS-1932
  { OnlineLimits }
  _OnlineLimits         = 'OnlineLimits';
  _MaxTotalAmount       = 'MaxTotalAmount';
  _MaxCashback          = 'MaxCashback';
  _MinTotalAmount       = 'MinTotalAmount';
  _MinCashback          = 'MinCashback';
  _MinCreditToDebit     = 'MinCreditToDebit';
  _PreauthAmount        = 'PreauthAmount';
  _AmountToHost         = 'AmountToHost';
  _AmountToPump         = 'AmountToPump';
  { OfflineLimits }
  _OfflineLimits        = 'OfflineLimits';
  _NoCVMAmount          = 'NoCVMAmount';            // MCD TFS-7513
  _UseEntryType         = 'UseEntryType';
  _MaxTotalAmountByEntryType = 'MaxTotalAmountByEntryType';
  _Swipe                = 'Swipe';
  _FallbackToSwipe      = 'FallbackToSwipe';
  _Manual               = 'Manual';
  _Chip                 = 'Chip';
  _Barcode              = 'Barcode';
  _RFID                 = 'RFID';
  _EMVContactless       = 'EMVContactless';
  _MaxVoiceAuthLimitByEntryType = 'MaxVoiceAuthLimitByEntryType';

function LoadCardProcessingProfile(const FileName: string): TXMLCardProcessingProfileType;
begin
  result := TXMLCardProcessingProfileType.Create(TXMLCardType);
  if Assigned(result) then
    if not result.LoadFromFile(FileName) then
      FreeAndNil(result);
end;

function NewCardProcessingProfile: TXMLCardProcessingProfileType;
begin
  result := TXMLCardProcessingProfileType.Create(TXMLCardType);
end;

{ TXMLCardProcessingProfileType }

constructor TXMLCardProcessingProfileType.Create(ItemClass: TCollectionItemClass);
begin
  inherited;
  ;
end;

destructor TXMLCardProcessingProfileType.Destroy;
begin
  if Assigned(FCard) then
    FreeAndNil(FCard);
  inherited;
end;

function TXMLCardProcessingProfileType.GetVersion: string;
begin
  Result := FVersion;
end;

procedure TXMLCardProcessingProfileType.SetVersion(Value: string);
begin
  if Value = FVersion then Exit;
  FVersion := Value;
end;

function TXMLCardProcessingProfileType.GetLastModified: string;
begin
  Result := FLastModified;
end;

procedure TXMLCardProcessingProfileType.SetLastModified(Value: string);
begin
  if Value = FLastModified then Exit;
  FLastModified := Value;
end;

function TXMLCardProcessingProfileType.GetCard(Index: Integer): TXMLCardType;
begin
  result := inherited Items[Index] as TXMLCardType;
end;

function TXMLCardProcessingProfileType.Add: TXMLCardType;
begin
  //result := inherited Add as TXMLCardType;
  result := TXMLCardType.Create(Self);
end;

function TXMLCardProcessingProfileType.LoadFromFile(aFileName: string): boolean;
var
  XMLObj: TXMLConfiguration;
  N1, N2, N3, N4: TXMLParserNode;
  ACard: TXMLCardType;
  i,j,k,l: integer;
begin
  result := false;
  try
    if not FileExists(aFileName) then Exit;
    XMLObj := TXMLConfiguration.Create;
    try
      if NOT XMLObj.FXMLParser.LoadFromFile(AnsiString(aFileName)) then             // 828.5
        Exit;                                                           // 828.5
      XMLObj.FXMLParser.StartScan;                                      // 828.5
      XMLObj.ScanElement(nil);

      if not SameText(XMLObj.Root.Name, _CardProcessingProfile) then
        Exit;
      { clear cards }
      while (Count > 0) do
        Card[0].Free;
      Version := XMLObj.Root.Attr.Values[_Version];
      LastModified := XMLObj.Root.Attr.Values[_LastModified];
      for i := 0 to XMLObj.Root.Children.Count - 1 do
      begin
        N1 := XMLObj.Root.Children[i];
        if SameText(N1.Name, _Card) then
        begin
          ACard := TXMLCardType.Create(Self);
          try
            //ACard := Add;
            ACard.Code := N1.Attr.Values[_Code];
            ACard.ProcessingFlags.DiscountType := 'None'; // set default

            for j := 0 to N1.Children.Count-1 do
            begin
              N2 := N1.Children[j];
              if SameText(N2.Name, _CardName) then
                ACard.CardName := N2.Text;
              if SameText(N2.Name, _FleetType) then ACard.FleetType := StrToIntDef(N2.Text, -1);
              { ReportingGroups }
              if SameText(N2.Name, _ReportingGroups) then
                for k := 0 to N2.Children.Count-1 do
                begin
                  N3 := N2.Children[k];
                  if SameText(N3.Name, _HostSettlement) then ACard.ReportingGroups.HostSettlement := N3.Text;
                  if SameText(N3.Name, _StoreSummary) then ACard.ReportingGroups.StoreSummary := N3.Text; //StrToIntDef(N3.Text, 0);
                  if SameText(N3.Name, _LaneSummary) then ACard.ReportingGroups.LaneSummary := N3.Text;
                  if SameText(N3.Name, _CheckerSummary) then ACard.ReportingGroups.CheckerSummary := N3.Text;
                  if SameText(N3.Name, _CheckerSignOff) then ACard.ReportingGroups.CheckerSignOff := N3.Text;
                end;
              { ProcessingFlags }
                if SameText(N2.Name, _ProcessingFlags) then
                  for k := 0 to N2.Children.Count-1 do
                  begin
                    N3 := N2.Children[k];
                    if SameText(N3.Name, _ManualEntryAllowed) then ACard.ProcessingFlags.ManualEntryAllowed := N3.Text
                    else if SameText(N3.Name, _PromptForZipCode) then ACard.ProcessingFlags.PromptForZipCode := N3.Text
                    else if SameText(N3.Name, _PromptForCVV2) then ACard.ProcessingFlags.PromptForCVV2 := N3.Text   // JTG Dev-6418
                    else if SameText(N3.Name, _PromptForSecurityCode) then ACard.ProcessingFlags.PromptForSecurityCode := N3.Text
                    else if SameText(N3.Name, _PromptForPO) then ACard.ProcessingFlags.PromptForPO := N3.Text
                    else if SameText(N3.Name, _PromptForTaxAmount) then ACard.ProcessingFlags.PromptForTaxAmount := N3.Text
                    else if SameText(N3.Name, _Mod10Check) then ACard.ProcessingFlags.Mod10Check := N3.Text
                    else if SameText(N3.Name, _PINRequired) then ACard.ProcessingFlags.PINRequired := N3.Text
                    else if SameText(N3.Name, _CustomerNameOnReceipt) then ACard.ProcessingFlags.CustomerNameOnReceipt := N3.Text
                    else if SameText(N3.Name, _Cashback) then
                    begin
                      ACard.ProcessingFlags.Cashback.Allowed := N3.Attr.Values[_Allowed];
                      ACard.ProcessingFlags.Cashback.CashOnManual := N3.Attr.Values[_CashOnManual]; 
                      ACard.ProcessingFlags.Cashback.AllowMaxCashBack := N3.Attr.Values[_AllowMaxCashBack]; // DEV-10250
                    end
                    else if SameText(N3.Name, _ExpirationDate) then
                    begin
                      ACard.ProcessingFlags.ExpirationDate.Required := N3.Attr.Values[_Required];
                      ACard.ProcessingFlags.ExpirationDate.SendOnlineOnly := N3.Attr.Values[_SendOnlineOnly]
                    end
                    else if SameText(N3.Name, _DoNotAllowPartialApproval) then ACard.ProcessingFlags.DoNotAllowPartialApproval := N3.Text // DEV-10666
                    else if SameText(N3.Name, _SkipSignatureCapture) then // DEV-5964 <
                    begin
                      ACard.ProcessingFlags.SkipSignatureCapture.Amount := StrToIntDef(N3.Text, 0); //StrToIntDef(N3.Attr.Values[_Amount], 0);
                      ACard.ProcessingFlags.SkipSignatureCapture.Skip := Trim(N3.Attr.Values[_Skip]); // DEV-5964
                      ACard.ProcessingFlags.SkipSignatureCapture.SkipOffline := SameText(N3.Attr.Values[_SkipOffline], 'Y');
                      ACard.ProcessingFlags.SkipSignatureCapture.PromptOnManual := iif(SameText(Trim(N3.Attr.Values[_PromptOnManual]),'N'), 'N', 'Y'); // DEV-20085: default Y
                    end // DEV-5964 >
                    else if SameText(N3.Name, _DeclineCashOnly) then ACard.ProcessingFlags.DeclineCashOnly := N3.Text // DEV-12509
                    else if SameText(N3.Name, _StandInForPrePaidCredit) then ACard.ProcessingFlags.StandInForPrePaidCredit := YN(SameText(N3.Text, 'Y'))
                    else if SameText(N3.Name, _PaymentOnAccount) then ACard.ProcessingFlags.PaymentOnAccount := YN(SameText(N3.Text, 'Y')) // DOEP-33255
                    else if SameText(N3.Name, _AllowFallback) then ACard.ProcessingFlags.AllowFallback := YN(SameText(N3.Text, 'Y')) // CPCLIENTS-19794
                    else if SameText(N3.Name, _MinimalActivationReceipt) then ACard.ProcessingFlags.PrintMinimalReceipt := YN(SameText(N3.Text, 'Y'))
                    else if SameText(N3.Name, _AllowRetryOnVerifyLast4) then ACard.ProcessingFlags.AllowRetryOnVerifyLast4 := YN(SameText(N3.Text, 'Y'))
                    else if SameText(N3.Name, _ContactlessEMV) then ACard.ProcessingFlags.ContactlessEMV := YN(SameText(N3.Text, 'Y'))
                    else if SameText(N3.Name, _DiscountType) then
                    begin
                      if SameText(N3.Text, 'A') then
                        ACard.ProcessingFlags.DiscountType := 'Amount'
                      else if SameText(N3.Text, 'P') then
                        ACard.ProcessingFlags.DiscountType := 'Percent';
                    end
                    else if SameText(N3.Name, _DiscountAmount) then ACard.ProcessingFlags.DiscountAmount := N3.Text
                    else if SameText(N3.Name, _SuppressBalance) then ACard.ProcessingFlags.SuppressBalance := UpperCase(N3.Text)   // TFS-114195/114196/114465, just 'Y' or 'N', or not present
                    else if SameText(N3.Name, _PromptForOdometer) then ACard.ProcessingFlags.PromptForOdometer := YN(SameText(N3.Text, 'Y'))
                    else if SameText(N3.Name, _PromptForTokenPin) then ACard.ProcessingFlags.PromptForTokenPin := YN(SameText(N3.Text, 'Y')) // DOEP-72732
                    else if SameText(N3.Name, _PromptForPinOnManual) then ACard.ProcessingFlags.PromptForPinOnManual := YN(SameText(N3.Text, 'Y')) // TFS-14047
                    else if SameText(N3.Name, _AllowBarTab) then ACard.ProcessingFlags.AllowBarTab := not SameText(N3.Text, 'N')       // CPCLIENTS-1940
                    else if SameText(N3.Name, _TipLineOnReceipt) then ACard.ProcessingFlags.TipLineOnReceipt := SameText(N3.Text, 'Y'); // CPCLIENTS-1942
                  end;
              { LocalAuthFlags }
               if SameText(N2.Name, _LocalAuthFlags) then
                  for k := 0 to N2.Children.Count-1 do
                  begin
                    N3 := N2.Children[k];
                    if SameText(N3.Name, _Purchase) then
                    begin
                      //ACard.LocalAuthFlags.Purchase.Offline := YN(N3.Attr.Values[_Offline]='Y');
                      ACard.LocalAuthFlags.Purchase.Offline := N3.Attr.Values[_Offline]; // DEV-28404
                      ACard.LocalAuthFlags.Purchase.OfflineVoid := YN(N3.Attr.Values[_OfflineVoid]='Y');
                    end;
                    if SameText(N3.Name, _Return) then
                    begin
                      ACard.LocalAuthFlags.Return.Offline := YN(N3.Attr.Values[_Offline]='Y');
                      ACard.LocalAuthFlags.Return.OfflineVoid := YN(N3.Attr.Values[_OfflineVoid]='Y');
                    end;
                    if SameText(N3.Name, _Force) then
                    begin
                      ACard.LocalAuthFlags.Force.Offline := YN(N3.Attr.Values[_Offline]='Y');
                      ACard.LocalAuthFlags.Force.OfflineVoid := YN(N3.Attr.Values[_OfflineVoid]='Y');
                    end;
                    if SameText(N3.Name, _PreAuth) then
                    begin
                      ACard.LocalAuthFlags.PreAuth.Offline := YN(N3.Attr.Values[_Offline]='Y');
                      ACard.LocalAuthFlags.PreAuth.OfflineVoid := YN(N3.Attr.Values[_OfflineVoid]='Y');
                    end;
                    if SameText(N3.Name, _Activate) then
                    begin
                      ACard.LocalAuthFlags.Activate.Offline := YN(N3.Attr.Values[_Offline]='Y');
                      ACard.LocalAuthFlags.Activate.OfflineVoid := YN(N3.Attr.Values[_OfflineVoid]='Y');
                    end;
                    if SameText(N3.Name, _HoldTab) then                           // CPCLIENTS-1932 <
                    begin
                      ACard.LocalAuthFlags.HoldTab.Offline := YN(N3.Attr.Values[_Offline]='Y');
                      ACard.LocalAuthFlags.HoldTab.OfflineVoid := YN(N3.Attr.Values[_OfflineVoid]='Y');
                    end;
                    if SameText(N3.Name, _ReleaseTab) then
                    begin
                      ACard.LocalAuthFlags.ReleaseTab.Offline := YN(N3.Attr.Values[_Offline]='Y');
                      ACard.LocalAuthFlags.ReleaseTab.OfflineVoid := YN(N3.Attr.Values[_OfflineVoid]='Y');
                    end;                                                          // CPCLIENTS-1932 >
                  end;
              { OnlineLimits }
               if SameText(N2.Name, 'OnlineLimits') then
                  for k := 0 to N2.Children.Count-1 do
                  begin
                    N3 := N2.Children[k];
                    if SameText(N3.Name, _MaxTotalAmount) then ACard.OnlineLimits.MaxTotalAmount := StrToIntDef(N3.Text, 0);
                    if SameText(N3.Name, _MaxCashback) then ACard.OnlineLimits.MaxCashback := StrToIntDef(N3.Text, 0);
                    if SameText(N3.Name, _MinTotalAmount) then ACard.OnlineLimits.MinTotalAmount := StrToIntDef(N3.Text, 0);
                    if SameText(N3.Name, _MinCashback) then ACard.OnlineLimits.MinCashback := StrToIntDef(N3.Text, 0);
                    if SameText(N3.Name, _MinCreditToDebit) then ACard.OnlineLimits.MinCreditToDebit := StrToIntDef(N3.Text, 0);
                    if SameText(N3.Name, _NoCVMAmount) then ACard.OnlineLimits.NoCVMAmount := StrToIntDef(N3.Text, 0);
                    if SameText(N3.Name, _PreauthAmount) then
                      for l := 0 to N3.Children.Count-1 do
                      begin
                        N4 := N3.Children[l];
                        if SameText(N4.Name, _AmountToHost) then ACard.OnlineLimits.PreauthAmount.AmountToHost := StrToIntDef(N4.Text, 0);
                        if SameText(N4.Name, _AmountToPump) then ACard.OnlineLimits.PreauthAmount.AmountToPump := StrToIntDef(N4.Text, 0);
                      end;
                      if SameText(N3.Name, _NoCVMAmount) then ACard.OnlineLimits.NoCVMAmount := StrToIntDef(N3.Text, 0);  // MCD TFS-7513
                  end;
              { OfflineLimits }
               if SameText(N2.Name, _OfflineLimits) then
               begin
                  ACard.OfflineLimits.UseEntryType := SameText(N2.Attr.Values[_UseEntryType], 'Y'); // TFS-14557
                  for k := 0 to N2.Children.Count-1 do
                  begin
                    N3 := N2.Children[k];
                    if SameText(N3.Name, _MaxTotalAmount) then ACard.OfflineLimits.MaxTotalAmount := StrToIntDef(N3.Text, 0);
                    if SameText(N3.Name, _MaxCashback) then ACard.OfflineLimits.MaxCashback := StrToIntDef(N3.Text, 0);
                    if SameText(N3.Name, _MinCreditToDebit) then ACard.OfflineLimits.MinCreditToDebit := StrToIntDef(N3.Text, 0);
                    if SameText(N3.Name, _PreauthAmount) then
                      for l := 0 to N3.Children.Count-1 do
                      begin
                        N4 := N3.Children[l];
                        // AFR, 5/11/2007 - OnlineLimits -> OfflineLimits
                        //if SameText(N4.Name, _AmountToHost) then ACard.OfflineLimits.PreauthAmount.AmountToHost := StrToIntDef(N4.Text, 0);
                        if SameText(N4.Name, _AmountToPump) then ACard.OfflineLimits.PreauthAmount.AmountToPump := StrToIntDef(N4.Text, 0);
                      end;
                    // TFS-14557
                    if SameText(N3.Name, _MaxTotalAmountByEntryType) then
                    begin
                      ACard.OfflineLimits.MaxTotalAmountByEntryType.SwipeAmount := StrToIntDef(N3.Attr.Values[_Swipe], ACard.OfflineLimits.MaxTotalAmount);
                      ACard.OfflineLimits.MaxTotalAmountByEntryType.FallbackToSwipeAmount := StrToIntDef(N3.Attr.Values[_FallbackToSwipe], ACard.OfflineLimits.MaxTotalAmount);
                      ACard.OfflineLimits.MaxTotalAmountByEntryType.ManualAmount := StrToIntDef(N3.Attr.Values[_Manual], ACard.OfflineLimits.MaxTotalAmount);
                      ACard.OfflineLimits.MaxTotalAmountByEntryType.ChipAmount := StrToIntDef(N3.Attr.Values[_Chip], ACard.OfflineLimits.MaxTotalAmount);
                      ACard.OfflineLimits.MaxTotalAmountByEntryType.BarcodeAmount := StrToIntDef(N3.Attr.Values[_Barcode], ACard.OfflineLimits.MaxTotalAmount);
                      ACard.OfflineLimits.MaxTotalAmountByEntryType.RFIDAmount := StrToIntDef(N3.Attr.Values[_RFID], ACard.OfflineLimits.MaxTotalAmount);
                      ACard.OfflineLimits.MaxTotalAmountByEntryType.EMVContactlessAmount := StrToIntDef(N3.Attr.Values[_EMVContactless], ACard.OfflineLimits.MaxTotalAmount);
                    end;
                    // TFS-15312
                    if SameText(N3.Name, _MaxVoiceAuthLimitByEntryType) then
                    begin
                      ACard.OfflineLimits.MaxVoiceAuthLimitByEntryType.SwipeAmount := StrToIntDef(N3.Attr.Values[_Swipe], 0);
                      ACard.OfflineLimits.MaxVoiceAuthLimitByEntryType.FallbackToSwipeAmount := StrToIntDef(N3.Attr.Values[_FallbackToSwipe], 0);
                      ACard.OfflineLimits.MaxVoiceAuthLimitByEntryType.ManualAmount := StrToIntDef(N3.Attr.Values[_Manual], 0);
                      ACard.OfflineLimits.MaxVoiceAuthLimitByEntryType.ChipAmount := StrToIntDef(N3.Attr.Values[_Chip], 0);
                      ACard.OfflineLimits.MaxVoiceAuthLimitByEntryType.BarcodeAmount := StrToIntDef(N3.Attr.Values[_Barcode], 0);
                      ACard.OfflineLimits.MaxVoiceAuthLimitByEntryType.RFIDAmount := StrToIntDef(N3.Attr.Values[_RFID], 0);
                      ACard.OfflineLimits.MaxVoiceAuthLimitByEntryType.EMVContactlessAmount := StrToIntDef(N3.Attr.Values[_EMVContactless], 0);
                    end;
                  end;
               end;
            end; // for j
          finally
//            ACard.Free;
          end;
          // default values for new tag
          if ACard.ProcessingFlags.ContactlessEMV = '' then
            ACard.ProcessingFlags.ContactlessEMV := 'Y';
          if ACard.ProcessingFlags.AllowFallback = '' then
            ACard.ProcessingFlags.AllowFallback := 'Y';
        end;
      end; // for i
    finally
      FreeAndNil(XMLObj);
    end;
    result := true;
  except
    on e: exception do
      SM(AnsiString('****TRY..EXCEPT: TXMLCardProcessingProfileType.LoadFromFile: ' + aFileName + ' - ' + e.message));
  end;
end;

function TXMLCardProcessingProfileType.SaveToFile(aFileName: string): boolean;
var
  Root, N1, N2, N3: TXMLParserNode;
  i: Integer;
begin
  result := false;
  try
    Root := TXMLParserNode.Create(nil);
    try
      Root.Name := _CardProcessingProfile;
      Root.Attr.Values[_Version] := Version;
      Root.Attr.Values[_LastModified] := FormatDateTime(FORMAT_LASTMODIFIED, Now);
      for i := 0 to Count -1 do
      begin
        N1 := Root.AddChild(_Card);
        N1.Attr.Values[_Code] := Card[i].Code;
        N1.AddChild(_CardName).Text := Card[i].CardName;
        N1.AddChild(_FleetType).Text := IntToStr(Card[i].FleetType);
        { Reporting Groups }
        N2 := N1.AddChild(_ReportingGroups);
        N2.AddChild(_HostSettlement).Text      := Card[i].ReportingGroups.HostSettlement;
        N2.AddChild(_StoreSummary).Text        := Card[i].ReportingGroups.StoreSummary; // IntToStr(Card[i].ReportingGroups.StoreSummary);
        N2.AddChild(_LaneSummary).Text         := Card[i].ReportingGroups.LaneSummary;
        N2.AddChild(_CheckerSummary).Text      := Card[i].ReportingGroups.CheckerSummary;
        N2.AddChild(_CheckerSignOff).Text      := Card[i].ReportingGroups.CheckerSignOff;
        { ProcessingFlags }
        N2 := N1.AddChild(_ProcessingFlags);
        N2.AddChild(_ManualEntryAllowed).Text  := YN(Card[i].ProcessingFlags.ManualEntryAllowed='Y');
        N2.AddChild(_PromptForZipCode).Text    := YN(Card[i].ProcessingFlags.PromptForZipCode='Y');
        N2.AddChild(_PromptForCVV2).Text       := YN(Card[i].ProcessingFlags.PromptForCVV2='Y');
        N2.AddChild(_PromptForSecurityCode).Text := YN(Card[i].ProcessingFlags.PromptForSecurityCode='Y');
        N2.AddChild(_PromptForPO).Text         := YN(Card[i].ProcessingFlags.PromptForPO='Y');  // JTG Dev-6418
        N2.AddChild(_PromptForTaxAmount).Text  := YN(Card[i].ProcessingFlags.PromptForTaxAmount='Y');  
        N2.AddChild(_PromptForOdometer).Text := YN(Card[i].ProcessingFlags.PromptForOdometer='Y');
        N2.AddChild(_Mod10Check).Text          := YN(Card[i].ProcessingFlags.Mod10Check='Y');
        N2.AddChild(_PINRequired).Text         := YN(Card[i].ProcessingFlags.PINRequired='Y');
        N2.AddChild(_CustomerNameOnReceipt).Text := YN(Card[i].ProcessingFlags.CustomerNameOnReceipt='Y');
        N3 := N2.AddChild(_Cashback);
        N3.Attr.Values[_Allowed]               := YN(Card[i].ProcessingFlags.Cashback.Allowed='Y');
        N3.Attr.Values[_CashOnManual]          := YN(Card[i].ProcessingFlags.Cashback.CashOnManual='Y');
        N3.Attr.Values[_AllowMaxCashBack]      := YN(SameText(Card[i].ProcessingFlags.Cashback.AllowMaxCashBack,'Y')); // DEV-10250
        N3 := N2.AddChild(_ExpirationDate);
        N3.Attr.Values[_Required]              := YN(Card[i].ProcessingFlags.ExpirationDate.Required='Y');
        N3.Attr.Values[_SendOnlineOnly]        := YN(Card[i].ProcessingFlags.ExpirationDate.SendOnlineOnly='Y');
        N2.AddChild(_DoNotAllowPartialApproval).Text := YN(Card[i].ProcessingFlags.DoNotAllowPartialApproval='Y'); // DEV-10666
        N2.AddChild(_ContactlessEMV).Text := YN(Card[i].ProcessingFlags.ContactlessEMV='Y');
        N2.AddChild(_AllowFallback).Text := YN(Card[i].ProcessingFlags.AllowFallback='Y');
        N2.AddChild(_MinimalActivationReceipt).Text := YN(Card[i].ProcessingFlags.PrintMinimalReceipt='Y');
        N2.AddChild(_AllowRetryOnVerifyLast4).Text := YN(Card[i].ProcessingFlags.AllowRetryOnVerifyLast4='Y');
        if SameText(Card[i].ProcessingFlags.SkipSignatureCapture.Skip, 'Y') then
        begin
          N3 := N2.AddChild(_SkipSignatureCapture); // DEV-5964 <
              //N3.Attr.Values[_Amount]               := IntToStr(Card[i].ProcessingFlags.SkipSignatureCapture.Amount);
              N3.Text := IntToStr(Card[i].ProcessingFlags.SkipSignatureCapture.Amount);
              N3.Attr.Values[_Skip]                 := YN(SameText(Card[i].ProcessingFlags.SkipSignatureCapture.Skip, 'Y')); // DEV-5964
              N3.Attr.Values[_SkipOffline]          := YN(Card[i].ProcessingFlags.SkipSignatureCapture.SkipOffline); // DEV-5964 >
              N3.Attr.Values[_PromptOnManual]       := YN(Card[i].ProcessingFlags.SkipSignatureCapture.PromptOnManual <> 'N'); // DEV-20085
        end;
        N2.AddChild(_DeclineCashOnly).Text := YN(Card[i].ProcessingFlags.DeclineCashOnly='Y'); // DEV-12509
        N2.AddChild(_StandInForPrePaidCredit).Text := YN(SameText(Card[i].ProcessingFlags.StandInForPrePaidCredit,'Y')); // DEV-9575
        N2.AddChild(_PaymentOnAccount).Text := YN(SameText(Card[i].ProcessingFlags.PaymentOnAccount,'Y')); // DOEP-33255
        if SameText(Card[i].ProcessingFlags.DiscountType,'Amount') then // DOEP-45145
          N2.AddChild(_DiscountType).Text := 'A'
        else if SameText(Card[i].ProcessingFlags.DiscountType,'Percent') then
          N2.AddChild(_DiscountType).Text := 'P'
        else N2.AddChild(_DiscountType).Text := '';
        N2.AddChild(_DiscountAmount).Text := FormatFloat(StringOfChar('0',CPP_DISCOUNT_AMOUNT_LENGTH), StrToIntDef(Card[i].ProcessingFlags.DiscountAmount,0));
        N2.AddChild(_PromptForTokenPin).Text := YN(SameText(Card[i].ProcessingFlags.PromptForTokenPin,'Y')); // DOEP-72732
        N2.AddChild(_PromptForPinOnManual).Text := YN(SameText(Card[i].ProcessingFlags.PromptForPinOnManual, 'Y')); // TFS-14047
        N2.AddChild(_AllowBarTab).Text := YN(Card[i].ProcessingFlags.AllowBarTab);           // CPCLIENTS-1940
        N2.AddChild(_TipLineOnReceipt).Text := YN(Card[i].ProcessingFlags.TipLineOnReceipt); // CPCLIENTS-1942
        { LocalAuthFlags }
        N2 := N1.AddChild(_LocalAuthFlags);
        N3 := N2.AddChild(_Purchase);
        //N3.Attr.Values[_Offline]               := YN(Card[i].LocalAuthFlags.Purchase.Offline='Y'); // DEV-28404
        N3.Attr.Values[_Offline]               := Card[i].LocalAuthFlags.Purchase.Offline; // DEV-28404
        N3.Attr.Values[_OfflineVoid]           := YN(Card[i].LocalAuthFlags.Purchase.OfflineVoid='Y');
        N3 := N2.AddChild(_Return);
        N3.Attr.Values[_Offline]               := YN(Card[i].LocalAuthFlags.Return.Offline='Y');
        N3.Attr.Values[_OfflineVoid]           := YN(Card[i].LocalAuthFlags.Return.OfflineVoid='Y');
        N3 := N2.AddChild(_Force);
        N3.Attr.Values[_Offline]               := YN(Card[i].LocalAuthFlags.Force.Offline='Y');
        N3.Attr.Values[_OfflineVoid]           := YN(Card[i].LocalAuthFlags.Force.OfflineVoid='Y');
        N3 := N2.AddChild(_PreAuth);
        N3.Attr.Values[_Offline]               := YN(Card[i].LocalAuthFlags.PreAuth.Offline='Y');
        N3.Attr.Values[_OfflineVoid]           := YN(Card[i].LocalAuthFlags.PreAuth.OfflineVoid='Y');
        N3 := N2.AddChild(_Activate);
        N3.Attr.Values[_Offline]               := YN(Card[i].LocalAuthFlags.Activate.Offline='Y');
        N3.Attr.Values[_OfflineVoid]           := YN(Card[i].LocalAuthFlags.Activate.OfflineVoid='Y');
        N3 := N2.AddChild(_HoldTab);                                                                       // CPCLIENTS-1932 start
        N3.Attr.Values[_Offline]               := YN(Card[i].LocalAuthFlags.HoldTab.Offline='Y');
        N3.Attr.Values[_OfflineVoid]           := YN(Card[i].LocalAuthFlags.HoldTab.OfflineVoid='Y');
        N3 := N2.AddChild(_ReleaseTab);
        N3.Attr.Values[_Offline]               := YN(Card[i].LocalAuthFlags.ReleaseTab.Offline='Y');
        N3.Attr.Values[_OfflineVoid]           := YN(Card[i].LocalAuthFlags.ReleaseTab.OfflineVoid='Y');   // CPCLIENTS-1932 end
        { OnlineLimits }
        N2 := N1.AddChild(_OnlineLimits);
        N2.AddChild(_MaxTotalAmount).Text      := IntToStr(Card[i].OnlineLimits.MaxTotalAmount);
        N2.AddChild(_MaxCashback).Text         := IntToStr(Card[i].OnlineLimits.MaxCashback);
        N2.AddChild(_MinTotalAmount).Text      := IntToStr(Card[i].OnlineLimits.MinTotalAmount);
        N2.AddChild(_MinCashback).Text         := IntToStr(Card[i].OnlineLimits.MinCashback);
        N2.AddChild(_MinCreditToDebit).Text    := IntToStr(Card[i].OnlineLimits.MinCreditToDebit);
        N2.AddChild(_NoCVMAmount).Text         := IntToStr(Card[i].OnlineLimits.NoCVMAmount);
        N3 := N2.AddChild(_PreauthAmount);
        N3.AddChild(_AmountToHost).Text        := IntToStr(Card[i].OnlineLimits.PreauthAmount.AmountToHost);
        N3.AddChild(_AmountToPump).Text        := IntToStr(Card[i].OnlineLimits.PreauthAmount.AmountToPump);
        { OfflineLimits }
        N2 := N1.AddChild(_OfflineLimits);
        N2.Attr.Values[_UseEntryType]          := YN(Card[i].OfflineLimits.UseEntryType); // TFS-14557
        N2.AddChild(_MaxTotalAmount).Text      := IntToStr(Card[i].OfflineLimits.MaxTotalAmount);
        N2.AddChild(_MaxCashback).Text         := IntToStr(Card[i].OfflineLimits.MaxCashback);
        N2.AddChild(_MinCreditToDebit).Text    := IntToStr(Card[i].OfflineLimits.MinCreditToDebit);
        N3 := N2.AddChild(_PreauthAmount);

        // AFR, 5/11/2007 - OnlineLimits -> OfflineLimits
        //N3.AddChild(_AmountToHost).Text        := IntToStr(Card[i].OfflineLimits.PreauthAmount.AmountToHost);
        N3.AddChild(_AmountToPump).Text        := IntToStr(Card[i].OfflineLimits.PreauthAmount.AmountToPump);
        // TFS-14557
        N3 := N2.AddChild(_MaxTotalAmountByEntryType);
        N3.Attr.Values[_EMVContactless]        := IntToStr(Card[i].OfflineLimits.MaxTotalAmountByEntryType.EMVContactlessAmount);
        N3.Attr.Values[_RFID]                  := IntToStr(Card[i].OfflineLimits.MaxTotalAmountByEntryType.RFIDAmount);
        N3.Attr.Values[_Barcode]               := IntToStr(Card[i].OfflineLimits.MaxTotalAmountByEntryType.BarcodeAmount);
        N3.Attr.Values[_Chip]                  := IntToStr(Card[i].OfflineLimits.MaxTotalAmountByEntryType.ChipAmount);
        N3.Attr.Values[_Manual]                := IntToStr(Card[i].OfflineLimits.MaxTotalAmountByEntryType.ManualAmount);
        N3.Attr.Values[_FallbackToSwipe]       := IntToStr(Card[i].OfflineLimits.MaxTotalAmountByEntryType.FallbackToSwipeAmount);
        N3.Attr.Values[_Swipe]                 := IntToStr(Card[i].OfflineLimits.MaxTotalAmountByEntryType.SwipeAmount);
        // TFS-15312
        N3 := N2.AddChild(_MaxVoiceAuthLimitByEntryType);
        N3.Attr.Values[_EMVContactless]        := IntToStr(Card[i].OfflineLimits.MaxVoiceAuthLimitByEntryType.EMVContactlessAmount);
        N3.Attr.Values[_RFID]                  := IntToStr(Card[i].OfflineLimits.MaxVoiceAuthLimitByEntryType.RFIDAmount);
        N3.Attr.Values[_Barcode]               := IntToStr(Card[i].OfflineLimits.MaxVoiceAuthLimitByEntryType.BarcodeAmount);
        N3.Attr.Values[_Chip]                  := IntToStr(Card[i].OfflineLimits.MaxVoiceAuthLimitByEntryType.ChipAmount);
        N3.Attr.Values[_Manual]                := IntToStr(Card[i].OfflineLimits.MaxVoiceAuthLimitByEntryType.ManualAmount);
        N3.Attr.Values[_FallbackToSwipe]       := IntToStr(Card[i].OfflineLimits.MaxVoiceAuthLimitByEntryType.FallbackToSwipeAmount);
        N3.Attr.Values[_Swipe]                 := IntToStr(Card[i].OfflineLimits.MaxVoiceAuthLimitByEntryType.SwipeAmount);
      end; // end of for
      Root.SaveToFile(aFileName);
    finally
      FreeAndNil(Root);
    end;
    result := true;
  except
    on e: exception do
      SM(AnsiString('****TRY..EXCEPT: TXMLCardProcessingProfileType.SaveToFile: ' + aFileName + ' - ' + e.message));
  end;
end;

{ TXMLCardType }

constructor TXMLCardType.Create(Collection: TCollection);
begin
  inherited;
  FReportingGroups := TXMLReportingGroupsType.Create;
  FProcessingFlags := TXMLProcessingFlagsType.Create;
  FLocalAuthFlags := TXMLLocalAuthFlagsType.Create;
  FOnlineLimits := TXMLOnlineLimitsType.Create;
  FOfflineLimits := TXMLOfflineLimitsType.Create;
end;

destructor TXMLCardType.Destroy;
begin
  FreeAndNil(FReportingGroups);
  FreeAndNil(FProcessingFlags);
  FreeAndNil(FLocalAuthFlags);
  FreeAndNil(FOnlineLimits);
  FreeAndNil(FOfflineLimits);
  inherited;
end;

function TXMLCardType.GetCode: string;
begin
  Result := FCode;
end;

procedure TXMLCardType.SetCode(Value: string);
begin
  if Value = FCode then Exit;
  FCode := Value;
end;

function TXMLCardType.GetCardName: string;
begin
  Result := FCardName;
end;

procedure TXMLCardType.SetCardName(Value: string);
begin
  if Value = FCardName then Exit;
  FCardName := Value;
end;

function TXMLCardType.GetFleetType: Integer;
begin
  Result := FFleetType;
end;

procedure TXMLCardType.SetFleetType(Value: Integer);
begin
  if Value = FFleetType then Exit;
  FFleetType := Value;
end;

function TXMLCardType.GetReportingGroups: TXMLReportingGroupsType;
begin
  Result := FReportingGroups;
end;

function TXMLCardType.GetProcessingFlags: TXMLProcessingFlagsType;
begin
  Result := FProcessingFlags;
end;

function TXMLCardType.GetLocalAuthFlags: TXMLLocalAuthFlagsType;
begin
  Result := FLocalAuthFlags;
end;

function TXMLCardType.GetOnlineLimits: TXMLOnlineLimitsType;
begin
  Result := FOnlineLimits;
end;

function TXMLCardType.GetOfflineLimits: TXMLOfflineLimitsType;
begin
  Result := FOfflineLimits;
end;

{ TXMLReportingGroupsType }

function TXMLReportingGroupsType.GetHostSettlement: string;
begin
  Result := FHostSettlement;
end;

procedure TXMLReportingGroupsType.SetHostSettlement(Value: string);
begin
  if Value = FHostSettlement then Exit;
  FHostSettlement := Value;
end;

function TXMLReportingGroupsType.GetStoreSummary: string;
begin
  Result := FStoreSummary;
end;

procedure TXMLReportingGroupsType.SetStoreSummary(Value: string);
begin
  if Value = FStoreSummary then Exit;
  FStoreSummary := Value;
end;

function TXMLReportingGroupsType.GetLaneSummary: string;
begin
  Result := FLaneSummary;
end;

procedure TXMLReportingGroupsType.SetLaneSummary(Value: string);
begin
  if Value = FLaneSummary then Exit;
  FLaneSummary := Value;
end;

function TXMLReportingGroupsType.GetCheckerSummary: string;
begin
  Result := FCheckerSummary;
end;

procedure TXMLReportingGroupsType.SetCheckerSummary(Value: string);
begin
  if Value = FCheckerSummary then Exit;
  FCheckerSummary := Value;
end;

function TXMLReportingGroupsType.GetCheckerSignOff: string;
begin
  Result := FCheckerSignOff;
end;

procedure TXMLReportingGroupsType.SetCheckerSignOff(Value: string);
begin
  if Value = FCheckerSignOff then Exit;
  FCheckerSignOff := Value;
end;

{ TXMLProcessingFlagsType }

constructor TXMLProcessingFlagsType.Create;
begin
  inherited;
  FCashback := TXMLCashbackType.Create;
  FExpirationDate := TXMLExpirationDateType.Create;
  SkipSignatureCapture := TXMLSkipSignatureCaptureType.Create;
  DoNotAllowPartialApproval := 'N'; // DEV-10666 default value
  FStandInForPrePaidCredit := 'N'; // DEV-9575
  PaymentOnAccount := 'N'; // DOEP-33255
  FPromptForTokenPin := 'N'; // DOEP-72732
  FPromptForPinOnManual := 'N'; //TFS-14047
  fSuppressBalance := 'N';         // TFS-114195/114196/114465, defaults to 'N' for not present
  FAllowBarTab := True;      // CPCLIENTS-1940
  FTipLineOnReceipt := True; // CPCLIENTS-1942
end;

destructor TXMLProcessingFlagsType.Destroy;
begin
  FreeAndNil(FCashback);
  FreeAndNil(FExpirationDate);
  if Assigned(SkipSignatureCapture) then // DEV-5964 ******** 
    FreeAndNil(SkipSignatureCapture); // DEV-5964
  inherited;
end;

function TXMLProcessingFlagsType.GetSkipSigLine: boolean;           // JTG Dev 5964
begin
  result := FSkipSigLine;
end;

procedure TXMLProcessingFlagsType.SetSkipSigLine(Value: boolean);   // JTG Dev 5964
begin
  if Value <> FSkipSigLine then
    FSkipSigLine := Value;
end;

function TXMLProcessingFlagsType.GetSkipSigLineAmount: integer;        // JTG Dev 5964
begin
  result := FSkipSigLineAmount;
end;

procedure TXMLProcessingFlagsType.SetSkipSigLineAmount(Value: integer); // JTG Dev 5964
begin
  if Value <> FSkipSigLineAmount then
    FSkipSigLineAmount := Value;
end;

function TXMLProcessingFlagsType.GetManualEntryAllowed: string;
begin
  Result := FManualEntryAllowed;
end;

procedure TXMLProcessingFlagsType.SetManualEntryAllowed(Value: string);
begin
  if Value = FManualEntryAllowed then Exit;
  FManualEntryAllowed := Value;
end;

function TXMLProcessingFlagsType.GetPromptForZipCode: string;
begin
  Result := FPromptForZipCode;
end;

procedure TXMLProcessingFlagsType.SetPromptForZipCode(Value: string);
begin
  if Value = FPromptForZipCode then Exit;
  FPromptForZipCode := Value;
end;

function TXMLProcessingFlagsType.GetPromptForCVV2: string;
begin
  Result := FPromptForCVV2;
end;

procedure TXMLProcessingFlagsType.SetPromptForCVV2(Value: string);
begin
  if Value <> FPromptForCVV2 then
    FPromptForCVV2 := Value;
end;

function TXMLProcessingFlagsType.GetPromptForSecurityCode: string;
begin
  Result := FPromptForSecurityCode;
end;

procedure TXMLProcessingFlagsType.SetPromptForSecurityCode(Value: string);
begin
  if Value <> FPromptForSecurityCode then
    FPromptForSecurityCode := Value;
end;

function TXMLProcessingFlagsType.GetPromptForPO: string;     // JTG Dev-6418
begin
  result := FPromptForPO;
end;

procedure TXMLProcessingFlagsType.SetPromptForPO(Value: string);   // JTG Dev-6418
begin
  if Value <> FPromptForPO
    then FPromptForPO := Value;
end;

function TXMLProcessingFlagsType.GetMod10Check: string;
begin
  result := FMod10Check;
end;

procedure TXMLProcessingFlagsType.SetMod10Check(Value: string);
begin
  if Value <> FMod10Check then
    FMod10Check := Value;
end;

function TXMLProcessingFlagsType.GetPINRequired: string;
begin
  result := FPINRequired;
end;

procedure TXMLProcessingFlagsType.SetPINRequired(Value: string);
begin
  if Value <> FPINRequired then
    FPINRequired := Value;
end;

function TXMLProcessingFlagsType.GetCustomerNameOnReceipt: string;
begin
  result := FCustomerNameOnReceipt;
end;

procedure TXMLProcessingFlagsType.SetCustomerNameOnReceipt(Value: string);
begin
  if Value = FCustomerNameOnReceipt then Exit;
  FCustomerNameOnReceipt := Value;
end;

function TXMLProcessingFlagsType.GetCashback: TXMLCashbackType;
begin
  Result := FCashback;
end;

function TXMLProcessingFlagsType.GetExpirationDate: TXMLExpirationDateType;
begin
  Result := FExpirationDate;
end;

{ TXMLCashbackType }

function TXMLCashbackType.GetAllowed: string;
begin
  Result := FAllowed;
end;

procedure TXMLCashbackType.SetAllowed(Value: string);
begin
  if Value = FAllowed then Exit;
  FAllowed := Value;
end;

function TXMLCashbackType.GetCashOnManual: string;
begin
  Result := FCashOnManual;
end;

procedure TXMLCashbackType.SetCashOnManual(Value: string);
begin
  if Value = FCashOnManual then Exit;
  FCashOnManual := Value;
end;

{ TXMLExpirationDateType }

function TXMLExpirationDateType.GetRequired: string;
begin
  Result := FRequired;
end;

procedure TXMLExpirationDateType.SetRequired(Value: string);
begin
  if Value = FRequired then Exit;
  FRequired := Value;
end;

function TXMLExpirationDateType.GetSendOnlineOnly: string;
begin
  Result := FSendOnlineOnly;
end;

procedure TXMLExpirationDateType.SetSendOnlineOnly(Value: string);
begin
  if Value = FSendOnlineOnly then Exit;
  FSendOnlineOnly := Value;
end;

{ TXMLLocalAuthFlagsType }

constructor TXMLLocalAuthFlagsType.Create;
begin
  inherited;
  FPurchase := TXMLPurchaseType.Create;
  FReturn := TXMLReturnType.Create;
  FForce := TXMLForceType.Create;
  FPreAuth := TXMLPreAuthType.Create;
  FActivate := TXMLActivateType.Create;
  FHoldTab := TXMLHoldTabType.Create;         // CPCLIENTS-1932
  FReleaseTab := TXMLReleaseTabType.Create;   // CPCLIENTS-1932
end;

destructor TXMLLocalAuthFlagsType.Destroy;
begin 
  FreeAndNil(FPurchase);
  FreeAndNil(FReturn);
  FreeAndNil(FForce);
  FreeAndNil(FPreAuth);
  FreeAndNil(FActivate);
  FreeAndNil(FHoldTab);         // CPCLIENTS-1932
  FreeAndNil(FReleaseTab);      // CPCLIENTS-1932
  inherited;
end;

function TXMLLocalAuthFlagsType.GetPurchase: TXMLPurchaseType;
begin
  Result := FPurchase;
end;

function TXMLLocalAuthFlagsType.GetReleaseTab: TXMLReleaseTabType;    // CPCLIENTS-1932
begin
  Result := FReleaseTab;
end;

function TXMLLocalAuthFlagsType.GetReturn: TXMLReturnType;
begin
  Result := FReturn;
end;

function TXMLLocalAuthFlagsType.GetForce: TXMLForceType;
begin
  Result := FForce;
end;

function TXMLLocalAuthFlagsType.GetHoldTab: TXMLHoldTabType;
begin
  Result := FHoldTab;          // CPCLIENTS-1932
end;

function TXMLLocalAuthFlagsType.GetPreAuth: TXMLPreAuthType;
begin
  Result := FPreAuth;
end;

function TXMLLocalAuthFlagsType.GetActivate: TXMLActivateType;
begin
  Result := FActivate;
end;

{ TXMLPurchaseType }

function TXMLPurchaseType.GetOffline: string;
begin
  Result := FOffline;
end;

procedure TXMLPurchaseType.SetOffline(Value: string);
begin
  if Value = FOffline then Exit;
  FOffline := Value;
end;

function TXMLPurchaseType.GetOfflineVoid: string;
begin
  Result := FOfflineVoid;
end;

procedure TXMLPurchaseType.SetOfflineVoid(Value: string);
begin
  if Value = FOfflineVoid then Exit;
  FOfflineVoid := Value;
end;

{ TXMLReturnType }

function TXMLReturnType.GetOffline: string;
begin
  Result := FOffline;
end;

procedure TXMLReturnType.SetOffline(Value: string);
begin
  if Value = FOffline then Exit;
  FOffline := Value;
end;

function TXMLReturnType.GetOfflineVoid: string;
begin
  Result := FOfflineVoid;
end;

procedure TXMLReturnType.SetOfflineVoid(Value: string);
begin
  if Value = FOfflineVoid then Exit;
  FOfflineVoid := Value;
end;

{ TXMLForceType }

function TXMLForceType.GetOffline: string;
begin
  Result := FOffline;
end;

procedure TXMLForceType.SetOffline(Value: string);
begin
  if Value = FOffline then Exit;
  FOffline := Value;
end;

function TXMLForceType.GetOfflineVoid: string;
begin
  Result := FOfflineVoid;
end;

procedure TXMLForceType.SetOfflineVoid(Value: string);
begin
  if Value = FOfflineVoid then Exit;
  FOfflineVoid := Value;
end;

{ TXMLPreAuthType }

function TXMLPreAuthType.GetOffline: string;
begin
  Result := FOffline;
end;

procedure TXMLPreAuthType.SetOffline(Value: string);
begin
  if Value = FOffline then Exit;
  FOffline := Value;
end;

function TXMLPreAuthType.GetOfflineVoid: string;
begin
  Result := FOfflineVoid;
end;

procedure TXMLPreAuthType.SetOfflineVoid(Value: string);
begin
  if Value = FOfflineVoid then Exit;
  FOfflineVoid := Value;
end;

{ TXMLActivateType }

function TXMLActivateType.GetOffline: string;
begin
  Result := FOffline;
end;

procedure TXMLActivateType.SetOffline(Value: string);
begin
  if Value = FOffline then Exit;
  FOffline := Value;
end;

function TXMLActivateType.GetOfflineVoid: string;
begin
  Result := FOfflineVoid;
end;

procedure TXMLActivateType.SetOfflineVoid(Value: string);
begin
  if Value = FOfflineVoid then Exit;
  FOfflineVoid := Value;
end;

{ TXMLOnlineLimitsType }

constructor TXMLOnlineLimitsType.Create;
begin
  inherited;
  FPreauthAmount := TXMLPreauthAmountType.Create;
end;

destructor TXMLOnlineLimitsType.Destroy;
begin
  FreeAndNil(FPreauthAmount);
  inherited;
end;

function TXMLOnlineLimitsType.GetMaxTotalAmount: Integer;
begin
  Result := FMaxTotalAmount;
end;

procedure TXMLOnlineLimitsType.SetMaxTotalAmount(Value: Integer);
begin
  if Value = FMaxTotalAmount then Exit;
  FMaxTotalAmount := Value;
end;

function TXMLOnlineLimitsType.GetMaxCashback: Integer;
begin
  Result := FMaxCashback;
end;

procedure TXMLOnlineLimitsType.SetMaxCashback(Value: Integer);
begin
  if Value = FMaxCashback then Exit;
  FMaxCashback := Value;
end;

function TXMLOnlineLimitsType.GetMinTotalAmount: Integer;
begin
  Result := FMinTotalAmount;
end;

procedure TXMLOnlineLimitsType.SetMinTotalAmount(Value: Integer);
begin
  if Value = FMinTotalAmount then Exit;
  FMinTotalAmount := Value;
end;

function TXMLOnlineLimitsType.GetMinCashback: Integer;
begin
  Result := FMinCashback;
end;

procedure TXMLOnlineLimitsType.SetMinCashback(Value: Integer);
begin
  if Value = FMinCashback then Exit;
  FMinCashback := Value;
end;

function TXMLOnlineLimitsType.GetMinCreditToDebit: Integer;
begin
  Result := FMinCreditToDebit;
end;

procedure TXMLOnlineLimitsType.SetMinCreditToDebit(Value: Integer);
begin
  if Value = FMinCreditToDebit then Exit;
  FMinCreditToDebit := Value;
end;

function TXMLOnlineLimitsType.GetPreauthAmount: TXMLPreauthAmountType;
begin
  Result := FPreauthAmount;
end;

procedure TXMLOnlineLimitsType.SetNoCVMAmount(const Value: Integer);  // MCD TFS-7513
begin
  FNoCVMAmount := Value;
end;

{ TXMLPreauthAmountType }

function TXMLPreauthAmountType.GetAmountToHost: Integer;
begin
  Result := FAmountToHost;
end;

procedure TXMLPreauthAmountType.SetAmountToHost(Value: Integer);
begin
  if Value = FAmountToHost then Exit;
  FAmountToHost := Value;
end;

function TXMLPreauthAmountType.GetAmountToPump: Integer;
begin
  Result := FAmountToPump;
end;

procedure TXMLPreauthAmountType.SetAmountToPump(Value: Integer);
begin
  if Value = FAmountToPump then Exit;
  FAmountToPump := Value;
end;

{ TXMLAmountByEntryType }

function TXMLAmountByEntryType.GetSwipeAmount: Integer;
begin
  Result := FSwipeAmount;
end;

function TXMLAmountByEntryType.GetFallbackToSwipeAmount: Integer;
begin
  Result := FFallbackToSwipeAmount;
end;

function TXMLAmountByEntryType.GetManualAmount: Integer;
begin
  Result := FManualAmount;
end;

function TXMLAmountByEntryType.GetChipAmount: Integer;
begin
  Result := FChipAmount;
end;

function TXMLAmountByEntryType.GetBarcodeAmount: Integer;
begin
  Result := FBarcodeAmount;
end;

function TXMLAmountByEntryType.GetRFIDAmount: Integer;
begin
  Result := FRFIDAmount;
end;

function TXMLAmountByEntryType.GetEMVContactlessAmount: Integer;
begin
  Result := FEMVContactlessAmount;
end;

procedure TXMLAmountByEntryType.SetSwipeAmount(Value: Integer);
begin
  if Value = FSwipeAmount then Exit;
  FSwipeAmount := Value;
end;

procedure TXMLAmountByEntryType.SetFallbackToSwipeAmount(Value: Integer);
begin
  if Value = FFallbackToSwipeAmount then Exit;
  FFallbackToSwipeAmount := Value;
end;

procedure TXMLAmountByEntryType.SetManualAmount(Value: Integer);
begin
  if Value = FManualAmount then Exit;
  FManualAmount := Value;
end;

procedure TXMLAmountByEntryType.SetChipAmount(Value: Integer);
begin
  if Value = FChipAmount then Exit;
  FChipAmount := Value;
end;

procedure TXMLAmountByEntryType.SetBarcodeAmount(Value: Integer);
begin
  if Value = FBarcodeAmount then Exit;
  FBarcodeAmount := Value;
end;

procedure TXMLAmountByEntryType.SetRFIDAmount(Value: Integer);
begin
  if Value = FRFIDAmount then Exit;
  FRFIDAmount := Value;
end;

procedure TXMLAmountByEntryType.SetEMVContactlessAmount(Value: Integer);
begin
  if Value = FEMVContactlessAmount then Exit;
  FEMVContactlessAmount := Value;
end;

{ TXMLOfflineLimitsType }

constructor TXMLOfflineLimitsType.Create;
begin
  inherited;
  FPreauthAmount := TXMLPreauthAmountType.Create;
  FMaxTotalAmountByEntryType := TXMLAmountByEntryType.Create;
  FMaxVoiceAuthLimitByEntryType := TXMLAmountByEntryType.Create;
end;

destructor TXMLOfflineLimitsType.Destroy;
begin
  FreeAndNil(FMaxVoiceAuthLimitByEntryType);
  FreeAndNil(FMaxTotalAmountByEntryType);
  FreeAndNil(FPreauthAmount);
  inherited;
end;

function TXMLOfflineLimitsType.GetMaxTotalAmount: Integer;
begin
  Result := FMaxTotalAmount;
end;

procedure TXMLOfflineLimitsType.SetMaxTotalAmount(Value: Integer);
begin
  if Value = FMaxTotalAmount then Exit;
  FMaxTotalAmount := Value;
end;

function TXMLOfflineLimitsType.GetMaxCashback: Integer;
begin
  Result := FMaxCashback;
end;

procedure TXMLOfflineLimitsType.SetMaxCashback(Value: Integer);
begin
  if Value = FMaxCashback then Exit;
  FMaxCashback := Value;
end;

function TXMLOfflineLimitsType.GetMinCreditToDebit: Integer;
begin
  Result := FMinCreditToDebit;
end;

procedure TXMLOfflineLimitsType.SetMinCreditToDebit(Value: Integer);
begin
  if Value = FMinCreditToDebit then Exit;
  FMinCreditToDebit := Value;
end;

function TXMLOfflineLimitsType.GetPreauthAmount: TXMLPreauthAmountType;
begin
  Result := FPreauthAmount;
end;

function TXMLOfflineLimitsType.GetMaxTotalAmountByEntryType: TXMLAmountByEntryType;
begin
  Result := FMaxTotalAmountByEntryType;
end;

function TXMLOfflineLimitsType.GetMaxVoiceAuthLimitByEntryType: TXMLAmountByEntryType;
begin
  Result := FMaxVoiceAuthLimitByEntryType;
end;

{ TXMLHoldTabType }

function TXMLHoldTabType.GetOffline: string;                     // CPCLIENTS-1932
begin
  Result := FOffline;
end;

function TXMLHoldTabType.GetOfflineVoid: string;                 // CPCLIENTS-1932
begin
  Result := FOfflineVoid;
end;

procedure TXMLHoldTabType.SetOffline(Value: string);             // CPCLIENTS-1932
begin
  if Value <> FOffline then
    FOffline := Value;
end;

procedure TXMLHoldTabType.SetOfflineVoid(Value: string);         // CPCLIENTS-1932
begin
  if Value <> FOfflineVoid then
    FOfflineVoid := Value;
end;

{ TXMLReleaseTabType }

function TXMLReleaseTabType.GetOffline: string;
begin
  Result := FOffline;
end;

function TXMLReleaseTabType.GetOfflineVoid: string;
begin
  Result := FOfflineVoid;
end;

procedure TXMLReleaseTabType.SetOffline(Value: string);
begin
  if Value <> FOffline then
    FOffline := Value;
end;

procedure TXMLReleaseTabType.SetOfflineVoid(Value: string);
begin
  if Value <> FOfflineVoid then
    FOfflineVoid := Value;
end;

initialization
  ExtendedLog('CardProcessingProfileXML Initialization');
finalization
  ExtendedLog('CardProcessingProfileXML Finalization');

end.


