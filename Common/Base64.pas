// (c) MTXEPS, Inc. 1988-2008

{$I OpenEPS_Def.inc}

unit Base64;

interface

uses
  FinalizationLog,
  Sysutils;

{ Base64 encode and decode a string }
function B64Encode(const S: AnsiString): AnsiString;  // MCD
function B64Decode(const S: AnsiString): AnsiString;  // MCD

implementation

const
  B64Table= AnsiString('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/');

function B64Encode(const S: AnsiString): AnsiString;  // MCD
var
  i: integer;
  InBuf: array[0..2] of byte;
  OutBuf: array[0..3] of AnsiChar;
begin
  SetLength(Result,((Length(S)+2) div 3)*4);
  for i:= 1 to ((Length(S)+2) div 3) do
  begin
    fillchar(InBuf,sizeof(Inbuf),0);
    if Length(S)< (i*3) then
      Move(S[(i-1)*3+1],InBuf,Length(S)-(i-1)*3)
    else
      Move(S[(i-1)*3+1],InBuf,3);
    OutBuf[0]:= B64Table[((InBuf[0] and $FC) shr 2) + 1];
    OutBuf[1]:= B64Table[(((InBuf[0] and $03) shl 4) or ((InBuf[1] and $F0) shr 4)) + 1];
    OutBuf[2]:= B64Table[(((InBuf[1] and $0F) shl 2) or ((InBuf[2] and $C0) shr 6)) + 1];
    OutBuf[3]:= B64Table[(InBuf[2] and $3F) + 1];
    Move(OutBuf,Result[(i-1)*4+1],4);
  end;
  if (Length(S) mod 3)= 1 then
  begin
    Result[Length(Result)-1]:= '=';
    Result[Length(Result)]:= '=';
  end
  else if (Length(S) mod 3)= 2 then
    Result[Length(Result)]:= '=';
end;

function B64Decode(const S: AnsiString): AnsiString;        // MCD
var
  i,j: integer;
  InBuf: array[0..3] of byte;
  OutBuf: array[0..2] of byte;
begin
  if (Length(S) mod 4)<> 0 then
    raise Exception.Create('Base64: Incorrect string format');
  SetLength(Result,((Length(S) div 4)-1)*3);
  for i:= 1 to ((Length(S) div 4)-1) do
    begin
    Move(S[(i-1)*4+1],InBuf,4);
    for j := 0 to 3 do
      if       InBuf[j] in [65..90]  then Dec(InBuf[j],65)
      else if  InBuf[j] in [97..122] then Dec(InBuf[j],71)
      else if  InBuf[j] in [48..57]  then Inc(InBuf[j],4)
      else if InBuf[j]= 43 then InBuf[j]:= 62
      else InBuf[j]:= 63;
    OutBuf[0]:= (InBuf[0] shl 2) or ((InBuf[1] shr 4) and $03);
    OutBuf[1]:= (InBuf[1] shl 4) or ((InBuf[2] shr 2) and $0F);
    OutBuf[2]:= (InBuf[2] shl 6) or (InBuf[3] and $3F);
    Move(OutBuf,Result[(i-1)*3+1],3);
    end;
  if Length(S)<> 0 then
    begin
    Move(S[Length(S)-3],InBuf,4);
    if InBuf[2]= 61 then
      begin
      for j := 0 to 1 do
        if       InBuf[j] in [65..90]  then Dec(InBuf[j],65)
        else if  InBuf[j] in [97..122] then Dec(InBuf[j],71)
        else if  InBuf[j] in [48..57]  then Inc(InBuf[j],4)
        else if InBuf[j]= 43 then InBuf[j]:= 62
        else InBuf[j]:= 63;
        OutBuf[0]:= (InBuf[0] shl 2) or ((InBuf[1] shr 4) and $03);
        Result:= Result + AnsiChar(OutBuf[0]);
      end
    else if InBuf[3]= 61 then
      begin
      for j := 0 to 2 do
        if       InBuf[j] in [65..90]  then Dec(InBuf[j],65)
        else if  InBuf[j] in [97..122] then Dec(InBuf[j],71)
        else if  InBuf[j] in [48..57]  then Inc(InBuf[j],4)
        else if InBuf[j]= 43 then InBuf[j]:= 62
        else InBuf[j]:= 63;
      OutBuf[0]:= (InBuf[0] shl 2) or ((InBuf[1] shr 4) and $03);
      OutBuf[1]:= (InBuf[1] shl 4) or ((InBuf[2] shr 2) and $0F);
      Result:= Result + AnsiChar(OutBuf[0]) + AnsiChar(OutBuf[1]);
      end
   else
    begin
    for j := 0 to 3 do
      if       InBuf[j] in [65..90]  then Dec(InBuf[j],65)
      else if  InBuf[j] in [97..122] then Dec(InBuf[j],71)
      else if  InBuf[j] in [48..57]  then Inc(InBuf[j],4)
      else if InBuf[j]= 43 then InBuf[j]:= 62
      else InBuf[j]:= 63;
    OutBuf[0]:= (InBuf[0] shl 2) or ((InBuf[1] shr 4) and $03);
    OutBuf[1]:= (InBuf[1] shl 4) or ((InBuf[2] shr 2) and $0F);
    OutBuf[2]:= (InBuf[2] shl 6) or (InBuf[3] and $3F);
    Result:= Result + AnsiChar(OutBuf[0]) + AnsiChar(OutBuf[1]) + AnsiChar(OutBuf[2]);
    end;
  end;
end;

initialization
  ExtendedLog('Base64 Initialization');
finalization
  ExtendedLog('Base64 Finalization');

end.
