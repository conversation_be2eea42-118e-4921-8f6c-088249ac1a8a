// Copyright (c) 2007 - 2008 by MTXEPS, Inc. All Rights Reserved.
unit HostProcessing;
(*******************************************************************************
 *  This unit contains classes and methods for host specific validations before
 *  and after the transaction is sent to the host
 Just make one call to PreCheckAllHostValidations and all preChecks for the
 appropriate host will be done.
 *******************************************************************************
  Revision History
  ================
  09-04-07 TSL Create
 ******************************************************************************)

interface

uses
  FinalizationLog,
  Classes,
  {$IFDEF MSWINDOWS}
  Windows,
  {$ENDIF}
  SysUtils,
  MTX_Lib,
  mtx_utils,
  MDMsg,
  MTX_Constants,
  {$IFDEF MTXEPSDLL}
  epsTrace,
  MRTypes,
  {$ENDIF MTXEPSDLL}
  fct,
  MTX_XMLClasses;


type
  TPreHost = class
    FSendOneCent: boolean;
  private
    function IsProgramIDOK(var aMdMsg: MdMsgRec): boolean; virtual;
    function IsVoidOK(var aMdMsg: MdMsgRec): boolean; virtual;
    //function IsInvalidDollarAmount(var aMdMsg: MdMsgRec): boolean; virtual;

  public
    function SetRspCodeIfInvalidTran(var aMdMsg: MdMsgRec): string; virtual;
    function IsHostOfflineRspCode(aRspCode: string): boolean; virtual;
    function IsStandInValid(var aMdMsg: MdMsgRec): boolean; virtual;
    property SendOneCent: boolean read FSendOneCent write FSendOneCent;
  end;

  TPreATLHost = class(TPreHost)
  private
    function IsVoidOK(var aMdMsg: MdMsgRec): boolean; override;
  public
    function SetRspCodeIfInvalidTran(var aMdMsg: MdMsgRec): string; override;
    function IsStandInValid(var aMdMsg: MdMsgRec): boolean; override;
  end;

  TPreBYLHost = class(TPreHost)
  private
    function IsProgramIDOK(var aMdMsg: MdMsgRec): boolean; override;
    function IsInvalidDollarAmount(var aMdMsg: MdMsgRec): boolean;

  public
    function SetRspCodeIfInvalidTran(var aMdMsg: MdMsgRec): string; override;
    function IsStandInValid(var aMdMsg: MdMsgRec): boolean; override;
    function IsHostOfflineRspCode(aRspCode: string): boolean; override;
  end;

  TPreCHSHost = class(TPreHost)
  private
  public
    function SetRspCodeIfInvalidTran(var aMdMsg: MdMsgRec): string; override;
    function IsStandInValid(var aMdMsg: MdMsgRec): boolean; override;
  end;

  TPreMPSHost = class(TPreHost)
  private
    function IsProgramIDOK(var aMdMsg: MdMsgRec): boolean; override;
  public
    function SetRspCodeIfInvalidTran(var aMdMsg: MdMsgRec): string; override;
    function IsStandInValid(var aMdMsg: MdMsgRec): boolean; override;
  end;

  TPreNOVHost = class(TPreHost)
  private
  public
    function IsStandInValid(var aMdMsg: MdMsgRec): boolean; override;
  end;

  TPreSBYHost = class(TPreHost)
  private
  public
    function IsStandInValid(var aMdMsg: MdMsgRec): boolean; override;
  end;

  TPrePBLHost = class(TPreHost)
  private
  public
    function SetRspCodeIfInvalidTran(var aMdMsg: MdMsgRec): string; override;
    function IsStandInValid(var aMdMsg: MdMsgRec): boolean; override;
  end;

  TPreLynkHost = class(TPreHost)  // TFS-14291, 14296
  public
    function IsStandInValid(var aMdMsg: MdMsgRec): boolean; override;
  end;
  
  TPreVNTHost = class(TPreHost); //TFS-14771, 17521

  // ---------------- Host post processing --------------------------------------
  (* // XE: Remove WinEPS - not for OpenEPS
  TPostBYLHost = class
  private
    function EBTTranAndBalanceRemaining(aMdMsg: MdMsgRec): boolean;
    function ResubmitEBTOfflineForBalanceLeft(aMdMsg: MdMsgRec): boolean;

  public
    function ResendBalanceRemainingLessThanPurchase(var aMdMsg: MdMsgRec): boolean;
  {$IFDEF MTXEPSDLL}
    function ResendBalRemainingLessThanPurchOpenEPS(var aMR:TFldByTran;
      aRspMMR:TServerEPSResponse ): boolean;
  {$ENDIF MTXEPSDLL}
  end;
  *)
  
  //function  HostOfflineRspCode(aHostSuffixCode, aRspCode: string): boolean; // XE: Remove WinEPS - not for OpenEPS
  function  PreCheckAllHostValidations(var aMdMsg: MdMsgRec; var aOfflineAllowed: boolean): string;
  procedure ReturnOfflineValuesByHost(var TotalSends, SendsPerDay: byte; aTender: integer; aHost: string);
  procedure ReturnOfflineValuesDefault(var TotalSends, SendsPerDay: byte);
  function  SetCheckNFOverrideFlagForHost(aMdMsg: MdMsgRec): boolean;
  function IsStaterBros: boolean;

implementation

procedure MsgLog(aMsg: string);
begin
  {$IFDEF MTXEPSDLL}
  ShowTrace(idTCP, 'HostProcessing: ' + aMsg);
  {$ELSE}
  sm('HostProcessing: ' + aMsg);
  {$ENDIF}
end;

procedure SetTrxNotAllowed(var aMdMsg: MdMsgRec);
begin
  aMdMsg.MtxRspCode  := TrxDecTrxNotAlwd;
  aMdMsg.MtxRspCodeN := TrxDecTrxNotAlwdN;
end;

procedure SetVoidNotAllowed(var aMdMsg: MdMsgRec);
begin
  aMdMsg.MTXRspCode := TrxDecVoidNotAllowed;
  aMdMsg.MTXRspCodeN := TrxDecVoidNotAllowedN;
end;

//----------------------TPREHOST ---------------------------
function TPreHost.IsProgramIDOK(var aMdMsg: MdMsgRec): boolean;
begin
  result := true;
end;

function TPreHost.IsVoidOK(var aMdMsg: MdMsgRec): boolean;
begin
  result := true;
end;

function TPreHost.SetRspCodeIfInvalidTran(var aMdMsg: MdMsgRec): string;
begin
  result := '';
end;

function TPreHost.IsHostOfflineRspCode(aRspCode: string): boolean;
begin
  result := false;
end;

function TPreHost.IsStandInValid(var aMdMsg: MdMsgRec): boolean;
begin
  result := not (aMdMsg.ReqCodeN in eWic_Set);  //TFS-14771, 17521
end;

//----------------------TPreATLHost -----------------------

function TPreATLHost.IsVoidOK(var aMdMsg: MdMsgRec): boolean;
begin
  result := true;
  (* //69739.. always usingServerEPSHost now
  if (aMdMsg.VoidCode = 'V') then
  begin
    if (aMdMsg.ReqCodeN in Db_Set) and NOT usingServerEPSHost then // DOEP-40627: Concord:EPC supports debit void
    begin
      SetVoidNotAllowed(aMdMsg);
      result := false;
    end;
  end;
  *)
end;

function TPreATLHost.SetRspCodeIfInvalidTran(var aMdMsg: MdMsgRec): string;
begin
  result := '';
  if not IsVoidOK(aMdMsg) then
    result := string(aMdMsg.MTXRspCode);
end;

function TPreATLHost.IsStandInValid(var aMdMsg: MdMsgRec): boolean;
begin
  result := inherited IsStandInValid(aMdMsg) and (not ((aMdMsg.ReqCodeN in Db_Set) and (aMdMsg.voidCode = 'V')));
  if not result then
    aMdMsg.OffLineAuthCodeN := TrxDecOffInvalidN;
end;

//----------------------TPreBYLHost -----------------------

function TPreBYLHost.IsProgramIDOK(var aMdMsg: MdMsgRec): boolean;
begin
  result := true;
  SendOneCent := false;
  if (aMdMsg.ProgramID = IDT) or (aMdMsg.ProgramID = SPP) then   // note don't send TORs for IDT
  begin
    if (aMdMsg.VoidCode = 'V') or
       (aMdMsg.ReqCodeN <> User_1_ActivationN) then
    begin
      SetTrxNotAllowed(aMdMsg);
      result := false;
    end
    else
    if (aMdMsg.ProgramID = IDT) then
    begin
      {$IFNDEF MTXEPSDLL}                     // only do for WinEPS, not OpenEPS
      SendOneCent := true;
      aMdMsg.TrxAmt := format('%.12u', [1]);  // must send one cent to host
      aMdMsg.TrxAmtN := 1;         // make sure we don't set lower amt on rsp
      {$ENDIF MTXEPSDLL}
    end;
  end;
end;

function TPreBYLHost.IsInvalidDollarAmount(var aMdMsg: MdMsgRec): boolean;
begin
  result := false;
  if
  {$IFDEF MTXEPSDLL}
   (aMdMsg.VoidCode <> 'V') and     // can't do this validation for voids with OpenEPS - don't have the amount
  {$ENDIF MTXEPSDLL}
    (aMdMsg.ReqCodeN <> eWicPreAuthN) then
  begin
    if (aMdMsg.ReqCodeN in All_Trxs)             and    { $ Trxs only }
       (aMdMsg.ReqCodeN in AuthComp_Set = false) and
       (aMdMsg.TrxAmtN = 0)                      then
    begin
      aMdMsg.MTXRspCode := TrxDecInvalidAmount;
      aMdMsg.MTXRspCodeN := TrxDecInvalidAmountN;
      result := true;
    end;
  end;
end;

function TPreBYLHost.SetRspCodeIfInvalidTran(var aMdMsg: MdMsgRec): string;
begin
  result := '';
  // CPCLIENTS-5179 To have meaningful name for MakeBYLATLIssuerCode and BYLATLIssuerCode as it is implemented for ATL too
  aMdMsg.BYLATLIssuerCode := mtx_Utils.MakeBYLATLIssuerCode(aMdMsg.ReqCodeN);  // make sure we have this
  if (not IsProgramIDOK(aMdMsg)) or IsInvalidDollarAmount(aMdMsg) then
    result := string(aMdMsg.MTXRspCode);
end;

function TPreBYLHost.IsHostOfflineRspCode(aRspCode: string): boolean;
begin
  result := SameText(aRspCode, 'E2D') or SameText(aRspCode, 'U2D') or SameText(aRspCode, 'E2G');
end;


function TPreBYLHost.IsStandInValid(var aMdMsg: MdMsgRec): boolean;
begin
  result := inherited IsStandInValid(aMdMsg) and (not (aMdMsg.ReqCodeN in [EBT_FS_BalInqN,EBT_Cash_BalInqN, user_1_DeactivateN]));
  if (aMdMsg.ReqCodeN in Check_set) then
  begin
    if (aMdMsg.Check_Type = 'W') then
      if (HostBuf.ChkAuthType <> CHECK_AUTH_TYPE_BUYCHECK) and (HostBuf.ChkAuthType <> CHECK_AUTH_TYPE_CERTEGY) then
        result := false;
    if (HostBuf.ChkAuthType = CHECK_AUTH_TYPE_TELECHECK) and (aMdMsg.Check_type <> 'P')  then
      result := false;
  end;
  if not result then
    aMdMsg.OffLineAuthCodeN := TrxDecOffInvalidN;
end;

//----------------------TPreCHSHost-----------------------

function TPreCHSHost.SetRspCodeIfInvalidTran(var aMdMsg: MdMsgRec): string;
begin
  result := '';
  if (aMdMsg.TrxAmtN > 9999999) then
  begin
    aMdMsg.MtxRspCode  := TrxDecInvalidAmount;
    aMdMsg.MtxRspCodeN := TrxDecInvalidAmountN;
    result := string(aMdMsg.MTXRspCode);
  end;
end;

function TPreCHSHost.IsStandInValid(var aMdMsg: MdMsgRec): boolean;
begin
  result := inherited IsStandInValid(aMdMsg);
  if (aMdMsg.ReqCodeN in [User_2_ReturnN,EBT_Cash_ReturnN]) then
    result := false;
  if (aMdMsg.ReqCodeN in Cr_Set) and
     (aMdMsg.CashBackN <> 0)     then
    result := false;
  if not result then
    aMdMsg.OffLineAuthCodeN := TrxDecOffInvalidN;
end;

//----------------------TPreMPSHost-----------------------

function TPreMPSHost.IsProgramIDOK(var aMdMsg: MdMsgRec): boolean;
begin
  result := true;
  try
    if (aMdMsg.ProgramID = SWAY) then
    begin
      if (aMdMsg.VoidCode = 'V') or
         (aMdMsg.ReqCodeN <> User_1_ActivationN) then
      begin
        SetTrxNotAllowed(aMdMsg);
        result := false;
      end;
    end;
  except
    on e: Exception do
      msgLog('Try..Except: MPS IsProgramIDOK ' + e.message);
  end;
end;

function TPreMPSHost.SetRspCodeIfInvalidTran(var aMdMsg: MdMsgRec): string;
begin
  result := '';
  try
    if not IsProgramIDOK(aMdMsg) then
      result := string(aMdMsg.MTXRspCode);

    if (result = '') and (aMdMsg.ReqCodeN in db_set) and (aMdMsg.voidCode = 'V') then
    begin
      MsgLog('MPS: Void of a DEBIT transaction.. will ALLOW...');  //JTG 34458
      //SetTrxNotAllowed(aMdMsg);                                  //JTG 34458
      //result := aMdMsg.MTXRspCode;                               //JTG 34458
    end;

  except
    on e: Exception do
      msgLog('Try..Except: MPS SetRspCodeIfInvalidTran ' + e.message);
  end;
end;

function TPreMPSHost.IsStandInValid(var aMdMsg: MdMsgRec): boolean;
begin
  result := inherited IsStandInValid(aMdMsg);
  try
    if ABlackhawkOrIDTGiftActivation(aMdMsg) then
      result := true
    else if aMdMsg.ReqCodeN = User_1_ActivationN then   //76898 - if NOT Blackhawk AND Activation, then make this valid
      result := true
    else if (aMdMsg.ReqCodeN in User_1_Set - [User_1_PreAuthCompN] + BalInq_Set) then   { can't take offline for these }
      result := false;

    if (aMdMsg.ReqCodeN in db_set) and (aMdMsg.voidCode = 'V') then
      result := false;

    if not result then
      aMdMsg.OffLineAuthCodeN := TrxDecOffInvalidN;
    MsgLog(format('TPreMPSHost.IsStandInValid[%s] - ABlackhawkOrIDTGiftActivation[%s] ReqCodeN[%d]',[sTF[result],sTF[ABlackhawkOrIDTGiftActivation(aMdMsg)],aMdMsg.ReqCodeN]));
  except
    on e: Exception do
      msgLog('Try..Except: MPS IsStandInValid ' + e.message);
  end;
end;

//----------------------TPreNOVHost -----------------------

function TPreNOVHost.IsStandInValid(var aMdMsg: MdMsgRec): boolean;
begin
  result := false;
  if (aMdMsg.ReqCodeN in cr_set) then
    result := (aMdMsg.OffLineAuthCodeN = TrxAppOffN)
  else
  if (aMdMsg.ReqCodeN in Check_set) then
  begin
    result := (aMdMsg.OffLineAuthCodeN = TrxAppOffN);
    if result then
      aMdMsg.OffLineAuthCodeN := TrxAppOffEccN;       // offline checks are ECC checks
  end;
end;

//----------------------TPreSBYHost -----------------------

function TPreSBYHost.IsStandInValid(var aMdMsg: MdMsgRec): boolean;
begin
  result := inherited IsStandInValid(aMdMsg);
  if (aMdMsg.ReqCodeN in Db_set) and
     (string(aMdMsg.Dr_License) = stringofchar('0', length(aMdMsg.Dr_License))) then
  begin
    result := false;
    aMdMsg.OffLineAuthCodeN := TrxDecOffInvalidN;
  end;
end;

//----------------------TPrePBLHost -----------------------

function TPrePBLHost.SetRspCodeIfInvalidTran(var aMdMsg: MdMsgRec): string;
begin
  result := '';
  if (aMdMsg.ReqCodeN in Check_Set) then   { special consideration for check }
  begin
    if (aMdMsg.Check_Type = ctOther)  then
      result  := TrxAppWCap
    else
    if (trim(string(aMdMsg.dr_license)) = '')      and
       (aMdMsg.trxAmtN = aMdMsg.cashBackN) and
       (aMdMsg.Check_Type <> ctTravelersCheck) then
      result := TrxDecNeedSecondID;
  end;
end;

function TPrePBLHost.IsStandInValid(Var aMdMsg: MdMsgRec): boolean;
begin
  result := inherited IsStandInValid(aMdMsg);
  if (aMdMsg.ReqCodeN in Check_Set) and (trim(aMdMsg.dr_license) = '') and (aMdMsg.Check_Type <> ctTravelersCheck) then
  begin
    aMdMsg.OfflineAuthCodeN := TrxDecNeedSecondIDN;
    result := false;
  end;
  (* // TFS-34675
  {$IFDEF MTXEPSDLL}
  if (aMdMsg.VoidCode = 'V') then   { no stand in at the post for voids }
  begin
    result := false;
    aMdMsg.OffLineAuthCodeN := TrxDecVoidNotAllowedN;
  end;
  {$ENDIF MTXEPSDLL}
  *)
end;

//----------------------TPreLynkHost -----------------------

function TPreLynkHost.IsStandInValid(var aMdMsg: MdMsgRec): boolean; // TFS-14291, 14296
begin
  result := inherited IsStandInValid(aMdMsg) and (not (aMdMsg.ReqCodeN in [EBT_FS_BalInqN,EBT_Cash_BalInqN, user_1_DeactivateN]));
  if (aMdMsg.ReqCodeN in Check_set) then
  begin
    if (aMdMsg.Check_Type = 'W') then
      if (HostBuf.ChkAuthType <> CHECK_AUTH_TYPE_BUYCHECK) and (HostBuf.ChkAuthType <> CHECK_AUTH_TYPE_CERTEGY) then
        result := false;
    if (HostBuf.ChkAuthType = CHECK_AUTH_TYPE_TELECHECK) and (aMdMsg.Check_type <> 'P')  then
      result := false;
  end;
  if not result then
    aMdMsg.OffLineAuthCodeN := TrxDecOffInvalidN;
end;

//----------------------POST BYL Host -----------------------
(* // XE: Remove WinEPS - not for OpenEPS
function TPostBYLHost.EBTTranAndBalanceRemaining(aMdMsg: MdMsgRec): boolean;
begin
  result := ((aMdMsg.ReqCodeN in EBT_FS_Set) and (aMdMsg.EBT_FS_BalanceN <> 0)) or
            ((aMdMsg.ReqCodeN in EBT_Cash_Set) and (aMdMsg.EBT_Cash_BalanceN <> 0));
end;

function TPostBYLHost.ResubmitEBTOfflineForBalanceLeft(aMdMsg: MdMsgRec): boolean;
begin
  result := ((aMdMsg.ReqCodeN in EBT_FS_Set) and (GetArrayText(HostBuf.ForceTenderOffline,6,1) = 'Y')) or
            ((aMdMsg.ReqCodeN in EBT_Cash_Set) and (GetArrayText(HostBuf.ForceTenderOffline,7,1) = 'Y'));
end;

function TPostBYLHost.ResendBalanceRemainingLessThanPurchase(var aMdMsg: MdMsgRec): boolean;
begin
  if (aMdMsg.TermRspCode = 'NB') and EBTTranAndBalanceRemaining(aMdMsg) and
     ResubmitEBTOfflineForBalanceLeft(aMdMsg)                           then
  begin
    msgNotice('Offline Fwd for ' + intToStr(aMdMsg.TrxAmtN) +
          ' declined, resubmit for remaining balance: '+ intToStr(aMdMsg.EBT_FS_BalanceN));
    aMdMsg.TrxAmt := LFill(intToStr(aMdMsg.EBT_FS_BalanceN),'0',9); { amount for approval    }
    aMdMsg.trxAmtN := aMdMsg.EBT_FS_BalanceN;
    aMdMsg.TermRspCode := '';                    { don't want to come back here }
    aMdMsg.HostApproveLowerAmount := '^';        { set that is was done }
    result := true;
  end
  else
    result := false;
end;

{$IFDEF MTXEPSDLL}
function TPostBYLHost.ResendBalRemainingLessThanPurchOpenEPS(var aMR:TFldByTran;
  aRspMMR:TServerEPSResponse ): boolean;
begin
  result := false;
  if (aRspMMR.ResponseCode = 'NB') and (aRspMMR.Balance <> 0) then
  begin
    if (aMR.TenderType = ttEBT_FS) and (GetArrayText(HostBuf.ForceTenderOffline,6,1) = 'Y') then
      result := true
    else
    if (aMR.TenderType = ttEBT_CA) and (GetArrayText(HostBuf.ForceTenderOffline,7,1) = 'Y') then
      result := true;

    if result then
    begin
      aMR.PurchaseAmount := aRspMMR.Balance;   // amount for approval
      aMR.CashBackAmount := 0;
      aMR.FeeAmount := 0;
      aRspMMR.ResponseCode := '';              // don't want to come back here
      msgNotice('Offline Fwd for ' + intToStr(aMR.PurchaseAmount) +
            ' declined, resubmit for remaining balance: '+ intToStr(aRspMMR.Balance));
    end;
  end;
end;
{$ENDIF MTXEPSDLL}
*)
//----------------------PRECHECK ALL HOSTS ROUTINE -----------------------

function CreateHost(aSuffixCode: string): TPreHost;
begin
  result := nil;                        // first set the host to check
  if sameText(aSuffixCode, 'ATL') then
    result := TPreATLHost.Create
  else
  if sameText(aSuffixCode, 'BYL') then
    result := TPreBYLHost.Create
  else
  if sameText(aSuffixCode, 'CHS') then
    result := TPreCHSHost.Create
  else
  if sameText(aSuffixCode, 'MPS') then
    result := TPreMPSHost.Create
  else
  if sameText(aSuffixCode, 'NOV') then
    result := TPreNOVHost.Create
  else
  if sameText(aSuffixCode, 'SBY') then
    result := TPreSBYHost.Create
  else
  if IsPublix(aSuffixCode) then
    result := TPrePBLHost.Create
  else
  if sameText(aSuffixCode, 'LYN') then // TFS-14291, 14296
    result := TPreLynkHost.Create
  else
  if sameText(aSuffixCode, 'VNT') then // TFS-14771, 17521
    result := TPreVNTHost.Create;	
end;

function PreCheckAllHostValidations(var aMdMsg: MdMsgRec; var aOfflineAllowed: boolean): string;
var aHost: TPreHost;
begin
  result := '';
  aOfflineAllowed := true;  { assume offline is OK }
  aHost := CreateHost(string(aMdMsg.HostSuffixCode));

  if assigned(aHost) then
  try
    try
      result := aHost.SetRspCodeIfInvalidTran(aMdMsg);   // first see if tran is valid
      if (result = '') then        // if Tran is OK
      begin
        if aOfflineAllowed then                             // if incoming offline ok
          aOfflineAllowed := aHost.IsStandInValid(aMdMsg); // see if offline ok by host
        if aHost.SendOneCent then
        begin end;    /////////WHAT TO DO HERE, NEED TO SEND ONE CENT TO HOST ????
      end
      else
      begin
        aOfflineAllowed := false;
        aMdMsg.OffLineAuthCodeN := aMdMsg.MTXRspCodeN;
      end;
    except
      on e: exception do
        msgLog('Try..Except: PreCheckAllHostValidations ' + e.message);
    end;
  finally
    FreeAndNil(aHost);
  end;
end;

//-------------------------Response validations here ---------------------
{ // XE: Remove WinEPS - not for OpenEPS
function HostOfflineRspCode(aHostSuffixCode, aRspCode: string): boolean;
var aHost: TPreHost;
begin
  result := false;
  aHost := CreateHost(aHostSuffixCode);
  if assigned(aHost) then
    try
      result := aHost.IsHostOfflineRspCode(aRspCode);
    finally
      FreeAndNil(aHost);
    end;
end;
}
//-------------------------Misc settings----------------------------------

procedure SetBYLSends(var aTotalSends, aSendsPerDay: byte; aTender: integer);
begin
  if (aTender in EBT_Set) then
  begin
    aTotalSends := 15;  // 3 days, 5 per day
    aSendsPerDay := 5;
  end
  else
  begin
    aTotalSends := 70;  // 14 days, 5 per day
    aSendsPerDay := 5;
  end;
end;

procedure ReturnOfflineValuesByHost(var TotalSends, SendsPerDay: byte; aTender: integer; aHost: string);
begin
  ReturnOfflineValuesDefault(TotalSends, SendsPerDay);
  if (aHost = 'BYL') then
    SetBYLSends(TotalSends,SendsPerDay,aTender);
  MsgLog(aHost + ' host offline fwd MaxDailySends/MaxTotalSends ' +
    intToStr(SendsPerDay) + '/' + intToStr(TotalSends));
end;

procedure ReturnOfflineValuesDefault(var TotalSends, SendsPerDay: byte);
begin
  TotalSends := 35;       // set some defaults
  SendsPerDay := 5;
end;

function SetCheckNFOverrideFlagForHost(aMdMsg: MdMsgRec): boolean;
var aHostList: TStringList;
begin
  aHostList := TStringList.Create;
  try
    aHostList.Add('BYL');      // create list of hosts for which NF tran should NOT be an override
    aHostList.Add('TRN');
    aHostList.Add('ACI');
    result := not (aHostList.IndexOf(string(aMdMsg.HostSuffixCode)) >= 0);
  finally
    aHostList.Free;
  end;
end;

function IsStaterBros: boolean;
begin
  result := false;
  if assigned(HostBuf) then
    result := SameText(HostBuf.Suffix, 'LM2') or (SameText(HostBuf.Suffix, 'LML') and (HostBuf.ChkAuthType = '8'));
end;

initialization
  ExtendedLog('HostProcessing initialization');
finalization
  ExtendedLog('HostProcessing finalization');

end.
