// (c) MTXEPS, Inc. 1988-2008
unit LanesXML;

interface

uses
  FinalizationLog,
  Classes, SysUtils, IdGlobal; 

type

{ Forward Decls }

  TXMLLanesType = class;
  TXMLLaneType = class;

{ TXMLLanesType }
  TXMLLanesType = class(TCollection)
  private
    FVersion: string;
    FLastModified: string;
    {$HINTS OFF}
    FLane: TXMLLaneType;
    {$HINTS ON}
    function GetVersion: string;
    function GetLastModified: string;
    function GetLane(Index: Integer): TXMLLaneType;
    procedure SetVersion(Value: string);
    procedure SetLastModified(Value: string);
  public
    property Version: string read GetVersion write SetVersion;
    property LastModified: string read GetLastModified write SetLastModified;
    property Lane[Index: Integer]: TXMLLaneType read GetLane; default;
    {
    constructor Create(ItemClass: TCollectionItemClass);
    destructor Destroy; override;
    }
    function Add: TXMLLaneType;
    //function Insert(const Index: Integer): IXMLLaneType;
    function LoadFromFile(aFileName: string): boolean;
    function SaveToFile(aFileName: string): boolean;
  end;


{ TXMLLaneType }
  TXMLLaneType = class(TCollectionItem)
  private
    FNumber: string;
    FTermType: string;
    FAutostart: string;
    FOtherLanes_ConfigNumber: string;
    FReceiptType: string;
    FLaneConfigXMLFileName: string;
    FCardProcessingProfileXMLFileName: string;
    FLaneType: string;
    FWorkingKey: string;
    FAtallaKey: string;
    FTerminalId: string;
    FTerminalIPAddress: string;

    function GetNumber: string;
    function GetTermType: string;
    function GetAutostart: string;
    function GetOtherLanes_ConfigNumber: string;
    function GetReceiptType: string;
    function GetLaneConfigXMLFileName: string;
    function GetCardProcessingProfileXMLFileName: string;
    function GetLaneType: string;
    function GetWorkingKey: string;
    function GetAtallaKey: string;
    function GetTerminalId: string;
    function GetTerminalIPAddress: string;

    procedure SetNumber(Value: string);
    procedure SetTermType(Value: string);
    procedure SetAutostart(Value: string);
    procedure SetOtherLanes_ConfigNumber(Value: string);
    procedure SetReceiptType(Value: string);
    procedure SetLaneConfigXMLFileName(Value: string);
    procedure SetCardProcessingProfileXMLFileName(Value: string);
    procedure SetLaneType(Value: string);
    procedure SetWorkingKey(Value: string);
    procedure SetAtallaKey(Value: string);
    procedure SetTerminalId(Value: string);
    procedure SetTerminalIPAddress(Value: string);
  public
    property Number: string read GetNumber write SetNumber;
    property TermType: string read GetTermType write SetTermType;
    property Autostart: string read GetAutostart write SetAutostart;
    property OtherLanes_ConfigNumber: string read GetOtherLanes_ConfigNumber write SetOtherLanes_ConfigNumber;
    property ReceiptType: string read GetReceiptType write SetReceiptType;
    property LaneConfigXMLFileName: string read GetLaneConfigXMLFileName write SetLaneConfigXMLFileName;
    property CardProcessingProfileXMLFileName: string read GetCardProcessingProfileXMLFileName write SetCardProcessingProfileXMLFileName;
    property LaneType: string read GetLaneType write SetLaneType;
    property WorkingKey: string read GetWorkingKey write SetWorkingKey;
    property AtallaKey: string read GetAtallaKey write SetAtallaKey;
    property TerminalId: string read GetTerminalId write SetTerminalId;
    property TerminalIPAddress: string read GetTerminalIPAddress write SetTerminalIPAddress;
  end;

{ Global Functions }

function LoadLanes(const FileName: string): TXMLLanesType;
function NewLanes: TXMLLanesType;

implementation

uses
  MTX_Constants,
  MTX_Lib,
  UXMLCommon,
  MTX_XMLClasses;

const
  _Lanes = 'Lanes';
  _LastModified = 'LastModified';
  _Version = 'Version';
  _Lane = 'Lane';
  _Number = 'Number';
  _TermType = 'TermType';
  _Autostart = 'Autostart';
  _OtherLanes_ConfigNumber = 'OtherLanes_ConfigNumber';
  _LaneConfigXMLFileName = 'LaneConfigXMLFileName';
  _ReceiptType = 'ReceiptType';
  _LaneType = 'LaneType';
  _CardProcessingProfileXMLFileName = 'CardProcessingProfileXMLFileName';
  _WorkingKey = 'WorkingKey';
  _AtallaKey = 'AtallaKey';
  _TerminalId = 'TerminalId';
  _TerminalIPAddress = 'TerminalIPAddress';

{ Global Functions }

function LoadLanes(const FileName: string): TXMLLanesType;
begin
  result := TXMLLanesType.Create(TXMLLaneType);
  if Assigned(result) then
    if not result.LoadFromFile(FileName) then
      FreeAndNil(result);
end;

function NewLanes: TXMLLanesType;
begin
  result := TXMLLanesType.Create(TXMLLaneType);
end;

{ TXMLLanesType }

function TXMLLanesType.GetVersion: string;
begin
  Result := FVersion;
end;

procedure TXMLLanesType.SetVersion(Value: string);
begin
  //SetAttribute('version', Value);
  if Value = FVersion then Exit;
  FVersion := Value;
end;

function TXMLLanesType.GetLastModified: string;
begin
  Result := FLastModified;
end;

procedure TXMLLanesType.SetLastModified(Value: string);
begin
  if Value = FLastModified then Exit;
  FLastModified := Value;
end;

function TXMLLanesType.GetLane(Index: Integer): TXMLLaneType;
begin
  //Result := List[Index] as IXMLLaneType;
  result := inherited Items[Index] as TXMLLaneType;
end;

function TXMLLanesType.Add: TXMLLaneType;
begin
  //Result := AddItem(-1) as IXMLLaneType;
  result := TXMLLaneType.Create(Self);
end;
{
function TXMLLanesType.Insert(const Index: Integer): IXMLLaneType;
begin
  Result := AddItem(Index) as IXMLLaneType;
end;
}

function TXMLLanesType.LoadFromFile(aFileName: string): boolean;
var
  XMLObj: TXMLConfiguration;
  N1, N2: TXMLParserNode;
  i,j: integer;
  ALane: TXMLLaneType;
begin
  result := false;
  try
    if not FileExists(aFileName) then Exit;
    XMLObj := TXMLConfiguration.Create;
    try
      if NOT XMLObj.FXMLParser.LoadFromFile(aFileName) then           // 828.5
        Exit;                                                         // 828.5
      XMLObj.FXMLParser.StartScan;                                    // 828.5
      XMLObj.ScanElement(nil);

      if not SameText(XMLObj.Root.Name, _Lanes) then
        Exit;
      { clear cards }
      while (Count > 0) do
        Lane[0].Free;
      Version := XMLObj.Root.Attr.Values[_Version];
      LastModified := XMLObj.Root.Attr.Values[_LastModified];
      for i := 0 to XMLObj.Root.Children.Count - 1 do
      begin
        N1 := XMLObj.Root.Children[i];
        if SameText(N1.Name, _Lane) then
        begin
          ALane := TXMLLaneType.Create(Self);
          try
            ALane.Number := N1.Attr.Values[_Number];
            for j := 0 to N1.Children.Count - 1 do
            begin
              N2 := N1.Children[j];
              if SameText(N2.Name, _TermType) then ALane.TermType := N2.Text
              else if SameText(N2.Name, _Autostart) then ALane.Autostart := N2.Text
              else if SameText(N2.Name, _OtherLanes_ConfigNumber) then ALane.OtherLanes_ConfigNumber := N2.Text
              else if SameText(N2.Name, _LaneConfigXMLFileName) then ALane.LaneConfigXMLFileName := N2.Text
              else if SameText(N2.Name, _ReceiptType) then ALane.ReceiptType := N2.Text
              else if SameText(N2.Name, _LaneType) then ALane.LaneType := N2.Text
              else if SameText(N2.Name, _CardProcessingProfileXMLFileName) then ALane.CardProcessingProfileXMLFileName := N2.Text
              else if SameText(N2.Name, _WorkingKey) then ALane.WorkingKey := N2.Text
              else if SameText(N2.Name, _AtallaKey) then ALane.AtallaKey := N2.Text
              else if SameText(N2.Name, _TerminalId) then ALane.TerminalId := N2.Text
              else if SameText(N2.Name, _TerminalIPAddress) then ALane.TerminalIPAddress := N2.Text
              ;
            end;
          finally
            ;
          end;
        end;
      end; // for i
    finally
      FreeAndNil(XMLObj);
    end;
    result := true;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLLanesType.LoadFromFile: ' + aFileName + ' - ' + e.message);
  end;
end;

function TXMLLanesType.SaveToFile(aFileName: string): boolean;
var
  Root, N1: TXMLParserNode;
  i: Integer;
begin
  result := false;
  try
    Root := TXMLParserNode.Create(nil);
    try
      Root.Name := _Lanes;
      Root.Attr.Values[_Version] := Version;
      Root.Attr.Values[_LastModified] := FormatDateTime(FORMAT_LASTMODIFIED, Now);
      for i := 0 to Count -1 do
      begin
        N1 := Root.AddChild(_Lane);
        N1.Attr.Values[_Number] := Lane[i].Number;
        N1.AddChild(_TermType).Text := Lane[i].TermType;
        N1.AddChild(_Autostart).Text := Lane[i].Autostart;
        N1.AddChild(_OtherLanes_ConfigNumber).Text := Lane[i].OtherLanes_ConfigNumber;
        N1.AddChild(_LaneConfigXMLFileName).Text := Lane[i].LaneConfigXMLFileName;
        N1.AddChild(_ReceiptType).Text := Lane[i].ReceiptType;
        N1.AddChild(_LaneType).Text := Lane[i].LaneType;
        N1.AddChild(_CardProcessingProfileXMLFileName).Text := Lane[i].CardProcessingProfileXMLFileName;
        N1.AddChild(_WorkingKey).Text := Lane[i].WorkingKey;
        N1.AddChild(_AtallaKey).Text := Lane[i].AtallaKey;
        N1.AddChild(_TerminalId).Text := Lane[i].TerminalId;
        N1.AddChild(_TerminalIPAddress).Text := Lane[i].TerminalIPAddress;
      end; // end of for
      Root.SaveToFile(aFileName);
    finally
      FreeAndNil(Root);
    end;
    result := true;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLLanesType.SaveToFile: ' + aFileName + ' - ' + e.message);
  end;
end;

{ TXMLLaneType }

function TXMLLaneType.GetNumber: string;
begin
  Result := FNumber;
end;

procedure TXMLLaneType.SetNumber(Value: string);
begin
  if Value = FNumber then Exit;
  FNumber := Value;
end;

function TXMLLaneType.GetTermType: string;
begin
  Result := FTermType;
end;

procedure TXMLLaneType.SetTermType(Value: string);
begin
  if Value = FTermType then Exit;
  FTermType := Value;
end;

function TXMLLaneType.GetAutostart: string;
begin
  Result := FAutostart;
end;

procedure TXMLLaneType.SetAutostart(Value: string);
begin
  if Value = FAutostart then Exit;
  FAutostart := Value;
end;

function TXMLLaneType.GetOtherLanes_ConfigNumber: string;
begin
  Result := FOtherLanes_ConfigNumber;
end;

procedure TXMLLaneType.SetOtherLanes_ConfigNumber(Value: string);
begin
  if Value = FOtherLanes_ConfigNumber then Exit;
  FOtherLanes_ConfigNumber := Value;
end;

function TXMLLaneType.GetReceiptType: string;
begin
  Result := FReceiptType;
end;

procedure TXMLLaneType.SetReceiptType(Value: string);
begin
  if Value = FReceiptType then Exit;
  FReceiptType := Value;
end;

function TXMLLaneType.GetLaneConfigXMLFileName: string;
begin
  Result := FLaneConfigXMLFileName;
end;

procedure TXMLLaneType.SetLaneConfigXMLFileName(Value: string);
begin
  if Value = FLaneConfigXMLFileName then Exit;
  FLaneConfigXMLFileName := Value;
end;

function TXMLLaneType.GetCardProcessingProfileXMLFileName: string;
begin
  Result := FCardProcessingProfileXMLFileName;
end;

procedure TXMLLaneType.SetCardProcessingProfileXMLFileName(Value: string);
begin
  if Value = FCardProcessingProfileXMLFileName then Exit;
  FCardProcessingProfileXMLFileName := Value;
end;

function TXMLLaneType.GetLaneType: string;
begin
  Result := FLaneType;
end;

procedure TXMLLaneType.SetLaneType(Value: string);
begin
  if Value = FLaneType then Exit;
  FLaneType := Value;
end;

function TXMLLaneType.GetWorkingKey: string;
begin
  Result := FWorkingKey;
end;

procedure TXMLLaneType.SetWorkingKey(Value: string);
begin
  if Value = FWorkingKey then Exit;
  FWorkingKey := Value;
end;

function TXMLLaneType.GetAtallaKey: string;
begin
  Result := FAtallaKey;
end;

function TXMLLaneType.GetTerminalId: string;
begin
  Result := FTerminalId;
end;

procedure TXMLLaneType.SetAtallaKey(Value: string);
begin
  if Value = FAtallaKey then Exit;
  FAtallaKey := Value;
end;

procedure TXMLLaneType.SetTerminalId(Value: string);
begin
  if Value = FTerminalId then Exit;
  FTerminalId := Value;
end;

function TXMLLaneType.GetTerminalIPAddress: string;
begin
  Result := FTerminalIPAddress;
end;

procedure TXMLLaneType.SetTerminalIPAddress(Value: string);
begin
  if Value = FTerminalIPAddress then Exit;
  FTerminalIPAddress := Value;
end;

initialization
  ExtendedLog('LanesXML Initialization');
finalization
  ExtendedLog('LanesXML Finalization');

end.


