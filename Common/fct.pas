// (c) MTXEPS, Inc. 1988-2014
Unit fct;

{$V-}
interface

uses
  FinalizationLog,
  MTX_Lib,
  MTX_Utils,StrUtils,
  classes,
  SysUtils,
  FleetU,
  MTX_XMLClasses,
  StoreConfigurationsXML,
  CardProcessingProfileXML,
  MTX_Constants, MdMsg, {LibXmlParser,} UXMLCommon,
  MTXEncryptionUtils,
  BinXmlLookup,
  HostFunctions;
//  CashiersXML;

//JTG these routines all use and modify an MdMsgRec in complex ways..
procedure ConvertToCredit(var InRec: mdmsgrec);    { JMR-A }
procedure CheckForFSACard(var InRec: mdMsgRec);
function FindCardPrefix(var DSTranBuf : MdMsgRec; const notAutoTenderLookup: boolean=true) : Boolean;
procedure SetSignatureValuesInMdMsg(var TranBuf: MdMsgRec);
function FoundCardProcID(var TranBuf: MdMsgRec): boolean;
function FindCardPfxAndGetFCTBuf(var DSTranBuf: MdMsgRec): boolean;
function SetSearchPfx(var aMdMsg: MdMsgRec): string;
function CkTransaction(var DSTranBuf : MdMsgRec; const OpenEPSConnected: boolean=false) : Boolean;
function IsOtherTenderDefined(var possibleEPSTenders : arrayWinEPSTenders): boolean;
function ResolveTenderType(TendersTaken: string; var DSTranBuf: MdMsgRec;  { returns 3 digit OpenEPS tender }
  var NumberOfTendersFound: integer; var possibleEPSTenders : arrayWinEPSTenders;
  const SetConnectPay: boolean = false; const defaultTender: integer = 0; const isSuppressDebit: Boolean = False;
  const TokenTransactionCardType: string = ''): string;
function GetOfflineCrToDbFloor(var DSTranBuf: MdMsgRec): integer; { JMR-P }
function GetOriginalProcIDAndName: string;       //JTG: sets a global from MdMsg rec from an earlier call to FindCardPrefix
procedure SetHostBuf(var InRec: MdMsgRec; AMdMsgTenderType: integer=iNone);

//JTG I think the following routines (including their support calls) can be moved out of here.. will do so soon...
function IsPfxInBinFile(aType: TBinType; aPrefix: string; aLen: integer; var NetworkList: TStrings): boolean;
function Init_FCT_Array: boolean;
function CheckIfLoyaltyCard(var DSTranBuf: MdMsgRec): boolean;
procedure SetOfflineAuthCodeByReqCode(var DSTranBuf: MdMsgRec);
function CheckIfGiftCard(var DSTranBuf: MdMsgRec): boolean; //CPCLIENTS-5359 Check PAN in Gift Prefix
function GetMaxOnlineCashback: integer; //CPCLIENTS-8550

var
  IsLoyaltyCard: boolean = false;
  LoyaltyCardDefined: boolean = false;
  CreditPfx     : TStringList;
  DebitPfx      : TStringList;
  PropCrPfx     : TStringList;
  PropDbPfx     : TStringList;
  GiftPfx       : TStringList;

implementation

uses
  SCATConstants,
{$IFDEF MTXEPSDLL}
  EMVManager,        // TFS-148515
  DllTypes,
  MTX_EPS_IProcs,   //17003 17004
  TransactionData,    // CPCLIENTS-1932
  TransactionLog,     // CPCLIENTS-1932
  OEOfflineClass,     // CPCLIENTS-1932
  UIsoio,
  hostname,                                        // CPCLIENTS-11904
{$ENDIF}
{$IFDEF FUEL}
  DllTypes,     //JTG: had to add this in since Eran is referencing wc below for 58719
{$ENDIF}
{$IFDEF OPENIP}
  DllTypes,     //JTG: had to add this in since Eran is referencing wc below for 58719
  MRTypes;
{$ENDIF}
{$IFNDEF OPENIP}
  XPIBaseTerm;
{$ENDIF}

const
  OnCode  = 1;
  OffCode = 2;
  DSFCTMaxCashOnL           = 6;
  DSFCTMaxCashOffL          = 6;
  DSFCTMaxOnAmtL            = 6;
  DSFCTMaxOffAmtL           = 6;
  FSAfalse = false;
  ProgIDfalse = false;
  CheckFalse = false;
  FSAtrue = true;
  ProgIDtrue = true;
  CheckTrue = true;
  TrimTrue = true;
  TrimFalse = false;
  PanSize = 10;
  PanSizeTimes2 = 20;
  PanLenSize = 2;
  CardTypeSize = 2;
  AutoTenderSize = 1;
  FSACodeSize = 2;
  StateCodeSize = 2;                                  // eWIC state code
  ProgramIDSize = 5; // DOEP-65086: was 4
  RetailerIDSize = 24;
  FIIDSize = 4;
  DraftCapSize = 1;
  SvcCodeSize = 3;

  { positions of vars in prefix TStringList }
  PanPos        = 1;                           { pan starting position }
  PanLenPos     = PanPos + PanSizeTimes2;
  CardTypePos   = PanLenPos + PanLenSize;
  AutoTenderPos = CardTypePos + CardTypeSize;
  FSACodePos    = AutoTenderPos + AutoTenderSize;
  StateCodePos  = FSACodePos + FSACodeSize;            // add this for eWIC state code
  ProgramIDPos  = StateCodePos + StateCodeSize;        // "
  RetailerIDPos = ProgramIDPos + ProgramIDSize;
  FIIDPos       = RetailerIDPos + RetailerIDSize;
  DraftCapPos   = FIIDPos + FIIDSize;
  SvcCodePos    = DraftCapPos + DraftCapSize;

  TDBSvcCodes: array[1..3] of string[3] = ('120','220','620');

type
  TAID = class(TCollectionItem) // DOEP-31746 <
  public
    RID: string;
    PIX: string;
    Tender: string;
    CardCode: string;
    AutoTenderExcluded: string;
  end;

  TAIDTable = class(TCollection)
  public
    function Add(RID: string; PIX: string; Tender: string; CardCode: string; AutoTenderExcluded: string): TAID;
    //function Find(RID: string; PIX: string): TAID; // XE; Remove WinEPS - not for OpenEPS
  end; // DOEP-31746 >

var
  OriginalProcIDAndNameForFSA: string;
  TheBINFileName: string;
  NotAcceptPfx  : TStringList;
  PfxInBinFileResult: boolean = false;
  // CreditPfx     : TStringList;       Moved to interface section to expose them to other units (EMVManager)
  // DebitPfx      : TStringList;
  //PropCrPfx     : TStringList;
  //PropDbPfx     : TStringList;
  CkAuthPfx     : TStringList;
  EBTFSPfx      : TStringList;
  EBTCAPfx      : TStringList;
  //GiftPfx       : TStringList;
  PINPfx        : TStringList;
  PhonePfx      : TStringList;
  WlessPfx      : TStringList;
  ACHPfx        : TStringList;
  ConnectPayPfx : TStringList;
  eWICPfx       : TStringList;
  BenefitsProgramPfx  : TStringList;  // CPCLIENTS-9586 For new Tender Type
  LoyaltyPfx    : TStringList;  

  IsPfxSearchWildCard: array[1..WinEPS_MaxTenders+1] of boolean;
  UseWildCardSearch: boolean;
  UseWildCardSearchForNotAccepted: boolean;
  DebitNetworkIDTable: TStringList;            // DEV-12503
  AIDTable: TAIDTable; // DOEP-31746
  crToDbCheckPrefix: boolean = false;          { JMR-B }
  OnTransOK        : Boolean;                  { JMR-E }
  CrToDbOnFloor,
  CrToDbOffFloor: string;                      { JMR-O }
  PfxForFindCardPrefix: String;
  SvcCodeForFindCardPfx: string;

function TAIDTable.Add(RID: string; PIX: string; Tender: string; CardCode: string; AutoTenderExcluded: string): TAID; // DOEP-31746
begin
  result := TAID.Create(Self);
  result.RID := RID;
  result.PIX := PIX;
  result.Tender := Tender;
  result.CardCode := CardCode;
  result.AutoTenderExcluded := AutoTenderExcluded;
end;

{ // XE: Remove WinEPS - not for OpenEPS
function TAIDTable.Find(RID: string; PIX: string): TAID; // DOEP-31746
var i: integer;
begin
  result := nil;
  for i := 0 to Count -1 do
    if SameText(TAID(Items[i]).RID, RID) and SameText(TAID(Items[i]).PIX, PIX) then
    begin
      result := TAID(Items[i]);
      break;
    end;
end;
}

procedure CheckForFSACard(var InRec: mdMsgRec);     //used by OEOfflineClass
begin   // offline rules for taking FSA cards
  if (InRec.OffLineAuthCodeN = TrxAppOffN) then
  begin
    if (InRec.ReqCodeN in Cr_Set) and (InRec.FSACard  = 'Y') then
    begin
      if (InRec.VoidCode = 'V') then   // don't send FSA info back on voids
      begin
        InRec.FsaAmount := 0;
        InRec.RxAmount := 0;
        InRec.DentalAmount := 0;
        InRec.VisionAmount := 0;
        InRec.MedicalAmount := 0;
      end
      else
      begin
        if ((InRec.HostApproveLowerAmount <> 'Y') and (InRec.FsaAmount < InRec.TrxAmtN)) or
           (InRec.FsaAmount = 0) then
        begin
          InRec.MTXRspCode := TrxDecSwitchDown;
          InRec.MTXRspCodeN := TrxDecSwitchDownN;
          InRec.OffLineAuthCodeN := TrxDecSwitchDownN;
        end
        else
        begin
          if (InRec.TrxAmtN <> InRec.FsaAmount) then   // say we approved for a lower amount
            InRec.HostApproveLowerAmount := '^';
//          InRec.TrxAmtN := InRec.FsaAmount; //CPCLIENTS-12851 commented, as Txn amount should go as it is not only FSA.
          InRec.TrxAmt := intToStr(InRec.TrxAmtN);
        end;
      end;
    end;
  end;
end;

function GetAcctNoFromTrack2(aTrack2: string): string;   //JTG: check for duplicates or move to common unit
Var Equ : Integer;
begin
  Equ := pos('=', aTrack2);
  If (Equ > 0)
    then result := Copy(aTrack2, 1, Equ-1)
    else result := aTrack2;
end;

function SetCardPfxTable(var DSTranBuf: MdMsgRec): integer;  // JTG todo simplify
begin
  if not IsValidCardProcID(DSTranBuf) or (DSTranBuf.ReqCodeN = User_2_ReturnN) or (DllTypes.OpenEPSTenderType= ttMultiTender) then //19132
    DSTranBuf.CardProcID := SetCardProcIDInteg(DSTranBuf.ReqCodeN);

  result := strToIntDef(DSTranBuf.CardProcID,0);    // sets the card pfx table
end;

function SetPrefixToCheck(DSTranBuf: MdMsgRec): string;   //JTG todo possibly reduce input
begin
  result := ''; //18825

  if (MyVal(DSTranBuf.CardProcID) = WinEPS_Check) and  (trim(DSTranBuf.Check_Type) <> '') then { if have chktype }
  begin
    if (trim(DSTranBuf.Track2Data) = '') then
      result := DSTranBuf.Check_Type + GetAcctNoFromTrack2(DSTranBuf.Dr_License)  { chkauth: 1st digit = Ck type }
    else
      result := DSTranBuf.Check_Type + GetAcctNoFromTrack2(MTXEncryptionUtils.RetrieveMdMsgTrack2(DSTranBuf)); { to allow limits by Ck type }
  end
  else
  if (MyVal(DSTranBuf.CardProcID) = WinEPS_WireLess) then
  begin
    if (trim(DSTranBuf.Track2Data) <> '') Then
      result := GetAcctNoFromTrack2(RetrieveMdMsgTrack2(DSTranBuf))
    else
    if (DSTranBuf.AcctNo <> '') then
      result := MTXEncryptionUtils.RetrieveMdMsgPersonalAccountNum(DSTranBuf);
  end
  else
  begin                                           { not check auth }
    if (DSTranBuf.AcctNo <> '') then                                     // first, try acct no
	    result := MTXEncryptionUtils.RetrieveMdMsgPersonalAccountNum(DSTranBuf)
    else
    if (DSTranBuf.Track2Data <> '') Then                                // then track2
      result := GetAcctNoFromTrack2(RetrieveMdMsgTrack2(DSTranBuf))
    else
      result := GetAcctNoFromTrack2(DSTranBuf.Dr_License);               // then dr license
  end;
  result := GetAcctNoFromTrack2(result); // strip the equal sign and following
	  
  
  if (result = '') then                //18825-Modified Bin8 related code to resolve the prefix search issue
  begin  
    result := Trim(DSTranBuf.LookupPrefix);
    if (result = '') then
      result := Trim(DSTranBuf.AcctNoFirst6);
  end;	

  {$IFDEF P2P}
  result := rpad(LeftStr(result, 10), DSTranBuf.PanLen); // DOEP-50090,50092 - use for prefix search the first 10 padded to the right with spaces for P2P
  {$ENDIF}

  {$IFDEF MTXEPSDLL}
  if dllTypes.IsSessionKeyAESEncryption then //DOEP-58895
  begin
    result := StringReplace(result, 'X', ' ', [rfReplaceAll]);
    result := rpad(LeftStr(result, 10), DSTranBuf.PanLen);
  end;
  {$ENDIF}
//  MsgDebug('SetPrefixToCheck (2) = [' + result + ']');
end;

function PrefixSearch(var DSTranBuf: MdMsgRec; prefixItem: String; IsCheckAuth: boolean): boolean;
var TrimmedPfx,TrySvcCode: string;
    TryPanLen: string[PanLenSize];
    TryPANLenN: integer;
    TryFmPrefix,TryToPrefix,TryPrefix: string;
    LenToCheck,j: integer;
begin
  TrimmedPfx := trim(PfxForFindCardPrefix);
  TryPANLen := Copy(prefixItem, PanLenPos, PanLenSize);
  TryPANLenN := StrToIntDef(TryPANLen, 0);
  TrySvcCode := trim(Copy(prefixItem,SvcCodePos,SvcCodeSize));
  LenToCheck := Length(PfxForFindCardPrefix);
  if IsCheckAuth and (trim(DSTranBuf.Check_Type) <> '') then
    dec(LenTocheck);      // because check type is on the front of the pfx, the actual pfx len is one less
  result := (TryPANLen = 'XX') or (TryPANLenN = LenToCheck);
  if result then  { len ok }
  begin
    if UseWildCardSearch then       // this is the old way, fm and to pfx are the same, but with possible wild cards
    begin
      TryPrefix := Copy(prefixItem, PanPos, PanSize);
      j := 1;
      while (j <= PanSize) and (j <= length(TrimmedPfx)) and result do   // result starts out true from above
      begin
        if (TryPrefix[j] <> 'X') and (TryPrefix[j] <> PfxForFindCardPrefix[j]) then {  if real digit  }
          result := False;  {  stop loop if one match not good  }
        inc(j);
      end;
    end
    else
    begin
      TryFmPrefix := LeftStr(Copy(prefixItem, PanPos, PanSize), length(TrimmedPfx));
      TryToPrefix := LeftStr(Copy(prefixItem, PanPos+PanSize, PanSize), length(TrimmedPfx));
      result := (CompareText(TrimmedPfx,TryFmPrefix) >= 0) and (CompareText(TrimmedPfx,TryToPrefix) <= 0);
    end;
    if result and (TrySvcCode <> '') and (SvcCodeForFindCardPfx <> '') then   // new stuff to check card service code
      result := (TrySvcCode = SvcCodeForFindCardPfx);
    MsgDebug(format('Pfx >%s< found is %s, in range >%s/%s<, SvcCode: inFile/Card >%s/%s<',
      [TrimmedPfx,BoolStr(result),TryFmPrefix,TryToPrefix,TrySvcCode,SvcCodeForFindCardPfx]));
  end;
  //else
  //  sm(format('****DEBUG: Pfx >%s< incorrect PAN length %d, length in Pfx file %s',[TrimmedPfx,LenToCheck,TryPanLen]));
end;

function IsPfxFound(var DSTranBuf: MdMsgRec; const notAutoTenderLookup: boolean; aPfxList: TStringList; SetFSA, SetProgramID, IsCheckAuth: boolean): boolean;
var
  i: integer;
  PfxDataFromList: string;
  sAutoTenderValue: string; // CPCLIENTS-16723
begin
  result := false;
  if not IsCheckAuth then // DOEP-65013 - need to skip for check auth as this clears the prefix
    PfxForFindCardPrefix := SetPrefixToCheck(DSTranBuf); // DOEP-60639
  //msgDebug('FindCardPrefix.IsPfxFound PfxForFindCardPrefix = ' + PfxForFindCardPrefix);
  if PfxForFindCardPrefix = '' then
    Exit;
  i := 0;
  while (i <= aPfxList.Count - 1) and not result do
  begin
    sAutoTenderValue := copy(aPfxList[i], AutoTenderPos, AutoTenderSize); // CPCLIENTS-16723
    if  (sAutoTenderValue = 'N') or (sAutoTenderValue = 'X') or (sAutoTenderValue = 'F') or notAutoTenderLookup then  // CPCLIENTS-16723   // 19438 included 'F' value for AutotenderExclude in the condition
    begin  // check auth prefixes have a 1 char check type as the first char.  Strip off if we don't have check type
      if IsCheckAuth then
      begin
        if (trim(DSTranBuf.Check_Type) = '') then  { used in OpenEPS tender resolution }
          PfxDataFromList := copy(aPfxList[i], 2, PanSize) + '0' + Copy(aPfxList[i], 2+PanSize, PanSize) + '9'
                             + copy(aPfxList[i], PanLenPos, PanLenSize + CardTypeSize)
        else
        begin
          PfxDataFromList := aPfxList[i];
          if (DSTranBuf.Check_Type[1] <> copy(PfxForFindCardPrefix,1,1)) then
            PfxForFindCardPrefix := DSTranBuf.Check_Type[1] + PfxForFindCardPrefix;
        end;
        //sm(format('****DEBUG: CheckType >%s<, PfxDataFromList >%s<, PfxForFindCard >%s<',[DSTranBuf.Check_Type,
        //  PfxDataFromList,PfxForFindCardPrefix]));
      end
      else
        PfxDataFromList := aPfxList[i];
      result := prefixSearch(DSTranBuf, PfxDataFromList, IsCheckAuth);
      if not result then
        inc(i)                  { if not done, get next record }
      else
      begin
         if (sAutoTenderValue = 'X') then  // CPCLIENTS-16723 - To exit the tender when AutoTenderExcluded flag value is "X".
          Exit(False);

        if (OpenEPSTenderType = ttNone) and          // 19846 run the logic for generic eft case in order to allow for independent Purchase/Return(single pass trx) when specific tender is selected
          (sAutoTenderValue = 'F') then              // 19438 for AutoTenderExclude value of 'F' in Prefix table the Pfx to be skipped for the first pass (ItemQualificationRefId is empty) and to be considered in the second pass of the trx
        begin
          if MR.ItemQualificationRefID.IsEmpty then
            Exit(False);
        end
        else if not MR.ItemQualificationRefID.IsEmpty and (sAutoTenderValue <> 'F')then   // 19438 if ItemQualificationRefID is not empty, consider the BIN with 'F' value and skip the other BINs
          Exit(False);

        if (DSTranBuf.FSACard = 'Y') and SetFSA then
        begin
          // ex) '4XXXXXXXXX4XXXXXXXXX16VSNHB                                       '
          DSTranBuf.CardProcID := copy(aPfxList[i], CardTypePos, CardTypeSize);
          if FoundCardProcID(DSTranBuf) then
            OriginalProcIDAndNameForFSA := DSTranBuf.CardProcID + FCTBuf.CardName;
          DSTranBuf.CardProcID := copy(aPfxList[i], FSACodePos, FSACodeSize);
        end
        else
        begin
          OriginalProcIDAndNameForFSA := '';         // not FSA so this is null
          DSTranBuf.CardProcID := copy(aPfxList[i], CardTypePos, CardTypeSize);
        end;

        if (DSTranBuf.St_Code = '') then     // eWic only
          DSTranBuf.St_Code := copy(aPfxList[i], StateCodePos, StateCodeSize);

        if SetProgramID then
          DSTranBuf.ProgramID := trim(copy(aPfxList[i], ProgramIDPos, ProgramIDSize)); //JMR-V

        DSTranBuf.TDBRetailerID := trim(copy(aPfxList[i], RetailerIDPos, RetailerIDSize));
        DSTranBuf.TDBFIID := trim(copy(aPfxList[i], FIIDPos, FIIDSize));
        DSTranBuf.TDBDraftCaptureFlag := copy(aPfxList[i], DraftCapPos, DraftCapSize);
      end;
    end
    else
      inc(i);
  end;
end;

function CheckIfLoyaltyCard(var DSTranBuf: MdMsgRec): boolean; // DOEP-60639
begin
  result := IsPfxFound(DSTranBuf, true, LoyaltyPfx, false, false, false);
  //IsLoyaltyCard := result;
  if result then 
    SM('IsLoyaltyCard: PAN found in loyalty cards');
end;

//CPCLIENTS-5359 Check whether PAN exists in Gift Prefix or not
function CheckIfGiftCard(var DSTranBuf: MdMsgRec): boolean;
begin
  UseWildCardSearch := True;
  result := IsPfxFound(DSTranBuf, true, GiftPfx, false, false, false);
  if result then
    SM('IsGiftCard: PAN found in Gift cards');
end;

{ FINDS CARD PROC ID BY SEARCHING CARD FILES }
function FindCardPrefix(var DSTranBuf: MdMsgRec; const notAutoTenderLookup: boolean=true) : Boolean;
var CardPfxTable: integer;
    IsNotAccepted: boolean;

    function IsCardCodeFound(aPfxList: TStringList): boolean;
    var
      i: integer;
      tmpCardCode: string;
    begin
      result := false;
      for i := 0 to aPfxList.Count -1 do
      begin
        tmpCardCode := copy(aPfxList[i], CardTypePos, CardTypeSize);
        if (tmpCardCode <> '') and SameText(tmpCardCode, DllTypes.TokenTransactionCardType) then
        begin
          result := true;
          DSTranBuf.CardProcId := TokenTransactionCardType;
          if FoundCardProcID(DSTranBuf) and Assigned(FCTBuf) then
            DSTranBuf.CardName := FCTBuf.CardName;
          Exit;
        end;
      end;
    end;


    function IsTicketLookupPfxFound: Boolean; // TFS-177513
    var
      CardPfxTableIdx, CardPfxTable, tenderType: Integer;
      SetFSA, SetProgID : Boolean;
      PfxList: TStringList;
    begin
      Result := False;
	  for CardPfxTableIdx := 1 to 7 do
      begin
        case CardPfxTableIdx of
         1: begin
              CardPfxTable := WinEPS_Credit;
              PfxList := CreditPfx;
              SetFSA := FSAtrue;
              SetProgID := ProgIDfalse;
              tenderType := ttCredit; 
            end;
         2: begin  
              CardPfxTable := WinEPS_PrivCr;
              PfxList := PropCrPfx;
              SetFSA := FSAfalse;
              SetProgID := ProgIDfalse;
              tenderType := ttPrivateCredit;
            end;  
         3: begin    
              CardPfxTable := WinEPS_PrivDb;
              PfxList := PropDbPfx;
              SetFSA := FSAfalse;
              SetProgID := ProgIDfalse;
              tenderType := ttPrivateDebit;
            end;
         4: begin
              CardPfxTable := WinEPS_EBT_FS;
              PfxList := EBTFSPfx;
              SetFSA := FSAfalse;
              SetProgID := ProgIDfalse;
              tenderType := ttEBT_FS;
            end;   
         5: begin
              CardPfxTable := WinEPS_EBT_CA;
              PfxList := EBTCAPfx;
              SetFSA := FSAfalse;
              SetProgID := ProgIDfalse;
              tenderType := ttEBT_CA;
            end;
         6: begin
              CardPfxTable := WinEPS_User1;
              PfxList := GiftPfx;
              SetFSA := FSAfalse;
              SetProgID := ProgIDtrue;
              tenderType := ttGiftCard;
            end;
         7: begin
              CardPfxTable := WinEPS_EWIC;
              PfxList := eWICPfx;
              SetFSA := FSAfalse;
              SetProgID := ProgIDfalse;
              tenderType := tteWIC;
            end;
        end;
        UseWildCardSearch := IsPfxSearchWildCard[CardPfxTable];
        if OpenEPSTenderTaken[tenderType] then
          result := IsPfxFound(DSTranBuf, notAutoTenderLookup, PfxList, SetFSA, SetProgID, CheckFalse);
        if Result then
          break;
      end;
    end;


begin { FindCardPrefix }     // Tom says leave alone
  result := false;
  try
    CardPfxTable := SetCardPfxTable(DSTranBuf);

    if DllTypes.IsTokenTransaction then
    begin
      case CardPfxTable of
        WinEPS_Credit: result := IsCardCodeFound(CreditPfx);
        WinEPS_CrToDb,                                                                               { JMR-B }
        WinEPS_Debit:  result := IsCardCodeFound(DebitPfx);
        WinEPS_PrivCr: result := IsCardCodeFound(PropCrPfx);
        WinEPS_PrivDb: result := IsCardCodeFound(PropDbPfx);
        WinEPS_Check:  result := IsCardCodeFound(CkAuthPfx);
        WinEPS_EBT_FS: result := IsCardCodeFound(EBTFSPfx);
        WinEPS_EBT_CA: result := IsCardCodeFound(EBTCAPfx);
        WinEPS_User1:  result := IsCardCodeFound(GiftPfx);
        WinEPS_Fleet:  result := IsCardCodeFound(PINPfx);
        WinEPS_User2:  result := IsCardCodeFound(PhonePfx);
        WinEPS_WireLess: result := IsCardCodeFound(WlessPfx);
        WinEPS_ACH:    result := IsCardCodeFound(ACHPfx);
        WinEPS_ConnectPay: result := IsCardCodeFound(ConnectPayPfx);
        WinEPS_EWIC:   result := IsCardCodeFound(eWICPfx);
        else
          SM(format('****WARNING! CardProcId>'+DSTranBuf.CardProcID+'< should be 1 to %d',[WinEPS_MaxTenders+1]));
      end;   { of case }
    end else
    begin
      PfxForFindCardPrefix := SetPrefixToCheck(DSTranBuf);
      SvcCodeForFindCardPfx := trim(DSTranBuf.CardServiceCode);
      if DSTranBuf.CardProcID = '' then    //18343
        DSTranBuf.CardProcID := '??';                   { init to we didn't find it }
      //SM(Format('DEBUG FindCardPrefix: PfxForFindCardPrefix=%s / CardPfxTable=%d', [PfxForFindCardPrefix, CardPfxTable]));  //62378
      if (trim(PfxForFindCardPrefix) = '') then
        sm('****WARNING: Find Card Prefix - prefix to find is empty, return not found')
      else
      begin
        // add new logic for prefix file of cards Not Accepted
        if (NotAcceptPfx.Count > 0) then    // if we have any of these prefixes in the file, then check 'em
        begin
          UseWildCardSearch := UseWildCardSearchForNotAccepted;
          IsNotAccepted := IsPfxFound(DSTranBuf, notAutoTenderLookup, NotAcceptPfx, FSAfalse, ProgIDfalse, CheckFalse);
          if IsNotAccepted then                            // this prefix is NOT accepted
          begin
            DSTranBuf.MTXRspCode := TrxDecNotAccepted;     // DOEP - 41971
            DSTranBuf.MTXRspCodeN := TrxDecNotAcceptedN;
          end
        end
        else
          IsNotAccepted := false;

        if IsNotAccepted then     // if IsNotAccepted then we don't take the card = same as not found
          result := false
        else
        begin
          if (CardPfxTable >= 1) and (CardPfxTable <= WinEPS_MaxTenders+1)
            then UseWildCardSearch := IsPfxSearchWildCard[CardPfxTable]
            else UseWildCardSearch := false;

          if DSTranBuf.FTranCode = IntToStr(trtTicketLookup) then // TFS-177513
          begin
            result := IsTicketLookupPfxFound;
          end
          else
          begin
            case CardPfxTable of
              WinEPS_Credit, WinEPS_MultiTender: result := IsPfxFound(DSTranBuf, notAutoTenderLookup, CreditPfx, FSAtrue, ProgIDfalse, CheckFalse);   //19132
              WinEPS_CrToDb,                                                                               { JMR-B }
              WinEPS_Debit:  result := IsPfxFound(DSTranBuf, notAutoTenderLookup, DebitPfx, FSAtrue, ProgIDfalse, CheckFalse);
              WinEPS_PrivCr: result := IsPfxFound(DSTranBuf, notAutoTenderLookup, PropCrPfx, FSAfalse, ProgIDfalse, CheckFalse);
              WinEPS_PrivDb: result := IsPfxFound(DSTranBuf, notAutoTenderLookup, PropDbPfx, FSAfalse, ProgIDfalse, CheckFalse);
              WinEPS_Check:  result := IsPfxFound(DSTranBuf, notAutoTenderLookup, CkAuthPfx, FSAfalse, ProgIDtrue, CheckTrue); // DOEP-65086: ProgIDtrue
              WinEPS_EBT_FS: result := IsPfxFound(DSTranBuf, notAutoTenderLookup, EBTFSPfx, FSAfalse, ProgIDfalse, CheckFalse);
              WinEPS_EBT_CA: result := IsPfxFound(DSTranBuf, notAutoTenderLookup, EBTCAPfx, FSAfalse, ProgIDfalse, CheckFalse);
              WinEPS_User1:  result := IsPfxFound(DSTranBuf, notAutoTenderLookup, GiftPfx, FSAfalse, ProgIDtrue, CheckFalse);
              WinEPS_Fleet:  result := IsPfxFound(DSTranBuf, notAutoTenderLookup, PINPfx, FSAfalse, ProgIDfalse, CheckFalse);
              WinEPS_User2:  result := IsPfxFound(DSTranBuf, notAutoTenderLookup, PhonePfx, FSAfalse, ProgIDtrue, CheckFalse);
              WinEPS_WireLess: result := IsPfxFound(DSTranBuf, notAutoTenderLookup, WlessPfx, FSAfalse, ProgIDtrue, CheckFalse);
              WinEPS_ACH:    result := IsPfxFound(DSTranBuf, notAutoTenderLookup, ACHPfx, FSAfalse, ProgIDfalse, CheckFalse);    // DOEP-51284, need to assign IsPfxFound result to local Result
              WinEPS_ConnectPay: result := IsPfxFound(DSTranBuf, notAutoTenderLookup, ConnectPayPfx, FSAfalse, ProgIDfalse, CheckFalse);
              WinEPS_EWIC:   result := IsPfxFound(DSTranBuf, notAutoTenderLookup, eWICPfx, FSAFalse, ProgIDfalse, CheckFalse);
              WinEPS_BenefitsProgram: result := IsPfxFound(DSTranBuf, notAutoTenderLookup, BenefitsProgramPfx, FSAtrue, ProgIDfalse, CheckFalse);  // CPCLIENTS-9586 To assign to new Tender Type
              else
                SM(format('****WARNING! CardProcId>'+DSTranBuf.CardProcID+'< should be 1 to %d',[WinEPS_MaxTenders+1]));
            end;   { of case }
            //SM('fct.FindCardPrefix: IsPfxFound is ' + booltostr(result, true));   //62378
          end;
        end;
        //SM(format('FindCardPrefix: CardProcID[%s] CardName[%s]', [DSTranBuf.CardProcId,DSTranBuf.CardName]));  //62378
      end;
    end;

    //CPCLIENTS-19133 Filter EBT tenders only for EBT Chip AID, no Debit or other tenders
    if (EMVMgr.IsEBTChip) and (EMVMgr.IsCardEntryChipCard) then
    begin
      if CardPfxTable in [WinEPS_EBT_FS, WinEPS_EBT_CA] then
        result := true
      else
        result := false;
    end;

  except
    on e:exception do
      SM('Try..Except: FCT.FindCardPrefix ' + e.message);
  end;
end; { FindCardPrefix }

procedure GetPreAuthAmount(var TranBuf: MdMsgRec);  // a configuration item; TODO refactor
begin
 if (TranBuf.LaneNoN > 0) and (TranBuf.LaneNoN <= MAXTERMS) then
   begin
   if (FCTBuf.OnlineLimits.PreauthAmount.AmountToHost = 0)
     then TranBuf.PreAuthAmt := IntToStr(TranBuf.TrxAmtN)
     else TranBuf.PreAuthAmt := IntToStr(FCTBuf.OnlineLimits.PreauthAmount.AmountToHost); { at least a zero }
   end;
end;

procedure SetSignatureValuesInMdMsg(var TranBuf: MdMsgRec); //TSL warns: DO NOT TOUCH (or) BE CAREFUL
begin
  if assigned(FCTBUF) and (not SameText(FCTBuf.ProcessingFlags.SkipSignatureCapture.Skip, '')) then
  begin
    TranBuf.skipSigOnline := SameText(FCTBuf.ProcessingFlags.SkipSignatureCapture.Skip, 'Y');
    TranBuf.skipSigOffline := FCTBuf.ProcessingFlags.SkipSignatureCapture.SkipOffline;
    TranBuf.skipSigAmount := FCTBuf.ProcessingFlags.SkipSignatureCapture.Amount;
    TranBuf.PromptOnManual := FCTBuf.ProcessingFlags.SkipSignatureCapture.PromptOnManual;
  end
  else if SameText(TranBuf.CardProcID, IntToStr(WinEPS_Credit)) then // DEV-28186
  begin     // this is the older way of doing the sig line thing without using CPP buf.
    TranBuf.skipSigOnline := (DSProcBuf.DSProcSigLineYN = 'Y');
    TranBuf.skipSigOffline := false;
    TranBuf.skipSigAmount := strToIntDef(DSProcBuf.DSProcSigLineAmt, 0);
    TranBuf.PromptOnManual := 'N';
  end
  else // DEV-28186
  begin
    TranBuf.skipSigOnline := false;
    TranBuf.skipSigOffline := false;
    TranBuf.skipSigAmount := 0;
    TranBuf.PromptOnManual := 'N';
  end;
end;

procedure SetHostApproveLowerInMdMsg(var TranBuf: MdMsgRec);  // TODO reduce input field
begin
  if TranBuf.IsIbmRegister then
  begin
    if Assigned(FCTBUF) then
    begin
      if (FCTBuf.ProcessingFlags.DoNotAllowPartialApproval = 'N')
        then TranBuf.HostApproveLowerAmount := 'Y'
        else TranBuf.HostApproveLowerAmount := 'N';
      //sm(format('****DEBUG: Assign HostApproveLower from FCTBuf:  FCTBuf.DoNotAllowPartialApproval/HostApproveLowerAmount %s/%s',
      //  [FCTBuf.ProcessingFlags.DoNotAllowPartialApproval,TranBuf.HostApproveLowerAmount]));
    end
    else
      sm('****WARNING: FCTBUF not assigned, cannot assign HostApproveLower for IBM');
  end;
end;

procedure SetFleetVehicleID(var TranBuf: MdMsgRec);
var aFleetCard: FleetU.TFleetCard;
begin
  if (TranBuf.ReqCodeN in Fleet_Set) and (TranBuf.fleetVehicleID = '') then
  begin
    GetHostBuf(mtx_lib.TenderNum(TranBuf.ReqCodeN));
    if assigned(HostBuf) and sameText(HostBuf.Suffix, 'ACI') then  // only ACI uses vehicleID from track
    begin
    aFleetCard := TFleetCard.Create(TranBuf.fleetCardType, RetrieveMdMsgTrack2(TranBuf));
    try
      TranBuf.fleetVehicleID := aFleetCard.VehicleID;
    finally
      aFleetCard.Free;     //don't free unless Create succeeds
    end;
    end;
  end;
end;

function FoundCardProcID(var TranBuf: MdMsgRec): boolean;
begin
  result := False;
  try
    XMLCardProcessingProfile := CPPList.FindCPP(TranBuf.LaneNoN);                 { JMR-U }
    if XMLCardProcessingProfile <> nil then
    begin
      FCTBuf := XMLCardProcessingProfile.FindFCT(TranBuf.CardProcID);
      if FCTBuf <> nil Then      { Set Card Name and Reporting groups }
      begin
        result := true;
        TranBuf.CardName := trim(FCTBuf.CardName);
        //if assigned(FCTBuf.ProcessingFlags.SkipSignatureCapture) then
        SetSignatureValuesInMdMsg(TranBuf);
        SetHostApproveLowerInMdMsg(TranBuf);
        TranBuf.fleetCardType := FCTBuf.FleetType;
        TranBuf.NoCVMAmount := FCTBuf.OnlineLimits.NoCVMAmount;                              // MCD TFS-7513
        if (length(FCTBuf.ProcessingFlags.CustomerNameOnReceipt) > 0) then
          TranBuf.PrintNameOnRcpt := AnsiChar(FCTBuf.ProcessingFlags.CustomerNameOnReceipt[1]);  //7934
        if (TranBuf.ReqCodeN in PreAuth_Set) and (GetLaneType(TranBuf.LaneNo) <> LANE_HOSPITALITY_CHAR) then
        begin
          if (TranBuf.HostSuffixCode = 'MON') then
            TranBuf.preAuthAmt := IntToStr(TranBuf.trxAmtN)
          else
          begin
            GetPreAuthAmount(TranBuf);
            TranBuf.trxAmt := TranBuf.preAuthAmt;
            TranBuf.trxAmtN := strToIntDef(TranBuf.trxAmt, 0);
            TranBuf.OriginalTrxAmtN := TranBuf.TrxAmtN;         // this is now the original amt we send to the host for fuel
            SetFleetVehicleID(TranBuf);
          end;
        end;
        TranBuf.MaxOfflineAmt  := FormatFloat(Fmt6Zero, FCTBuf.OfflineLimits.MaxTotalAmount);
        TranBuf.PreAuthAmtOnlinePump := FormatFloat(Fmt6Zero, FCTBuf.OnlineLimits.PreauthAmount.AmountToPump); 
        if (TranBuf.HostSuffixCode = 'MON')                         // Dev-48931
          then TranBuf.PreAuthAmtOfflinePump := TranBuf.preAuthAmt
          else TranBuf.PreAuthAmtOfflinePump := FormatFloat(Fmt6Zero, FCTBuf.OfflineLimits.PreauthAmount.AmountToPump); 
      end;                                                                          
    end
    else
      SM('****WARNING: (fct.FoundCardProcID) CPPList.FindCPP returned nil.');
  except
    on e:exception do
      SM('Try..Except: FCT.FoundCardProcID ' + e.message);
  end;
end; { FoundCardProcID }

function GetOriginalProcIDAndName: string;
begin
  result := OriginalProcIDAndNameForFSA;     //JTG: this is a global here, and is set by an earlier call to FindCardPrefix
end;

function IsHost(aSuffix: string): boolean;                                          // CPCLIENTS-11904 moved here because I need it below
begin
  result := assigned(HostBuf) and sameText(HostBuf.Suffix, aSuffix);
end;

function FindCardPfxAndGetFCTBuf(var DSTranBuf: MdMsgRec): boolean;  {JMR-T}
begin
{$IFDEF MTXEPSDLL}
  if (DllTypes.P2PEncryption or DllTypes.IsVSPEnabled or DllTypes.IsSessionKeyAESEncryption) and
    (DSTranBuf.ReqCodeN in AuthComp_Set) then // DOEP-59378
  begin
    SM('FindCardPfxAndGetFCTBuf - P2P/VSP/MX-SV pre-auth completion skips checking FCT');
    result := true;
    Exit;
  end;
  if (DSTranBuf.ReqCodeN in [User_1_ReActivationN, User_1_FinalTenderN]) then
  begin
    SM('FindCardPfxAndGetFCTBuf - GiftCard Reactivation / FinalTender checking FCT');
    result := true;
    Exit;
  end;
  if IsWholeFoodsMarketProgID(dllTypes.FCTBufProgID) then // TFS-137131
  begin
    SM('FindCardPfxAndGetFCTBuf - WFM gift card with embossed track2 skips checking FCT');
    result := true;
    Exit;
  end;
  if (DSTranBuf.ReqCodeN in [User_1_PreAuthN]) and IsHost(INCHOST) then                               // CPCLIENTS-11904
  begin
    SM('FindCardPfxAndGetFCTBuf - gift card Pre Authorization has no card data - skip checking FCT');
    result := true;
    Exit;
  end;

{$ENDIF}
  result := FindCardPrefix(DSTranBuf);
  try
    if result then
    begin         { Locate the card processing type }
      {$IFNDEF OpenIP}       //80472
      // For XPI terminal, replace CardProcId with CardType if it was returned in the E05 response
      if CharInSet(TermType, XPISet) and Assigned(XPIMXTerminal) then
        if XPIMXTerminal.CardType > '' then
          DSTranBuf.CardProcID := XPIMXTerminal.CardType;
      {$ENDIF}               //80472
      if not FoundCardProcID(DSTranBuf) and NOT IsLoyaltyCard then { Card Processing ID not in FCT file }
      begin
        result := False;
        DSTranBuf.MtxRspCode  := TrxDecNoFCT;      //JTG TODO possibly make function to simplify this setting
        DSTranBuf.MtxRspCodeN := TrxDecNoFCTN;
      end;
    end
    else if (DSTranBuf.MTXRspCode <> TrxDecNotAccepted) then   // DOEP-41971
    begin
      if (trim(PfxForFindCardPrefix) = '') or (copy(PfxForFindCardPrefix,1,1) = MASKCHAR) or
         ((DSTranBuf.ReqCodeN in Check_Set) and (copy(PfxForFindCardPrefix,1,1) = MASKCHAR)) then
      begin
        DSTranBuf.MTXRspCode  := TrxDecTrxNotAlwd;
        DSTranBuf.MTXRspCodeN := TrxDecTrxNotAlwdN;
      end
      else
      if (DSTranBuf.Entry_Track2 = 'M') then
      begin
        DSTranBuf.MTXRspCode  := TrxDecBadPfxManual;
        DSTranBuf.MTXRspCodeN := TrxDecBadPfxManualN;
      end
      else
      begin
        DSTranBuf.MTXRspCode  := TrxDecBadPrefix;
        DSTranBuf.MTXRspCodeN := TrxDecBadPrefixN;
      end;
    end;
  except
    on e:exception do
      SM('Try..Except: FCT.FindCardPfxAndGetFCTBuf ' + e.message);
  end;
end; { FindCardPfxAndGetFCTBuf }

function SetSearchPfx(var aMdMsg: MdMsgRec): string;   // related to bin files from banks (TODO possibly combine with other one)
var i: integer;
begin
  if (aMdMsg.AcctNo <> '') then
  begin
    result := MTXEncryptionUtils.RetrieveMdMsgPersonalAccountNum(aMdMsg);
    Result := StringReplace(Result, 'X', ' ', [rfReplaceAll]); // DOEP-58895
    i := pos(' ', result);           // apparently the PAN could have first 6, spaces, then last 4 so just take first 6
    if (i > 1) then                  // DOEP-52324 - Issue verifying debit prefix
      result := leftStr(result, pred(i));
  end
  else
  if (aMdMsg.Track2Data <> '') then
    result := GetAcctNoFromTrack2(MTXEncryptionUtils.RetrieveMdMsgTrack2(aMdMsg));
end;

function IsPfxInBinFile(aType: TBinType; aPrefix: string; aLen: integer; var NetworkList: TStrings): Boolean;
begin
  result := false;
  try
    if aLen = 0 then
      BinLookup0Length(aType, aPrefix, result, NetworkList)
    else
      BinLookup(aType, aPrefix, aLen, result, NetworkList);  //TODO maybe improve?
    SM(Format('IsPfxInBinFile BinType=%d PanLen=%d result=%s', [Ord(aType), aLen, YN(result)]));
  except
    on e: exception do
      sm(format('Try..Except: IsPfxInBinFile Pfx to find >%s< PanLen >%d< ',[aPrefix,aLen]) + e.message);
  end;
end;   { IsPfxInBinFile }

procedure IsBigYLynkDebitFreqShopperOfflineOK(var DSTranBuf: MdMsgRec);  // DOEP-48196
begin
  If IsHost('LYN') and (DSTranBuf.ReqCodeN in Db_Set)then      // only applies to debit for Lynk
  begin
    if (HostBuf.FreqShopReqOfflineDebit = 'Y') then
    begin  { don't do stand in if no freq shop num: len freqShp num is 8 to 16 }
       if (length(trim(DSTranBuf.Dr_license)) < 8)                       or
          (DSTranBuf.Dr_License = StringOfchar('0', length(DSTranBuf.Dr_License))) then
       begin
         if (length(trim(DSTranBuf.Dr_License)) < 8) then
           sm(format('****NOTICE: Offline not allowed for db: Freq Shopper# length is less than 8 >%s<',[DSTranBuf.Dr_License]))
         else
           sm(format('****NOTICE: Offline not allowed for db: Freq Shopper# is all zeros >%s<',[DSTranBuf.Dr_License]));
         DSTranBuf.OffLineAuthCodeN := TrxDecOffInvalidN;
       end;
    end
    else
      DSTranBuf.OfflineAuthCodeN := TrxDecOffInvalidN;
  end;
end;

function VerifyDebitPfxChecked(var DSTranBuf: MdMsgRec; OnOffCode: Byte) : Boolean;
begin
  result := True;
  if assigned(HostBuf) and (DSTranBuf.VoidCode <> 'V') then
  begin
    if ((OnOffCode = OnCode)  and (HostBuf.VerifyDebitPrefixOnline = 'Y')  and (not crToDbFlag)) or   // not a conversion
       ((OnOffCode = OffCode) and (HostBuf.VerifyDebitPrefixOffline = 'Y') and (not crToDbFlag)) or   // JMR-B
       crToDbCheckPrefix                                                                         then // JMR-B
    begin
      crToDbCheckPrefix := false;             { JMR-B }
      if (DSTranBuf.ReqCodeN in Db_Set + ConnectPay_Set) then
      begin
        if not PfxInBinFileResult then
        begin
          if (DSTranBuf.Entry_Track2 = 'M') then
          begin
            DSTranBuf.MTXRspCode  := TrxDecBadPfxManual;
            DSTranBuf.MTXRspCodeN := TrxDecBadPfxManualN;
          end
          else
          begin
            DSTranBuf.MTXRspCode  := TrxDecBadPrefix;
            DSTranBuf.MTXRspCodeN := TrxDecBadPrefixN;
          end;
          result := False;
        end;
      end;
    end;
  end;
end;  {VerifyDebitPfxChecked }

function FCTOfflineAllowed(DSTranBuf: MdMsgRec): boolean; //CPCLIENTS-7091: refactored to be more readable by avoiding double negative

  {$IFDEF MTXEPSDLL}
  function IsBarTabTransAllowed: boolean;    // CPCLIENTS-1932
  begin
    result := true;
    if Assigned(FCTBuf) then
    begin
      if FCTBuf.ProcessingFlags.AllowBarTab then
      begin
        if MR.IsHoldTransaction then
          result := FCTBuf.LocalAuthFlags.HoldTab.Offline = 'Y'
        else if MR.IsReleaseTransaction then
          result := FCTBuf.LocalAuthFlags.ReleaseTab.Offline = 'Y'
      end
      else
        result := false;
    end
    else
      SM('FCTOfflineAllowed.IsBarTabTransAllowed - FCTBuf not assigned');
  end;
  {$ENDIF}

begin
  with DSTranBuf do
    if ReqCodeN in AuthComp_Set then
    begin
      if (ReqCodeN in eWic_Set) then
        result := FCTBuf.LocalAuthFlags.Purchase.Offline = 'Y'
    end
    else if ReqCodeN in preAuth_Set then
    begin
      result := FCTBuf.LocalAuthFlags.PreAuth.Offline = 'Y';
      {$IFDEF MTXEPSDLL}
      if (MR.IsHoldTransaction or MR.IsReleaseTransaction) then  // we can ignore offline preauth setting for bar tabs
        result := IsBarTabTransAllowed
      {$ENDIF}
    end
    else if (ReqCodeN in All_Purchase_Trxs) and not (ReqCodeN in AuthComp_Set) then   // CPCLIENTS-1932
    begin
      result := FCTBuf.LocalAuthFlags.Purchase.Offline = 'Y';
      {$IFDEF MTXEPSDLL}
      if MR.IsHoldTransaction or MR.IsReleaseTransaction then               // CPCLIENTS-1932
        result := IsBarTabTransAllowed                                      // CPCLIENTS-1932
      {$ENDIF}
    end
    else if (ReqCodeN in (All_Return_Trxs + [PropCrRechargeN, User_1_RechargeN])) Then
      result := FCTBuf.LocalAuthFlags.Return.Offline = 'Y'
    else if (ReqCodeN in All_Voice_Trxs) Then
      result := FCTBuf.LocalAuthFlags.Force.Offline = 'Y'
    else
      result := false;
end;  // FCTOfflineAllowed

function IsBankIdInLocalNetworkIdList(aList: TStrings): Boolean; // DEV-12593
var i: integer;
begin
  result := false;
  i := 0;
  while not result and (i < aList.Count) do
  begin
    result := DebitNetworkIDTable.IndexOf(aList[i]) >= 0;
    inc(i);
  end;
end;

function ThisHostVerifyDebitPrefixOffline: boolean;
begin
  result := assigned(HostBuf) and SameText(HostBuf.VerifyDebitPrefixOffline,'Y');
end;

function SetOfflineAuthCodeForDebit(DSTranBuf: MdMsgRec): integer;  // TSL warns OK
var Networks: TStrings;
begin
  if (DSTranBuf.OfflineAuthCodeN = TrxAppOffN) then
  begin
    result := DSTranBuf.OfflineAuthCodeN;
    Networks := TStringList.Create;
    try
      if not IsPfxInBinFile(btDebit, SetSearchPfx(DSTranBuf){Copy(MTXEncryptionUtils.RetrieveMdMsgPersonalAccountNum(DSTranBuf), 1, 10)}, DSTranBuf.PanLen, Networks) then // DOEP-48196
        result := TrxDecBadPrefixN
      else
      if ThisHostVerifyDebitPrefixOffline and (DebitNetworkIDTable.Count > 0) then // don't check if no ID's
      begin
        if not IsBankIdInLocalNetworkIdList(Networks)
          then result := TrxDecDbNoOfflineN;
      end;
    finally
      Networks.Free;
    end;
  end
  else
    result := DSTranBuf.OfflineAuthCodeN;
  SM('fct.SetOfflineAuthCodeForDebit - result=' + result.ToString());
end;

function SetOfflineAuthCodeForCredit(DSTranBuf: MdMsgRec): integer;
var Networks: TStrings;
begin
  if (DSTranBuf.OfflineAuthCodeN = TrxAppOffN) then
  begin
    result := DSTranBuf.OfflineAuthCodeN;
    Networks := TStringList.Create;   //CPCLIENTS-11616
    try
      if IsPfxInBinFile(btPrepaid, SetSearchPfx(DSTranBuf){Copy(MTXEncryptionUtils.RetrieveMdMsgPersonalAccountNum(DSTranBuf), 1, 10)}, DSTranBuf.PanLen, Networks) then // DOEP-48196
        result := TrxDecPrePaidNoOfflineN;   // if the prefix exists, i.e. it IS a prePaid credit, then don't stand in
    finally
      Networks.Free;
    end;
  end
  else
    result := DSTranBuf.OfflineAuthCodeN;
  SM('fct.SetOfflineAuthCodeForCredit - result=' + result.ToString());
end;

{ JMR-E : this was taken from CkTransaction.  It is used twice on CrToDb transactions that time out}
procedure CkABunchOfStuff(var DSTranBuf: MdMsgRec);
var i: integer;
    tmpStr: string;

  function Mod10 : Boolean;
  var
    Mod10_Num: Integer;
    Temp_Acct_No: String[20];
    Temp_Digit: String[1];
    Temp_Two: String[2];
    Double: Boolean;
  begin
    result := true;
    try
      Mod10_Num := 0;
      Double := False;
      if (DSTranBuf.fleetCardType = ftWrightExpress)
        then Temp_Acct_No := Copy(MTXEncryptionUtils.RetrieveMdMsgPersonalAccountNum(DSTranBuf), 7, 13) { WEX fleet-diregard first 6 digits }
        else Temp_Acct_No := MTXEncryptionUtils.RetrieveMdMsgPersonalAccountNum(DSTranBuf);
      while (Temp_Acct_No <> '') do
      begin
        Temp_Digit := Copy(Temp_Acct_No, Length(Temp_Acct_No), 1);
        if Double Then
        begin
          Temp_Two   := TwoDigits(2 * MyVal(Temp_Digit));
          Temp_Digit := Copy(Temp_Two, 1, 1);
          Mod10_Num  := Mod10_Num + MyVal(Temp_Digit);
          Temp_Digit := Copy(Temp_Two, 2, 1);
          Mod10_Num  := Mod10_Num + MyVal(Temp_Digit);
          Double := False;
        end
        else
        begin
          Mod10_Num  := Mod10_Num + MyVal(Temp_Digit);
          Double := True;
        end;
        Delete(Temp_Acct_No, Length(Temp_Acct_No), 1);
      end;

      // clean hints - result is initialized with true
      if (Mod10_Num mod 10) <> 0 then
        result := False;

     Temp_Acct_No := StringOfChar(' ', 20);
    except
      on e: exception do
      begin
        sm('Try..Except: Mod10 ' + e.message);
        result := false;
      end;
    end;
  end; { Mod10     }

  function IsAcctNoBlankOrTruncated: boolean;
  var tmpAcctNo: string;
  begin
    tmpAcctNo := MTXEncryptionUtils.RetrieveMdMsgPersonalAccountNum(DSTranBuf);
    result := (Pos(MASKCHAR, tmpAcctNo) > 0) or (tmpAcctNo = '');
    tmpAcctNo := stringOfChar(' ', 22);  // clear for PCI?
  end;

  function mod10CheckOK: boolean;
  begin
    result := true;
    {$IFDEF P2P} // DllTypes.P2PEncryption is OpenEPS specific
    SM('Mod10CheckOK NOT performed for all terminals except Ingenico because we are a P2P module!');
    Exit;
    {$ENDIF}
    {$IFDEF MTXEPSDLL}
    if DllTypes.IsSessionKeyAESEncryption then // DOEP-58895
    begin
      SM('Mod10CheckOK NOT performed because we are using enhanced MX encryption');
      Exit;
    end else
    if DllTypes.IsVSPEnabled then // TFS-19642
    begin
      SM('Mod10CheckOK NOT performed because we are using TAVE/VSP encryption');
      Exit;
    end
    else if DllTypes.IsTokenDataFromPOS then // TFS-20539
    begin
      SM('Mod10CheckOK NOT performed because we are using token data from POS');
      Exit;
    end;
    {$ENDIF}

    if (not IsAcctNoBlankOrTruncated) and assigned(FCTBuf) and (FCTBuf.ProcessingFlags.Mod10Check = 'Y') then
      result := Mod10;

    if not result then
    begin
      DSTranBuf.MtxRspCode  := TrxDecBadM10;
      DSTranBuf.MtxRspCodeN := TrxDecBadM10N;
    end;
  end;   { mod10CheckOK }

  function SetBadExpDateRspCode: byte;
  begin
    if (DSTranBuf.Entry_Track2 = 'M') then
      result := TrxDecExpDateManualN
    else
      result := TrxDecExpDateN;
  end;

  function expDateOK: boolean;
  begin
    msgDebug('ExpDateOK - ExpDate=' + DSTranBuf.ExpDate);
    if trim(DSTranBuf.ExpDate) = 'TEST' then             //////////////////////////// MCD hackaround
      DSTranBuf.ExpDate := '';
    SM(format('ExpDateOK: SendOnlineOnly[%s] Required[%s] ReqCodeN[%d] ReqType[%s] Entry[%s]',
      [FCTBuf.ProcessingFlags.ExpirationDate.SendOnlineOnly,FCTBuf.ProcessingFlags.ExpirationDate.Required,DSTranbuf.ReqCodeN,DSTranbuf.ReqType, DSTranbuf.Entry]));
    if (trim(DSTranBuf.ExpDate) = '') or (assigned(HostBuf) and (sameText(HostBuf.Suffix, 'TDB')))
      {$IFDEF MTXEPSDLL} or (DllTypes.IsVSPEnabled and (DSTranBuf.Entry = 'M')) or //DOEP-58719
      DllTypes.IsTokenDataFromPOS {$ENDIF} then  // TFS-20539
    begin
      result := true;
      SM('Setting ExpDateOK to true');
    end
    else
    begin
      result := IsExpDateOK(trim(DSTranBuf.ExpDate));
      if (FCTBuf.ProcessingFlags.ExpirationDate.SendOnlineOnly = 'N') and (FCTBuf.ProcessingFlags.ExpirationDate.Required = 'Y') and  //55069
         (DSTranBuf.ReqType[1] in [NormalTrx]) then       //55069
        begin
        SM('ExpDate NOT validated because this is an ONLINE transaction and VerifyExpDateOnline is NOT checked');
        result := true;
        end
      else if (FCTBuf.ProcessingFlags.ExpirationDate.SendOnlineOnly = 'Y') or          
         ((FCTBuf.ProcessingFlags.ExpirationDate.Required = 'Y') and not (DSTranbuf.ReqCodeN in User_1_Set)) then  { or required }
      begin
        //msgDebug('55069: ExpDateOK - in 2nd conditional');
        if not result Then
        begin
          SM('Setting ExpDateOK to FALSE');
          DSTranBuf.MTXRspCodeN := SetBadExpDateRspCode;
          DSTranBuf.MTXRspCode := intToStr(DSTranBuf.MTXRspCodeN);
          DSTranBuf.OffLineAuthCodeN := SetBadExpDateRspCode;
        end;
      end
      else
        result := true;  { don't check it, not req'd , so make it true regardless }
    end;
    SM('ExpDateOK: Result=' + BoolStr(Result));
  end;   { expDateOK }

  function manualEntryOK: boolean;
  begin
    if DllTypes.IsTokenDataFromPOS or // CPCLIENTS-1461: Ignore CPP that disables manual entry per card type
       ( (DSTranBuf.ReqCodeN in db_set + ConnectPay_Set) and    { let db voids go so we can check }
         (DSTranBuf.voidCode = 'V') ) then   { for VoidTransactionDataOK later }
      result := true
    else
    if ((DSTranBuf.Entry = CEV_MANUAL_CUST) or (DSTranBuf.Entry = CEV_MANUAL_POS)) and  // TFS-163698
       (FCTBuf.ProcessingFlags.ManualEntryAllowed = 'N') and   { if Card profile doesn't allow it }
       (not DSTranBuf.BarCodeScan) then // DOEP-50079, 50254 - if barcode was not scanned
    begin
      DSTranBuf.MtxRspCode  := TrxDecNoManEnt;
      DSTranBuf.MtxRspCodeN := TrxDecNoManEntN;
      result := false;
    end
    else
      result := true;
    MsgDebug(Format('fct.ManualEntryOK: result >%s< DllTypes.IsTokenDataFromPOS >%s< DllTypes.IsOnlineOnlyToken >%s<', [YN(result), YN(DllTypes.IsTokenDataFromPOS), YN(DllTypes.IsOnlineOnlyToken)]))
  end;   { manualEntryOK }

{ // XE: Remove WinEPS
  function SetNeedTrack2andFalseResult: boolean;
  begin
    DSTranBuf.MtxRspCode  := TrxDecNeedPINAndTrack2;
    DSTranBuf.MtxRspCodeN := TrxDecNeedPINAndTrack2N;
    result := false;
  end;

  function IsHost(aSuffixCode: string): boolean;
  begin
    result := (DSTranBuf.HostSuffixCode = aSuffixCode);
  end;

  function IsTrack2Blank: boolean;
  begin
    result := (DSTranBuf.Track2Data = '');
  end;
}

  function VoidTransactionDataOK: boolean;
  begin
    {$IFNDEF MTXEPSDLL}        // EPSDLL doesn't have acctno or track unless it is a void last, so let it through
(* XE: Remove WinEPS
    if (DSTranBuf.voidCode = 'V') then
    begin
      if (DSTranBuf.ReqCodeN in db_set) and (not IsHost('BYL')) and (not IsHost('ACI')) and (not IsHost('MON')) then
      begin
        if (IsTrack2Blank and IsAcctNoBlankOrTruncated) or (trim(DSTranBuf.PIN) = '')  { void by seqNo needs both of these }
          then result := SetNeedTrack2andFalseResult
          else result := true;
      end
      else
      if (DSTranBuf.ReqCodeN in Check_Set) then
      begin
          if (trim(DSTranBuf.Check) = '') and IsAcctNoBlankOrTruncated and IsTrack2Blank and (trim(DSTranBuf.Dr_License) = '')
          then result := SetNeedTrack2andFalseResult
          else result := true;
      end
      else
      if IsTrack2Blank and IsAcctNoBlankOrTruncated then
      begin
        if IsHost('ACI') and (DSTranBuf.ReqCodeN in (Db_Set + Cr_Set))
          then result := true
          else result := SetNeedTrack2andFalseResult;
      end
      else
        result := true;
    end
    else
*)    
    {$ENDIF MTXEPSDLL}
      result := true;               { not a void so it is OK }
  end;   { VoidTransactionDataOK }

  function IsGiftCardPurchByProgramIDOK: boolean;
  begin
    // CPCLIENTS-9469, to sync up with 828.7 CPCLIENTS-9607
    if (not(SameText(DSTranBuf.ProgramID, BHN)) and
       ((mtx_utils.ABlackhawkGiftActivation(DSTranBuf) and (DSTranBuf.VoidCode = 'V')) or
       (mtx_utils.IsCardBlackhawk(DSTranBuf.ProgramID) and (DSTranBuf.ReqCodeN <> User_1_ActivationN)))) or
       (SameText('OTC',DSTranBuf.ProgramID) and (DSTranBuf.ReqCodeN in [User_1_PurchN,User_1_ReturnN,User_1_BalInqN] = false)) then
    begin
      DSTranBuf.MtxRspCode  := TrxDecTrxNotAlwd;
      DSTranBuf.MtxRspCodeN := TrxDecTrxNotAlwdN;
      result := false;
    end
    else
      result := true;
  end;

  procedure SetOfflineAuthCode;
  begin
    if (DSTranBuf.MTXRspCodeN = 0) then
      DSTranBuf.OffLineAuthCodeN := TrxAppOffN { allow offline for this card/trx type? - init to OK }
    else
      DSTranBuf.OfflineAuthCodeN := DSTranBuf.MTXRspCodeN;  { if declined already, set offlineCode, too }
  end;

  procedure SetEBTExpDate;
  begin
    if (DSTranBuf.ReqCodeN in EBT_Set) then       { set expDate if needed for EBT }
    begin
      if (DSTranBuf.ExpDate = '')               or
         (strToIntDef(DSTranBuf.ExpDate,0) = 0) then
        DSTranBuf.ExpDate := '4912';
    end;
  end;

  function isValidAlphaNumeric (const aValue: string):boolean;
  const
  CHARS = ['0'..'9', 'a'..'z', 'A'..'Z'];
  var
  i: Integer;
  begin
    Result := true;
    for i := 1 to Length(aValue) do
    begin
      if not (aValue[i] in CHARS) then
        result := false;
    end;
  end;

  function IsValidKSN(aKSN: string): boolean;   // SAK CPCLIENTS-1616
  begin
    result:= true;
    SM('Method IsValidKSN. KSN length: ' + intToStr(length(aKSN)) + ' KSN value: ' + aKSN);
    result:= true;
    if (trim (aKSN) = '') or (not isValidAlphaNumeric(trim (aKSN))) or (length(aKSN) < 15) or (length(aKSN) > 25) then
    begin
      SM('Invalid KSN. KSN Length: ' + intToStr(length(aKSN))  + ' KSN value: ' + aKSN);
      result := false;
      DSTranBuf.MtxRspCode  := TrxDecTrxNotAlwd;
      DSTranBuf.MtxRspCodeN := TrxDecTrxNotAlwdN;
    end;
  end;

  function IsCVV2OK: boolean;
  begin
    result := true;
    if (DSTranBuf.CVV2 <> '') then
    begin
      MsgDebug('(DSTranBuf.CVV2=' + DSTranBuf.CVV2);                                                                // TFS-11034 below
      result := {$IFNDEF P2P} (StrToIntDef(DSTranBuf.CVV2, -1) <> -1) and {$ENDIF} ((length(DSTranBuf.CVV2) < 5) or (length(DSTranBuf.CVV2) >= 8));
      if not result then
      begin
        DSTranBuf.MtxRspCode  := TrxDecCVV2Bad;
        DSTranBuf.MtxRspCodeN := TrxDecCVV2BadN;
      end;
    end;
  end;

  function ValidateCashierID: boolean;
  begin
    result := true;
    if DSTranBuf.ValidateChecker then
    begin
      if not MTX_XMLClasses.ValidateCashier(DsTranBuf.cashier) then // XE: Remove WinEPS - was Cashstuf.ValidateCasher
      begin
        DSTranBuf.TermRspCode := 'IC';
        DSTranBuf.MTXRspCode := CashierInvalid;
        DSTranBuf.MTXRspCodeN := CashierInvalidN;
        result := false;
      end;
    end;
  end;
  
  // DOEP-34246
  function IsPaymentOnAccountOK: boolean;
  begin
    result := true;
    if (DSTranBuf.ReqCodeN = CrPaymentOnAcctN) and   { if it is a payment on account request }
       (FCTBuf.ProcessingFlags.PaymentOnAccount = 'N') then  { if card profile doesn't allow it }
    begin
      DSTranBuf.MtxRspCode  := TrxDecTrxNotAlwd;
      DSTranBuf.MtxRspCodeN := TrxDecTrxNotAlwdN;
      result := false;
    end;
  end;   { IsPaymentOnAccountOK }


begin { CkABunchOfStuff }
  if fileExists(NORSPFILE) then  { for QA testing }
  begin
    tmpStr := Reg_Lookup(NORSPFILE,'IgnoreApprovedForLower',false);
    if (tmpStr = 'Y') then
      DSTranBuf.HostApproveLowerAmount := 'S';
  end;
  OnTransOK := True;
  SetEBTExpDate;
  if (DSTranBuf.ReqCodeN in BalInq_Set)
    then DSTranBuf.OffLineAuthCodeN := TrxDecOffInvalidN
    else SetOfflineAuthCode;
{$IFDEF MTXEPSDLL}
  if (P2PEncryption or DllTypes.IsVSPEnabled or DllTypes.IsSessionKeyAESEncryption) and (DSTranBuf.ReqCodeN in AuthComp_Set) then // DOEP-59378
  begin
    SM('CkABunchOfStuff - P2P/VSP/MX-SV Pre-auth completion skips validations');
    Exit;
  end;
  if Assigned(TransactionProcessor) and TransactionProcessor.TranIsAVoidBySeqNumOrPostNum then // DOEP-57933
  begin
    SM('CkABunchOfStuff - Void transaction skips validations');
    Exit;
  end;
{$ENDIF}
  i := 1;
  while OnTransOK and (i < 10) do  { so far, 9 things to check, in this order... }
  begin
    case i of
      1: OnTransOK := VoidTransactionDataOK;
      2: if not (DSTranBuf.ReqCodeN in BalInq_Set) then OnTransOK := mod10CheckOK;
      3: if not (DSTranBuf.ReqCodeN in BalInq_Set) then OnTransOK := expDateOK;
      4: if not (DSTranBuf.ReqCodeN in BalInq_Set) then OnTransOK := manualEntryOK;
      5: if not (DSTranBuf.ReqCodeN in BalInq_Set) then OnTransOK := VerifyDebitPfxChecked(DSTranBuf, OnCode);
      6: OnTransOK := IsGiftCardPurchByProgramIDOK;
      7: OnTransOK := IsCVV2OK;
      8: if not (DSTranBuf.ReqCodeN in BalInq_Set) then OnTransOK := ValidateCashierID;
      {$IFDEF MTXEPSDLL}
      9: if (MR.ServerEPSKeyType = ktPP) or (MR.ServerEPSKeyType = ktXP) then OnTransOK := IsValidKSN (MR.KSNTrack2OrPAN);   // SAK CPCLIENTS-1616If Encryption type Ig3 or lg4 then check if the KSN is valid
      {$ENDIF}
    end;
    inc(i);
  end;

  OnTransOK := OnTransOK and IsPaymentOnAccountOK;   // DOEP-34246
  SetOfflineAuthCodeByReqCode(DSTranBuf);
end; { CkABunchOfStuff }

procedure SetOfflineAuthCodeByReqCode(var DSTranBuf: MdMsgRec); //

  function IsDiscoverCashbackOnly: boolean;
  begin
    if (DSTranBuf.AcctNoFirst6 <> '')
      then result := (DSTranBuf.ReqCodeN in Cr_Set) and mtx_Utils.IsDiscover(DSTranBuf.AcctNoFirst6) and
                (DSTranBuf.TrxAmtN = DSTranBuf.CashBackN) and (DSTranBuf.TrxAmtN <> 0)
      else result := false;
  end;

  procedure setOnlineCashbackDeclineCode;
  begin
    if (FCTBuf.ProcessingFlags.Cashback.Allowed = 'Y') then                     
    begin
      if IsDiscoverCashbackOnly then
      begin
        DSTranBuf.MtxRspCode  := TrxDecTrxNotAlwd;
        DSTranBuf.MtxRspCodeN := TrxDecTrxNotAlwdN;
      end
      else
      begin
        DSTranBuf.MtxRspCode  := TrxDecCashB;
        DSTranBuf.MtxRspCodeN := TrxDecCashBN;
      end;
    end
    else
    begin
      DSTranBuf.MtxRspCode  := TrxDecCashBNAllow;
      DSTranBuf.MtxRspCodeN := TrxDecCashBNAllowN;
    end;
  end;    { setOnlineCashbackDeclineCode }

  function onlineCashbackOK: boolean;
  begin
    if IsDiscoverCashbackOnly then
      result := false
    else
    if host1Up then
    begin
        if (MyVal(DSTranBuf.CashBack) = 0) or (DSTranBuf.ManagerID <> '') then
          result := true
        else
        if (FCTBuf.ProcessingFlags.Cashback.Allowed = 'Y') then  { is cashback allowed? } 
        begin

           if (MyVal(DSTranBuf.CashBack) <= GetMaxOnlineCashback) or
          {$IFDEF MTXEPSDLL}
             ((not EMVMgr.IsCardEntryChipCard) and (FCTBuf.OnlineLimits.MaxCashback = StrToInt(StringOfChar('9', DSFCTMaxCashOnL)))) then
          {$ELSE}
             (FCTBuf.OnlineLimits.MaxCashback = StrToInt(StringOfChar('9', DSFCTMaxCashOnL))) then
          {$ENDIF}
          begin
            if (MyVal(DSTranBuf.CashBack) >= FCTBuf.OnlineLimits.MinCashback)  
              then result := true
              else result := false;
          end
          else
            result := false;
        end
        else
          result := false;
    end
    else   { don't check online limit if host is offline - use offline limit }
      result := true;
  end;   { onlineCashbackOK }

  function offlineCashbackOK: boolean;
  begin
    if (MyVal(DSTranBuf.CashBack) = 0) or (DSTranBuf.ManagerID <> '') then         { manager overriide is  OK }
      result := true
    else
    if (FCTBuf.ProcessingFlags.Cashback.Allowed = 'Y') then  { is cash back allowed? }
    begin
      if  (MyVal(DSTranBuf.CashBack) <= FCTBuf.OfflineLimits.MaxCashback) or
          (FCTBuf.OfflineLimits.MaxCashback = StrToInt(StringOfChar('9', DSFCTMaxCashOffL))) { all 9's = OK } 
        then result := true
        else result := false;
    end
    else                                  { cashback is not allowed }
      result := false;
  end;   { offlineCashbackOK }

  procedure setOnlineAmtDeclineCode;
  begin
    with DSTranBuf do
    //with DSFCTBuf do
    begin
      //if (MyVal(TrxAmt) < MyVal(DSFCTMinTotalAmt_)) then    { JMR-R }
      if (MyVal(TrxAmt) < FCTBuf.OnlineLimits.MinTotalAmount) then { JMR-R }
      begin
        MtxRspCode  := TrxUnderMin;
        MtxRspCodeN := TrxUnderMinN;
      end
      else
      begin
        MtxRspCode  := TrxDecAmt;
        MtxRspCodeN := TrxDecAmtN;
      end;
    end;
  end;    { setOnlineAmtDeclineCode }

  function onlineAmtOK: boolean;
  begin
    if host1Up then
    begin
      with DSTranBuf do
      begin
        result := false;
        if (MyVal(TrxAmt) <= FCTBuf.OnlineLimits.MaxTotalAmount) or      
           (FCTBuf.OnlineLimits.MaxTotalAmount = StrToInt(StringOfChar('9', DSFCTMaxOnAmtL))) or { TSL-E }
           (ManagerID <> '')                                    or
           (ReqCodeN in [CrPurchN,DbPurchN,CheckPurchN,PropDbPurchN,PropCrPurchN,
                         EBT_FS_PurchN,EBT_Cash_PurchN,User_1_PurchN,FleetPurchN,
                         User_2_PurchN, ConnectPayPurchN] = false)                Then
          result := true;
        if (MyVal(TrxAmt) < FCTBuf.OnlineLimits.MinTotalAmount) and { JMR-R } 
           (ManagerID = '')                           then
          result := false;
      end;
    end
    else             { don't check online if we are offline to host }
      result := true;
  end;   { onlineAmtOK }


  function GetMaxTotalAmountByEntryType: Integer; // TFS-14557
  begin
    if (DSTranBuf.Entry = CEV_SWIPED_CUST) or (DSTranBuf.Entry = CEV_SWIPED_POS) then
      result := FCTBuf.OfflineLimits.MaxTotalAmountByEntryType.SwipeAmount
    else
    if DSTranBuf.Entry = CEV_FALLBACK then
      result := FCTBuf.OfflineLimits.MaxTotalAmountByEntryType.FallbackToSwipeAmount
    else
    if (DSTranBuf.Entry = CEV_MANUAL_CUST) or (DSTranBuf.Entry = CEV_MANUAL_POS) then
      result := FCTBuf.OfflineLimits.MaxTotalAmountByEntryType.ManualAmount
    else
    if DSTranBuf.Entry = CEV_CHIPCARD then
      result := FCTBuf.OfflineLimits.MaxTotalAmountByEntryType.ChipAmount
    else
    if DSTranBuf.Entry = CEV_BARCODE then
      result := FCTBuf.OfflineLimits.MaxTotalAmountByEntryType.BarcodeAmount
    else
    if DSTranBuf.Entry = CEV_RFID then
      result := FCTBuf.OfflineLimits.MaxTotalAmountByEntryType.RFIDAmount
    else
    if DSTranBuf.Entry = CEV_CONTACTLESS then
      result := FCTBuf.OfflineLimits.MaxTotalAmountByEntryType.EMVContactlessAmount
    else
      result := FCTBuf.OfflineLimits.MaxTotalAmount;
    MsgDebug(Format('GetMaxTotalAmountByEntryType - Entry=%s MaxTotalAmount=%d', [DSTranBuf.Entry, result]));
  end;

  function GetMaxVoiceAuthLimitByEntryType: Integer; // TFS-15312
  begin
    if (DSTranBuf.Entry = CEV_SWIPED_CUST) or (DSTranBuf.Entry = CEV_SWIPED_POS) then
      result := FCTBuf.OfflineLimits.MaxVoiceAuthLimitByEntryType.SwipeAmount
    else
    if DSTranBuf.Entry = CEV_FALLBACK then
      result := FCTBuf.OfflineLimits.MaxVoiceAuthLimitByEntryType.FallbackToSwipeAmount
    else
    if (DSTranBuf.Entry = CEV_MANUAL_CUST) or (DSTranBuf.Entry = CEV_MANUAL_POS) then
      result := FCTBuf.OfflineLimits.MaxVoiceAuthLimitByEntryType.ManualAmount
    else
    if DSTranBuf.Entry = CEV_CHIPCARD then
      result := FCTBuf.OfflineLimits.MaxVoiceAuthLimitByEntryType.ChipAmount
    else
    if DSTranBuf.Entry = CEV_BARCODE then
      result := FCTBuf.OfflineLimits.MaxVoiceAuthLimitByEntryType.BarcodeAmount
    else
    if DSTranBuf.Entry = CEV_RFID then
      result := FCTBuf.OfflineLimits.MaxVoiceAuthLimitByEntryType.RFIDAmount
    else
    if DSTranBuf.Entry = CEV_CONTACTLESS then
      result := FCTBuf.OfflineLimits.MaxVoiceAuthLimitByEntryType.EMVContactlessAmount
    else
      result := 0;
    MsgDebug(Format('MaxVoiceAuthLimitByEntryType - Entry=%s MaxVoiceAuthLimit=%d', [DSTranBuf.Entry, result]));
  end;

  // TFS-15312 - Support voice auth offline limits by entry method
  type
    TVoiceAuthLimitByEntryTypeStatus = (tOk, tInRange, tAbove);

  function GetVoiceAuthLimitByEntryTypeStatus: TVoiceAuthLimitByEntryTypeStatus;
  const
    VoiceAuthLimitByEntryTypeStatusArr: array [0..2] of string = ('tOk', 'tInRange', 'tAbove');
  var
    trxAmt, maxTotalAmount, maxVoiceAuthLimit: Integer;
  begin
    result := tOk;
    trxAmt := 0;
    maxTotalAmount := 0;
    maxVoiceAuthLimit := 0;
    if FCTBuf.OfflineLimits.UseEntryType and
      (DSTranBuf.ReqCodeN in [CrPurchN,DbPurchN,CheckPurchN,PropDbPurchN,PropCrPurchN,
       EBT_FS_PurchN,EBT_Cash_PurchN,User_1_PurchN,FleetPurchN,
       User_2_PurchN, ConnectPayPurchN]) then
    begin
      maxVoiceAuthLimit := GetMaxVoiceAuthLimitByEntryType;
      if maxVoiceAuthLimit > 0 then
      begin
        trxAmt := MyVal(DSTranBuf.TrxAmt);
        if trxAmt > maxVoiceAuthLimit then
           result := tAbove
        else
        begin
          maxTotalAmount := GetMaxTotalAmountByEntryType;
          if (trxAmt <= maxVoiceAuthLimit) and ((maxTotalAmount = 0) or
            ((maxTotalAmount > 0) and (trxAmt > maxTotalAmount))) then // TFS-15855
            result := tInRange;
        end;
      end;
    end;
    MsgDebug(Format('GetVoiceAuthLimitStatusByEntryType - trxAmt=%d maxVoiceAuthLimit=%d maxTotalAmount=%d result=%s',
      [trxAmt, maxVoiceAuthLimit, maxTotalAmount, VoiceAuthLimitByEntryTypeStatusArr[Ord(result)]]));
  end;

  function offlineAmtOK: boolean;
  var
    maxTotalAmount: Integer;
  begin
    if FCTBuf.OfflineLimits.UseEntryType then  // TFS-14557
      maxTotalAmount := GetMaxTotalAmountByEntryType
    else
      maxTotalAmount := FCTBuf.OfflineLimits.MaxTotalAmount;

    with DSTranBuf do
      result := (MyVal(TrxAmt) <= maxTotalAmount) or
         (maxTotalAmount = StrToInt(StringOfChar('9', DSFCTMaxOffAmtL))) or
         (ManagerID <> '')  { JGS-I }                           or
         (ReqCodeN in All_Voice_Trxs)                           or
         ((ReqCodeN in PreAuth_Set) and not ChkOfflineLimitsPreAuth) or  // TFS-120841
         (ReqCodeN in AuthComp_Set)                             or
         (ReqCodeN in (All_Return_Trxs + [PropCrRechargeN,User_1_RechargeN]));
  end;   { offlineAmtOK }

  function AValidVoiceAuth: boolean;
  begin
    result := (DSTranBuf.ReqCodeN in All_Voice_Trxs - [User_2_VoiceN]);
    if result then
    begin
      if (DSTranBuf.ReqCodeN = PropDbVoiceAuthN) and assigned(HostBuf) and (HostBuf.Suffix = 'CON') then
        result := false;      // not a voice auth for CON, a PIN reset no authcode required
    end;
  end;

  function IsACIGiftIncDeactivate: boolean;
  begin
    with DSTranBuf do
      result := SameText(ProgramID, 'INC') and (ReqCodeN = User_1_DeactivateN) and SameText(HostSuffixCode, 'ACI');  //69739
  end;

var
  voiceAuthLimitByEntryTypeStatus: TVoiceAuthLimitByEntryTypeStatus;
begin
  if NOT Assigned(FCTBuf) then
  begin
    SM('SetOfflineAuthCode - FCTBuf not assigned');
    Exit;
  end;
  if not (DSTranBuf.ReqCodeN in BalInq_Set) then
  begin
    if OnTransOK {$IFDEF MTXEPSDLL} or TransactionProcessor.TranIsAVoidBySeqNumOrPostNum {$ENDIF} then // 6855
    begin
      Case DSTranBuf.VoidCode[1] of
      ' ' :
            if AValidVoiceAuth and (DSTranBuf.AuthCode = '') and (not DSTranBuf.VoucherClearProcess) then
            begin
              DSTranBuf.MtxRspCode  := TrxDecNoAuthNo;
              DSTranBuf.MtxRspCodeN := TrxDecNoAuthNoN;
              OnTransOK := False;
            end
            else                                   

            Case DSTranBuf.ReqCodeN of

            DbReturnN,
            ConnectPayReturnN,
            PropDbReturnN,
            User_1_ReturnN,
            User_1_RechargeN : if (FCTBuf.LocalAuthFlags.Return.Offline = 'N') then
                                 DSTranBuf.OfflineAuthCodeN := TrxDecOffInvalidN;

            DbVoiceAuthN,
            PropDbVoiceAuthN,
            CheckVoiceAuthN,
            User_1_VoiceN    : if (FCTBuf.LocalAuthFlags.Force.Offline = 'N') then
                                 DSTranBuf.OfflineAuthCodeN := TrxDecOffInvalidN;

            User_2_ReturnN,
            EBT_FS_VoiceN,
            EBT_Cash_VoiceN:  { let these go, since they are OK'd on phone } ;

            User_1_ActivationN,    { TSL-I }
            User_1_PreActivationN,
            User_1_ReActivationN,
            User_1_DeactivateN,    { TSL-I }
            User_1_FinalTenderN,
            User_2_PurchN,         { phone card activation }
            User_2_VoiceN,         { phone card deactivation }
            WirelessActivationN,
            WirelessDeactivateN:
              begin
                if IsACIGiftIncDeactivate then          // see KVAT SOW, deactivations not allowed offline
                  DSTranBuf.OfflineAuthCodeN := TrxDecOffInvalidN
                else
                if (FCTBuf.LocalAuthFlags.Activate.Offline = 'N') and
                   NOT ABlackhawkOrIDTGiftActivation(DSTranBuf) and { JMR-S }
                   NOT (DSTranBuf.ReqCodeN in [User_1_ReActivationN, User_1_FinalTenderN]) then 
                  DSTranBuf.OfflineAuthCodeN := TrxDecOffInvalidN;          // always take Blackhawk activation offline
              end;

            CrPurchN,
            MtPurchN,   // CPCLIENTS-19227 - Added this for not to print error msg in the journal
            CrOverRideN,         // TSL-B
            CrReturnN,
            CrVoiceAuthN,
            CrPreAuthN,          // TSL-H
            CrPreAuthCompN,      // TSL-H
            CrPaymentOnAcctN,    // DOEP-33267
            CrReturnWithValidN,  // DOEP-33268
            PropCrPurchN,
            PropCrRechargeN,     // TSL-B
            PropCrVoiceAuthN,
            PropCrReturnN,
            PropCrPreAuthN,
            PropCrPreAuthCompN,
            PropDbPreAuthN,      // CPCLIENTS-12016
            PropDbPreAuthCompN,  // CPCLIENTS-12016
            AchPurchN,
            AchVoiceAuthN,
            AchReturnN,
            DbPurchN,
            ConnectPayPurchN,
            DbOverRideN,         // TSL-B
            DbPreAuthN,          // TSL-H
            DbPreAuthCompN,      // TSL-H
            DbReturnWithValidN,  // DOEP-33268
            //DbKeyExchangeN,      // DOEP-33273
            PropDbPurchN,
            PropDbOverRideN,     // TSL-B
            eWicForceN,
            eWicPreAuthN,
            eWicPreAuthCompN,
            CheckPurchN,
            CheckOverRideN,
            EBT_FS_PurchN,
            EBT_FS_OverRideN,    // TSL-B
            EBT_FS_ReturnN,
            EBT_Cash_PurchN,
            EBT_Cash_OverRideN,  // TSL-B
            EBT_Cash_ReturnN,
            EBT_Cash_PreAuthN,
            EBT_Cash_PreAuthCompN,
            User_1_PurchN,
            User_1_PreAuthN,     // TSL-I
            User_1_PreAuthCompN, // TSL-I
            User_1_OverRideN,    // TSL-B
            User_1_RefundActivationN, // DOEP-33269
            User_1_CashOutN,          // DOEP-33271
            User_1_PreRechargeN,      // DOEP-33272
            User_2_OverRideN,
            FleetPurchN,
            FleetReturnN,
            FleetPreAuthN,
            FleetPreAuthCompN,
            WirelessPreAuthN,          // CPCLIENTS-11904
            BenefitPurchaseN,
            BenefitBalanceInquiryN,
            BenefitReturnN,
            BenefitItemQualificationN  // CPCLIENTS-9586 To support void
                        : begin
                            if not onlineCashbackOK then
                            begin
                              setOnlineCashbackDeclineCode;
                              OnTransOK := false;
                            end
                            else
                            if not onlineAmtOK then
                            begin
                              setOnlineAmtDeclineCode;
                              OnTransOK := false;
                            end
                            else  { online is OK, now check offline stuff }
                            begin
                              DSTranBuf.MtxRspCode  := TrxAppWCap;  { online Tran is OK }
                              DSTranBuf.MtxRspCodeN := TrxAppWCapN;
                              voiceAuthLimitByEntryTypeStatus := GetVoiceAuthLimitByEntryTypeStatus;
                              if (not FCTOfflineAllowed(DSTranBuf)) or (voiceAuthLimitByEntryTypeStatus = tAbove) then // TFS-15312
                                DSTranBuf.OfflineAuthCodeN := TrxDecOffInvalidN
                              else
                              if SameText(FCTBuf.LocalAuthFlags.Purchase.Offline, 'V') or // DEV-28404
                                (voiceAuthLimitByEntryTypeStatus = tInRange) then // TFS-15312
                                DSTranBuf.OfflineAuthCodeN := TrxDecOffVoiceAuthN
                              else
                              if not offlineCashbackOK then
                                DSTranBuf.OffLineAuthCodeN := TrxDecOffCashBN
                              else
                              if not offlineAmtOK then
                                DSTranBuf.OfflineAuthCodeN := TrxDecOffAmtN
                              else
                              if not VerifyDebitPfxChecked(DSTranBuf, OffCode) Then
                                DSTranBuf.OfflineAuthCodeN := TrxDecOffPrefN
                              else
                              begin
                              //  if (DSTranBuf.ReqCodeN = checkOverrideN) and (DSTranBuf.EChkCapable = 'Y') then
                              //    DSTranBuf.OfflineAuthCodeN := TrxAppOffEccN
                              //  else
                                if (DSTranBuf.MTXRspCodeN = TrxAppOffEccN) and (DSTranBuf.ReqType = OfflineReq)   // was it ECC off fwd from lane?
                                  then DSTranBuf.OffLineAuthCodeN := TrxAppOffEccN
                                  else DSTranBuf.OffLineAuthCodeN := TrxAppOffN;
                              end;
                              {$IFNDEF FUEL}                                     // DOEP-52623 -Debit Completions for RBS Lynk not working
                              IsBigYLynkDebitFreqShopperOfflineOK(DSTranBuf);    // DOEP-48196, only for Lynk host, for Big Y
                              {$ENDIF FUEL}
                            end;
                          end; { Purchase Trxs }
            else
              begin
                DSTranBuf.OfflineAuthCodeN := TrxDecOffInvalidN;
                OnTransOK := false;
                SM('****ERROR:  OFFLINE INVALID CKABunchOfStuff: ReqCode >' + DSTranBuf.ReqCode + '<  ' +
                   'not in Case statement');
              end;
            end;

      'V' : Case DSTranBuf.ReqCodeN of
            CrPurchN,
            CrOverRideN,        // TSL-B
            CrPaymentOnAcctN,    // DOEP-33267
            DbPurchN,
            ConnectPayPurchN,
            DbOverRideN,        // TSL-B
            PropCrPurchN,
            AchPurchN,
            PropDbPurchN,
            PropDbOverRideN,    // TSL-B
            CheckPurchN,
            CheckOverRideN,
            EBT_FS_PurchN,
            EBT_FS_OverRideN,   // TSL-B
            EBT_Cash_PurchN,
            EBT_Cash_OverRideN, // TSL-B
            User_1_PurchN,
            User_1_OverRideN,   // TSL-B
            User_2_PurchN,
            User_2_OverRideN,    // TSL-B
            FleetPurchN,
            BenefitPurchaseN:  // CPCLIENTS-10564
            begin
              {$IFDEF MTXEPSDLL}                                                                                                       // CPCLIENTS-1932 begin
              if TranMR.TransModifier.Contains('HT') and  (FCTBuf.LocalAuthFlags.HoldTab.Offline = 'N') then
                DSTranBuf.OfflineAuthCodeN := TrxDecOffInvalidN
              else if TranMR.TransModifier.Contains('RT') and (FCTBuf.LocalAuthFlags.ReleaseTab.Offline = 'N') then
                DSTranBuf.OfflineAuthCodeN := TrxDecOffInvalidN
              else
              {$ENDIF}
              if (FCTBuf.LocalAuthFlags.Purchase.OfflineVoid = 'N') then                                              // CPCLIENTS-1932 End
                DSTranBuf.OfflineAuthCodeN := TrxDecOffInvalidN;
            end;

            User_2_ReturnN,
            EBT_FS_VoiceN,
            EBT_Cash_VoiceN:  { let these go, since they are OK'd on phone } ;

            CrReturnN,
            DbReturnN,
            // DOEP-33268
            CrReturnWithValidN,
            DbReturnWithValidN,
            PropCrReturnWithValidN,
            PropDbReturnWithValidN,
            /////////////
            ConnectPayReturnN,
            PropCrReturnN,
            PropCrRechargeN,
            AchReturnN,
            PropDbReturnN,
            EBT_FS_ReturnN,
            EBT_Cash_ReturnN,
            User_1_ReturnN,
            User_1_RechargeN,      { TSL-I }
            FleetReturnN,
            BenefitReturnN: // CPCLIENTS-10564
              if (FCTBuf.LocalAuthFlags.Return.OfflineVoid = 'N') then
                            DSTranBuf.OfflineAuthCodeN := TrxDecOffInvalidN;

            CrVoiceAuthN,
            DbVoiceAuthN,
            PropCrVoiceAuthN,
            AchVoiceAuthN,
            PropDbVoiceAuthN,
            CheckVoiceAuthN,
            eWicForceN,
            User_1_VoiceN,
            User_2_VoiceN: if (FCTBuf.LocalAuthFlags.Force.OfflineVoid = 'N') then
                             DSTranBuf.OfflineAuthCodeN := TrxDecOffInvalidN;

            User_1_ActivationN,    { TSL-I }
            User_1_PreActivationN,
            User_1_DeactivateN,    { TSL-I }
            User_1_RefundActivationN, // DOEP-33269
            WirelessActivationN,
            WirelessDeactivateN:
            begin
              if IsACIGiftIncDeactivate then          // see KVAT SOW, deactivations not allowed offline
                DSTranBuf.OfflineAuthCodeN := TrxDecOffInvalidN
              else
              if (FCTBuf.LocalAuthFlags.Activate.OfflineVoid = 'N') then { JMR-S } 
                DSTranBuf.OfflineAuthCodeN := TrxDecOffInvalidN;
            end;

            DbPreAuthN,
            CrPreAuthN,
            eWicPreAuthN,
            PropCrPreAuthN,
            User_1_PreAuthN,
            EBT_Cash_PreAuthN,
            FleetPreAuthN: DSTranBuf.OfflineAuthCodeN := TrxAppOffN; // was TrxDecOffInvalidN

            DbPreAuthCompN,
            CrPreAuthCompN,
            eWicPreAuthCompN,
            PropCrPreAuthCompN,
            FleetPreAuthCompN,
            EBT_Cash_PreAuthCompN,
            User_1_PreAuthCompN: if (FCTBuf.LocalAuthFlags.PreAuth.OfflineVoid = 'N') then { TSL-I } 
                                   DSTranBuf.OfflineAuthCodeN := TrxDecOffInvalidN;

            BenefitItemQualificationN: DSTranBuf.OfflineAuthCodeN := TrxAppOffN;  // CPCLIENTS-10564

            else
              begin
                DSTranBuf.OfflineAuthCodeN := TrxDecOffInvalidN;
                OnTransOK := false;
                SM('****ERROR:  OFFLINE INVALID CKABunchOfStuff: ReqCode >' + DSTranBuf.ReqCode + '<  ' +
                   'not in Void Case statement');
              end;
            end;
      end; { of case }
    end;
  end;
  if OnTransOK then
  begin
    case DSTranBuf.ReqCodeN of
      DbPurchN:
          if ThisHostVerifyDebitPrefixOffline then
            DSTranBuf.OfflineAuthCodeN := SetOfflineAuthCodeForDebit(DSTranBuf);

      CrPreAuthN,    // DEV-53477  and DEV-53499
      CrPurchN:
          if not SameText(FCTBuf.ProcessingFlags.StandInForPrePaidCredit, 'Y') then  // if we can't stand in then
            DSTranBuf.OfflineAuthCodeN := SetOfflineAuthCodeForCredit(DSTranBuf);    // see if this is a prePaid
    end;
  end;
end; // SetOfflineAuthCodeByReqCode

procedure SetBackToCredit(var aRec: MdMsgRec);
begin
  aRec.PIN := '';      {If we ever use PINs for credit, comment this out otherwise the receipt will say 'PIN Used' }
  aRec.CrToDbCode := CrToDbToCr;
  if (aRec.ReqCode = DbPurch) then
  begin
    aRec.ReqCode := CrPurch;
    aRec.ReqCodeN := CrPurchN;
  end
  else
  if (aRec.ReqCode = DbReturn) then
  begin
    aRec.ReqCode := CrReturn;
    aRec.ReqCodeN := CrReturnN;
  end
  else
  if (aRec.ReqCode = DbPreAuth) then      //17794
  begin
    aRec.ReqCode := CrPreAuth;
    aRec.ReqCodeN := CrPreAuthN;
  end;
  // CPCLIENTS-5179 To assign Issuer code depends on Tender/Transaction type and
  // to have meaningful name for MakeBYLATLIssuerCode and BYLATLIssuerCode as it is implemented for ATL too
  if IsBYLHostGroup(aRec.HostSuffixCode) then // CPCLIENTS-5179 To assign Issuer code depends on Tender/Transaction type
    aRec.BYLATLIssuerCode := mtx_utils.MakeBYLATLIssuerCode(aRec.ReqCodeN); // TODO see where BYLIssuerCode is used.. maybe do this in BYL host
end;

Procedure ConvertToCredit(var InRec: mdmsgrec);    { JMR-A }
const
  CREDIT = '01';
begin
  try
    { You shouldn't even be here if you're not a CrToDb, but just to be sure... }
    if (InRec.CrToDbCode = CrToDb) and not HostFunctions.pblHostDefined and (InRec.VoidCode <> 'V') then
    begin
      if (MyVal(InRec.CashBack) > 0) then   { JMR-N }
        SM('cashback, must process as debit (timed out)')
      else
      if (MyVal(InRec.TrxAmt) < fct.GetOfflineCrToDbFloor(InRec)) or
         (InRec.OfflineAuthCodeN in [TrxDecOffInvalidN, TrxDecDbNoOfflineN]) then { convert back to credit }{ JMR-G }
      begin
        SM('below CrToDb offline floor, process as credit (timed out)');
        SetBackToCredit(InRec);
        InRec.ReqType := NormalTrx;
        InRec.CardProcID := CREDIT;  //TODO see if this constant already exist   { JMR: means credit }
        { clear these fields, Return_Rsp (called later) will set these }
        InRec.MTXRspCode := '';
        InRec.MTXRspCodeN := 0;
        InRec.TermRspCode := '';
        fct.FindCardPfxAndGetFCTBuf(InRec);
        CkABunchOfStuff(InRec);              { JMR-D }
      end { if (MyVal(InRec.TrxAmt) < fct.GetOfflineCrToDbFloor(InRec)) }
      else
        SM('above CrToDb offline floor, process as debit (timed out)');
    end; { if (CrToDbCode = CrToDb) }
  except
    on e: exception do
      sm('Try..Except fct.ConvertToCredit: ' + e.message);
  end;
end; { ConvertToCredit }

{ JMR-B : This procedure does all the necessary pre-processing of CrToDb trans based on the options selected in the GUI. }
function CkTransaction(var DSTranBuf: MdMsgRec; const OpenEPSConnected: boolean=false) : Boolean;
var PfxInBinFileNetworks: TStrings;
    convertBackToCr : boolean;

  function IsAVoid: boolean;
  begin
    result := (DSTranBuf.VoidCode = 'V');
  end;

  function IsHospitality: boolean;
  begin
    result := (DSTranBuf.EPSLaneType = DSLaneHospitality);// and (DSTranBuf.ReqCodeN = CrPreAuthCompN)
  end;

  function NoTrack2: boolean;
  begin
    result := (Trim(DSTranBuf.Track2Data) = '');
  end;

  function NoCheck: boolean;
  begin
    result := (trim(DSTranBuf.Check) = '');
  end;

  function NoAcct: boolean;
  begin
    try
      //result := (DSTranBuf.PAN10digits = '');
      result := Trim(Copy(MTXEncryptionUtils.RetrieveMdMsgPersonalAccountNum(DSTranBuf), 1, 10)) = ''; // pseudo pan10
    except
      result := true;
    end;
  end;

  function NoDr_License: boolean;
  begin
    result := (trim(DSTranBuf.Dr_License) = '');
  end;

  function CrDbOrEBT: boolean;
  begin
    result := (DSTranBuf.ReqCodeN in Cr_Set) or (DSTranBuf.ReqCodeN in Db_Set + ConnectPay_Set) or
              (DSTranBuf.ReqCodeN in EBT_Set + [User_2_ReturnN]);   // don't forget EBT voucher
  end;

  procedure CrToDbPreProcess(var DSTranBuf : MdMsgRec);

    procedure CheckBinFile;
    var onOffCode : integer;
    begin
      if host1Up      { JMR-I }
        then onOffCode := onCode
        else onOffCode := offCode;
      {$IFDEF ENGINE}
      crToDbCheckPrefix := true;
      {$ENDIF ENGINE}
      if (not (VerifyDebitPfxChecked(DSTranBuf, onOffCode))) then  { not in BIN file }
      begin
        SM('>' + MTX_Utils.TruncMdMsgAcctNo(DSTranBuf) + '< not in BIN : process as credit');
        convertBackToCr := true;                       { process as credit}
      end
      else { in BIN file }
        SM('in BIN : process as debit');
    end; { CheckBinFile }

  begin { CrToDbPreProcess }
    with MTX_XMLClasses.DSProcBuf do
    begin
      if host1Up then                                                  { JMR-I }
      begin
        if (MyVal(DSTranBuf.TrxAmt) < MyVal(CrToDbOnFloor)) then         { JMR-O }
        begin
          convertBackToCr := true;
          SM(format('CrToDbPreProcess - Online: TrxAmt [%s] is below CrToDb online floor of [%s], so process as credit',[DSTranBuf.TrxAmt,CrToDbOnFloor]));
        end
        else
        begin
          SM('online, CheckBinFile');
          CheckBinFile;
        end
      end
      else { offline }
      begin
        GetHostBuf(mtx_lib.tenderNum(DSTranBuf.ReqCodeN));   // make sure we have the correct host
        if PfxInBinFileResult and
           ThisHostVerifyDebitPrefixOffline and (DebitNetworkIDTable.Count > 0) and
           IsBankIdInLocalNetworkIdList(PfxInBinFileNetworks) then       // leave as debit
          CheckBinFile
        else
        if (MyVal(DSTranBuf.TrxAmt) < MyVal(CrToDbOffFloor)) then        { JMR-O }
        begin
          convertBackToCr := true;
          SM(format('CrToDbPreProcess - Offline: TrxAmt [%s] is below CrToDb offline floor of [%s], so process as credit',[DSTranBuf.TrxAmt,CrToDbOffFloor]));
        end
        else
        begin
          SM('offline, CheckBinFile');
          CheckBinFile;
        end;
      end;
    end;
  end; { CrToDbPreProcess }

  procedure PossiblyConvertBackToCredit;    //17003  17004
  var
    ReqType: AnsiChar;
  begin
    if length(DSTranBuf.ReqType) > 0
      then ReqType := DSTranBuf.ReqType[1]
      else ReqType := ' ';

    if crToDbFlag and (not (ReqType in [OpenEPSDecline, TORReq])) and not IsAVoid then
      begin
        if (MyVal(DSTranBuf.CashBack) > 0) then   { JMR-N }
          SM(format('Cashback of %s, so must process as debit',[DSTranBuf.CashBack]))
        else
        begin
          CrToDbPreProcess(DSTranBuf);
          if convertBackToCr then  { process as credit }
          begin
            SM('Fct.CkTransaction Converting back to credit..');
            DSTranBuf.CardProcID := inttostr(WinEPS_Credit);
            SetBackToCredit(DSTranBuf);
            OnTransOK := FindCardPfxAndGetFCTBuf(DSTranBuf);
          end;
        end;
      end
    else
      SM('Fct.CkTransaction: Did not convert back to credit because conditions not met');
  end;

  procedure GetCrToDbFloorAmounts;  //17003 compartmentalized
  begin
    DSTranBuf.CardProcID := IntToStr(WinEPS_Credit);
    if FindCardPrefix(DSTranBuf) then
    begin
      XMLCardProcessingProfile := CPPList.FindCPP(DSTranBuf.LaneNoN);
      if XMLCardProcessingProfile <> nil then
      begin
        FCTBuf := XMLCardProcessingProfile.FindFCT(DSTranBuf.CardProcID);
        if FCTBuf <> nil Then
        begin
          CrToDbOnFloor := FormatFloat(Fmt6Zero, FCTBuf.OnlineLimits.MinCreditToDebit);
          CrToDbOffFloor := FormatFloat(Fmt6Zero, FCTBuf.OfflineLimits.MinCreditToDebit);
        end
        else
        begin
          CrToDbOnFloor := '0';
          CrToDbOffFloor := '0';
          SM('WARNING: (fct.CkTransaction) XMLCardProcessingProfile.FindFCT returned nil.');
        end;
        SM(format('fct.CkTransaction.GetCrToDbFloorAmounts: CrToDbOnlineFloor[%s] CrToDbOfflineFloor[%s]',[CrToDbOnFloor,CrToDbOffFloor]));
      end
      else
        SM('WARNING: (fct.CkTransaction) CPPList.FindCPP returned nil.');
    end
    else
    begin
      crToDbFlag := false;
      DSTranBuf.CrToDbCode := ' ';
      SM('CrToDb invalid, not in CrPrefix file; resetting CrToDbFlag, CrToDbCode');
    end;
  end;

  {$IFDEF MTXEPSDLL}
  procedure TryToFindfctBufForRT; // CPCLIENTS-1932
  var
    TrxData: TTransactionData;
    FilePosition: integer;
    OffRec: AnsiString;
    tPos: integer;
    CardProcId: string;
  begin
    try //CPCLIENTS-19664
      if GenerateReferenceIdEnabled then
        TrxData := TrxLog.FindHTRecord(TranMR.SeqNumToVoid, TranMR.OriginalReferenceId) //CPCLIENTS-19218
      else
        TrxData := TrxLog.FindHTRecord(TranMR.SeqNumToVoid); //CPCLIENTS-19218            // look for HR transaction in the transaction log
      if Assigned(TrxData) and not TrxData.CardProcId.IsEmpty then
        FCTBuf := XMLCardProcessingProfile.FindFCT(TrxData.CardProcId)
      else
      begin
        if (OfflineProcessor.FindOfflineRecordBySeqNumber(TranMR.SeqNumToVoid, OffRec)) or
            (OfflineProcessor.FindOfflineRecordByRefId(TranMR.OriginalReferenceId, OffRec)) then //CPCLIENTS-19218
        begin
          if OffRec > '' then
          begin
            tPos := Pos(#$1C'T', OffRec);
            if tPos > 0 then
            begin
              CardProcId := copy(OffRec, tPos + 2, 2);
              if CardProcId > '' then
                FCTBuf := XMLCardProcessingProfile.FindFCT(CardProcId);
            end;
          end;
        end;
      end;
    finally
      FreeAndNil(TrxData); //CPCLIENTS-19664
    end;
  end;
  {$ENDIF}

  function GetCrToDbFlag: boolean;  //80472
  begin
    {$IFDEF OPENIP}
     result := false;  //80472 MTX_EPS_IProcs.GET_CrToDbFlag returns value of MR.CrToDbFlag, and we don't have MR in OpenIP, so for now, force FALSE
    {$ELSE}
     result := MTX_EPS_IProcs.GET_CrToDbFlag(0);
    {$ENDIF}
  end;

begin { CkTransaction }
  if (DSTranBuf.BlankPreAuthCardInfo and (DSTranBuf.ReqCodeN in AuthComp_Set)) or DllTypes.IsOnlineOnlyTokenTran then // costco Fuel completions don't have track2, use RRN instead so don't do CkTransaction, done for preAuth // CPCLIENTS-1461: was DllTypes.IsTokenTransactio
  begin
    SM(format('Fct.CkTransaction DllTypes.IsTokenTransaction[%s] DllTypes.IsOnlineOnlyTokenTran [%s]',[sTF[DllTypes.IsTokenTransaction], sTF[DllTypes.IsOnlineOnlyTokenTran]]));
    result := true;
  end
  else
  begin
    PfxInBinFileNetworks := TStringList.Create;
    try
      PfxInBinFileResult := IsPfxInBinFile(btDebit, SetSearchPfx(DSTranBuf), DSTranBuf.PanLen, PfxInBinFileNetworks);
      if not PfxInBinFileResult then
        MsgLog('CkTransaction prefix NOT FOUND in DebitBin');
      crToDbFlag := false;
      if not (DSTranBuf.CrToDbCode in ['C','D',' ']) then
        DSTranBuf.CrToDbCode := ' ';    { init to no crtodb }
      convertBackToCr := false;
      host1Up := false;   // F if anything other than EPS or OpenIP compile this  //80472
      {$IFDEF MTXEPSDLL}
      host1Up := OpenEPSConnected;
      {$ENDIF}
      {$IFDEF OPENIP}   //80472
      host1Up := OpenEPSConnected;
      {$ENDIF}
      // I have to check this before the CardProcID is overwritten
      SM(format('Fct.CkTransaction Host1Up[%s] CardProcID[%s] WinEPS_CrToDb[%s]',[sTF[Host1Up],DSTranBuf.CardProcID,sTF[MyVal(DSTranBuf.CardProcID) = WinEPS_CrToDb]]));

      if GetCrToDbFlag or ((MyVal(DSTranBuf.CardProcID) = WinEPS_CrToDb) and not IsAVoid) then  //17003 17004.. if MR record has the CrToDbFlag set, then let us look at it here too
      begin                                                      { JMR-B }
        crToDbFlag := true;                                      { JMR-B }
        DSTranBuf.CrToDbCode := CrToDb;
        SM('This MAY BE A CrToDb!');

        GetCrToDbFloorAmounts;       //17003 17004
        DSTranBuf.CardProcID := IntToStr(WinEPS_CrToDb);   { set back to original }
        SM(format('Fct.CkTransaction CardProcID reset to [%s]',[DSTranBuf.CardProcID]));
      end;
      {$IFDEF MTXEPSDLL}
      if TranMR.IsReleaseTransaction then                                                         // CPCLIENTS-1932
          TryToFindfctBufForRT;                                                                   // CPCLIENTS-1932
      if ((IsAVoid or IsHospitality or TranMR.IsReleaseTransaction) and NoTrack2 and NoAcct and NoCheck and NoDr_License and CrDbOrEBT) then
        OnTransOK := true     { let voids through if no track 2 so get NI rsp code later }
      else   { let voids through if no track 2 so get NI rsp code later }
      {$ENDIF}
        OnTransOK := FindCardPfxAndGetFCTBuf(DSTranBuf); { JMR-B : start }
      PossiblyConvertBackToCredit;    //17003 17004
    finally
      FreeAndNil(PfxInBinFileNetworks);
    end;

    DSTranBuf.VoucherClearProcess := (DSTranBuf.ReqCodeN in All_Voice_Trxs) and (Pos('M', DSTranBuf.CmdSequence) < 1) and
                                     (DSTranBuf.HostSuffixCode <> 'FLC'); // JMR DEV-8000

{$IFDEF MTXEPSDLL}
    if (P2PEncryption or DllTypes.IsSessionKeyAESEncryption or DllTypes.IsVSPEnabled) and (DSTranBuf.ReqCodeN in AuthComp_Set) then // DOEP-59378
      OnTransOK := true // suppress the result
    else if (DSTranBuf.ReqCodeN in [User_1_ReActivationN, User_1_FinalTenderN, User_1_PreAuthN]) then         // CPCLIENTS-11904
    begin
      OnTransOK := true;
      if (DSTranBuf.MTXRspCodeN = 0)
        then DSTranBuf.OffLineAuthCodeN := TrxAppOffN
        else DSTranBuf.OfflineAuthCodeN := DSTranBuf.MTXRspCodeN;
    end;
{$ENDIF}
    if OnTransOK and assigned(FCTBuf) Then    { if it hasn't failed, prefix ok, FCT ok }
      CkABunchOfStuff(DSTranBuf);                                      { JMR-E }
    result := OnTransOK;      // TODO possibly make this NON GLOBAL (parameter)
  end;
end;

procedure SetHostSpecificVars(var InRec: MdMsgRec);      // rename to BYL specific?
begin
  // CPCLIENTS-5179 To assign Issuer code depends on Tender/Transaction type and
  // to have meaningful name for MakeBYLATLIssuerCode and BYLATLIssuerCode as it is implemented for ATL too
  if IsBYLHostGroup(InRec.HostSuffixCode) then
    InRec.BYLATLIssuerCode := mtx_utils.MakeBYLATLIssuerCode(InRec.ReqCodeN);
end;

procedure SetHostBuf(var InRec: MdMsgRec; AMdMsgTenderType: integer=iNone);   { JGS-MH }
var itender: integer;
begin
  try
    if not assigned(HostArray) then exit;
    if AMdMsgTenderType = iNone
      then itender := mtx_Lib.TenderNum(InRec.ReqCodeN)
      else iTender := AMdMsgTenderType;
    HostFunctions.hostNumForTran := 0;
    if (itender <> MaxTerms) then
    begin
      if (itender = iMultiTender) then  // CPCLIENTS-19227 - To fetch the host defifned for Credit as MultiTender will runs on Credit configuration
        GetHostBuf(icredit, (InRec.Check_Type = ctPersonal))
      else
        GetHostBuf(itender, (InRec.Check_Type = ctPersonal));

      if (HostFunctions.hostNumForTran <> 0) then
      begin
        InRec.HostSuffixCode := HostBuf.Suffix;
        SetHostSpecificVars(InRec);       // specifically only for BYL
      end;
    end
    else
      SM('****ERROR: Invalid reqCode >'+InRec.ReqCode+'< fct.pas.SetHostBuf');

  except on e: exception do
    SM('Try..Except: fct.pas.SetHostBuf ' + e.message);
  end;
end; { SetHostBuf }

function IsOtherTenderDefined(var possibleEPSTenders : arrayWinEPSTenders): boolean;
var tender: integer;
begin
  result := false;
  for tender := 3 to WinEPS_MaxTenders do
    if NOT (tender in [WinEPS_ConnectPay]) and
       ((possibleEPStenders[1] = tender) or (possibleEPStenders[2] = tender)) then
    begin
      result := true;
      SM('fct.ResolveTenderType - IsOtherTenderDefined: ' + IntToStr(tender));
      Exit;
    end
end;

function ResolveTenderType(TendersTaken: string; var DSTranBuf: MdMsgRec;  { returns 3 digit OpenEPS tender }
  var NumberOfTendersFound: integer; var possibleEPSTenders : arrayWinEPSTenders;
  const SetConnectPay: boolean = false; const defaultTender: integer = 0; const isSuppressDebit: Boolean = False;
  const TokenTransactionCardType: string = ''): string;
var i : integer;
    WinEPSTender : integer;
    TendersFound: string;
    Networks: TStrings;
    searchResult: boolean;

  function SetCardProcID(aTender: integer): string;     // JTG TODO no need for nesting... gen func
  begin
    result := '00';
    case aTender of                              { so convert to WinEPS tender }
      hsDebit      : result := DbProcID;
      hsCredit     : result := CrProcID;
      hsPrivDb     : result := PropDbProcID;
      hsPrivCr     : result := PropCrProcID;
      hsCheck      : result := CkProcID;
      hsEBTfs      : result := EBT_FS_ProcID;
      hsEBTca      : result := EBT_Cash_ProcID;
      hsGiftCard   : result := User1ProcID;
      hsFleet      : result := FleetProcID;
      hsPhoneCard  : result := User2ProcID;
      hsWirelessPhone : result := WirelessProcID;
      hsACH        : result := ACHProcID;
      hsConnectPay : result := ConnectPayProcID;
      hsEWIC       : result := eWICProcID;
      hsBenefitsProgram : result := BenefitsProgramProcID;      //9635
      hsMultiTender : result := MultiTenderProcID;   //19132
    end;
    WinEPSTender := StrToIntDef(result, 0);
  end;

  procedure SetDefaultTender;
  begin
    NumberOfTendersFound := 1;
    SetCardProcID(defaultTender);
    possibleEPStenders[NumberOfTendersFound] := WinEPSTender;
    TendersFound := DSTranBuf.CardProcID;
  end;

  function IsManualEntry: boolean;                                                    // TFS-14951
  begin
    Result := POSSetManualTrackData or TrackReadFailed or (DSTranBuf.Entry = CEV_MANUAL_POS) or (DSTranBuf.Entry = CEV_MANUAL_CUST);   // TFS-163698
  end;


var
    IsCreditPrefixFound : Boolean;  //19638
begin
  try
    result := '';
    IsCreditPrefixFound := False;   //19638
    MsgDebug('ResolveTenderType MR.First6PAN =    [' + MR.First6PAN + ']');
    MsgDebug('ResolveTenderType MR.LookupPrefix = [' + MR.LookupPrefix + ']');
    NumberOfTendersFound := 0;                 { init number of tenders }
    TendersFound := '';                                                         
    if (NotAcceptPfx.Count > 0) then
      {searchResult := }FindCardPrefix(DSTranBuf);   // not auto tender lookup for this one or it won't work

    if (DSTranBuf.MtxRspCode <> TrxDecNotAccepted) then  // if not accepted, we are done
    begin
      for i := 1 to WinEPS_MaxTenders do         { go thru all flags in TendersTaken }
      begin
        if SameText(TendersTaken[i],'Y') and (i <> defaultTender) then { tender taken if = 'Y', default tender = don't check prefix file }
        begin                                                          { if we get no tender for those taken, then set tender = default }
          DSTranBuf.CardProcID := SetCardProcID(i);                    { default tender should be set to all wild cards }
          if not((DSTranBuf.CardProcID = MultiTenderProcID) and IsCreditPrefixFound)  then  //19638-For credit and multitender, the prefix search is same. So if credit prefix already found, avoiding the search for multitender
            searchResult := FindCardPrefix(DSTranBuf, false);            { false = auto tender lookup }

          if searchResult then
          begin
            if (SetCardProcID(i) = crProcID) then     //19638
              IsCreditPrefixFound := True;
            inc(NumberOfTendersFound);
            possibleEPStenders[NumberOfTendersFound] := WinEPSTender;
            TendersFound := TendersFound + DSTranBuf.CardProcID + ' ';
            if (SetCardProcID(i) = DbProcID) and IsTDBHost(iDebit) {and (not isSuppressDebit)} then // DOEP-44727   //69276-undo prev change
              break;
          end;
        end;
      end;
      if DllTypes.IsTokenTransaction and (NumberOfTendersFound = 0) then
      begin
        SM(Format('ResolveTenderType - CardType %s not found in prefix table', [DllTypes.TokenTransactionCardType]));
        DSTranBuf.MtxRspCode := TrxDecNotAccepted;
        DSTranBuf.MTXRspCodeN := TrxDecNotAcceptedN;
      end;

      if NOT DllTypes.IsTokenTransaction then // if not token transaction
      begin
        Networks := TStringList.Create;
        try
          if (NumberOfTendersFound = 2) then   // i.e. use the debit BIN to determine one tender from two
          begin                                                // only XPI for canada does NOT UseDebitBIN
            if (possibleEPStenders[1] in CrOrDbTender) and
               (possibleEPStenders[2] in CrOrDbTender) then   { could be combo card, check BIN }
            begin
              if IsPfxInBinFile(btDebit, SetSearchPfx(DSTranBuf), DSTranBuf.PanLen, Networks) and not IsManualEntry then { two possible tenders }
              begin
                result := format('%.3u',[ttComboCardDborCr]); { must be a combo card, set it }
                DSTranBuf.CardProcID := '  ';
                SM('fct.ResolveTenderType - Must be a Combo Card');
              end
              else
              begin
                NumberOfTendersFound := 1;                    { only one tender is possible }
                possibleEPSTenders[1] := WinEPS_Credit;       { not a combo must be credit }
                SM('fct.ResolveTenderType - Not a combo or was manually entered must be Credit');
              end;
            end
            else
            if (possibleEPStenders[1] = WinEPS_Debit) or      { is one of the two DB? }
               (possibleEPStenders[2] = WinEPS_Debit) then    { and the other is not CR }
            begin
              NumberOfTendersFound := 1;      { resolve to one if only two and other is not CR }
              if IsPfxInBinFile(btDebit, SetSearchPfx(DSTranBuf), DSTranBuf.PanLen, Networks) then    { if in dbBIN then assume db }
              begin
                if IsOtherTenderDefined(possibleEPStenders) then // DOEP-68024
                  NumberOfTendersFound := 2
                else
                if SetConnectPay then                  { pump doens't know about connect pay }
                begin
                  if (possibleEPStenders[1] = WinEPS_ConnectPay) or   { is one of the two DB? }
                     (possibleEPStenders[2] = WinEPS_ConnectPay)
                    then possibleEPStenders[1] := WinEPS_ConnectPay
                    else possibleEPStenders[1] := WinEPS_Debit;
                end
                else
                  possibleEPStenders[1] := WinEPS_Debit;
              end
              else   { we set the other tender }
              if (possibleEPStenders[1] = WinEPS_Debit) then
                possibleEPStenders[1] := possibleEPStenders[2];  { use other tender }
            end;
          end;

          {$IFDEF MTXEPSDLL}
          if (NumberOfTendersFound = 2) and (EMVMgr.CardEntryType = CEV_EMV_CTLS) then  // TFS-148515
          begin
            if not EMVMgr.IsEBTChip then //CPCLIENTS-19133
            begin
              if not EMVMgr.RequiresOnlinePIN  then
              begin
                if possibleEPSTenders[1] = WinEPS_Debit then
                  possibleEPSTenders[1] := possibleEPSTenders[2];
              end
              else
              begin
                if possibleEPSTenders[1] = WinEPS_Credit then
                  possibleEPSTenders[1] := possibleEPSTenders[2];
              end;
              NumberOfTendersFound := 1;
              possibleEPSTenders[2] := 0;
            end;
          end;
          {$ENDIF}

          if (NumberOfTendersFound = 1)             and    { if all we found was debit }
             (possibleEPStenders[1] = WinEPS_Debit) and    { but not in the BIN file }
             not IsPfxInBinFile(btDebit, SetSearchPfx(DSTranBuf), DSTranBuf.PanLen, Networks) and
             not IsTDBHost(iDebit) then
          begin
            NumberOfTendersFound := 0;                     { then say we found nothing }
            SM('fct.ResolveTenderType - All we found was Debit but not in the BIN file');
          end;

          if (NumberOfTendersFound = 0) and (defaultTender > 0) then
            SetDefaultTender;
          SM(Format('fct.ResolveTenderType Found %d tenders (%s)', [NumberOfTendersFound, TendersFound]));
        finally
          Networks.Free;
        end;
      end;

      if (NumberOfTendersFound = 1) then               { resolved, set all fields }
      begin
        result := format('%.3u',[WinEPSTenderToOpenEPS(possibleEPSTenders[1])]);
        DSTranBuf.CardProcID := IntToStr(possibleEPSTenders[1]); { have to reset again for final tender }
        if FindCardPrefix(DSTranBuf, false) then
          FoundCardProcID(DSTranBuf);                  { set card name }
      end;
    end;
  except
    on e:exception do
      SM('Try..Except: FCT.ResolveTenderType ' + e.message);
  end;
end;  { ResolveTenderType }

function GetOfflineCrToDbFloor(var DSTranBuf: MdMsgRec): integer;  { JMR-P }
var
  origCardProcID: string;
begin
  result := 0;
  try
    origCardProcID := DSTranBuf.CardProcID;
    DSTranBuf.CardProcID := IntToStr(WinEPS_Credit);
    if FindCardPrefix(DSTranBuf) Then
    begin
      XMLCardProcessingProfile := CPPList.FindCPP(DSTranBuf.LaneNoN);           { JMR-U }
      if XMLCardProcessingProfile <> nil then
      begin
        FCTBuf := XMLCardProcessingProfile.FindFCT(DSTranBuf.CardProcID);
        if FCTBuf <> nil then
          result := FCTBuf.OfflineLimits.MinCreditToDebit                       
        else
          result := 0;
      end
      else
        SM('WARNING: (fct.GetOfflineCrToDbFloor) CPPList.FindCPP returned nil.');
    end;
    DSTranBuf.CardProcID := origCardProcID;   { set back to original }
  except
    on e:exception do
      sm('Try..Except: FCT.GetOfflineCrToDbFloor ' + e.message);
  end;
end; { GetOfflineCrToDbFloor }

procedure ClearStringLists;
begin
  try
    NotAcceptPfx.Clear;
    CreditPfx.Clear; // DEV-12503 <
    DebitPfx.Clear;
    PropCrPfx.Clear;
    PropDbPfx.Clear;
    CkAuthPfx.Clear;
    EBTFSPfx.Clear;
    EBTCAPfx.Clear;
    GiftPfx.Clear;
    PINPfx.Clear;
    PhonePfx.Clear;
    WlessPfx.Clear;
    ACHPfx.Clear;
    ConnectPayPfx.Clear;
    eWICPfx.Clear;
    BenefitsProgramPfx.Clear; // CPCLIENTS-9586 Clearing Stringlist created for Benefits Program Tender Type
    LoyaltyPfx.Clear;
  except on e: exception do
    SM('FCT.ClearStringLists (Unassigned) EXCEPTION: '+e.message);
  end;
end;

function ReadCardPfxFiles: boolean;
var
  XMLConfig: TXMLConfiguration;
  aDir     : string;
  IsUsingV1: boolean;
  //XMLError: boolean;

  //procedure ReadOnePfxFile(Fname: string; var aPfxList: TStringList);
  procedure LoadXML; // DEV-12503
  var
    i,j: integer;
    Idx: Integer;
    N1, N2: TXMLParserNode;
    TenderType: string;
    aPfxList: TStringList;
    //aPanLen,
    tmpStr: string;

    function ReplaceX(inStr: string; inChr: char): string;
    begin
      result := StringReplace(inStr, 'X', inChr, [rfReplaceAll, rfIgnoreCase]);
    end;

    procedure DebugPrefix(aTender: string; aList: TStringList);
    var M: integer;
    begin
      for M := 0 to aList.Count -1 do
        SM(Format('DebugPrefix %s: %s', [aTender, aList.Strings[M]]));
    end;

    procedure LoadTenders(aBaseNode: TXMLParserNode);
    var
      ND1, ND2: TXMLParserNode;
      M, N, i, k: integer;
      Pfx: string;
    begin
      fillChar(IsPfxSearchWildCard, sizeOf(IsPfxSearchWildCard), 0);    // should init to false
      UseWildCardSearchForNotAccepted := false;
      LoyaltyCardDefined := false;
      i := 0; // TODO: confirm (Yohan)
      for M := 0 to aBaseNode.Children.Count - 1 do
      begin
        ND1 := aBaseNode.Children[M];
        if SameText(ND1.Name, 'Tender') then
        begin
          TenderType := ND1.Attr.Values['Type'];
          aPfxList := nil;
          if SameText(TenderType, CRDPFX_CREDIT) then begin aPfxList := CreditPfx;              i := WinEPS_Credit; end
          else if SameText(TenderType, CRDPFX_DEBIT) then begin aPfxList := DebitPfx;           i := WinEPS_Debit; end
          else if SameText(TenderType, CRDPFX_PLCREDIT) then begin aPfxList := PropCrPfx;       i := WinEPS_PrivCr; end
          else if SameText(TenderType, CRDPFX_PLDEBIT) then begin aPfxList := PropDbPfx;        i := WinEPS_PrivDb; end
          else if SameText(TenderType, CRDPFX_CHECK) then begin aPfxList := CkAuthPfx;          i := WinEPS_Check; end
          else if SameText(TenderType, CRDPFX_EBTFOODSTAMP) then begin aPfxList := EBTFSPfx;    i := WinEPS_EBT_FS; end
          else if SameText(TenderType, CRDPFX_EBTCASH) then begin aPfxList := EBTCAPfx;         i := WinEPS_EBT_CA; end
          else if SameText(TenderType, CRDPFX_GIFTCARD) then begin aPfxList := GiftPfx;         i := WinEPS_User1; end
          else if SameText(TenderType, CRDPFX_FLEET) then begin aPfxList := PINPfx;             i := WinEPS_Fleet; end
          else if SameText(TenderType, CRDPFX_PHONECARD) then begin aPfxList := PhonePfx;       i := WinEPS_User2; end
          else if SameText(TenderType, CRDPFX_WIRELESS) then begin aPfxList := WlessPfx;        i := WinEPS_WireLess; end
          else if SameText(TenderType, CRDPFX_ACH) then begin aPfxList := ACHPfx;               i := WinEPS_ACH; end
          else if SameText(TenderType, CRDPFX_CONNECTPAY) then begin aPfxList := ConnectPayPfx; i := WinEPS_ConnectPay; end
          else if SameText(TenderType, CRDPFX_EWIC) then begin aPfxList := eWICPfx;             i := WinEPS_eWIC; end
          else if SameText(TenderType, CRDPFX_BENEFITSPROGRAM) then begin aPfxList := BenefitsProgramPfx;             i := WinEPS_BenefitsProgram; end  // CPCLIENTS-9586 Loading Tender into List that exist in Prefix.XML
          else if SameText(TenderType, CRDPFX_LOYAL) then
          begin
            aPfxList := LoyaltyPfx;
            i := WinEPS_Loyalty;
            LoyaltyCardDefined := ND1.Children.Count > 0;
            if LoyaltyCardDefined then
              SM(Format('fct.LoadTenders - %d Loyalty card(s) defined', [ND1.Children.Count]));
          end
          else if SameText(TenderType, CRDPFX_NOT_ACCEPT) then begin aPfxList := NotAcceptPfx;  i := 0; end;

          //aPfxList.Clear;
          if Assigned(aPfxList) then
            for N := 0 to ND1.Children.Count - 1 do
            begin
              ND2 := ND1.Children[N]; // prefix
              if (ND2.Attr.Values['Data'] = '') then  // all prefixes are now From prefix + To Prefix
              begin
                Pfx := RFill(ND2.Attr.Values['Begin'], '0', PanSize) + RFill(ND2.Attr.Values['End'], '9', PanSize);
              end
              else
              begin
                Pfx := RFill(ND2.Attr.Values['Data'], 'X', PanSize);  // file out to length with wild cards
                Pfx := Pfx + Pfx;                                     // same for fm and to so record is correct len
                if SameText(TenderType, CRDPFX_NOT_ACCEPT)
                  then UseWildCardSearchForNotAccepted := true
                  else IsPfxSearchWildCard[i] := true;
              end;
              tmpStr := ND2.Attr.Values['CardCode'] + ND2.Attr.Values['AutoTenderExcluded']
                  + rpad(ND2.Attr.Values['FSACode'], FSACodeSize)      // don't forget to pad in case not there
                  + rpad(ND2.Attr.Values['StateCode'], StateCodeSize)  // don't forget to pad in case not there eWic
                  + rpad(ND2.Attr.Values['ProgramID'], ProgramIDSize)  // pad in case not there
                  + rpad(ND2.Attr.Values['RetailerID'], RetailerIDSize)
                  + rpad(ND2.Attr.Values['FIID'], FIIDSize)
                  + rpad(ND2.Attr.Values['DraftCapture'], DraftCapSize);

              if IsTDBHost(iDebit) and (i = WinEPS_Debit) then                // this is hard coded for TDB host (Canada)
              begin
                // ex) '4XXXXXXXXX4XXXXXXXXX16VSNHB                                       '
                for k := low(TDBSvcCodes) to high(TDBSvcCodes) do
                  Idx := aPfxList.Add(Pfx + ND2.Attr.Values['CardLen'] + tmpStr + TDBSvcCodes[k]);
              end
              else
                Idx := aPfxList.Add(Pfx + ND2.Attr.Values['CardLen'] + tmpStr + '   '); // 3 spaces for svcCode
            end;
        end else if SameText(ND1.Name, 'DebitNetworkIDTable') then
        begin
          for N := 0 to ND1.Children.Count - 1 do
          begin
            ND2 := ND1.Children[N];
            if SameText(ND2.Name, 'AllowedDebitNetwork') and (Trim(ND2.Text) <> '') then
              if Assigned(DebitNetworkIDTable) then
                DebitNetworkIDTable.Add(Trim(ND2.Text));
          end;
        end
        else if SameText(ND1.Name, 'AIDTable') then // DOEP-31746 <
        begin
          for N := 0 to ND1.Children.Count - 1 do
          begin
            ND2 := ND1.Children[N];
            if SameText(ND2.Name, 'AID') then
              if Assigned(AIDTable) then
                AIDTable.Add(ND2.Attr.Values['RID'],
                        ND2.Attr.Values['PIX'],
                        ND2.Attr.Values['Tender'],
                        ND2.Attr.Values['CardCode'],
                        ND2.Attr.Values['AutoTenderExcluded']
                );
          end;
        end // DOEP-31746 >
      end;
      IsPfxSearchWildCard[WinEPS_CrToDb] := IsPfxSearchWildCard[WinEPS_Debit];
    end;

  begin
    ClearStringLists;
    DebitNetworkIDTable.Clear; // DEV-12503 >
    IsUsingV1 := true;

    if IsUsingV1 then
      LoadTenders(XMLConfig.Root)
    else
    begin // We don't use this for now
      for I := 0 to XMLConfig.Root.Children.Count - 1 do
      begin
        N1 := XMLConfig.Root.Children[I];
        if SameText(N1.Name, 'Tenders') then
        begin
          LoadTenders(N1);
        end else if SameText(N1.Name, 'DebitNetworkIDTable') then
        begin
          for J := 0 to N1.Children.Count - 1 do
          begin
            N2 := N1.Children[J];
            if SameText(N2.Name, 'AllowedDebitNetwork') and (Trim(N2.Text) <> '') then
              DebitNetworkIDTable.Add(Trim(N2.Text));
          end;
        end;
      end;
    end;
    {
    msgDebug('ReadCardPfxFiles');
    DebugPrefix('Credit', CreditPfx);
    DebugPrefix('Debit', DebitPfx);
    DebugPrefix('P.Credit', PropCrPfx);
    DebugPrefix('P.Debit', PropDbPfx);
    DebugPrefix('Check', CkAuthPfx);
    DebugPrefix('EBF FS', EBTFSPfx);
    DebugPrefix('EBF Cash', EBTCAPfx);
    DebugPrefix('Gift', GiftPfx);
    DebugPrefix('PIN', PINPfx);
    DebugPrefix('Phone', PhonePfx);
    DebugPrefix('Wireless', WlessPfx);
    DebugPrefix('ACH', ACHPfx);
    DebugPrefix('ConnectPay', ConnectPayPfx);
    DebugPrefix('DebitNetworkIDTable', DebitNetworkIDTable);
    }
  end;    { ReadOnePfxFile }                                                    

begin        // ReadCardPfxFiles MAIN
  result := false;
  //XMLError := false;
  try
    try
      {$IFDEF MTXEPSDLL}
      aDir := DefaultDir;
      {$ELSE MTXEPSDLL}
      aDir := WinEPSDir;
      {$ENDIF MTXEPSDLL}

      if not FileExists(aDir + CARDPREFIX_FILENAME) then
      begin
        SM('****ERROR:  File does not exist: ' + aDir + CARDPREFIX_FILENAME);
        //XMLError := true;
        Exit;
      end;

      XMLConfig := TXMLConfiguration.Create;
      try
        if NOT XMLConfig.FXMLParser.LoadFromFile(aDir + CARDPREFIX_FILENAME) then     // 828.5
        begin                                                                         // 828.5
          //XMLError := true;
          Exit;                                                                       // 828.5
        end;                                                                          // 828.5
        XMLConfig.FXMLParser.StartScan;                                               // 828.5
        XMLConfig.ScanElement(nil);
        if XMLConfig.Root.Name <> 'CardPrefix' then
        begin
          SM('****ERROR:  Wrong xml format: ' + aDir + CARDPREFIX_FILENAME);
          XMLConfig.Free;
          //XMLError := true;
          Exit;
        end;
        LoadXML; // DEV-12503
      finally
        XMLConfig.Free;
      end;
      result := true;
    except
      on e:exception do
      begin
        SM('Try..Except: FCT.ReadCardPfxFiles ' + e.message);
        //XMLError := true;
      end;
    end;
  finally
{$IFDEF MTXEPSDLL} // will stop OpenEPS in UIsio.LoadOpenEpsXMLFiles - ReadHostFile 
    //if XMLError and Assigned(TransactionProcessor) then
    //  TransactionProcessor.StopOpenEPS(esXML);
{$ENDIF}            
  end;
end;    { ReadCardPfxFiles }

procedure ReadProcFile;
begin
  try
    with TXMLProcess.Create(DefaultDir + ProcessXML_) do
      try
        GetRecord(MTX_XMLClasses.DSProcBuf);
      finally
        Free;
      end;
  except on e: exception do
    SM('Try..Except: FCT.ReadProcFile ' + e.message);
  end;
end;

function GetMaxOnlineCashback: integer; //CPCLIENTS-8550
begin
  try
    result := 0;
    {$IFDEF MTXEPSDLL}
    if EMVMgr.IsCardEntryChipCard then
      result := EMVMgr.GetMaxCashbackFromAid(EMVMgr.AppID);
    {$ENDIF}
    if (result = 0) and Assigned(FCTBuf) then // use CPP limits if EMV CB setting is zero // 9207
      result := FCTBuf.OnlineLimits.MaxCashback;
    MsgDebug('GetMaxOnlineCashback = ' + IntToStr(result));
  except on e: exception do
    SM('Try..Except: FCT.GetMaxOnlineCashback ' + e.message);
  end;
end;

function Init_FCT_Array: boolean;
var aFileName: string;
begin
  result := false;
  try
{$IFDEF MTXEPSDLL}
    if not Assigned(XMLCardProcessingProfile) then
      XMLCardProcessingProfile := TXMLCardProcessingProfile.Create(DefaultDir + MTX_Constants.CardProcessingProfilesFileName);
    InitHostFile;
    ReadProcFile;
{$ENDIF}
    if NOT ReadCardPfxFiles then
      Exit;
    aFileName := defaultDir + MTX_Utils.CheckForNewEPSBINFileAndSetName;
    TheBINFileName := aFileName;
    //sm('****DEBUG: Init_FCT - Call ReloadBinXML(' + aFileName + ')');
    BinXmlLookup.ReloadBinXml(aFileName);
    result := true;
  except on e: exception do
    SM('Try..Except: FCT.Init_FCT_Array - ' + e.message);
  end;                                                                        
end;    { Init_FCT_Array}

procedure InitStringLists;
begin
  NotAcceptPfx := TStringList.Create;
  CreditPfx := TStringList.Create;
  DebitPfx  := TStringList.Create;
  PropCrPfx := TStringList.Create;
  PropDbPfx := TStringList.Create;
  CkAuthPfx := TStringList.Create;
  EBTFSPfx  := TStringList.Create;
  EBTCAPfx  := TStringList.Create;
  GiftPfx   := TStringList.Create;
  PINPfx    := TStringList.Create;
  PhonePfx  := TStringList.Create;
  WlessPfx  := TStringList.Create;
  ACHPfx    := TStringList.Create;
  ConnectPayPfx := TStringList.Create;
  eWICPfx   := TStringList.Create;
  BenefitsProgramPfx   := TStringList.Create;  // CPCLIENTS-9586 For Benefits Program Tender Type
  LoyaltyPfx   := TStringList.Create;
  DebitNetworkIDTable := TStringList.Create; // DEV-12503
  DebitNetworkIDTable.CaseSensitive := false; // DEV-12503
  AIDTable := TAIDTable.Create(TAID); // DOEP-31746
end;

procedure DestroyStringLists;
begin
  FreeAndNil(NotAcceptPfx);
  FreeAndNil(CreditPfx);
  FreeAndNil(DebitPfx);
  FreeAndNil(PropCrPfx);
  FreeAndNil(PropDbPfx);
  FreeAndNil(CkAuthPfx);
  FreeAndNil(EBTFSPfx);
  FreeAndNil(EBTCAPfx);
  FreeAndNil(GiftPfx);
  FreeAndNil(PINPfx);
  FreeAndNil(PhonePfx);
  FreeAndNil(WlessPfx);
  FreeAndNil(ACHPfx);
  FreeAndNil(ConnectPayPfx);
  FreeAndNil(eWICPfx);
  FreeAndNil(BenefitsProgramPfx);  // CPCLIENTS-9586 Free and set Nil for Benefits Program
  FreeAndNil(LoyaltyPfx);
  FreeAndNil(DebitNetworkIDTable); // DEV-12503
  FreeAndNil(AIDTable); // DOEP-31746
end;

procedure FctInitialization;
begin
  ExtendedLog('fct Initialization', procedure
  begin
    InitStringLists;
  end
  );
end;

initialization

FctInitialization;

finalization // SRM [060204]
  ExtendedLog('fct Finalization', procedure
  begin
    DestroyStringLists;
  end
  );

end.

