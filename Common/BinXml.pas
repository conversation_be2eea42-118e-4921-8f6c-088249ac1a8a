
{******************************************}
{                                          }
{         Delphi XML Data Binding          }
{                                          }
{         Generated on: 5/3/2010 12:04:20 PM }
{       Generated from: D:\debitbin.xml    }
{                                          }
{******************************************}
unit BinXml;

interface

uses
  FinalizationLog,
  SysUtils, // DEV-16236
  xmldom, XMLDoc, XMLIntf;

type

{ Forward Decls }

  IXMLServerEPS_BinFileType = interface;
  IXMLBinType = interface; 
  IXMLNetworksType = interface;
  IXMLNetworkType = interface;

{ IXMLServerEPS_BinFileType }

  IXMLServerEPS_BinFileType = interface(IXMLNode)
    ['{865BA10B-D55F-4B18-8576-48B43208A9DC}']
    { Property Accessors }
    function Get_FormatVersion: string;
    function Get_DebitBin: IXMLBinType;
    function Get_FsaBin: IXMLBinType;
    function Get_PrepaidBin: IXMLBinType;
    function Get_DCCBin: IXMLBinType;
    procedure Set_FormatVersion(Value: string);
    { Methods & Properties }
    property FormatVersion: string read Get_FormatVersion write Set_FormatVersion;
    property DebitBin: IXMLBinType read Get_DebitBin;
    property FsaBin: IXMLBinType read Get_FsaBin;
    property PrepaidBin: IXMLBinType read Get_PrepaidBin;
    property DCCBin: IXMLBinType read Get_DCCBin;
  end;

{ IXMLDebitBinType }

  IXMLBinType = interface(IXMLNode)
    ['{332B13C3-E547-4491-9C6A-503F9B5DF5B2}']
    { Property Accessors }
    function Get_UpdateType: WideString;
    function Get_BatchId: Integer;
    function Get_BatchDate: WideString;
    function Get_CompanyNumber: Integer;
    function Get_Networks: IXMLNetworksType;
    procedure Set_UpdateType(Value: WideString);
    procedure Set_BatchId(Value: Integer);
    procedure Set_BatchDate(Value: WideString);
    procedure Set_CompanyNumber(Value: Integer);
    { Methods & Properties }
    property UpdateType: WideString read Get_UpdateType write Set_UpdateType;
    property BatchId: Integer read Get_BatchId write Set_BatchId;
    property BatchDate: WideString read Get_BatchDate write Set_BatchDate;
    property CompanyNumber: Integer read Get_CompanyNumber write Set_CompanyNumber;
    property Networks: IXMLNetworksType read Get_Networks;
  end;

{ IXMLNetworksType }

  IXMLNetworksType = interface(IXMLNodeCollection)
    ['{217EF1D3-14CB-4276-930A-F2B1553C7456}']
    { Property Accessors }
    function Get_Network(Index: Integer): IXMLNetworkType;
    function GetNetworkById(NetworkId: string): IXMLNetworkType; // DEV-16236
    { Methods & Properties }
    function Add: IXMLNetworkType;
    function Insert(const Index: Integer): IXMLNetworkType;
    property Network[Index: Integer]: IXMLNetworkType read Get_Network; default;
  end;

{ IXMLNetworkType }

  IXMLNetworkType = interface(IXMLNode)
    ['{A6D7E330-8FEB-470A-965A-7347FCF936B4}']
    { Property Accessors }
    function Get_Id: WideString;
    function Get_BinRanges: WideString;
    procedure Set_Id(Value: WideString);
    procedure Set_BinRanges(Value: WideString);
    { Methods & Properties }
    property Id: WideString read Get_Id write Set_Id;
    property BinRanges: WideString read Get_BinRanges write Set_BinRanges;
  end;

{ Forward Decls }

  TXMLServerEPS_BinFileType = class;
  TXMLBinType = class;
  TXMLNetworksType = class;
  TXMLNetworkType = class;

{ TXMLServerEPS_BinFileType }

  TXMLServerEPS_BinFileType = class(TXMLNode, IXMLServerEPS_BinFileType)
  protected
    { IXMLServerEPS_BinFileType }
    function Get_FormatVersion: string;
    function Get_DebitBin: IXMLBinType; 
    function Get_FsaBin: IXMLBinType;
    function Get_PrepaidBin: IXMLBinType;
    function Get_DCCBin: IXMLBinType;
    procedure Set_FormatVersion(Value: string);
  public
    procedure AfterConstruction; override;
  end;

{ TXMLDebitBinType }

  TXMLBinType = class(TXMLNode, IXMLBinType) 
  protected
    { IXMLDebitBinType }
    function Get_UpdateType: WideString;
    function Get_BatchId: Integer;
    function Get_BatchDate: WideString;
    function Get_CompanyNumber: Integer;
    function Get_Networks: IXMLNetworksType;
    procedure Set_UpdateType(Value: WideString);
    procedure Set_BatchId(Value: Integer);
    procedure Set_BatchDate(Value: WideString);
    procedure Set_CompanyNumber(Value: Integer);
  public
    procedure AfterConstruction; override;
  end;

{ TXMLNetworksType }

  TXMLNetworksType = class(TXMLNodeCollection, IXMLNetworksType)
  protected
    { IXMLNetworksType }
    function Get_Network(Index: Integer): IXMLNetworkType;
    function GetNetworkById(NetworkId: string): IXMLNetworkType; // DEV-16236
    function Add: IXMLNetworkType;
    function Insert(const Index: Integer): IXMLNetworkType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLNetworkType }

  TXMLNetworkType = class(TXMLNode, IXMLNetworkType)
  protected
    { IXMLNetworkType }
    function Get_Id: WideString;
    function Get_BinRanges: WideString;
    procedure Set_Id(Value: WideString);
    procedure Set_BinRanges(Value: WideString);
  end;

{ Global Functions }

function GetServerEPS_BinFile(Doc: IXMLDocument): IXMLServerEPS_BinFileType;
function LoadServerEPS_BinFile(const FileName: WideString): IXMLServerEPS_BinFileType;
function NewServerEPS_BinFile: IXMLServerEPS_BinFileType;

implementation

{ Global Functions }

function GetServerEPS_BinFile(Doc: IXMLDocument): IXMLServerEPS_BinFileType;
begin
  Result := Doc.GetDocBinding('ServerEPS_BinFile', TXMLServerEPS_BinFileType) as IXMLServerEPS_BinFileType;
end;

function LoadServerEPS_BinFile(const FileName: WideString): IXMLServerEPS_BinFileType;
begin
  Result := LoadXMLDocument(FileName).GetDocBinding('ServerEPS_BinFile', TXMLServerEPS_BinFileType) as IXMLServerEPS_BinFileType;
end;

function NewServerEPS_BinFile: IXMLServerEPS_BinFileType;
begin
  Result := NewXMLDocument.GetDocBinding('ServerEPS_BinFile', TXMLServerEPS_BinFileType) as IXMLServerEPS_BinFileType;
end;

{ TXMLServerEPS_BinFileType }

procedure TXMLServerEPS_BinFileType.AfterConstruction;
begin
  RegisterChildNode('DebitBin', TXMLBinType);
  RegisterChildNode('FsaBin', TXMLBinType);
  RegisterChildNode('CreditGiftCardBin', TXMLBinType);
  RegisterChildNode('DCCBin', TXMLBinType);
  inherited;
end;

function TXMLServerEPS_BinFileType.Get_FormatVersion: string;
begin
  Result := AttributeNodes['FormatVersion'].Text;
end;

procedure TXMLServerEPS_BinFileType.Set_FormatVersion(Value: string);
begin
  SetAttribute('FormatVersion', Value);
end;

function TXMLServerEPS_BinFileType.Get_DebitBin: IXMLBinType;
begin
  Result := ChildNodes['DebitBin'] as IXMLBinType;
end;

function TXMLServerEPS_BinFileType.Get_FsaBin: IXMLBinType; 
begin
  Result := ChildNodes['FsaBin'] as IXMLBinType;
end;

function TXMLServerEPS_BinFileType.Get_PrepaidBin: IXMLBinType;
begin
  Result := ChildNodes['CreditGiftCardBin'] as IXMLBinType;
end;

function TXMLServerEPS_BinFileType.Get_DCCBin: IXMLBinType;
begin
  Result := ChildNodes['DCCBin'] as IXMLBinType;
end;

{ TXMLDebitBinType }

procedure TXMLBinType.AfterConstruction;
begin
  RegisterChildNode('Networks', TXMLNetworksType);
  inherited;
end;

function TXMLBinType.Get_UpdateType: WideString;
begin
  Result := AttributeNodes['UpdateType'].Text;
end;

procedure TXMLBinType.Set_UpdateType(Value: WideString);
begin
  SetAttribute('UpdateType', Value);
end;

function TXMLBinType.Get_BatchId: Integer;
begin
  Result := AttributeNodes['BatchId'].NodeValue;
end;

procedure TXMLBinType.Set_BatchId(Value: Integer);
begin
  SetAttribute('BatchId', Value);
end;

function TXMLBinType.Get_BatchDate: WideString;
begin
  Result := AttributeNodes['BatchDate'].Text;
end;

procedure TXMLBinType.Set_BatchDate(Value: WideString);
begin
  SetAttribute('BatchDate', Value);
end;

function TXMLBinType.Get_CompanyNumber: Integer;
begin
  Result := AttributeNodes['CompanyNumber'].NodeValue;
end;

procedure TXMLBinType.Set_CompanyNumber(Value: Integer);
begin
  SetAttribute('CompanyNumber', Value);
end;

function TXMLBinType.Get_Networks: IXMLNetworksType;
begin
  Result := ChildNodes['Networks'] as IXMLNetworksType;
end;

{ TXMLNetworksType }

procedure TXMLNetworksType.AfterConstruction;
begin
  RegisterChildNode('Network', TXMLNetworkType);
  ItemTag := 'Network';
  ItemInterface := IXMLNetworkType;
  inherited;
end;

function TXMLNetworksType.Get_Network(Index: Integer): IXMLNetworkType;
begin
  Result := List[Index] as IXMLNetworkType;
end;

function TXMLNetworksType.GetNetworkById(NetworkId: string): IXMLNetworkType; // DEV-16236
var
  i: integer;
  Network: IXMLNetworkType;
begin
  result := nil;
  for i := 0 to List.Count -1 do
  begin
    Network := List[i] as IXMLNetworkType;
    if SameText(Network.Id, NetworkId) then
    begin
      result := Network; 
      Exit;
    end;
  end;
end;

function TXMLNetworksType.Add: IXMLNetworkType;
begin
  Result := AddItem(-1) as IXMLNetworkType;
end;

function TXMLNetworksType.Insert(const Index: Integer): IXMLNetworkType;
begin
  Result := AddItem(Index) as IXMLNetworkType;
end;


{ TXMLNetworkType }

function TXMLNetworkType.Get_Id: WideString;
begin
  Result := AttributeNodes['Id'].Text;
end;

procedure TXMLNetworkType.Set_Id(Value: WideString);
begin
  SetAttribute('Id', Value);
end;

function TXMLNetworkType.Get_BinRanges: WideString;
begin
  Result := ChildNodes['BinRanges'].Text;
end;

procedure TXMLNetworkType.Set_BinRanges(Value: WideString);
begin
  ChildNodes['BinRanges'].NodeValue := Value;
end;

initialization
  ExtendedLog('BinXml Initialization');
finalization
  ExtendedLog('BinXml Finalization');
  
end.
