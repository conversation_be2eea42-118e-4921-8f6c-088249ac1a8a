// (c) MTXEPS, Inc. 1988-2008
unit StringUtils;
(******************************************************************************

  StringUtils

  Misc. string handling procedures and functions.

 ******************************************************************************
  Revision History
  ================
  2006-02-22 SRM
    - Unit created.
 ******************************************************************************)

interface

uses
  FinalizationLog,
  Classes,
  MTX_Types;

const
  JOIN_DEFAULT_DELIMITER       = ',';
  DEFAULT_RightJustify_PADCHAR  = ' ';

function Join(AItems: array of const; const ADelimiter: TMTXString = JOIN_DEFAULT_DELIMITER): TMTXString;
function MakeStringPrintable(const AStr: TMTXString): TMTXString;
function ArrayToStr(const A: array of TMTXByte): TMTXString; overload;
function ArrayToStr(const A: array of TMTXChar): TMTXString; overload;
function RightJustify(const AValue: TMTXString; const ALength: TMTXInteger; const APadChar: TMTXChar = DEFAULT_RightJustify_PADCHAR): TMTXString; overload;
function RightJustify(const AValue: TMTXInteger; const ALength: TMTXInteger; const APadChar: TMTXChar = DEFAULT_RightJustify_PADCHAR): TMTXString; overload;
// 828.5
// function RightJustify(const AValue: TMTXInt64; const ALength: TMTXInteger; const APadChar: TMTXChar = DEFAULT_RightJustify_PADCHAR): TMTXString; overload;
function ExtractString(const AStr: TMTXString; const ADelimChar: TMTXChar; AIndex: TMTXInteger): TMTXString;
function ReplaceString(const AStr, AFindStr, AReplaceStr: TMTXString; const ACaseSensitive: TMTXBoolean = False): TMTXString;
function ExtractValue(const aStr: TMTXString): TMTXString;  // extracts value from a string like [02]77MTXVER=320[03]f ---> 320
function IsAlphaNumericOnly(const aStr: TMTXString): Boolean;

implementation

uses
  SysUtils;

function Join(AItems: array of const; const ADelimiter: TMTXString): TMTXString;
const
  BoolChars: array[Boolean] of TMTXString = ('False', 'True');
var
  I: Integer;
begin
  Result:= '';
  for I:= Low(AItems) to High(AItems) do
  begin
    if Result <> '' then Result:= Result + ADelimiter;
    with AItems[I] do case VType of
        vtInteger:    Result := Result + IntToStr(VInteger);
        vtBoolean:    Result := Result + BoolChars[VBoolean];
        vtChar:       Result := Result + VChar;
        vtExtended:   Result := Result + FloatToStr(VExtended^);

        vtString:     Result := Result + VString^;
        vtPChar:      Result := Result + VPChar;
        vtObject:     Result := Result + VObject.ClassName;
        vtClass:      Result := Result + VClass.ClassName;
        vtAnsiString: Result := Result + string(VAnsiString);
        vtCurrency:   Result := Result + CurrToStr(VCurrency^);
        vtVariant:    Result := Result + string(VVariant^);
        vtInt64:      Result := Result + IntToStr(VInt64^);
    end;
  end;
end;

function MakeStringPrintable(const AStr: TMTXString): TMTXString;
const
  Labels: array[1..31] of TMTXString =
    ( '<SOH>',  '<STX>', '<ETX>',  '<EOT>', '<ENQ>',  '<ACK>', '<BELL>', '<BS>',
      '<TAB>',  '<LF>',  '<VT>',   '<FF>',  '<CR>',   '<SO>',  '<SI>',   '<DLE>',
      '<DC1>',  '<DC2>', '<DC3>',  '<DC4>', '<NAK>',  '<SYN>', '<ETB>',  '<CAN>',
      '<EM>',   '<SUB>', '<ESC>',  '<FS>',  '<GS>',   '<RS>',  '<US>' );
var
  CharPtr: PMTXChar;
  C: TMTXChar;
begin
  Result:= '';
  CharPtr:= PMTXChar(AStr);
  while CharPtr^ <> #0 do
  begin
    C:= CharPtr^;
    case Ord(C) of
      1..31: Result:= Result + Labels[Ord(C)];
    else
      Result:= Result + C;
    end;
    Inc(CharPtr);
  end;
end;

function ArrayToStr(const A: array of TMTXByte): TMTXString; overload;
var
  I: TMTXInteger;
begin
  Result:= '';
  for I:= Low(A) to High(A) do Result:= Result + Chr(A[I]);
end;

function ArrayToStr(const A: array of TMTXChar): TMTXString; overload;
var
  I: TMTXInteger;
begin
  Result:= '';
  for I:= Low(A) to High(A) do Result:= Result + A[I];
end;

function RightJustify(const AValue: TMTXString; const ALength: TMTXInteger; const APadChar: TMTXChar = DEFAULT_RightJustify_PADCHAR): TMTXString; overload;
var
  PadCount: TMTXInteger;
begin
  Result:= '';
  PadCount:= ALength - Length(AValue);
  while PadCount > 0 do
  begin
    Result:= Result + APadChar;
    Dec(PadCount);
  end;
  Result:= Result + AValue;
end;

function RightJustify(const AValue: TMTXInteger; const ALength: TMTXInteger; const APadChar: TMTXChar = DEFAULT_RightJustify_PADCHAR): TMTXString; overload;
begin
  Result:= RightJustify(IntToStr(AValue), ALength, APadChar);
end;

(*                    // 828.5
function RightJustify(const AValue: TMTXInt64; const ALength: TMTXInteger; const APadChar: TMTXChar = DEFAULT_RightJustify_PADCHAR): TMTXString; overload;
begin
  Result:= RightJustify(IntToStr(AValue), ALength, APadChar);
end;
*)

function ExtractValue(const aStr: TMTXString): TMTXString;  // extracts value from a string like [02]77MTXVER=320[03]f ---> 320
const
  ETX = #3;
var
  i: integer;
begin
  result := aStr;
  i := pos('=',result);
  if i > 0 then
    Delete(result,1,i);
  i := pos(ETX,result);     // if string has an ETX, then strip it and everything after it.
  if i > 0 then
    Delete(result,i,length(result));
end;

function ExtractString(const AStr: TMTXString; const ADelimChar: TMTXChar; AIndex: TMTXInteger): TMTXString;
var
  i,DelimCount: integer;
begin
  result := '';         //JTG refactor. Neither of above two works
  DelimCount := 1;
  i := 1;
  while i <= length(AStr) do
    begin
    if AStr[i] = ADelimChar then             //JTG: I just saw now that multiple spaces (Delimiters) will increment DelimCount!!
      inc(DelimCount)
    else if DelimCount = AIndex then
      result := result + AStr[i]
    else if DelimCount > AIndex then
      break;
    inc(i);
    end;
end;

function ReplaceString(const AStr, AFindStr, AReplaceStr: TMTXString; const ACaseSensitive: TMTXBoolean = False): TMTXString;
var
  I: Integer;
  TestStr: string;
begin
  Result:= AStr;
  I:= 1;
  repeat
    TestStr:= Copy(result, I, Length(AFindStr));
    if Lowercase(TestStr) = Lowercase(AFindStr) then begin
      Result:= Copy(Result, 1, I-1) + AReplaceStr + Copy(Result, I + Length(AFindStr), $7FFFFFFF);
      I := I + Length(AReplaceStr);
    end
    else
      Inc(I);
  until I > (Length(result) - Length(AFindStr) + 1);                            
end;

function IsAlphaNumericOnly(const aStr: TMTXString): Boolean;
var
  i: Integer;
begin
  for i := 1 to length(aStr) do
    if not (aStr[i] in ['0'..'9', 'A'..'Z', 'a'..'z']) then
    begin
      Result := false;
      exit;
    end;

  Result := true;
end;

initialization
  ExtendedLog('StringUtils Initialization');
finalization
  ExtendedLog('StringUtils Finalization');

end.
