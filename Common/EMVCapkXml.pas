
{*********************************************}
{                                             }
{           Delphi XML Data Binding           }
{                                             }
{         Generated on: 6/22/2012 10:39:30 AM }
{       Generated from: C:\temp\EMVCAPK.xml   }
{   Settings stored in: C:\temp\EMVCAPK.xdb   }
{                                             }
{*********************************************}
unit EMVCapkXml;

interface

uses
  FinalizationLog,
  xmldom, XMLDoc, XMLIntf, variants;

type

{ Forward Decls }

  IXMLEmvCapK = interface;
  IXMLRidType = interface;
  IXMLCapkType = interface;

{ IXMLEmvCapK }

  IXMLEmvCapK = interface(IXMLNodeCollection)
    ['{8EA3688B-05F0-45B1-8FAB-9476882D299A}']
    { Property Accessors }
    function Get_Xmlns: WideString;
    function Get_Rid(Index: Integer): IXMLRidType;
    function Get_FileIdentifier: WideString;                         // TFS-11731
    procedure Set_Xmlns(Value: WideString);
    procedure Set_FileIdentifier(Value: WideString);                 // TFS-11731
    { Methods & Properties }
    function Add: IXMLRidType;
    function Insert(const Index: Integer): IXMLRidType;
    property Xmlns: WideString read Get_Xmlns write Set_Xmlns;
    property Rid[Index: Integer]: IXMLRidType read Get_Rid; default;
    property FileIdentifier: WideString read Get_FileIdentifier write Set_FileIdentifier;   // TFS-11731
  end;

{ IXMLRidType }

  IXMLRidType = interface(IXMLNodeCollection)
    ['{E1BCA399-77B1-4899-975B-2F66F094F93D}']
    { Property Accessors }
    function Get_Id: WideString;
    function Get_Capk(Index: Integer): IXMLCapkType;
    procedure Set_Id(Value: WideString);
    { Methods & Properties }
    function Add: IXMLCapkType;
    function Insert(const Index: Integer): IXMLCapkType;
    property Id: WideString read Get_Id write Set_Id;
    property Capk[Index: Integer]: IXMLCapkType read Get_Capk; default;
  end;

{ IXMLCapkType }

  IXMLCapkType = interface(IXMLNode)
    ['{D2F36A06-833D-48E8-B41E-5992F40FECE4}']
    { Property Accessors }
    function Get_Id: WideString;
    function Get_Modulus: WideString;
    function Get_Exponent: WideString;
    function Get_Hash: WideString;
    procedure Set_Id(Value: WideString);
    procedure Set_Modulus(Value: WideString);
    procedure Set_Exponent(Value: WideString);
    procedure Set_Hash(Value: WideString);
    { Methods & Properties }
    property Id: WideString read Get_Id write Set_Id;
    property Modulus: WideString read Get_Modulus write Set_Modulus;
    property Exponent: WideString read Get_Exponent write Set_Exponent;
    property Hash: WideString read Get_Hash write Set_Hash;
  end;

{ Forward Decls }

  TXMLEmvType = class;
  TXMLRidType = class;
  TXMLCapkType = class;

{ TXMLEmvType }

  TXMLEmvType = class(TXMLNodeCollection, IXMLEmvCapK)
  protected
    { IXMLEmvCapK }
    function Get_Xmlns: WideString;
    function Get_Rid(Index: Integer): IXMLRidType;
    function Get_FileIdentifier: WideString;                                // TFS-11731
    procedure Set_Xmlns(Value: WideString);
    procedure Set_FileIdentifier(Value: WideString);                        // TFS-11731
    function Add: IXMLRidType;
    function Insert(const Index: Integer): IXMLRidType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLRidType }

  TXMLRidType = class(TXMLNodeCollection, IXMLRidType)
  protected
    { IXMLRidType }
    function Get_Id: WideString;
    function Get_Capk(Index: Integer): IXMLCapkType;
    procedure Set_Id(Value: WideString);
    function Add: IXMLCapkType;
    function Insert(const Index: Integer): IXMLCapkType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLCapkType }

  TXMLCapkType = class(TXMLNode, IXMLCapkType)
  protected
    { IXMLCapkType }
    function Get_Id: WideString;
    function Get_Modulus: WideString;
    function Get_Exponent: WideString;
    function Get_Hash: WideString;
    procedure Set_Id(Value: WideString);
    procedure Set_Modulus(Value: WideString);
    procedure Set_Exponent(Value: WideString);
    procedure Set_Hash(Value: WideString);
  end;

{ Global Functions }

function GetEmvCapK(Doc: IXMLDocument): IXMLEmvCapK;
function LoadEmvCapK(const FileName: WideString): IXMLEmvCapK;
function NewEmvCapK: IXMLEmvCapK;

implementation

{ Global Functions }

function GetEmvCapK(Doc: IXMLDocument): IXMLEmvCapK;
begin
  Result := Doc.GetDocBinding('Emv', TXMLEmvType) as IXMLEmvCapK;
end;
function LoadEmvCapK(const FileName: WideString): IXMLEmvCapK;
begin
  Result := LoadXMLDocument(FileName).GetDocBinding('Emv', TXMLEmvType) as IXMLEmvCapK;
end;

function NewEmvCapK: IXMLEmvCapK;
begin
  Result := NewXMLDocument.GetDocBinding('Emv', TXMLEmvType) as IXMLEmvCapK;
end;

{ TXMLEmvType }

procedure TXMLEmvType.AfterConstruction;
begin
  RegisterChildNode('Rid', TXMLRidType);
  ItemTag := 'Rid';
  ItemInterface := IXMLRidType;
  inherited;
end;

function TXMLEmvType.Get_Xmlns: WideString;
begin
  Result := AttributeNodes['xmlns'].Text;
end;

procedure TXMLEmvType.Set_FileIdentifier(Value: WideString);        // TFS-11731
begin
  SetAttribute('FileIdentifier', Value);
end;

procedure TXMLEmvType.Set_Xmlns(Value: WideString);
begin
  SetAttribute('xmlns', Value);
end;

function TXMLEmvType.Get_FileIdentifier: WideString;                // TFS-11731
begin
  Result := AttributeNodes['FileIdentifier'].Text;
end;

function TXMLEmvType.Get_Rid(Index: Integer): IXMLRidType;
begin
  Result := List[Index] as IXMLRidType;
end;

function TXMLEmvType.Add: IXMLRidType;
begin
  Result := AddItem(-1) as IXMLRidType;
end;

function TXMLEmvType.Insert(const Index: Integer): IXMLRidType;
begin
  Result := AddItem(Index) as IXMLRidType;
end;


{ TXMLRidType }

procedure TXMLRidType.AfterConstruction;
begin
  RegisterChildNode('Capk', TXMLCapkType);
  ItemTag := 'Capk';
  ItemInterface := IXMLCapkType;
  inherited;
end;

function TXMLRidType.Get_Id: WideString;
begin
  Result := AttributeNodes['id'].Text;
end;

procedure TXMLRidType.Set_Id(Value: WideString);
begin
  SetAttribute('id', Value);
end;

function TXMLRidType.Get_Capk(Index: Integer): IXMLCapkType;
begin
  Result := List[Index] as IXMLCapkType;
end;

function TXMLRidType.Add: IXMLCapkType;
begin
  Result := AddItem(-1) as IXMLCapkType;
end;

function TXMLRidType.Insert(const Index: Integer): IXMLCapkType;
begin
  Result := AddItem(Index) as IXMLCapkType;
end;


{ TXMLCapkType }

function TXMLCapkType.Get_Id: WideString;
begin
  Result := AttributeNodes['id'].Text;
end;

procedure TXMLCapkType.Set_Id(Value: WideString);
begin
  SetAttribute('id', Value);
end;

function TXMLCapkType.Get_Modulus: WideString;
begin
  Result := ChildNodes['Modulus'].Text;
end;

procedure TXMLCapkType.Set_Modulus(Value: WideString);
begin
  ChildNodes['Modulus'].NodeValue := Value;
end;

function TXMLCapkType.Get_Exponent: WideString;
begin
  Result := ChildNodes['Exponent'].Text;
end;

procedure TXMLCapkType.Set_Exponent(Value: WideString);
begin
  ChildNodes['Exponent'].NodeValue := Value;
end;

function TXMLCapkType.Get_Hash: WideString;
begin
  Result := ChildNodes['Hash'].Text;
end;

procedure TXMLCapkType.Set_Hash(Value: WideString);
begin
  ChildNodes['Hash'].NodeValue := Value;
end;

initialization
  ExtendedLog('EMVCapkXml Initialization');
finalization
  ExtendedLog('EMVCapkXml Finalization');

end.
