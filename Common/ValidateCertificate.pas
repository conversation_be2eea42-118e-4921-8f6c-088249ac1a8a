unit ValidateCertificate;

interface

{$UNDEF APL_OR_DIALBACKUP}

{$IFDEF APL}
  {$DEFINE APL_OR_DIALBACKUP}
{$ENDIF}

{$IFDEF DIALBACKUP}
  {$DEFINE APL_OR_DIALBACKUP}
{$ENDIF}

uses
  FinalizationLog,
  SysUtils, Classes, TypInfo,
  {$IFDEF INDYSSL} // 6208: was {$IFNDEF WOLF}
  SBConstants, SBX509, SBX509Ext, SBPKCS12, SBCustomCertStorage,
  SBRDN, SBMessages, SBUtils,
  SBTypes, SBCertValidator, CustomTransports,
  SBWinCertStorage,
  {$ENDIF}

{$IFDEF USE_INDY}
  IdHttp, IdURI,
{$ENDIF}
{$IFDEF INDYSSL}
  ServerEPSHelperLogger, // LOGGING
  SBSSLConstants, <PERSON>SL<PERSON>ommon, <PERSON><PERSON>ndy<PERSON>Handler10,
{$ENDIF}
  MTX_Constants
  ;

type
  TValidateCertResult = (vrUnknown=0, vrTrusted, vrUnTrusted, vrExpired);
  TCheckRevocationType = (crtNone=0, crtCRL, crtOCSP);

  {$HINTS OFF}
  TValidateCert = class
  private
    UntrustedStorageList: TStringList;
    TrustedCertList: TStringList;
    ValidatorOptionsLogged: boolean;
    FCheckRevocationType: TCheckRevocationType;
    function ValidateCertDate(Cert: TELx509Certificate) : boolean;
    function ValidateCertificateEx(Cert: TElX509Certificate): TValidateCertResult; // for internal products
  public
    StorageList: TStringList; // public for EncryptCert app
    constructor Create; overload;
    destructor Destroy; override;
    function ValidateCertificateInternal(Cert: TElX509Certificate): boolean; // for internal products
    function ValidateCertificate(Cert: TElX509Certificate; DomainName: string=''; IPAddress: string=''): boolean;
  end;
  {$HINTS ON}

  TSSLUtils = class
  public
    class function GetEncryptionAlghorithm(Algorithm : integer) : string;
    class function GetCertDisplayName(Cert : TelX509Certificate) : string;
    class procedure PrintCertificateListInStorage(StorageList: TStringList);
    class function SBByteArrayToStr(Source: ByteArray): AnsiString;
    class function SBByteArrayToHexStr(Source: ByteArray): AnsiString;
    {$IFDEF USE_INDY}
    class procedure ParseURI(AURI: string; var VProtocol, VHost, VPath, VDocument, VPort, VBookmark : string);
    {$ENDIF}
    {$IFDEF INDYSSL}
    class procedure SetSSLOptions(SSLIOHandler: TElClientIndySSLIOHandlerSocket; FuncName: string='');
    class procedure SetSSLIOHandler(IndyHttp: TIdHttp;
        SSLIOHandler: {$IFDEF INDY9}TElIndySSLIOHandlerSocket{$ELSE}TElClientIndySSLIOHandlerSocket{$ENDIF};
        OnCertificateValidate: TSBCertificateValidateEvent;
        OnSSLEstablished: TSBSSLEstablishedEvent;
        OnError: TSBErrorEvent;
        FuncName: string='');
    {$ENDIF}
  end;

{$IFDEF INDYSSL}
const
  {$I certstorage.inc}
{$ENDIF}

var
  ValidateCert: TValidateCert;

function ValidateCertificateWithCRL(Cert: TElX509Certificate; IsPublicCert: boolean=false; DomainName: string=''; IPAddress: string=''): boolean;

implementation

uses
  MTXEncryptionUtils,
  ServerEpsConstants,
  MRTypes,               // CPCLIENTS-14502
  MTX_Lib;

function ValidateCertificateWithCRL(Cert: TElX509Certificate; IsPublicCert: boolean=false; DomainName: string=''; IPAddress: string=''): boolean;
begin
  if NOT Assigned(ValidateCert) then
    ValidateCert := TValidateCert.Create;
  if IsPublicCert then
    result := ValidateCert.ValidateCertificate(Cert, DomainName, IPAddress)
  else
    result := ValidateCert.ValidateCertificateInternal(Cert);
  SM('ValidateCertificate result = ' + BoolToStr(result, true), IS_NOT_IN_TRANS);      // CPCLIENTS-14502
end;

procedure SM(msg: string; IsInTrans: TIsInTrans = iitTrue);   // CPCLIENTS-14502
begin
  MTX_Lib.SM('[Certificate] ' + msg, IsInTrans);              // CPCLIENTS-14502
end;

constructor TValidateCert.Create;
var
  I, CertCount: integer;
  proxyInUse: boolean;
  proxyHost: string;
  proxyPort: integer;
  tmpCheckCRL: string;

{$IFDEF MSWINDOWS}
  procedure LoadWinStorage(StorageName: string);
  var
    Storage: TElWinCertStorage;
  begin
    try
      SM('Loading cert storage ... ' + StorageName, IS_NOT_IN_TRANS);  // CPCLIENTS-14502
      Storage := TElWinCertStorage.Create(nil);
      Storage.SystemStores.Clear;
      Storage.SystemStores.Add(StorageName);
      StorageList.AddObject(StorageName, Storage);
      CertCount := CertCount + Storage.Count;
      SM(IntToStr(Storage.Count) + ' loaded', IS_NOT_IN_TRANS);        // CPCLIENTS-14502
    except
      on e: exception do
        SM('TValidateCert.Create LoadWinStorage - Failed to load certificates - ' + e.message, IS_NOT_IN_TRANS);    // CPCLIENTS-14502
    end;
  end;
{$ENDIF}

{$IFDEF INDYSSL}
  procedure LoadCertStorageFromFile(FileName: string; IsEncrypted: boolean = false);
  var
    Storage: TElFileCertStorage;
    FileStream : TFileStream;
    MemoryStream : TMemoryStream;
    StringStream : TStringStream;
    tmpStr: AnsiString;
  begin
    try
      SM('Loading file ... ', IS_NOT_IN_TRANS);     // CPCLIENTS-14502
      Storage := TElFileCertStorage.Create(nil);
      if NOT IsEncrypted then
        Storage.FileName := FileName
      else
      begin
        tmpStr := '';
        FileStream := TFileStream.Create(FileName, fmOpenRead);
        StringStream := TStringStream.Create('');
        try
          StringStream.LoadFromStream(FileStream);
          tmpStr := MTXEncryptionUtils.DecryptString(StringStream.DataString);
        finally
          FileStream.Free;
          StringStream.Free;
        end;
        // TODO: pass StringStream to LoadFromStreamPKCS7?
        if tmpStr <> '' then
        begin
          MemoryStream := TMemoryStream.Create();
          try
            MemoryStream.Write(Pointer(tmpStr)^, Length(tmpStr));
            MemoryStream.Position := 0;
            Storage.LoadFromStreamPKCS7(MemoryStream);
          finally
            MemoryStream.Free;
          end;
        end;
      end;
      StorageList.AddObject('TRUSTED', Storage);
      CertCount := Storage.Count;
    except
      on e: exception do
        SM('TValidateCert.Create LoadCertStorageFromFile - Failed to load certificates - ' + e.message, IS_NOT_IN_TRANS);   // CPCLIENTS-14502
    end;
  end;

  procedure LoadCertStorageFromResource;
  var
    Storage: TElFileCertStorage;
    hFind: THandle;
    ResourceStream: TResourceStream;
    MemoryStream: TMemoryStream;
    tmpStr: AnsiString;
  begin
    try
      SM('Loading cert resource ... ', IS_NOT_IN_TRANS);    // CPCLIENTS-14502
      Storage := TElFileCertStorage.Create(nil);

      tmpStr := DecryptString(ProductionCertStorage);
      MemoryStream :=TMemoryStream.Create();
      try
        MemoryStream.Write(Pointer(tmpStr)^, Length(tmpStr));
        MemoryStream.Position := 0;
        Storage.LoadFromStreamPKCS7(MemoryStream);
      finally
        MemoryStream.Free;
      end;

      StorageList.AddObject('TRUSTED', Storage);
      CertCount := Storage.Count;
    except
      on e: exception do
        SM('TValidateCert.Create LoadCertStorageFromFile - Failed to load certificates - ' + e.message, IS_NOT_IN_TRANS);   // CPCLIENTS-14502
    end;
  end;
{$ENDIF INDYSSL}

begin
  inherited;
  //SM('Loading Certificates...');
  CertCount := 0;

{$IFDEF APL_OR_DIALBACKUP}
  SM('TValidateCert.Create - CheckCRL in setup.txt', IS_NOT_IN_TRANS);     // CPCLIENTS-14502
  tmpCheckCRL := uppercase(MTX_Lib.Reg_Lookup(SetupTxt, 'CheckCRL', true));    //26920
{$ELSE}
  tmpCheckCRL := uppercase(MTX_Lib.Reg_Lookup(DefaultDir+OpenEPSIni, 'CheckCRL', true));  //26920
{$ENDIF}
  if tmpCheckCRL.Equals('Y') then
    FCheckRevocationType := crtCRL
  else if tmpCheckCRL.Equals('O') then
    FCheckRevocationType := crtOCSP
  else // by default N
    FCheckRevocationType := crtNone;
  SM('TValidateCert.Create - CheckCRL=' + tmpCheckCRL, IS_NOT_IN_TRANS);   // CPCLIENTS-14502

  proxyInUse := false;
  proxyHost := '';
  proxyPort := 0;

  StorageList := TStringList.Create;
  TrustedCertList := TStringList.Create;
  try
{$IFNDEF INDYSSL}
  {$IFDEF MSWINDOWS}
    LoadWinStorage('ROOT');
    LoadWinStorage('CA');
  {$ENDIF}
{$ELSE}
    if FileExists(DefaultDir + LINUX_CERT_STORAGE) then
    begin
      SM('>>>>>>>>>>>>>>> OpenEPS will use certificate storage in ' + DefaultDir, IS_NOT_IN_TRANS);   // CPCLIENTS-14502
      LoadCertStorageFromFile(DefaultDir + LINUX_CERT_STORAGE, true);
    end
    else
      LoadCertStorageFromResource;
{$ENDIF}
    SM(IntToStr(CertCount) + ' certificate(s) loaded', IS_NOT_IN_TRANS);    // CPCLIENTS-14502
{$IFNDEF LOGGING}
    if SameText(Reg_Lookup(DefaultDir+OpenEPSIni, 'PrintCertificate', false), 'Y') then
{$ENDIF}
      TSSLUtils.PrintCertificateListInStorage(StorageList);

    if FCheckRevocationType = crtCRL then
      RegisterHTTPCRLRetrieverFactory(proxyInUse,proxyHost,proxyPort);
    if FCheckRevocationType = crtOCSP then
      RegisterHTTPOCSPClientFactory;

    proxyInUse := StrToBoolDef(MTX_Lib.Reg_Lookup(DefaultDir+SETUPTXT,_AlwaysUseProxy, false), false);
    if proxyInUse then
    begin
      //Lines copied from ServerEPS function GetProxyServer: string;
      {$IFDEF ENGINE_NOT_FUEL}
        proxyHost := MTX_Lib.Reg_Lookup(DefaultDir+REGISTRYMTX,_ProxyServer,false);
      {$ELSE}
        proxyHost := MTX_Lib.Reg_Lookup(DefaultDir+SETUPTXT,_ProxyServer,false);
      {$ENDIF}

       proxyPort := StrToIntDef(proxyHost.SubString((proxyHost.LastIndexOf(':')+1)),0);
       proxyHost := proxyHost.SubString(0,proxyHost.LastIndexOf(':'));

       if proxyHost.indexOf(':') <> -1 then
       begin
         proxyHost := proxyHost.SubString(proxyHost.IndexOf(':')+3);
       end;
    end;

    SM('Configibg CRLProxy Settings EnableProxy:' + IntToStr(Integer(proxyInUse)) + ' ProxyHost:' + proxyHost + ' ProxyPort:' + IntToStr(proxyPort),
          IS_NOT_IN_TRANS);   // CPCLIENTS-14502
    RegisterHTTPCRLRetrieverFactory(proxyInUse,proxyHost,proxyPort);
    //RegisterHTTPOCSPClientFactory;
  except
    on e: exception do
      SM('Try..Except TValidateCert.Create - ' + e.message, IS_NOT_IN_TRANS);    // CPCLIENTS-14502
  end;
end;

destructor TValidateCert.Destroy;
begin
  SM('TValidateCert.Destroy ...', IS_NOT_IN_TRANS);    // CPCLIENTS-14502
  if Assigned(StorageList) then
  begin
    StorageList.Clear;
    FreeAndNil(StorageList);
  end;
  if Assigned(TrustedCertList) then
  begin
    TrustedCertList.Clear;
    FreeAndNil(TrustedCertList);
  end;

  if FCheckRevocationType = crtCRL then
    UnregisterHTTPCRLRetrieverFactory;
  if FCheckRevocationType = crtOCSP then
    UnregisterHTTPOCSPClientFactory;
  inherited;
end;

function TValidateCert.ValidateCertDate(Cert : TELx509Certificate) : boolean;
begin
  Result := Cert.ValidTo >= Date;
end;

function TValidateCert.ValidateCertificateInternal(Cert: TElX509Certificate): boolean; // DEV-11196
var tmpResult: TValidateCertResult;
begin
  result := false;
  try
    if NOT Assigned(ValidateCert) then
      ValidateCert := TValidateCert.Create;
    if NOT Assigned(ValidateCert) then
      exit;

    if ValidateCert.TrustedCertList.IndexOf(TSSLUtils.SBByteArrayToStr(Cert.Signature)) > -1 then
    begin
      result := true;
      Exit;
    end;

    tmpResult := ValidateCert.ValidateCertificateEx(Cert);
    result := tmpResult = vrTrusted;
{$IFDEF TCP}
    if NOT result then
      SM('Bypass invalid host certificate', IS_NOT_IN_TRANS);    // CPCLIENTS-14502
    ValidateCert.TrustedCertList.Add(TSSLUtils.SBByteArrayToStr(Cert.Signature));
    result := true;
{$ENDIF}
  except
    on e: exception do
    SM('Try..Except ValidateCertificate - ' + e.message, IS_NOT_IN_TRANS);    // CPCLIENTS-14502
  end;
end;

function TValidateCert.ValidateCertificate(Cert: TElX509Certificate; DomainName: string=''; IPAddress: string=''): boolean;
var
  I: integer;
  Validator: TElX509CertificateValidator;
  Validity : TSBCertificateValidity;
  Reason : TSBCertificateValidityReason;

  function ReasonToString: string;
  begin
    Result := '';
    if vrBadData in Reason then
      Result := Result + 'bad data,';
    if vrRevoked in Reason then
      Result := Result + 'revoked,';
    if vrNotYetValid in Reason then
      Result := Result + 'yet not valid,';
    if SBX509.vrExpired in Reason then
      Result := Result + 'expired,';
    if vrInvalidSignature in Reason then
      Result := Result + 'invalid sign.,';
    if vrUnknownCA in Reason then
      Result := Result + 'unknown CA,';
    if vrCAUnauthorized in Reason then
      Result := Result + 'CA unauthorized,';
    if vrCRLNotVerified in Reason then
      Result := Result + 'CRL not verified,';
    if vrOCSPNotVerified in Reason then
      Result := Result + 'OCSP not verified,';
    if vrIdentityMismatch in Reason then
      Result := Result + 'identity mismatch,';
    if vrNoKeyUsage in Reason then
      Result := Result + 'no key usage,';
    if vrBlocked in Reason then
      Result := Result + 'blocked,';

    Delete(Result, Length(Result), 1);
  end;
begin
  result := false;
  if NOT Assigned(Cert) then
    Exit;
  try
    Validator := TElX509CertificateValidator.Create(nil);
    try
      for i := 0 to StorageList.Count - 1 do
      begin
        Validator.AddTrustedCertificates(StorageList.Objects[i] as TElCustomCertStorage);
        //SM(Format('ValidateCertificate - AddTrustedCertificates (Count=%d)', [(StorageList.Objects[i] as TElCustomCertStorage).Count]));
      end;
      {
      CheckCRL property specifies if CRLs must be retrieved and used for validation of certificates.
      For each CRL Distribution point found in the certificate,
      the corresponding CRL is first searched in the list of known CRLs, then requested from the specified location.
      Before the CRL is requested, OnBeforeCRLRetrieverUse event is fired. The default value is True.
      }
      if FCheckRevocationType = crtCRL then
      begin
        Validator.CheckCRL := true; // default
        SM('ValidateCertificate - Checking CRL (MandatoryRevocationCheck)', IS_NOT_IN_TRANS);    // CPCLIENTS-14502
	    end
      else
      begin
        Validator.CheckCRL := false;
        SM('ValidateCertificate - NO CRL Checking', IS_NOT_IN_TRANS);   // CPCLIENTS-14502
      end;

      {
      CheckOCSP property specifies if OCSP must be used for validation of certificates.
      First the list of known OCSP responses is searched for certificate status.
      If no valid OCSP response is found and AuthorityInformationAccess certifiate extension is present,
      information in this extension is used for locating and connecting to OCSP server.
      Before the OCSP server is contacted, OnBeforeOCSPClientUse event is fired. The default value is True.
      }
      if FCheckRevocationType = crtOCSP then
        Validator.CheckOCSP := true // default
      else
        Validator.CheckOCSP := false;
      {
      CheckValidityPeriodForTrusted property specifies if validity period check should be performed for certificates,
      which are treated as trusted.
      }
      Validator.CheckValidityPeriodForTrusted := true; // default
      {
      ForceCompleteChainValidationForTrusted property specifies if the certificate chain down to CA certificate should be validated for trusted certificates.
      The default value is True.
      }
      Validator.ForceCompleteChainValidationForTrusted := true; // default
      {
      ForceRevocationCheckForRoot property specifies if root certificate revocation check should be performed. The default value is True.
      }
      if FCheckRevocationType = crtCRL then
        Validator.ForceRevocationCheckForRoot := true // default
      else
        Validator.ForceRevocationCheckForRoot := false;
      {
      Set IgnoreBadOCSPChains property to True to force the validator to ignore bad OCSP chains during validation. The default value is False.
      }
      Validator.IgnoreBadOCSPChains := false; // default
      {
      not documented
      }
      Validator.IgnoreCABasicConstraints := false; // default
      {
      The RFC 6125 requires that SubjectAltName (SAN) extension should be checked if it is present,
      and if it is, then SubjectName.CommonName (CN) should not be checked.
      This contradicts to some existing certificates where, for instance, a.com is put in CN and www.a.com is put to SAN.
      Therefore, the new behavior is the default option in SecureBlackbox, which can be disabled
      if the old way of checking both SAN and CN is needed. The default value is True.
      }
      Validator.IgnoreCAKeyUsage := false; // default
      {
      not documented
      }
      Validator.IgnoreCANameConstraints := false; // default
      {
      IgnoreRevocationKeyUsage property specifies whether the issuer (CA) certificates should be checked,
      if their key usage extension (when available) allows use of such certificates for signing revocation information.
      The default value is False.
      }
      Validator.IgnoreRevocationKeyUsage := false; // default
      {
      IgnoreSSLKeyUsage property specifies whether the issuer (CA) certificates should be checked,
      if their key usage extension (when available) allows use of such certificates in SSL protocol communications.
      The default value is False.
      }
      Validator.IgnoreSSLKeyUsage := false; // default
      {
      IgnoreSystemTrust property specifies whether trusted Windows Certificate Stores should be treated as trusted
      (i.e. no validation check is performed for such certificates) or as known certificates.
      If IgnoreSystemTrust = true, then such certificates are validated when they are found in trusted Windows Certificate Stores.
      If IgnoreSystemTrust = false, then certificates found in trusted Windows Certificate Stores are not validated,
      and are explicitly treated as trusted.
      Note that this property doesn't affect use of Trusted Certificates, added using AddTrustedCertificates() method.
      }
      Validator.IgnoreSystemTrust := true; // default is false / not for custom CA, for Windows CA
      {
      Set ImplicitlyTrustSelfSignedCertificates property to True to force the validator to implicitly trust all self-signed certificates.
      This mode is mostly used to only collect revocation information, but not to validate it. The default value is False.
      }
      Validator.ImplicitlyTrustSelfSignedCertificates := false; // default
      {
      not documented
      }
      Validator.LookupCRLByNameIfDPNotPresent := false; // default is true
      {
      MandatoryCRLCheck property affects result of certificate validation when CRL retrieval fails.
      If MandatoryCRLCheck = true and the CRL can not be retrieved, the certificate is reported to be invalid.
      If MandatoryCRLCheck = false and the CRL can not be retrieved, certificate validation continues.
      In both cases, when the CRL can not be retrieved, vrCRLNotVerified flag is included to Reason status of certificate.
      The default value is True.
      }
      Validator.MandatoryCRLCheck := true;
      {
      MandatoryOCSPCheck property affects result of certificate validation when OCSP status check fails.
      If MandatoryOCSPCheck = true and OCSP status check can not be performed, the certificate is reported to be invalid.
      If MandatoryOCSPCheck = false and OCSP status check can not be performed, certificate validation continues.
      In both cases, when OCSP status check can not be performed, vrOCSPNotVerified flag is included to Reason status of certificate.
      The default value is True.
      }
      Validator.MandatoryOCSPCheck := true; // default
      {
      MandatoryRevocationCheck property specifies if mandatory revocation check should be performed.
      A mandatory revocation check requires that at least one type of revocation information,
      CRL-based or OCSP-based, should be retrieved and checked. The default value is True.
      }
      Validator.MandatoryRevocationCheck := true; // default
      {
      Set OfflineMode property to True to make TElX509CertificateValidator working in offline mode.
      In this mode it is impossible to retrieve certificate and revocation information from remote locations. The default value is False.
      }
      Validator.OfflineMode := false; // default
      {
      Set PromoteLongOCSPResponses property to True to force the validator to publish 'long' form of OCSP responses.
      Otherwise, only BasicOCSPResponse blobs are promoted. The default value is False.
      }
      Validator.PromoteLongOCSPResponses := false; // default
      {
      Use RevocationCheckPreference property to tell the component how it should deal with revocation information sources.
      rcpPreferCRL = 0:  always try to retrieve the CRL first. If failed to retrieve the CRL, get status from the OCSP server.
      rcpPreferOCSP = 1: always try to get the OCSP status first. If failed to get the OCSP status, try to download the CRL.
      rcpCheckBoth = 2: always download the CRL and get the OCSP status, and check revocation against both of them.
      }
      case FCheckRevocationType of
        crtCRL:  Validator.RevocationCheckPreference := rcpPreferCRL;
        crtOCSP: Validator.RevocationCheckPreference := rcpPreferOCSP;
        else     Validator.RevocationCheckPreference := rcpCheckBoth; // default
      end;
      {
      Use RevocationMomentGracePeriod property to specify the grace period in seconds.
      Grace period allows the certificate revocation information to propagate through the revocation process.
      The default value is 60 (1 minute).
      }
      Validator.RevocationMomentGracePeriod := 60; // default
      {
      The RFC 6125 requires that SubjectAltName (SAN) extension should be checked if it is present,
      and if it is, then SubjectName.CommonName (CN) should not be checked.
      This contradicts to some existing certificates where, for instance, a.com is put in CN and www.a.com is put to SAN.
      Therefore, the new behavior is the default option in SecureBlackbox,
      which can be disabled if the old way of checking both SAN and CN is needed. The default value is True.
      }
      Validator.SkipSubjectNameIfAltNameExists := true; // default
      {
      Set UseSystemStorages property to true on Windows to tell the component, that it needs to include Windows Certificate Stores
      when searching for CA and root certificates and when checking if the certificate is blocked or trusted.
      Note, that use of Windows stores slows down certificate validation because it takes time to check
      if certain certificate is in the store.

      Set this property to false if you maintain your own certificate lists and don't want to spend extra time
      searching for certificaets in Windows Certificate Stores.
      }
      Validator.UseSystemStorages := false; // default is true
      {
      If ValidateInvalidCertificates = true and certain certificate check determines that the certificate is not valid,
      further checks are done anyway. This lets you create validation report for the client
      which should include and all checks for all certificates in the chain.

      When ValidateInvalidCertificates = false and the certificate is found to be not valid,
      further checks of this certificate are not performed.
      }
      Validator.ValidateInvalidCertificates := false;

      Validity := cvInvalid;
      Reason := [];
      {
      procedure ValidateForSSL(
          Certificate : TElX509Certificate;
          DomainName : string;
          IPAddress : string;
          HostRole : TSBHostRole;
          AdditionalCertificates : TElCustomCertStorage;
          CompleteChainValidation : boolean;
          ValidityMoment : TDateTime;
          var Validity : TSBCertificateValidity;
          var Reason : TSBCertificateValidityReason);

      Parameters
          AdditionalCertificates - Additional certificates that might be known or obtained during the handshake.
          Certificate - Certificate to be validated.
          CompleteChainValidation - Specifies whether to check issuer (CA) certificates when the certificate is invalid.
          DomainName - Domain name of the host, whose certificate is validated. Can be empty, if it's not known.
          HostRole - The role of the remote host. Can be none (then the key usage is not checked against the role), server, client or both.
          IPAddress - IP address of the host, whose certificate is validated. CAn be empty if it's not known.
          InternalValidation - specifies if internal validation should be performed.
          Reason - On return contains validity status reasons of the certificate.
          Validity - On return contains validity status of the certificate.
          ValidityMoment - Specifies the time when the certificate must be valid (i.e. the moment of handshake).
          ResetCertificateCache - When the certificates are validated,
              they are also added to the cache which is used to save the component
              from validating the same certificate chains again while the process is active.
              Using ResetCertificateCache method you can remove all certificates
              from the cache and on the next validation all certificates will be validated again.
              It makes sense to use this method periodically if you have a long-running process (a service or constantly operating application).

      Description
          Use this method to validate the certificate received during SSL / TLS handshake (e.g., via OnCertificateValidate event of SSL components).
          You need to pass the end-entity certificate only - CA certificates will be retrieved via Certificate's Chain property.
          So the event handler for OnCertificateValidate should look like this (in pseudocode):

          If the chain is not available, you can pass CA certificates via AdditionalCertificates parameter or using AddKnownCertificates() method.
          The method checks whether the certificate subject and names correspond to given domain name or IP address.
          Next certificate's Key Usage fields are checked to ensure that the certificate may be used for TLS handshake.
          Finally, Validate() method is called to validate the certificate itself.

          Domain name and IP address of the other party can be obtained via RemoteHost
          and RemoteIP properties of SSL classes (TElHTTPSClient, TElSimpleFTPSClient, TElSMTPClient).

          When CompleteChainValidation = true and the certificate is found to be not valid, certificate validation continues,
          i.e. issuer (CA) certificates are validated as well. This lets you create validation report which should include all certificates in the chain.
          When CompleteChainValidation = false and the certificate is not valid,
          further validation is not performed and Validate() method returns immediately.

          Since version 13 ResetCertificateCache parameter has been replaced with ResetCertificateCache class method.
      }
      SM(Format('ValidateCertificate - DomainName=%s, IPAddress=%s', [DomainName, IPAddress]), IS_NOT_IN_TRANS);   // CPCLIENTS-14502
{$IFDEF SBB_VER_16_UP}
	    Validator.ValidateForSSL(Cert, DomainName, '', hrServer, nil, true, Now, Validity, Reason);
{$ELSE}
      Validator.ValidateForSSL(Cert, DomainName, '', hrServer, nil, true, true, Now, Validity, Reason); // SBB v12
{$ENDIF}
      //Validator.Validate(Cert, Validity, Reason);
      result := Validity = cvOk;
      SM(Format('ValidateCertificate - CN=%s, Validity=%s, Reason=%s',
          [Cert.SubjectName.CommonName,
           GetEnumName(TypeInfo(TSBCertificateValidity), integer(Validity)),
           ReasonToString]), IS_NOT_IN_TRANS);    // CPCLIENTS-14502

      // CPCLIENTS-6461 - CRL Check Logging
      if (not Validator.CheckCRL) then
        SM('CRL Check = Not Attempted', IS_NOT_IN_TRANS)   // CPCLIENTS-14502
      else if (Validity = cvOk) or ((Validity <> cvOk) and not (vrCRLNotVerified in Reason)) then
        SM('CRL Check = Success', IS_NOT_IN_TRANS)         // CPCLIENTS-14502
      else if vrCRLNotVerified in Reason then
        SM('CRL Check = Failed', IS_NOT_IN_TRANS);         // CPCLIENTS-14502

      if not ValidatorOptionsLogged then
      begin
        SM('--------------------------------------------------------------------------------', IS_NOT_IN_TRANS);                      // CPCLIENTS-14502 start
        SM('ValidateCertificate - Validator Options', IS_NOT_IN_TRANS);
        SM('--------------------------------------------------------------------------------');
        SM('        CheckCRL='+BooleanStrings[Validator.CheckCRL], IS_NOT_IN_TRANS);
        SM('        CheckOCSP='+BooleanStrings[Validator.CheckOCSP], IS_NOT_IN_TRANS);
        SM('        CheckValidityPeriodForTrusted='+BooleanStrings[Validator.CheckValidityPeriodForTrusted], IS_NOT_IN_TRANS);
        SM('        ForceCompleteChainValidationForTrusted='+BooleanStrings[Validator.ForceCompleteChainValidationForTrusted], IS_NOT_IN_TRANS);
        SM('        ForceRevocationCheckForRoot='+BooleanStrings[Validator.ForceRevocationCheckForRoot], IS_NOT_IN_TRANS);
        SM('        IgnoreBadOCSPChains='+BooleanStrings[Validator.IgnoreBadOCSPChains], IS_NOT_IN_TRANS);
        SM('        IgnoreCABasicConstraints='+BooleanStrings[Validator.IgnoreCABasicConstraints], IS_NOT_IN_TRANS);
        SM('        IgnoreCAKeyUsage='+BooleanStrings[Validator.IgnoreCAKeyUsage], IS_NOT_IN_TRANS);
        SM('        IgnoreCANameConstraints='+BooleanStrings[Validator.IgnoreCANameConstraints], IS_NOT_IN_TRANS);
        SM('        IgnoreRevocationKeyUsage='+BooleanStrings[Validator.IgnoreRevocationKeyUsage], IS_NOT_IN_TRANS);
        SM('        IgnoreSSLKeyUsage='+BooleanStrings[Validator.IgnoreSSLKeyUsage], IS_NOT_IN_TRANS);
        SM('        IgnoreSystemTrust='+BooleanStrings[Validator.IgnoreSystemTrust], IS_NOT_IN_TRANS);
        SM('        ImplicitlyTrustSelfSignedCertificates='+BooleanStrings[Validator.ImplicitlyTrustSelfSignedCertificates], IS_NOT_IN_TRANS);
        SM('        LookupCRLByNameIfDPNotPresent='+BooleanStrings[Validator.LookupCRLByNameIfDPNotPresent], IS_NOT_IN_TRANS);
        SM('        MandatoryCRLCheck='+BooleanStrings[Validator.MandatoryCRLCheck], IS_NOT_IN_TRANS);
        SM('        MandatoryOCSPCheck='+BooleanStrings[Validator.MandatoryOCSPCheck], IS_NOT_IN_TRANS);
        SM('        MandatoryRevocationCheck='+BooleanStrings[Validator.MandatoryRevocationCheck], IS_NOT_IN_TRANS);
        SM('        OfflineMode='+BooleanStrings[Validator.OfflineMode], IS_NOT_IN_TRANS);
        SM('        PromoteLongOCSPResponses='+BooleanStrings[Validator.PromoteLongOCSPResponses], IS_NOT_IN_TRANS);
        {$IFNDEF D2007}
        SM('        RevocationCheckPreference='+Ord(Validator.RevocationCheckPreference).ToString(), IS_NOT_IN_TRANS);
        SM('        RevocationMomentGracePeriod='+Validator.RevocationMomentGracePeriod.ToString(), IS_NOT_IN_TRANS);
        {$ENDIF}
        SM('        SkipSubjectNameIfAltNameExists='+BooleanStrings[Validator.SkipSubjectNameIfAltNameExists], IS_NOT_IN_TRANS);
        SM('        UseSystemStorages='+BooleanStrings[Validator.UseSystemStorages], IS_NOT_IN_TRANS);
        SM('        ValidateInvalidCertificates='+BooleanStrings[Validator.ValidateInvalidCertificates], IS_NOT_IN_TRANS);
        SM('--------------------------------------------------------------------------------', IS_NOT_IN_TRANS);                    // CPCLIENTS-14502 end
        ValidatorOptionsLogged := true;
      end;

    finally
      if Assigned(Validator) then
        FreeAndNil(Validator);
    end;
  except
    on e: exception do
      SM('ValidateCertificate Try..Except - ' + e.message, IS_NOT_IN_TRANS);   // CPCLIENTS-14502
  end;
end;

function TValidateCert.ValidateCertificateEx(Cert: TElX509Certificate): TValidateCertResult;
var
  i, j: integer;
  CACert: TElX509Certificate;
  CAIdx: integer;
  CertName, IssuerCertName: string;
  Found: boolean;
  CertStorage: TElCustomCertStorage;
begin
  Result := vrUnknown;
  try
    CertName := TSSLUtils.GetCertDisplayName(Cert);
    //SM(Format('DEBUG Signature for certificate >%s<', [GetMD5Hash(Cert.Signature)])); // for logging cert

    // step 1: accept internal-purpose self-signed certificate
    SM('ValidateCertificate - step 1 ...', IS_NOT_IN_TRANS);   // CPCLIENTS-14502
    if Cert.SelfSigned then
    begin
      SM(Format('%s - self signed cerfiticate.', [CertName]), IS_NOT_IN_TRANS);   // CPCLIENTS-14502
      Found := false;
      for i := Low(MTX_Constants.CertSignaturesHash) to High(MTX_Constants.CertSignaturesHash) do
      if SameText(GetCryptoHash(TSSLUtils.SBByteArrayToStr(Cert.Signature)), MTX_Constants.CertSignaturesHash[i]) then  //TODO: update hash
        Found := true;
{$IFDEF WOLF}
      if true then //TODO-WOLF- not sure if we are still going to do it this way
{$ELSE}
      if Found then
{$ENDIF}
      begin
        Result := vrTrusted;
        TrustedCertList.Add(TSSLUtils.SBByteArrayToStr(Cert.Signature));
        SM(Format('TRUSTED: %s - belongs to trusted self-signed storage.', [CertName]), IS_NOT_IN_TRANS);   // CPCLIENTS-14502
        Exit;
      end
      else
      begin
        Result := vrUnTrusted;
        SM(Format('%s - does not belong to trusted self-signed storage.', [CertName]), IS_NOT_IN_TRANS);    // CPCLIENTS-14502
        /// keep validating - check if belongs to trusted cert storage
      end;
    end;

    // step 2: not self signed: looking in trusted storages
    SM('ValidateCertificate - step 2 ...', IS_NOT_IN_TRANS);    // CPCLIENTS-14502
    for i := 0 to StorageList.Count - 1 do
    begin
      try
        CAIdx := (StorageList.Objects[i] as TElCustomCertStorage).GetIssuerCertificate(Cert);
      except
        CAIdx := -1; //skipping buggy KAV certificates
      end;

      if CAIdx > -1 then
      begin
        SM('ValidateCertificate - step 2-1 ...', IS_NOT_IN_TRANS);    // CPCLIENTS-14502
        CACert := (StorageList.Objects[i] as TElCustomCertStorage).Certificates[CAIdx];
        IssuerCertName := TSSLUtils.GetCertDisplayName(CACert);
        if ValidateCertDate(CACert) then
        begin
          SM(Format('TRUSTED: "%s" issued by "%s" - belongs to trusted storage %s.', [CertName, IssuerCertName, StorageList[i]]), IS_NOT_IN_TRANS);  // CPCLIENTS-14502
{$IFDEF SBB9}
          TrustedCertList.Add(Cert.Signature);
{$ELSE}
          TrustedCertList.Add(TSSLUtils.SBByteArrayToStr(Cert.Signature));
{$ENDIF}
          Result := vrTrusted;
        end
        else
        begin
          SM(Format('EXPIRED: "%s" issued by "%s" - belongs to trusted storage %s but issuer certificate expired.', [CertName, IssuerCertName, StorageList[i]])
                , IS_NOT_IN_TRANS);  // CPCLIENTS-14502
        end;
        Exit; // if issuer cert is expired, do not check same certificate
      end
      else
      begin // same cert exists in trusted certificate storage (only when no issuer found)?
        SM('ValidateCertificate - step 2-2 ...', IS_NOT_IN_TRANS);   // CPCLIENTS-14502
        CertStorage := (StorageList.Objects[i] as TElCustomCertStorage);
        for j := 0 to CertStorage.Count -1 do
        begin
          CACert := (StorageList.Objects[i] as TElCustomCertStorage).Certificates[j];
{$IFDEF SBB9}
          if SameText(Cert.Signature, CACert.Signature) then
{$ELSE}
          if SameText(TSSLUtils.SBByteArrayToStr(Cert.Signature), TSSLUtils.SBByteArrayToStr(CACert.Signature)) then
{$ENDIF}
          begin
            if ValidateCertDate(CACert) then
            begin
              Result := vrTrusted;
{$IFDEF SBB9}
              TrustedCertList.Add(Cert.Signature);
{$ELSE}
              TrustedCertList.Add(TSSLUtils.SBByteArrayToStr(Cert.Signature));
{$ENDIF}
              SM(Format('TRUSTED: %s - same certificate belongs to trusted storage %s.', [CertName, StorageList[i]]), IS_NOT_IN_TRANS);  // CPCLIENTS-14502
            end
            else
            begin
              SM(Format('EXPIRED: %s - same certificate belongs to trusted storage %s but expired.', [CertName, StorageList[i]]), IS_NOT_IN_TRANS);  // CPCLIENTS-14502
            end;
            Exit;
          end;
        end;
      end;
    end; // end of step 2

    // step 3: check expiration date
    SM('ValidateCertificate - step 3 ...', IS_NOT_IN_TRANS);   // CPCLIENTS-14502
    if (NOT ValidateCertDate(Cert)) then
    begin
      SM(Format('EXPIRED: %s - expired.', [CertName]), IS_NOT_IN_TRANS);   // CPCLIENTS-14502
    end;

    SM(Format('Failed to validate %s (result = %s)', [CertName, GetEnumName(TypeInfo(TValidateCertResult), Ord(Result))]), IS_NOT_IN_TRANS); // CPCLIENTS-14502
  except
    on e: exception do
      SM('ValidateCertificate Try..Except - ' + e.message, IS_NOT_IN_TRANS);   // CPCLIENTS-14502
  end;
end;

//--------------- Helper functions ------------------ <

class function TSSLUtils.GetEncryptionAlghorithm(Algorithm : integer) : string;
begin
  case (Algorithm) of
    SB_CERT_ALGORITHM_ID_RSA_ENCRYPTION     : Result := 'RSA';
    SB_CERT_ALGORITHM_MD2_RSA_ENCRYPTION    : Result := 'MD2 with RSA';
    SB_CERT_ALGORITHM_MD5_RSA_ENCRYPTION    : Result := 'MD5 with RSA';
    SB_CERT_ALGORITHM_SHA1_RSA_ENCRYPTION   : Result := 'SHA1 with RSA';
    SB_CERT_ALGORITHM_ID_DSA                : Result := 'DSA';
    SB_CERT_ALGORITHM_ID_DSA_SHA1           : Result := 'DSA with SHA1';
    SB_CERT_ALGORITHM_DH_PUBLIC             : Result := 'DH';
    SB_CERT_ALGORITHM_SHA224_RSA_ENCRYPTION : Result := 'SHA224 with RSA';
    SB_CERT_ALGORITHM_SHA256_RSA_ENCRYPTION : Result := 'SHA256 with RSA';
    SB_CERT_ALGORITHM_SHA384_RSA_ENCRYPTION : Result := 'SHA384 with RSA';
    SB_CERT_ALGORITHM_SHA512_RSA_ENCRYPTION : Result := 'SHA512 with RSA';
    SB_CERT_ALGORITHM_ID_RSAPSS             : Result := 'RSA-PSS';
    SB_CERT_ALGORITHM_ID_RSAOAEP            : Result := 'RSA-OAEP';
    SB_CERT_ALGORITHM_UNKNOWN               : Result := 'Unknown';
  end;
end;

class function TSSLUtils.GetCertDisplayName(Cert : TelX509Certificate) : string;
begin
  try
    result := Cert.SubjectName.CommonName;
    if Result = '' then
      Result := Cert.SubjectName.Organization;
  except Result:=''; end;
end;

{$IFDEF SBB9}
class function TSSLUtils.GetStringByOID(const S: {$IFDEF SBB9} BufferType {$ELSE} ByteArray {$ENDIF}): string;
begin
  if CompareContent(S, SB_CERT_OID_COMMON_NAME) then
    Result := 'CommonName'
  else
  if CompareContent(S, SB_CERT_OID_COUNTRY) then
    Result := 'Country'
  else
  if CompareContent(S, SB_CERT_OID_LOCALITY) then
    Result := 'Locality'
  else
  if CompareContent(S, SB_CERT_OID_STATE_OR_PROVINCE) then
    Result := 'StateOrProvince'
  else
  if CompareContent(S, SB_CERT_OID_ORGANIZATION) then
    Result := 'Organization'
  else
  if CompareContent(S, SB_CERT_OID_ORGANIZATION_UNIT) then
    Result := 'OrganizationUnit'
  else
  if CompareContent(S, SB_CERT_OID_EMAIL) then
    Result := 'Email'
  else
    Result := 'UnknownField';
end;
{$ENDIF}

{$IFDEF SBB9}
class function TSSLUtils.GetOIDValue(NTS: TElRelativeDistinguishedName; const S: {$IFDEF SBB9} BufferType {$ELSE} ByteArray {$ENDIF}; const Delimeter: AnsiString = ' / '): AnsiString;
var
  i: Integer;
  t: AnsiString;
begin
  Result := '';
  for i := 0 to NTS.Count - 1 do
    if CompareContent(S, NTS.OIDs[i]) then
    begin
      t := AnsiString(NTS.Values[i]);
      if t = '' then
        Continue;
      if Result = '' then
      begin
        Result := t;
        if Delimeter = '' then
          Exit;
      end
      else
        Result := Result + Delimeter + t;
    end;
end;
{$ENDIF}

class procedure TSSLUtils.PrintCertificateListInStorage(StorageList: TStringList);
var
  CACert: TElX509Certificate;
  CustomStorage: TElCustomCertStorage;
  i, j: integer;
begin
  SM('List of loaded certificates', IS_NOT_IN_TRANS);  // CPCLIENTS-14502
  if NOT Assigned(StorageList) then
    Exit;
  for i := 0 to StorageList.Count - 1 do
  begin
    if NOT (StorageList.Objects[i] is TElCustomCertStorage) then
      Continue;
    CustomStorage := (StorageList.Objects[i] as TElCustomCertStorage);
    for j := 0 to CustomStorage.Count -1 do
    begin
      try
        CACert := TElX509Certificate.Create(nil);
        CACert.Assign(CustomStorage.Certificates[j]);
        SM('    ' + TSSLUtils.GetCertDisplayName(CACert));
      except
        on E : Exception do
          SM('EXCEPT: ' + e.Message, IS_NOT_IN_TRANS);  // CPCLIENTS-14502
      end;
    end;
  end;
end;

{$IFNDEF SBB9}
class function TSSLUtils.SBByteArrayToStr(Source: ByteArray): AnsiString;
var i : integer;
begin
  result := '';
  for I := 0 to Length(Source) - 1 do
    result := result + AnsiChar(Source[i]);
end;
{$ENDIF}

{$IFNDEF SBB9}
class function TSSLUtils.SBByteArrayToHexStr(Source: ByteArray): AnsiString;
var i : integer;
begin
  result:='';
  for I := 0 to Length(Source) - 1 do
    result := result + IntToHex(Source[i], 2) + ' ';
end;
{$ENDIF}

{$IFDEF USE_INDY}
class procedure TSSLUtils.ParseURI(AURI: string; var VProtocol, VHost, VPath, VDocument,
    VPort, VBookmark : string);
var
  URI: TIdURI;
begin
  URI := TIdURI.Create(AURI);
  try
    VProtocol := URI.Protocol;
    VHost := URI.Host;
    VPath := URI.Path;
    VDocument := URI.Document;
    VPort := URI.Port;
    VBookmark := URI.Bookmark;
  finally
    URI.Free;
  end;
end;
{$ENDIF}

{$IFDEF INDYSSL}
class procedure TSSLUtils.SetSSLOptions(SSLIOHandler: TElClientIndySSLIOHandlerSocket; FuncName: string='');
const
  AlgCount = 10;
  // Hash algorithms: none(0), md5(1), sha1(2), sha224(3), sha256(4), sha384(5), sha512(6)
  // Signature algorithms: anonymous(0), rsa(1), dsa(2), ecdsa(3)
  // Added SHA1 + RSA (2015.06.22)
  HashAlgs : array[0..AlgCount - 1] of byte = ( 2, 4, 4, 4, 5, 5, 5, 6, 6, 6 );
  SigAlgs : array[0..AlgCount - 1] of byte =  ( 1, 1, 2, 3, 1, 2, 3, 1, 2, 3 );
var I: integer;
begin
  if NOT Assigned(SSLIOHandler) then
    Exit;
{$IFDEF INDY_TEST}
  SSLIOHandler.Versions := SE_SSLVersions;
{$ELSE}
  SSLIOHandler.Versions := [sbTLS12];
  Log(FuncName + ' - Set SSL version to TLS 1.2');
{$ENDIF}
  for I  := SB_SUITE_FIRST to SB_SUITE_LAST do
    SSLIOHandler.CipherSuites[I] := false;
  for I := SB_SUITE_FIRST to SB_SUITE_LAST do
    SSLIOHandler.CipherSuitePriorities[I] := 0;
{$IFDEF INDY_TEST}
  for I := Low(SECiphers) to High(SECiphers) do
  begin
    SSLIOHandler.CipherSuites[SECiphers[i]] := true;
    Log(FuncName + ' - Limit Ciphers to ' + IntToStr(SECiphers[i]));
  end;
{$ELSE}
  // AES GCM cipher suites except NULL, ANON, PSK, DSS and CBC
  {
  // SHA1 (2015.06.22)
  SB_SUITE_RSA_AES128_SHA = SmallInt(12);
  SB_SUITE_RSA_AES256_SHA = SmallInt(13);

  // AES-GCM ciphers (RFC5288)
  SB_SUITE_RSA_AES128_GCM_SHA256 = SmallInt(121);
  SB_SUITE_RSA_AES256_GCM_SHA384 = SmallInt(122);
  SB_SUITE_DHE_RSA_AES128_GCM_SHA256 = SmallInt(123);
  SB_SUITE_DHE_RSA_AES256_GCM_SHA384 = SmallInt(124);
  SB_SUITE_DH_RSA_AES128_GCM_SHA256 = SmallInt(125);
  SB_SUITE_DH_RSA_AES256_GCM_SHA384 = SmallInt(126);

  // EC AES-GCM and SHA2 ciphers (RFC5289)
  SB_SUITE_ECDHE_ECDSA_AES128_GCM_SHA256 = SmallInt(141);
  SB_SUITE_ECDHE_ECDSA_AES256_GCM_SHA384 = SmallInt(142);
  SB_SUITE_ECDH_ECDSA_AES128_GCM_SHA256 = SmallInt(143);
  SB_SUITE_ECDH_ECDSA_AES256_GCM_SHA384 = SmallInt(144);
  SB_SUITE_ECDHE_RSA_AES128_GCM_SHA256 = SmallInt(145);
  SB_SUITE_ECDHE_RSA_AES256_GCM_SHA384 = SmallInt(146);
  SB_SUITE_ECDH_RSA_AES128_GCM_SHA256 = SmallInt(147);
  SB_SUITE_ECDH_RSA_AES256_GCM_SHA384 = SmallInt(148);
  }
  for I := SB_SUITE_RSA_AES128_SHA to SB_SUITE_RSA_AES256_SHA do // 12-13
    SSLIOHandler.CipherSuites[I] := true;
//  for I := SB_SUITE_RSA_AES128_GCM_SHA256 to SB_SUITE_DH_RSA_AES256_GCM_SHA384 do // 121-126
//    SSLIOHandler.CipherSuites[I] := true;
  for I := SB_SUITE_RSA_AES128_GCM_SHA256 to SB_SUITE_RSA_AES256_GCM_SHA384 do // 121-122
    SSLIOHandler.CipherSuites[I] := true;
  // TFS-28347  skip 123-124 since these are known to have problems
  for I := SB_SUITE_DH_RSA_AES128_GCM_SHA256 to SB_SUITE_DH_RSA_AES256_GCM_SHA384 do // 125-126
    SSLIOHandler.CipherSuites[I] := true;
  for I := SB_SUITE_ECDHE_ECDSA_AES128_GCM_SHA256  to SB_SUITE_ECDH_RSA_AES256_GCM_SHA384 do // 141-148
    SSLIOHandler.CipherSuites[I] := true;
  Log(FuncName + ' - Limit Ciphers to AES-GCM ciphers [12,13,121,122,125,126,141-148]');

  {$ENDIF}
  SSLIOHandler.RenegotiationAttackPreventionMode := rapmStrict;
  //Log(FuncName + ' - Enable RenegotiationAttackPreventionMode');
  //SSLIOHandler.Extensions.SignatureAlgorithms.EnableAllSupported;
  SSLIOHandler.Extensions.SignatureAlgorithms.Count := AlgCount;
  for I := 0 to AlgCount-1 do
  begin
    SSLIOHandler.Extensions.SignatureAlgorithms.HashAlgorithms[I] := HashAlgs[I];
    SSLIOHandler.Extensions.SignatureAlgorithms.SignatureAlgorithms[I] := SigAlgs[I];
  end;
  SSLIOHandler.Extensions.SignatureAlgorithms.Enabled := true;
  //Log(FuncName + ' - Enable SignatureAlgorithms extension');
end;
{$ENDIF INDYSSL}

{$IFDEF INDYSSL}
class procedure TSSLUtils.SetSSLIOHandler(IndyHttp: TIdHttp;
    SSLIOHandler: {$IFDEF INDY9}TElIndySSLIOHandlerSocket{$ELSE}TElClientIndySSLIOHandlerSocket{$ENDIF};
    OnCertificateValidate: TSBCertificateValidateEvent;
    OnSSLEstablished: TSBSSLEstablishedEvent;
    OnError: TSBErrorEvent;
    FuncName: string='');
begin
  if not Assigned(SSLIOHandler) then
    SSLIOHandler := {$IFDEF INDY9}TElIndySSLIOHandlerSocket{$ELSE}TElClientIndySSLIOHandlerSocket{$ENDIF}.Create(IndyHttp);
  SetSSLOptions(SSLIOHandler, FuncName);
  SSLIOHandler.OnCertificateValidate := OnCertificateValidate;
  SSLIOHandler.OnSSLEstablished      := OnSSLEstablished;
  SSLIOHandler.OnError               := OnError;
  IndyHttp.IOHandler := SSLIOHandler;
end;
{$ENDIF}

initialization
  ExtendedLog('ValidateCertificate Initialization');

finalization
  ExtendedLog('ValidateCertificate Finalization',
    procedure
    begin
      ExtendedLog('ValidateCertificate Finalization ValidateCert',
        procedure
        begin
          if Assigned(ValidateCert) then
          begin
            FreeAndNil(ValidateCert);
          end;
        end
        );
    end
    );

end.
