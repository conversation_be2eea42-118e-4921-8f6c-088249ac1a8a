// (c) MTXEPS, Inc. 1988-2008
unit UXMLCommon;

interface

uses
  FinalizationLog,
  Classes, SysUtils, LibXmlParser,                                         // 828.5
  MTX_Constants, MdMsg;

const
  SLanguage = 'Language';
  SId = 'Id';
  sUseForm = 'UseForm';
  SLine1 = 'Line1';
  SLine2 = 'Line2';
  SLine3 = 'Line3';
  SLine4 = 'Line4';
  SLine5 = 'Line5';
  Bal    = 'Balance';

  BooleanStr: array [Boolean] of string = ('N', 'Y');

const
  rDate            = 'D';                   { TSL-A start }
  rBal             = 'B';
  cNA              = 'N/A';
  tCashPad1        = 1;
  tCashPad2        = 2;
  tCust1           = 3;
  tCust2           = 4;
  replaceFieldCode = '~';                   { TSL-A end }
  rApprovalCode    = 'a';

type
  TXMLParserNode = class;

  TXMLConfiguration = class(TObject)
  public
    Root: TXMLParserNode;
    FXMLParser: TXMLParser;                                               // 828.5
    procedure ScanElement(AParent: TXMLParserNode);
    constructor Create;
    destructor Destroy; override;
  end;

  TCustomerLineList = class;

  TCustomerLine = class(TObject)
  private
    FCustomerLineList: TCustomerLineList;
    FId: Integer;
    FLine1: string;
    FLine2: string;
  public
    constructor Create(ACustomerLineList: TCustomerLineList; AId: Integer);
    destructor Destroy; override;
    procedure Assign(ACustomerLine: TCustomerLine);
    procedure Clear;
    property Id: Integer read FId write FId;
    property Line1: string read FLine1 write FLine1;
    property Line2: string read FLine2 write FLine2;
  end;

  TCustomerLineList = class(TObject)
  private
    FItems: TList;
    function GetItem(Index: Integer): TCustomerLine;
    function GetCount: Integer;
  public
    constructor Create;
    destructor Destroy; override;
    procedure Clear;
    function Add(AId: Integer): TCustomerLine;
    procedure Delete(Index: Integer);
    procedure Assign(ACustomerLineList: TCustomerLineList);
    function CustomerLineById(AId: Integer): TCustomerLine;
    property Items[Index: Integer]: TCustomerLine read GetItem; default;
    property Count: Integer read GetCount;
  end;

  TLineCollection2 = class(TObject)
  private
    FLine1: string;
    FLine2: string;
  public
    property Line1: string read FLine1 write FLine1;
    property Line2: string read FLine2 write FLine2;
  end;

  TLineCollection4 = class(TObject)
  private
    FLine1: string;
    FLine2: string;
    FLine3: string;
    FLine4: string;
  public
    procedure Clear;
    property Line1: string read FLine1 write FLine1;
    property Line2: string read FLine2 write FLine2;
    property Line3: string read FLine3 write FLine3;
    property Line4: string read FLine4 write FLine4;
  end;

  TLineCollection5 = class(TObject)
  private
    FLine1: string;
    FLine2: string;
    FLine3: string;
    FLine4: string;
    FLine5: string;
  public
    procedure Assign(ALineCollection5: TLineCollection5);
    procedure Clear;
    property Line1: string read FLine1 write FLine1;
    property Line2: string read FLine2 write FLine2;
    property Line3: string read FLine3 write FLine3;
    property Line4: string read FLine4 write FLine4;
    property Line5: string read FLine5 write FLine5;
  end;

  TCashbackCustomerLineList = class(TObject)
  private
  public
    Lang1: TLineCollection4;
    Lang2: TLineCollection4;
    Lang3: TLineCollection4;
    constructor Create;
    destructor Destroy; override;
    procedure Clear;
    function CustomerLineById(AId: Integer): TLineCollection4;
  end;

  TXMLParserNode = class(TObject)
  private
    FParent: TXMLParserNode;
    FChildren: TList;
    FAttr: TStrings;
    FText: string;
    FName: string;
    procedure SaveToFileInt(Indent: Integer);
    function GetText: string;
    procedure SetText(Value: string);
  public
    constructor Create(AParent: TXMLParserNode);
    destructor Destroy; override;
    procedure FreeChildren;
    function AddChild(ANodeName: string): TXMLParserNode;
    procedure SaveToFile(AFileName: string);
    function GetXMLStr: string;
    property Name: string read FName write FName;
    //property Text: string read FText write FText;
    property Text: string read GetText write SetText;
    property Attr: TStrings read FAttr;
    property Children: TList read FChildren;
  end;

  TECCReceiptInfo = class
  public
    fileName: string;
    Version: string;
    LastModified: string;
    AcceptanceAgreement: TStringList;
    DenialAgreement: TStringList; // CPCLIENTS-1190 & CPCLIENTS-5020
    PhoneNumber: string;
    ReturnCheckFee: Integer;
    constructor Create(HostSuffix: string);
    destructor Destroy; override;
    function ValidateVersion: boolean;
  end;

  TIBMFtpInfo = class
  private
    oldPrimaryServerIP: string;
    oldBackupServerIP: string;
    oldUserName: string;
    oldPassword: string;
  public
    fileName: string;
    Version: string;
    LastModified: string;
    PrimaryServerIP: string;
    BackupServerIP: string;
    UserName: string;
    Password: AnsiString;
    constructor Create(loadXML: Boolean);
    destructor Destroy; override;
    function LoadFromXML(aFileName: string; UseOldKey: boolean=false): Boolean;
    function SaveToXML: Boolean;
    function ValidateVersion: boolean;
  end;

procedure AddCustomerLines(ParentNode: TXMLParserNode; CustomerLines: TCustomerLineList);
procedure AddCashbackCustomerLines(ParentNode: TXMLParserNode; CashbackCustomerLines: TCashbackCustomerLineList);
procedure AddLineCollection5(ParentNode: TXMLParserNode; Line: TLineCollection5);
procedure GetCustomerLines(AParent: TXMLParserNode; CustomerLines: TCustomerLineList);
procedure GetCashbackCustomerLines(AParent: TXMLParserNode; CashbackCustomerLines: TCashbackCustomerLineList);
procedure GetLineCollection2(AParent: TXMLParserNode; LineCollection2: TLineCollection2);
procedure GetLineCollection4(AParent: TXMLParserNode; LineCollection4: TLineCollection4);
procedure GetLineCollection5(AParent: TXMLParserNode; LineCollection5: TLineCollection5);
function StrToBoolean(S: string): Boolean;
function ReplaceField(var InRec  : MDMsgRec;
                      Msg    : string40;
                      padlen : integer;
                      varType: integer): string;        { TSL-A }

implementation

uses
  MTX_Lib,
  MTXEncryptionUtils;


var
  xmlfile: TextFile;

procedure AddCustomerLines(ParentNode: TXMLParserNode; CustomerLines: TCustomerLineList);
var
  I, j: Integer;
  Language, Line: TXMLParserNode;
  Found: Boolean;
begin
  for I := 1 to 3 do
  begin
    Found := False;
    for J := 0 to CustomerLines.Count - 1 do
      if CustomerLines.Items[J].Id = I then
      begin
        Found := True;
        Break;
      end;

    if not Found then
      with CustomerLines.Add(I) do
      begin
        Line1 := '';
        Line2 := '';
      end;
  end;

  for j := 0 to CustomerLines.Count - 1 do
  begin
    Language := ParentNode.AddChild(SLanguage);
    Language.Attr.Values[SId] := IntToStr(CustomerLines[j].Id);
    Line := Language.AddChild(SLine1);
    Line.Text := CustomerLines[j].Line1;
    Line := Language.AddChild(SLine2);
    Line.Text := CustomerLines[j].Line2;
  end;
end;

procedure AddCashbackCustomerLines(ParentNode: TXMLParserNode; CashbackCustomerLines: TCashbackCustomerLineList);
var
  I: Integer;
  Language, Line: TXMLParserNode;
  tmpCustomerLine: TLineCollection4;
begin
  tmpCustomerLine := nil;
  for i := 1 to 3 do
  begin
    if i = 1 then tmpCustomerLine := CashbackCustomerLines.Lang1
    else if i = 2 then tmpCustomerLine := CashbackCustomerLines.Lang2
    else if i = 3 then tmpCustomerLine := CashbackCustomerLines.Lang3;
    if Assigned(tmpCustomerLine) then
    begin
      Language := ParentNode.AddChild(SLanguage);
      Language.Attr.Values[SId] := IntToStr(i);
      Line := Language.AddChild(SLine1);
      Line.Text := tmpCustomerLine.Line1;
      Line := Language.AddChild(SLine2);
      Line.Text := tmpCustomerLine.Line2;
      Line := Language.AddChild(SLine3);
      Line.Text := tmpCustomerLine.Line3;
      Line := Language.AddChild(SLine4);
      Line.Text := tmpCustomerLine.Line4;
    end;
  end;
end;

procedure AddLineCollection5(ParentNode: TXMLParserNode; Line: TLineCollection5);
var
  Child: TXMLParserNode;
begin
  Child := ParentNode.AddChild(SLine1);
  Child.Text := Line.Line1;
  Child := ParentNode.AddChild(SLine2);
  Child.Text := Line.Line2;
  Child := ParentNode.AddChild(SLine3);
  Child.Text := Line.Line3;
  Child := ParentNode.AddChild(SLine4);
  Child.Text := Line.Line4;
  Child := ParentNode.AddChild(SLine5);
  Child.Text := Line.Line5;
end;

procedure GetCustomerLines(AParent: TXMLParserNode; CustomerLines: TCustomerLineList);
var
  k, m: Integer;
  Node3, Node4: TXMLParserNode;
  CustomerLine: TCustomerLine;
begin
  for k := 0 to AParent.Children.Count - 1 do
  begin
    Node3 := AParent.Children[k];
    if Node3.Name = SLanguage then
    begin
      CustomerLine := CustomerLines.Add(StrToIntDef(Node3.Attr.Values[SId], -1));
      if CustomerLine <> nil then                                      { JMR-B }
        for m := 0 to Node3.Children.Count - 1 do
        begin
          Node4 := Node3.Children[m];
          if Node4.Name = SLine1 then
            CustomerLine.Line1 := Node4.Text
          else
          if Node4.Name = SLine2 then
            CustomerLine.Line2 := Node4.Text
        end;
    end;
  end;
end;

procedure GetCashbackCustomerLines(AParent: TXMLParserNode; CashbackCustomerLines: TCashbackCustomerLineList);
var
  k, m, Id: Integer;
  Node3, Node4: TXMLParserNode;
  tmpCustomerLine: TLineCollection4;
begin
  for k := 0 to AParent.Children.Count - 1 do
  begin
    Node3 := AParent.Children[k];
    if Node3.Name = SLanguage then
    begin
      tmpCustomerLine := nil;
      Id := StrToIntDef(Node3.Attr.Values[SId], -1);
      if Id = 1 then tmpCustomerLine := CashbackCustomerLines.Lang1
      else if Id = 2 then tmpCustomerLine := CashbackCustomerLines.Lang2
      else if Id = 3 then tmpCustomerLine := CashbackCustomerLines.Lang3;

      if Assigned(tmpCustomerLine) then
        for m := 0 to Node3.Children.Count - 1 do
        begin
          Node4 := Node3.Children[m];
          if Node4.Name = SLine1 then
            tmpCustomerLine.Line1 := Node4.Text
          else if Node4.Name = SLine2 then
            tmpCustomerLine.Line2 := Node4.Text
          else if Node4.Name = SLine3 then
            tmpCustomerLine.Line3 := Node4.Text
          else if Node4.Name = SLine4 then
            tmpCustomerLine.Line4 := Node4.Text;
        end;
    end;
  end;
end;

procedure GetLineCollection2(AParent: TXMLParserNode; LineCollection2: TLineCollection2);
var
  i: Integer;
  Node: TXMLParserNode;
begin
  for i := 0 to AParent.Children.Count - 1 do
  begin
    Node := AParent.Children[i];
    if Node.Name = SLine1 then
      LineCollection2.Line1 := Node.Text
    else
    if Node.Name = SLine2 then
      LineCollection2.Line2 := Node.Text;
  end;
end;

procedure GetLineCollection4(AParent: TXMLParserNode; LineCollection4: TLineCollection4);
var
  i: Integer;
  Node: TXMLParserNode;
begin
  for i := 0 to AParent.Children.Count - 1 do
  begin
    Node := AParent.Children[i];
    if Node.Name = SLine1 then
      LineCollection4.Line1 := Node.Text
    else
    if Node.Name = SLine2 then
      LineCollection4.Line2 := Node.Text
    else
    if Node.Name = SLine3 then
      LineCollection4.Line3 := Node.Text
    else
    if Node.Name = SLine4 then
      LineCollection4.Line4 := Node.Text;
  end;
end;

procedure GetLineCollection5(AParent: TXMLParserNode; LineCollection5: TLineCollection5);
var
  i: Integer;
  Node: TXMLParserNode;
begin
  for i := 0 to AParent.Children.Count - 1 do
  begin
    Node := AParent.Children[i];
    if Node.Name = SLine1 then
      LineCollection5.Line1 := Node.Text
    else
    if Node.Name = SLine2 then
      LineCollection5.Line2 := Node.Text
    else
    if Node.Name = SLine3 then
      LineCollection5.Line3 := Node.Text
    else
    if Node.Name = SLine4 then
      LineCollection5.Line4 := Node.Text
    else
    if Node.Name = SLine5 then
      LineCollection5.Line5 := Node.Text;
  end;
end;

function StrToBoolean(S: string): Boolean;
var
  r: Boolean;
begin
  Result := False;
  for r := Low(BooleanStr) to High(BooleanStr) do
  begin
    if BooleanStr[r] = S then
    begin
      Result := r;
      break;
    end;
  end;
end;

function decimalStr(rval : string): string;
begin
  if (pos('.', rval) = 0) and (trim(rval) <> '') then
  begin
    if (length(rval) < 2) then
      rval := '0' + rval;     // pad 1 to 9 cents with a zero
    result := Copy(rval, 1, length(rval) - 2) +  '.' + Copy(rval, length(rval) - 1, 2);
  end
  else
    result := rval;
end;

function ReplaceField(var InRec  : MDMsgRec;
                      Msg    : string40;
                      padlen : integer;
                      varType: integer): string;        { TSL-A }
var
  tmpPos : integer;
  tType  : AnsiChar;
  rvalue : string40;
  tempEBT: string40;
  TempTag: string16;

  function publixHostDefined: boolean;    { TSL-C }
  begin
    with InRec do
    begin
      if (InRec.HostSuffixCode = 'PBL') or
         (InRec.HostSuffixCode = 'PB2') or
         (InRec.HostSuffixCode = 'PB3') then
        result := true
      else
        result := false;
    end;
  end;

begin
with InRec do
begin
  Msg := StringReplace(Msg, '~a', AuthCode, [rfReplaceAll]);
  tmpPos := Pos(ReplaceFieldCode, Msg);
  if (tmpPos > 0)
    then tType  := upcase(Msg[tmpPos + 1])    { new way - look for ~d or ~b }
    else tType := ' ';
  If (tType in [rDate, rBal]) then
    begin
      rvalue := '';
      case tType of
        rDate:            { replace ~d with RewardsVoucherRedeemedDate as mmdd }
          begin
          if (trim(RewardsVoucherRedeemedDate) = '') then
            rvalue := cNA
          else
          if (pos('/', RewardsVoucherRedeemedDate) = 0)
            then rvalue := copy(RewardsVoucherRedeemedDate, 1, 2) + '/' +
                           copy(RewardsVoucherRedeemedDate, 3, 2)
            else rvalue := RewardsVoucherRedeemedDate;
          end;
        rBal:             { replace ~b with a balance }
          begin
          if (ReqCodeN in EBT_Cash_Set)
            then rvalue := trim(Zdel(EBT_Cash_Balance,3))  { EBT cash bal here }
            else rvalue := trim(Zdel(EBT_FS_Balance,3));   { other bal's in FS bal }

          if (rvalue = '')
            then rvalue := cNA
            else rvalue := decimalStr(rvalue);

          if (ReqCodeN in db_Set) then
            rvalue := '';
          end;
      end;
      result := rpad(Copy(Msg, 1, tmpPos - 1) + rvalue +
                  trim(Copy(Msg, tmpPos + 2, padlen - tmpPos - 1)), padlen);
    end
  else      { make balances the old way for certain ReqCodes }
    begin
      If (ReqCodeN in [EBT_Cash_BalinqN,EBT_FS_BalinqN]) then
        Begin
          Case varType of
            tCashPad1 : result := 'Cust Bal Showing';
            tCashPad2 : result := ' ';
            tCust1    :
              begin
                TempEBT := ' Bal not Avail. ';
                TempTag := '';

                case ReqCodeN of
                  EBT_FS_BalinqN:
                    If (EBT_FS_Balance <> '') Then
                      Begin
                        TempEBT := Zdel(EBT_FS_Balance, 3);
                        if (EBT_FS_BalanceN < 99999)
                          then TempTag := Bal + ': $'
                          else TempTag := Bal + ' $';
                      End;
                  EBT_Cash_BalinqN:
                    If (EBT_Cash_Balance <> '') Then
                      Begin
                        TempEBT := Zdel(EBT_Cash_Balance, 3);
                        if (EBT_Cash_BalanceN < 99999)
                          then TempTag := Bal + ': $'
                          else TempTag := Bal + ' $';
                      End;
                end;
                result := TempTag + decimalStr(TempEBT)
              end;
            tCust2    : result := 'Cancel/clear To Exit';   {TSL-E for Albertsons }
          end;
        End
      else
        begin
          If (ReqCodeN in User_1_set)     and   { Set GiftCard bal the old way }
             (trim(EBT_FS_Balance) <> '') and
             (varType = tCust2)           then
          begin
            if (EBT_FS_BalanceN > 99999) then
              result := Bal + ' $' + decimalStr(Zdel(EBT_FS_Balance, 3))
            else
            begin
              if publixHostDefined and (termRspCode[1] <> 'A')
                then result := ''
                else result := Bal + ': $' + decimalStr(Zdel(EBT_FS_Balance, 3));
            end;
          end
          else                             { TSL-B }
          if (trim(msg) = SHOWAPPROVAL)
            then result := RPad('APPR# ' + AuthCode, padLen)
            else result := msg;    { else no change to message }
        end;
    end;
end;
end;  { function ReplaceField }

{ TXMLConfiguration }

constructor TXMLConfiguration.Create;
begin
  inherited;
  FXMLParser := TXMLParser.Create;                              // 828.5
  FXMLParser.Normalize := False;                                // 828.5
end;

destructor TXMLConfiguration.Destroy;
begin
  FreeAndNil(Root);         { JMR-A }
  FreeAndNil(FXMLParser);                                       // 828.5
  inherited;
end;

procedure TXMLConfiguration.ScanElement(AParent: TXMLParserNode);
var
  Node: TXMLParserNode;
  i: Integer;
  temp: string;
begin
//  try
//    Assert(FXMLParser <> nil, 'FXMLParser = nil.');
    //Node:= nil; // [Hint] UXMLCommon.pas(610): Value assigned to 'Node' never used
//    (*                                                                                               // 828.5
    while FXMLParser.Scan do
    begin
      case FXmlParser.CurPartType of
        ptXmlProlog : ;
        ptDtdc      : ;
        ptStartTag,
        ptEmptyTag  : begin
                        Node := TXMLParserNode.Create(AParent);
                        Assert(Node <> nil, 'Node = nil (ptEmptyTag)');
                        Node.Name := FXmlParser.CurName;
                        if AParent = nil then
                        begin
                          if Root <> nil then
                            Root.Free;
                          Root := Node;
                        end;

                        if FXmlParser.CurAttr.Count > 0 then
                        begin
                          for i := 0 to FXmlParser.CurAttr.Count - 1 do
                          begin
                            Node.Attr.Values[FXmlParser.CurAttr.Name(i)] := FXmlParser.CurAttr.Value(i);
                          end;
                        end;

                        if FXmlParser.CurPartType = ptStartTag then ScanElement(Node);
                      end;
        ptEndTag    : break;
        ptContent,
        ptCData     : begin
                        // Need to assign AParent to Node otherwise we get an access violation
                        Node := AParent;
                        //Assert(Node <> nil, 'Node = nil (ptCData)');
                        {$IFDEF LINUX}
                        if Node.Name = 'this blows up on Linux if this line is not here' then
                          ;
                        {$ENDIF LINUX}
                        SetString (Temp, FXmlParser.CurStart, FXmlParser.CurFinal-FXmlParser.CurStart+1);
                        //Node.Text := FXmlParser.CurContent;
                        Node.Text := Temp;
                      end;
        ptComment   : ;
        ptPI        : ;
      end;
    end;
//    *)
{  except
    on E: Exception do
      SM('EXCEPTION: TXMLConfiguration.ScanElement: ' + E.Message);
  end;
}
end;

{ TCustomerLine }
procedure TCustomerLine.Assign(ACustomerLine: TCustomerLine);
begin
  Id := ACustomerLine.Id;
  Line1 := ACustomerLine.Line1;
  Line2 := ACustomerLine.Line2;
end;

procedure TCustomerLine.Clear;
begin
  FLine1 := '';
  FLine2 := '';
end;

constructor TCustomerLine.Create(ACustomerLineList: TCustomerLineList;
  AId: Integer);
begin
  inherited Create;
  FCustomerLineList := ACustomerLineList;
  FCustomerLineList.FItems.Add(Self);
  FId := AId;
end;

destructor TCustomerLine.Destroy;
begin
  FCustomerLineList.FItems.Remove(Self);
  inherited;
end;

{ TCustomerLineList }

function TCustomerLineList.Add(AId: Integer): TCustomerLine;
var
  I: Integer;
begin
  for I := 0 to Count - 1 do
    if Items[I].Id = AId then
    begin
      // TFS 11251, this function was returning nil, but if Add() finds an existing line already
      //  for the given language ID, then it should just return it rather than return nil
      Result := Self.Items[I];
      Exit;
    end;
  Result := TCustomerLine.Create(Self, AId);
end;

procedure TCustomerLineList.Assign(ACustomerLineList: TCustomerLineList);
var
  i: Integer;
begin
  Clear;
  for i := 0 to ACustomerLineList.Count - 1 do
    Add(0).Assign(ACustomerLineList[i]);
end;

procedure TCustomerLineList.Clear;
begin
  while (FItems.Count > 0) do TCustomerLine(FItems.Last).Free;
end;

constructor TCustomerLineList.Create;
begin
  inherited;
  FItems := TList.Create;
end;

function TCustomerLineList.CustomerLineById(AId: Integer): TCustomerLine;
var
  i: Integer;
begin
  Result := nil;
  for i := 0 to Count - 1 do
  begin
    if Items[i].Id = AId then
    begin
      Result := Items[i];
      break;
    end;
  end;
end;

procedure TCustomerLineList.Delete(Index: Integer);
begin
  TCustomerLine(FItems[Index]).Free;
end;

destructor TCustomerLineList.Destroy;
begin
  Clear;
  FreeAndNil(FItems);
  inherited;
end;

function TCustomerLineList.GetCount: Integer;
begin
  Result := FItems.Count;
end;

function TCustomerLineList.GetItem(Index: Integer): TCustomerLine;
begin
  Result := FItems[Index];
end;

{ TLineCollection4 }

procedure TLineCollection4.Clear;
begin
  Line1 := '';
  Line2 := '';
  Line3 := '';
  Line4 := '';
end;

{ TCashbackCustomerList }

constructor TCashbackCustomerLineList.Create;
begin
  inherited;
  Lang1 := TLineCollection4.Create;
  Lang2 := TLineCollection4.Create;
  Lang3 := TLineCollection4.Create;
end;

destructor TCashbackCustomerLineList.Destroy;
begin
  FreeAndNil(Lang1);
  FreeAndNil(Lang2);
  FreeAndNil(Lang3);
  inherited;
end;

procedure TCashbackCustomerLineList.Clear;
begin
  Lang1.Clear;
  Lang2.Clear;
  Lang3.Clear;
end;

function TCashbackCustomerLineList.CustomerLineById(AId: Integer): TLineCollection4;
begin
  result := nil;
  if AId = 1 then result := Lang1
  else if AId = 2 then result := Lang2
  else if AId = 3 then result := Lang3;
end;

{ TLineCollection5 }

procedure TLineCollection5.Assign(ALineCollection5: TLineCollection5);
begin
  Line1 := ALineCollection5.Line1;
  Line2 := ALineCollection5.Line2;
  Line3 := ALineCollection5.Line3;
  Line4 := ALineCollection5.Line4;
  Line5 := ALineCollection5.Line5;
end;

procedure TLineCollection5.Clear;
begin
  FLine1 := '';
  FLine2 := '';
  FLine3 := '';
  FLine4 := '';
  FLine5 := '';
end;

{ TXMLParserNode }

function TXMLParserNode.AddChild(ANodeName: string): TXMLParserNode;
begin
  Result := TXMLParserNode.Create(Self);
  Result.Name := ANodeName;
end;

constructor TXMLParserNode.Create(AParent: TXMLParserNode);
begin
  inherited Create;
  FParent := AParent;
  if Assigned(FParent) then FParent.Children.Add(Self);
  FAttr := TStringList.Create;
  FChildren := TList.Create;
end;

destructor TXMLParserNode.Destroy;
begin
  if Assigned(FParent) then FParent.Children.Remove(Self);
  FreeChildren;
  FreeAndNil(FChildren);
  FreeAndNil(FAttr);
  inherited;
end;

procedure TXMLParserNode.FreeChildren;
begin
  while FChildren.Count > 0 do TXMLParserNode(FChildren.Last).Free;
end;

procedure TXMLParserNode.SaveToFile(AFileName: string);
begin
  AssignFile(xmlfile, AFileName);
  {$I-}
  Rewrite(xmlfile);
  SaveToFileInt(0);
  CloseFile(xmlfile);
  {$I+}
end;

procedure TXMLParserNode.SaveToFileInt(Indent: Integer);
var
  Attrs, s: string;
  i, j: Integer;
begin
  try
    Attrs := '';
    for i := 0 to Attr.Count - 1 do
      Attrs := Attrs + ' ' + Attr.Names[i] + '="' + FmtXMLToWrite(Attr.Values[Attr.Names[i]]) + '"'; { YHJ-482 }
    if Text <> '' then
    begin
      s := StringOfChar(' ', Indent) + '<' + Name + Attrs + '>' + FmtXmlToWrite(Text);
      if Children.Count = 0 then
        s := s + '</' + Name + '>';
      WriteLn(xmlfile, s);
    end
    else
    begin
      if Children.Count > 0 then
      begin
        WriteLn(xmlfile, StringOfChar(' ', Indent) + '<' + Name + Attrs + '>');
        for i := 0 to Children.Count - 1 do
        begin
          TXMLParserNode(Children[i]).SaveToFileInt(Indent + 2);
          if TXMLParserNode(Children[i]).Text <> '' then
          begin
            if TXMLParserNode(Children[i]).Children.Count > 0 then
            begin
              for j := 0 to TXMLParserNode(Children[i]).Children.Count - 1 do
                TXMLParserNode(TXMLParserNode(Children[i]).Children[j]).SaveToFileInt(0);
              Writeln(xmlfile, StringOfChar(' ', Indent) + '</' + TXMLParserNode(Children[i]).Name + '>');
            end;
          end;
        end;
        WriteLn(xmlfile, StringOfChar(' ', Indent) + '</' + Name + '>');
      end
      else
        WriteLn(xmlfile, StringOfChar(' ', Indent) + '<' + Name + Attrs + '/>');
    end;
  except
    ;
  end;
end;

function TXMLParserNode.GetXMLStr: string;                                      { YHJ-511 }
var
  Attrs: string;
  i, j: Integer;
begin
  result := '';
  Attrs := '';
  for i := 0 to Attr.Count - 1 do
    Attrs := Attrs + ' ' + Attr.Names[i] + '="' + FmtXMLToWrite(Attr.Values[Attr.Names[i]]) + '"'; { YHJ-482 }
  if Text <> '' then
  begin
    result := result + '<' + Name + Attrs + '>' + FmtXmlToWrite(Text);
    if Children.Count = 0 then
      result := result + '</' + Name + '>';
  end
  else
  begin
    if Children.Count > 0 then
    begin
      result := result + '<' + Name + Attrs + '>';
      for i := 0 to Children.Count - 1 do
      begin
        result := result + TXMLParserNode(Children[i]).GetXMLStr;
        if TXMLParserNode(Children[i]).Text <> '' then
        begin
          if TXMLParserNode(Children[i]).Children.Count > 0 then
          begin
            for j := 0 to TXMLParserNode(Children[i]).Children.Count - 1 do
              result := result + TXMLParserNode(TXMLParserNode(Children[i]).Children[j]).GetXMLStr;
            result := result + '</' + TXMLParserNode(Children[i]).Name + '>';
          end;  
        end;
      end;
      result := result + '</' + Name + '>';
    end
    else
      //result := result + '<' + Name + Attrs + '/>';
      if Attrs <> '' then
        result := result + '<' + Name + Attrs + '/>'
      else
        result := result + '<' + Name + '>' + '</' + Name + '>';
  end;
end;

function TXMLParserNode.GetText: string;
begin
  result := FmtXmlToRead(FText);
end;

procedure TXMLParserNode.SetText(Value: string);
begin
  if Value = FText then Exit;
  FText := FmtXMLToWrite(Value);
end;

{ ---------------------------------------------------------------------------- }

constructor TECCReceiptInfo.Create(HostSuffix: string);
var
  I, J, K: Integer;
  XMLConfig: TXMLConfiguration;
  N1, N2, N3: TXMLParserNode;
  Found: Boolean;
  aDir     : string;
  procedure SetDefaultForDenial(HostSuffix: string); //CPCLIENTS-1190 & CPCLIENTS-5020
  begin
    DenialAgreement.Clear;
    if SameText(HostSuffix, 'BYL') then
    begin
      DenialAgreement.Add('Our decision not to accept your');
      DenialAgreement.Add('check is based, in part,');
      DenialAgreement.Add('on information provided to us');
      DenialAgreement.Add('from TeleCheck. To learn');
      DenialAgreement.Add('more call or write TeleCheck');
      DenialAgreement.Add('at ************** / P.O. Box 4513,');
      DenialAgreement.Add('Houston, TX 77210-4513.');
      DenialAgreement.Add('Please have information on your');
      DenialAgreement.Add('bank account and driver license');
      DenialAgreement.Add('numbers available for reference.');
      DenialAgreement.Add('If you are a consumer,');
      DenialAgreement.Add('for the next 60 days, you have');
      DenialAgreement.Add('the right to obtain a free copy');
      DenialAgreement.Add('of a consumer report, pursuant');
      DenialAgreement.Add('to the Fair Credit Reporting Act,');
      DenialAgreement.Add('from TeleCheck. You may also');
      DenialAgreement.Add('dispute the accuracy or completeness');
      DenialAgreement.Add('of any information furnished');
      DenialAgreement.Add('in a consumer report.');
    end;
    // CPCLIENTS-5020 To have blank for Denial receipt
    if SameText(HostSuffix, 'ATL') then
      DenialAgreement.Add('');
  end;

  procedure SetDefault(HostSuffix: string); // for OpenEPS only
  begin
    AcceptanceAgreement.Clear;
    if SameText(HostSuffix, 'BYL') or SameText(HostSuffix, 'FLC') then
    begin
      AcceptanceAgreement.Add('When you provide a check as payment,');
      AcceptanceAgreement.Add('you authorize us to use information');
      AcceptanceAgreement.Add('from your check to process a one-time');
      AcceptanceAgreement.Add('Electronic Funds Transaction (EFT)');
      AcceptanceAgreement.Add('or draft drawn from your account, or');
      AcceptanceAgreement.Add('process the payment as a check');
      AcceptanceAgreement.Add('transaction. You also authorize us to');
      AcceptanceAgreement.Add('process credit adjustments, if');
      AcceptanceAgreement.Add('applicable. If your payment is');
      AcceptanceAgreement.Add('returned unpaid, you authorize us to');
      AcceptanceAgreement.Add('collect your payment and the Return');
      AcceptanceAgreement.Add('fee amount below by an EFT(s) or');
      AcceptanceAgreement.Add('draft(s) from your account. If you');
      AcceptanceAgreement.Add('are presenting a corporate check,');
      AcceptanceAgreement.Add('you make these representations as an');
      AcceptanceAgreement.Add('authorized corporate representative');
      AcceptanceAgreement.Add('and agree that the corporation will be');
      AcceptanceAgreement.Add('bound by the NACHA rules.');
      PhoneNumber := '**************';
    end
    else if SameText(HostSuffix, 'ATL') then // CPCLIENTS-5020
    begin
      AcceptanceAgreement.Add('When you provide a check as payment,');
      AcceptanceAgreement.Add('you authorize us to use information');
      AcceptanceAgreement.Add('from your check to process a one-time');
      AcceptanceAgreement.Add('Electronic Funds Transaction (EFT)');
      AcceptanceAgreement.Add('or draft drawn from your account, or');
      AcceptanceAgreement.Add('process the payment as a check');
      AcceptanceAgreement.Add('transaction. You also authorize us to');
      AcceptanceAgreement.Add('process credit adjustments, if');
      AcceptanceAgreement.Add('applicable. If your payment is');
      AcceptanceAgreement.Add('returned unpaid, you authorize us to');
      AcceptanceAgreement.Add('collect your payment and the Return');
      AcceptanceAgreement.Add('fee amount below by an EFT(s) or');
      AcceptanceAgreement.Add('draft(s) from your account. If you');
      AcceptanceAgreement.Add('are presenting a corporate check,');
      AcceptanceAgreement.Add('you make these representations as an');
      AcceptanceAgreement.Add('authorized corporate representative.');
      PhoneNumber := '';
    end
    else if SameText(HostSuffix, 'NOV') then
    begin
      {
      AcceptanceAgreement.Add('I AUTHORIZE THE MERCHANT TO USE THE');
      AcceptanceAgreement.Add('INFORMATION FROM MY CHECK TO INITIATE');
      AcceptanceAgreement.Add('AN ELECTRONIC FUNDS TRANSFER (EFT) OR');
      AcceptanceAgreement.Add('THE PAPER DRAFT TO DEBIT MY BANK');
      AcceptanceAgreement.Add('ACCOUNT FOR THE AMOUNT OF THE');
      AcceptanceAgreement.Add('TRANSACTION. I ACKNOWLEDGE AND AGREE');
      AcceptanceAgreement.Add('THAT THE MERCHANT-INITIATED EFT IS NOT');
      AcceptanceAgreement.Add('A CHECK TRANSACTION AND IS GOVERNED BY');
      AcceptanceAgreement.Add('APPLICABLE EFT LAW. IN THE EVENT THAT');
      AcceptanceAgreement.Add('THE EFT OR DREAFT IS RETURNED UNPAID,');
      AcceptanceAgreement.Add('I UNDERSTAND AND AGREE THAT THE');
      AcceptanceAgreement.Add('MERCHANT MAY CHARGE A RETURN FEE OR');
      AcceptanceAgreement.Add('OTHER ADMINISTRATIVE FEE TO MY BANK');
      AcceptanceAgreement.Add('ACCOUNT VIA EFT OR DRAFT AS PERMITTED');
      AcceptanceAgreement.Add('BY STATE OR FEDERAL LAW.');
      }
      AcceptanceAgreement.Add('WHEN YOU PROVIDE A CHECK AS PAYMENT, YOU');
      AcceptanceAgreement.Add('AUTHORIZE US EITHER TO  USE  INFORMATION');
      AcceptanceAgreement.Add('FROM  YOUR  CHECK  TO  MAKE  A  ONE-TIME');
      AcceptanceAgreement.Add('ELECTRONIC  FUND  TRANSFER   FROM   YOUR');
      AcceptanceAgreement.Add('ACCOUNT OR TO PROCESS THE PAYMENT  AS  A');
      AcceptanceAgreement.Add('CHECK   TRANSACTION.   FUNDS   MAY    BE');
      AcceptanceAgreement.Add('WITHDRAWN FROM YOUR ACCOUNT AS  SOON  AS');
      AcceptanceAgreement.Add('THE SAME DAY AND YOU  WILL  NOT  RECEIVE');
      AcceptanceAgreement.Add('THE  CHECK  BACK  FROM  YOUR   FINANCIAL');
      AcceptanceAgreement.Add('INSTITUTION. IF YOUR PAYMENT IS RETURNED');
      AcceptanceAgreement.Add('DUE TO INSUFFICIENT FUNDS, YOU AUTHORIZE');
      AcceptanceAgreement.Add('US TO MAKE A  ONE-TIME  ELECTRONIC  FUND');
      AcceptanceAgreement.Add('TRANSFER FROM YOUR ACCOUNT TO COLLECT  A');
      AcceptanceAgreement.Add('FEE AS ALLOWED BY STATE LAW.');
    end
    else if SameText(HostSuffix, 'DEM') or
       SameText(HostSuffix, 'TRN') or
       SameText(HostSuffix, 'LML') then
    begin
      { // DEV-8002
      AcceptanceAgreement.Add('I authorize the merchant to convert');
      AcceptanceAgreement.Add('my check to an Electronic Funds');
      AcceptanceAgreement.Add('Transfer or paper draft, and to');
      AcceptanceAgreement.Add('debit my account for the amount of');
      AcceptanceAgreement.Add('the transaction. In the event that');
      AcceptanceAgreement.Add('my draft of EFT is returned unpaid,');
      AcceptanceAgreement.Add('I agree that a fee of the amount');
      AcceptanceAgreement.Add('shown below or the maximum allowed');
      AcceptanceAgreement.Add('by law may be charged to my account');
      AcceptanceAgreement.Add('via draft of EFT.');
      }
      AcceptanceAgreement.Add('I authorize the merchant to convert'); // DEV-8002 <
      AcceptanceAgreement.Add('my check to an Electronic Funds');
      AcceptanceAgreement.Add('Transfer or paper draft, and to');
      AcceptanceAgreement.Add('debit my account for the amount of');
      AcceptanceAgreement.Add('the transaction. In the event that');
      AcceptanceAgreement.Add('my draft or EFT is returned unpaid,');
      AcceptanceAgreement.Add('I agree that a fee of the amount');
      AcceptanceAgreement.Add('shown below may be charged to my');
      AcceptanceAgreement.Add('account via draft or EFT. I am aware');
      AcceptanceAgreement.Add('my acct may be debited as soon as');
      AcceptanceAgreement.Add('today and my check will not be');
      AcceptanceAgreement.Add('returned to me by my bank.'); // DEV-8002 >
      ReturnCheckFee := 2500;
    end
    else if SameText(HostSuffix, 'LYN') then
    begin
      AcceptanceAgreement.Add('I authorize conversion of my check');
      AcceptanceAgreement.Add('presented as part of this transaction');
      AcceptanceAgreement.Add('to an electronic funds transfer, which');
      AcceptanceAgreement.Add('will be used to debit from my account');
      AcceptanceAgreement.Add('the amount of the check.  I ackknow-');
      AcceptanceAgreement.Add('ledge return of my check back to me.');
      AcceptanceAgreement.Add('I agree that if the bank draft is');
      AcceptanceAgreement.Add('returned unpaid, I authorize Certegy');
      AcceptanceAgreement.Add('to debit my checking account by EFT');
      AcceptanceAgreement.Add('or bank draft, the amount of the check');
      AcceptanceAgreement.Add('together with a service charge of $25');
      AcceptanceAgreement.Add('or the maximum allowed by law.');
    end
    else // default agreement - same as DEM
    begin
      AcceptanceAgreement.Add('I authorize the merchant to convert');
      AcceptanceAgreement.Add('my check to an Electronic Funds');
      AcceptanceAgreement.Add('Transfer or paper draft, and to');
      AcceptanceAgreement.Add('debit my account for the amount of');
      AcceptanceAgreement.Add('the transaction. In the event that');
      AcceptanceAgreement.Add('my draft of EFT is returned unpaid,');
      AcceptanceAgreement.Add('I agree that a fee of the amount');
      AcceptanceAgreement.Add('shown below or the maximum allowed');
      AcceptanceAgreement.Add('by law may be charged to my account');
      AcceptanceAgreement.Add('via draft of EFT.');
    end;
  end;
begin
  try
    {$IFDEF MTXEPSDLL}
    aDir := DefaultDir;
    {$else MTXEPSDLL}
    aDir := WinEPSDir;
    {$ENDIF MTXEPSDLL}
    AcceptanceAgreement := TStringList.Create;
    DenialAgreement := TStringList.Create;  // CPCLIENTS-1190 & CPCLIENTS-5020
    if not FileExists(aDir + ECC_REC_INFO_FILENAME) then
    begin
      SM('****ERROR:  File does not exist in ' + aDir + ECC_REC_INFO_FILENAME);
      SM('            Use default acceptance agreement.');
      SetDefault(HostSuffix);
      SetDefaultForDenial(HostSuffix);      // CPCLIENTS-1190 & CPCLIENTS-5020
      Exit;
    end;
    fileName := aDir + ECC_REC_INFO_FILENAME;
    XMLConfig := TXMLConfiguration.Create;
    try
      XMLConfig.FXMLParser.LoadFromFile(aDir + ECC_REC_INFO_FILENAME);      // 828.5
      XMLConfig.FXMLParser.StartScan;                                       // 828.5
      XMLConfig.ScanElement(nil);
      if XMLConfig.Root.Name <> 'RECINFO' then
      begin
        SM('****ERROR: Wrong xml format: ' + aDir + ECC_REC_INFO_FILENAME);
        XMLConfig.Free;
        SetDefault(HostSuffix);
        SetDefaultForDenial(HostSuffix);    // CPCLIENTS-1190 & CPCLIENTS-5020
        Exit;
      end;
      Version := XMLConfig.Root.Attr.Values['Version'];
      if Version = '' then Version := DEFAULT_XML_VERSION;
      LastModified := XMLConfig.Root.Attr.Values['LastModified'];
      Found := False;
      for I := 0 to XMLConfig.Root.Children.Count - 1 do
      begin
        N1 := XMLConfig.Root.Children[I];
        if SameText(N1.Name, HostSuffix) then
        begin
          Found := True;
          for J := 0 to N1.Children.Count - 1 do
          begin
            N2 := N1.Children[J];
            if SameText(N2.Name, 'AcceptanceAgreement') then
            begin
              for K := 0 to N2.Children.Count - 1 do
              begin
                N3 := N2.Children[K];
                AcceptanceAgreement.Add(N3.Text);
              end;
            end
            else
            if SameText(N2.Name, 'DenialAgreement') then   // CPCLIENTS-1190 & CPCLIENTS-5020
            begin
              for K := 0 to N2.Children.Count - 1 do
              begin
                N3 := N2.Children[K];
                DenialAgreement.Add(N3.Text);
              end;
            end
            else
            if SameText(N2.Name, 'PhoneNumber') then
              PhoneNumber := N2.Text
            else
            if SameText(N2.Name, 'ReturnCheckFee') then
              ReturnCheckFee := StrToIntDef(N2.Text, 0);
          end;
        end;
      end;
      if not Found then
      begin
        SetDefault(HostSuffix);
        SetDefaultForDenial(HostSuffix);                   // CPCLIENTS-1190 & CPCLIENTS-5020
      end;
      ValidateVersion;
    finally
      XMLConfig.Free;
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TECCReceiptInfo.Create ' + e.message);
  end;
end;

destructor TECCReceiptInfo.Destroy;
begin
  FreeAndNil(AcceptanceAgreement);
  FreeAndNil(DenialAgreement);                             // CPCLIENTS-1190 & CPCLIENTS-5020

  inherited Destroy;    // Call the base class's destructor.
end;

function TECCReceiptInfo.ValidateVersion: boolean;
var
  ver: Double;
  sVer: string;
begin
  // TODO: need to convert XMLObj to double
  ver := GetValidXMLVersion(xfECCRecInfo);
  sVer := ConvertToDecimalSeparator(Version);     // 33046
  result := StrToFloatDef(sVer, 0) = ver;
  if not result then
    SM(Format('%s%s Version (%s) is %s valid (should be %.1f)',
        [iif(not result, '***WARNING ',''), ExtractFileName(fileName), sVer, iif(not result, 'not', ''), ver]));
end;

{ ---------------------------------------------------------------------------- }
constructor TIBMFtpInfo.Create(loadXML: Boolean);
var
  aDir : string;
begin
  try
    aDir := {$IFDEF MTXEPSDLL} DefaultDir; {$ELSE} ExtractFilePath(ParamStr(0)); {$ENDIF}
    if not FileExists(aDir + IBM_FTP_INFO_FILENAME) then
    begin
//JMR      SM('****WARNING:  File does not exist in ' + aDir + IBM_FTP_INFO_FILENAME);
//JMR      SaveToXML;
//JMR      SM('Blank xml file created: ' + aDir + IBM_FTP_INFO_FILENAME);
      Exit;
    end;
    if loadXML then
      LoadFromXML(aDir + IBM_FTP_INFO_FILENAME);
  except
    on e: exception do
      SM('****TRY..EXCEPT: TIBMFtpInfo.Create ' + e.message);
  end;
end;

destructor TIBMFtpInfo.Destroy;
begin
  inherited Destroy;
end;

function TIBMFtpInfo.LoadFromXML(aFileName: string; UseOldKey: boolean=false): Boolean;
var
  I: Integer;
  XMLConfig: TXMLConfiguration;
  N1: TXMLParserNode;
begin
  result := false;
  try
    XMLConfig := TXMLConfiguration.Create;
    try
      XMLConfig.FXMLParser.LoadFromFile(aFileName);                    // 828.5
      XMLConfig.FXMLParser.StartScan;                                  // 828.5
      XMLConfig.ScanElement(nil);
      if not SameText(XMLConfig.Root.Name, 'IBMFTPINFO') then
      begin
        SM('****ERROR: Wrong xml format: ' + aFileName);
        XMLConfig.Free;
        Exit;
      end;
      fileName := aFileName;
      Version := XMLConfig.Root.Attr.Values['Version'];
      if Version = '' then Version := DEFAULT_XML_VERSION;
      LastModified := XMLConfig.Root.Attr.Values['LastModified'];

      for I := 0 to XMLConfig.Root.Children.Count - 1 do
      begin
        N1 := XMLConfig.Root.Children[I];
        if SameText(N1.Name, 'PrimaryServerIP') then
          PrimaryServerIP := N1.Text;
        if SameText(N1.Name, 'BackupServerIP') then
          BackupServerIP := N1.Text;
        if SameText(N1.Name, 'UserName') then
          UserName := N1.Text;
        if SameText(N1.Name, 'Password') then
        begin
          Password := MTXEncryptionUtils.DecryptString(N1.Text);
        end;
      end;
      oldPrimaryServerIP := PrimaryServerIP;
      oldBackupServerIP := BackupServerIP;
      oldUserName := UserName;
      oldPassword := Password;
      ValidateVersion;
    finally
      XMLConfig.Free;
    end;
    result := True;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TIBMFtpInfo.LoadFromXML - ' + e.message);
  end;
end;

function TIBMFtpInfo.SaveToXML: Boolean;
var
  Root, Node: TXMLParserNode;
begin
  result := false;
  try
    Root := TXMLParserNode.Create(nil);
    try
      Root.Name := 'IBMFtpInfo';
      Root.Attr.Values['Version'] := FormatFloat(FORMAT_XML_VERSION, GetValidXMLVersion(xfIBMFtpInfo));
      Root.Attr.Values['LastModified'] := FormatDateTime(FORMAT_LASTMODIFIED, Now);
      Node := Root.AddChild('PrimaryServerIP');
      Node.Text := PrimaryServerIP;
      Node := Root.AddChild('BackupServerIP');
      Node.Text := BackupServerIP;
      Node := Root.AddChild('UserName');
      Node.Text := UserName;
      Node := Root.AddChild('Password');
      Node.Text := MTXEncryptionUtils.EncryptString(Password);
      Root.SaveToFile(IBM_FTP_INFO_FILENAME);

      if not SameText(PrimaryServerIP, oldPrimaryServerIP) then
        SMUserActivity('IBMFtpInfo.PrimaryServerIP modified: ' + oldPrimaryServerIP + ' -> ' + PrimaryServerIP);
      if not SameText(BackupServerIP, oldBackupServerIP) then
        SMUserActivity('IBMFtpInfo.BackupServerIP modified: ' + oldBackupServerIP + ' -> ' + BackupServerIP);
      if not SameText(UserName, oldUserName) then
        SMUserActivity('IBMFtpInfo.UserName modified.');
      if not SameText(Password, oldPassword) then
        SMUserActivity('IBMFtpInfo.Password modified.');
    finally
      Root.Free;
    end;
    result := true;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TIBMFtpInfo.SaveToXML - ' + e.message);
  end;
end;

function TIBMFtpInfo.ValidateVersion: boolean;
var
  ver: Double;
  sVer: string;
begin
  // TODO: need to convert XMLObj to double
  ver := GetValidXMLVersion(xfIBMFtpInfo);
  sVer := ConvertToDecimalSeparator(Version);     // 33046
  result := StrToFloatDef(sVer, 0) = ver;
  if not result then
    SM(Format('%s%s Version (%s) is %s valid (should be %.1f)',
        [iif(not result, '***WARNING ',''), ExtractFileName(fileName), sVer, iif(not result, 'not', ''), ver]));
end;

initialization
  ExtendedLog('UXMLCommon Initialization');
finalization
  ExtendedLog('UXMLCommon Finalization');

end.

