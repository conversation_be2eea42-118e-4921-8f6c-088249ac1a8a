// (c) MTXEPS, Inc. 1988-2008

{$I OpenEPS_Def.inc}

unit ComputerInfo;
{
v823.0 04-13-06 AFR    Initial checkin
}

interface

uses
  FinalizationLog,
  {$IFDEF MSWINDOWS}
  Windows,
  Winsock,
  Forms,
  {$ELSE}
  Libc,
  {$ENDIF}
  SysUtils, Classes;

type
  TComputerInfo = class
  private
    sLetter: string;
    iCapacity: Int64;
    iFreeSpace: Int64;
    bNT: boolean;
    sOS: string;
    slIP: TStringList;

    procedure GetHDInfo;
    function GetWinVersion: string;
    function GetNT: boolean;
    procedure GetIPAddresses;

  public
    constructor Create;
    destructor Destroy; override;
    function GetVersionString: string;
    function GetOSString: string;

  published
    property letter: string   read sLetter;
    property capacity: int64  read iCapacity;
    property FreeSpace: Int64 read iFreeSpace;
    property NT: boolean      read bNT;
    property OS: string       read sOS;
    property IP: TStringList  read slIP;
  end;

implementation


{$IFNDEF D2007}
uses
{$IFDEF LINUX}
  epsTrace,
  StringUtils,
  MTX_Constants,
{$ENDIF}
  GeneralUtilities,   // for SplitStr
  MTX_Utils,
  MTX_Lib;
{$ENDIF}

constructor TComputerInfo.Create;
begin
  slIP := TStringList.Create;
  GetHDInfo;
end;

destructor TComputerInfo.Destroy;
begin
  try //CPCLIENTS-18308 - To Free Object
  inherited;
  sLetter:= '';
  iCapacity:= 0;
  iFreeSpace:= 0;
  bNT:= False;
  sOS:= '';

  if Assigned(slIP) then FreeAndNil(slIP);
  except
    on e: exception do
    begin
      SM('TComputerInfo.Destroy - Exception occured ' + e.Message); // 18308
    end;
  end;
end;

// This code is based on code found at
// http://delphi.about.com/cs/adptips2000/a/bltip1100_2.htm
procedure TComputerInfo.GetHDInfo;
{$IFDEF MSWINDOWS}
var
  drv         : String;
  pVolName    : PChar;
  nVNameSer   : PDWORD;
  maxCmpLen   : DWord;
  FSSysFlags  : DWord;
  pFSBuf      : PChar;

  AvailToCaller, TotalBytes: Int64;
  TotalFreeBytes: PLargeInteger;
begin
  GetMem(pVolName, MAX_PATH);
  GetMem(pFSBuf, MAX_PATH);
  GetMem(nVNameSer, MAX_PATH);
  GetMem(TotalFreeBytes, MAX_PATH);

  // Get OS information
  bNT := GetNT;
  sOS := GetWinVersion;
  // Get drive letter
  drv := ExtractFileDrive(Application.ExeName);
  //Get the volume information
  GetVolumeInformation(PChar(drv), pVolName, MAX_PATH, nVNameSer,
                       maxCmpLen, FSSysFlags, pFSBuf, MAX_PATH);
  sLetter := drv;

  // Get Free Space.  GetDiskFreeSpaceEx works on 98 or greater.
  if ((bNT = false) and (Version = '4.0')) then begin
    raise Exception.Create('Hard drive capacity and free space can not be determined in Windows 95.');
    iCapacity := 0;
    iFreeSpace := 0;
  end
  else begin
    GetDiskFreeSpaceEx(PChar(drv), AvailToCaller, TotalBytes, TotalFreeBytes);
    iCapacity := (TotalBytes div 1048576);
    iFreeSpace := (TotalFreeBytes^ div 1048576);
  end;

  // Get IP addresses
  GetIPAddresses;

  //Application.ProcessMessages;
  {$IFNDEF D2007}     // calls the MTX_Lib delay function; later Delphi version can't compile MTX_lib 
  Delay(100);
  {$ENDIF}
  FreeMem(pVolName);
  FreeMem(pFSBuf);
  FreeMem(nVNameSer);
  FreeMem(TotalFreeBytes);
{$ELSE} 
var
  step: string;

  procedure DF(CommandStr: string);
  var
    i, FileSystemIdx, UsedSizeIdx, AvailableSizeIdx, MountDirIdx: integer;
    SL, tmpList: TStringList;
    tmpLetter, CmdOutput: string;
    tmpCapacity: Int64;
    tmpFreeSpace: Int64;
    
    WaitTime: Longint;
    _IOResult: integer;
    _ErrCount: integer;
  const
    FileName = '/usr/local/OpenEPS/df.txt';

    procedure ParseHeader(Header: string);
    var j: integer;
    begin
      for j := 0 to tmpList.Count -1 do
      begin
        if SameText(tmpList[j], 'Filesystem') then
          FileSystemIdx := j;
        if SameText(tmpList[j], 'Used') then
          UsedSizeIdx := j;
        if SameText(tmpList[j], 'Available') then
          AvailableSizeIdx := j;
        if SameText(tmpList[j], 'Mounted') then
          MountDirIdx := j;
      end;
    end;
  begin
    CmdOutput := '';
      
    FileSystemIdx := -1;
    UsedSizeIdx := -1;
    AvailableSizeIdx := -1;
    MountDirIdx := -1;

    try
      DeleteFile(FileName);
      Libc.System(PChar('chmod 777 ' + FileName));
      Libc.System(PChar(CommandStr + ' > ' + FileName));
      //showTrace(idIN, '[GetHDInfo] CommandStr=' + CommandStr + ' > ' + FileName);
      {$IFNDEF D2007}              //JTG: oh come on.. do we really want to tie our showtrace command in with the ComputerInfo unit?
      if NOT FileExists(FileName) then
        showTrace(idIN, '[GetHDInfo] Failed to create - ' + FileName);

      if FileExists(FileName) and (_ErrCount > 0) then
        showTrace(idIN, Format('[GetHDInfo] Get result after %d trial.', [_ErrCount]))
      else if NOT FileExists(FileName) and (WaitTime > FILE_LOCK_WAIT_MS) then
        showTrace(idIN, Format('[GetHDInfo] Timed out to get df result (%d time(s) tried)', [_ErrCount]));
      {$ENDIF}

      SL := TStringList.Create;
      try
        SL.LoadFromFile(FileName);

        //for i := 0 to SL.Count -1 do showTrace(idIN, Format('[GetHDInfo] %d: %s', [i, SL[i]]));
        sLetter := '';
        iCapacity := 0;
        iFreeSpace := 0;
        for i := 0 to SL.Count -1 do
        begin
          while Pos('  ', SL[i]) > 0 do
            SL[i] := ReplaceString(SL[i],'  ',' ');
          tmpList := TStringList.Create;
          try
            SplitStr(SL[i], ' ', tmpList, true);
            // parse header
            if i = 0 then
              ParseHeader(SL[i]);
            if (FileSystemIdx = -1) or (UsedSizeIdx = -1) or (AvailableSizeIdx = -1) or (MountDirIdx = -1) then
            begin
              showTrace(idIN, CommandStr + ' result has unexpected format');
              sLetter := '/';
              iCapacity := 0;
              iFreeSpace := 0;
              Exit;
            end;
            
            tmpLetter := tmpList[FileSystemIdx];
            tmpCapacity := (StrToIntDef(tmpList[UsedSizeIdx], 0) + StrToIntDef(tmpList[AvailableSizeIdx],0)) div 1024;
            tmpFreeSpace := StrToIntDef(tmpList[AvailableSizeIdx], 0) div 1024;

            //showTrace(idIN, Format('[GetHDInfo] Command=%s: Drive=%s, Capacity=%d (MB), Available=%d (MB), Mount=%s',
            //  [CommandStr, tmpLetter, tmpCapacity, tmpFreeSpace, tmpList[MountDirIdx]]));

            // check biggist drive
            if iCapacity < tmpCapacity then
            begin
              sLetter := tmpLetter;
              iCapacity := tmpCapacity;
              iFreeSpace := tmpFreeSpace;
            end;

            // get root info
            if SameText(tmpList[MountDirIdx], '/') then
            begin
              sLetter := tmpLetter;
              iCapacity := tmpCapacity;
              iFreeSpace := tmpFreeSpace;
              Exit;
            end;
          finally
            FreeAndNil(tmpList);
          end;
        end;
      finally
        FreeAndNil(SL);
        DeleteFile(FileName);
      end;
    except
      ;
    end;
  end;
begin
  step := 'df';
  DF(step);
  if (iCapacity = 0) and (iFreeSpace = 0) then
  begin
    step := 'busybox df';
    DF(step);
  end;
  showTrace(idIN, Format('[GetHDInfo] Step >%s< used', [Step]));
{$ENDIF}
end;

function TComputerInfo.GetWinVersion: string;
{$IFDEF MSWINDOWS}
var
   osVerInfo: TOSVersionInfo;
   majorVersion, minorVersion: Integer;
{$ENDIF}
begin
{$IFDEF MSWINDOWS}
   osVerInfo.dwOSVersionInfoSize := SizeOf(TOSVersionInfo) ;
   if GetVersionEx(osVerInfo) then
   begin
     minorVersion := osVerInfo.dwMinorVersion;
     majorVersion := osVerInfo.dwMajorVersion;
     result := inttostr(majorVersion) + '.' + inttostr(minorVersion);
   end
   else
     result := 'unknown';
{$ENDIF}
end;

function TComputerInfo.GetNT: boolean;
{$IFDEF MSWINDOWS}
var
   osVerInfo: TOSVersionInfo;
{$ENDIF}
begin
{$IFDEF MSWINDOWS}
  result := false;
  osVerInfo.dwOSVersionInfoSize := SizeOf(TOSVersionInfo) ;
  if GetVersionEx(osVerInfo) then
  begin
    if osVerInfo.dwPlatformId = VER_PLATFORM_WIN32_NT then
      result := true
    else if osVerInfo.dwPlatformId = VER_PLATFORM_WIN32_WINDOWS then
      result := false
    else
      raise Exception.Create('Can not determine if computer is NT based.');
  end
{$ENDIF}
end;

function TComputerInfo.GetVersionString: string;
begin
{$IFDEF MSWINDOWS}
  if bNT then
    begin
    if (sOS = '4.0')          then result := 'Windows NT4'
    else if (sOS = '5.0')     then result := 'Windows 2000'
    else if (sOS = '5.1')     then result := 'Windows XP'
    else if (sOS = '5.2')     then result := 'Windows Server 2003 or Windows XP x64'
    else if (sOS = '6.0')     then result := 'Windows Vista'
    else if (sOS = '6.1')     then result := 'Windows 7'           //JTG Win7 is called 6.1 AND 7.x
    else if pos('7.',sOS) > 0 then result := 'Windows 7'
    else                           result := 'Windows UNK';
    end
  else
    begin
    if (sOS = '4.0') then       result := 'Windows 95'
    else if (sOS = '4.10') then result := 'Windows 98'
    else if (sOS = '4.90') then result := 'Windows ME'
    else                        result := 'Windows UNK';
  end;
{$ENDIF}
end;

function TComputerInfo.GetOSString: string;
{$IFDEF MSWINDOWS}
begin
  result := sOS + '|' + booltostr(bNT) + '|' + GetversionString;
end;  
{$ELSE}                                                                         
var
  MachineInfo: utsname;
begin
  uname(MachineInfo);
  Result := Format('%s|0|%s', [MachineInfo.release, MachineInfo.sysname]);
end;
{$ENDIF}


// This code found at:
// http://www.delphipages.com/threads/thread.cfm?ID=86015&G=86014
procedure TComputerInfo.GetIPAddresses;
{$IFDEF MSWINDOWS}
type
 TaPInAddr = array[0..0] of PInAddr;
 PaPInAddr = ^TaPInAddr;
var
 phe: PHostEnt;
 pptr: PaPInAddr;
 Buffer: array[0..63] of AnsiChar;
 i: integer;
 GInitData: TWSADATA;
 address: in_Addr;
 addressstr: string;
begin
 WSAStartup($101, GInitData);
 try
   GetHostName(Buffer, SizeOf(Buffer));
   phe := GetHostByName(Buffer);
   if not Assigned(phe) then Exit;
   pptr := PaPInAddr(phe^.h_addr_list);
   i := 0;
   {$R-}
   while Assigned(pptr^[i]) do begin
     address := pptr^[i]^;
     addressstr := inet_ntoa(address);
     if addressstr <> '127.0.0.1' then begin
       slIP.Add(addressstr);
     end;
     inc(i);
   end;
   {$R+}
 finally
   WSACleanup;
 end;
{$ELSE}                                                                                                 
var
  Sock: Integer;
  IfReq: TIfReq;
  SockAddrPtr: PSockAddrIn;
  ListSave, IfList: PIfNameIndex;
begin
  //need a socket for ioctl()
  Sock := socket(AF_INET, SOCK_STREAM, 0);
  if Sock < 0 then
    RaiseLastOSError;

  try
    //returns pointer to dynamically allocated list of structs
    ListSave := if_nameindex();
    try
      IfList := ListSave;
      //walk thru the array returned and query for each
      //interface's address
      while IfList^.if_index <> 0 do
      begin
        //copy in the interface name to look up address of
        strncpy(IfReq.ifrn_name, IfList^.if_name, IFNAMSIZ);
        //get the address for this interface
        if ioctl(Sock, SIOCGIFADDR, @IfReq) <> 0 then
          RaiseLastOSError;
        //print out the address
        SockAddrPtr := PSockAddrIn(@IfReq.ifru_addr);
        //Results.Add(Format('%s=%s', [IfReq.ifrn_name, inet_ntoa(SockAddrPtr^.sin_addr)]));
        if inet_ntoa(SockAddrPtr^.sin_addr) <> '127.0.0.1' then
          slIP.Add(inet_ntoa(SockAddrPtr^.sin_addr));
        Inc(IfList);
      end;
    finally
      //free the dynamic memory kernel allocated for us
      if_freenameindex(ListSave);
    end;
  finally
    Libc.__close(Sock)
  end;
{$ENDIF}
end;

initialization
  ExtendedLog('ComputerInfo initialization');
finalization
  ExtendedLog('ComputerInfo finalization');


end.
