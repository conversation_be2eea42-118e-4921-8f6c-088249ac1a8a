// (c) MTXEPS, Inc. 1988-2008
Unit Fmt_8583; { Format 8583 }
{ **
v815.3 04-28-05 TSL-F Change parm list of TruncIfNeeded call
v815.2 03-14-05 TSL-E add Structure_Var_LLLsBcd
v813.1 02-09-04 TSL-D Changes to truncate account number/track2
  ** 01-28-99 TSL-C Change Setting Bit to Dlist from SM
  ** 01-04-00 TSL-B Additions for Bcd structure
  ** 11-01-99 TSL-A Take out references to all but windows
  ** 05-03-99 JGS JGS-A Set new strings to '' to stop garbage print when set error occurs
  *************************************************************************************** }
{ ================================================================= }
Interface
{ ================================================================= }
Uses
     FinalizationLog,
     {$IFDEF MSWINDOWS}   
     Windows,
     {$ENDIF}
     Base8583,
     MTX_Constants,
     MTX_Lib;

function  BCD(AnyNumber :AnsiString): AnsiString;   // TSL-B
function  bcdLen(aStr: AnsiString): AnsiString;
function  HEXBcd(inStr: AnsiString):AnsiString;     // TSL-B
Procedure Clear_All_Out_Bits (Var In_Ptr : P_ISO);
Procedure Cancel_Out_Bit     (Var In_Ptr : P_ISO; Out_Bit : Integer);
Procedure Set_Bits_Required  (Var In_Ptr : P_ISO; B1, B2, B3, B4, B5, B6, B7, B8, B9, BA, BB, BC, BD, BE, BF : Integer);
Procedure Set_Bit_Data       (Var In_Ptr : P_ISO; Out_Bit : Integer; In_Str : AnsiString);
Function  Bit_Is_Required    (Var In_Ptr : P_ISO; Out_Bit : Integer) : Boolean;
Function  Format_8583        (Var In_Ptr : P_ISO; Var Out_Data : B_Record;
                              Var PrintData: arrayByte) : Boolean;
Function  A_Copy             (Var In_Char; Len : Integer) : AnsiString;
Function  A_Int              (Var In_Char : AnsiChar; Len : Integer) : Integer;
{ ================================================================= }
Implementation
{ ================================================================= }

{$IFDEF HOSTSIM}
uses smToDisplay;
{$ENDIF HOSTSIM}

function BCD(AnyNumber :AnsiString): AnsiString;   { BCD a number }
var                                        { '='(3D) becomes D0 or 0D }
  tempStr : AnsiString;                        { '?'(3F) filler becomes 0F }
  i,
  tempChr1,
  tempChr2 : byte;
begin
  if (length(AnyNumber) mod 2 <> 0) then            { must be even # digits }
    AnyNumber := AnyNumber + '?';                   { filler }

  i := 1;
  tempStr := '';
  while (i < length(AnyNumber)) do                  { turn 2 digits at }
  begin                                             { a time into hex chars }
    tempChr1 := ord(AnyNumber[i]) shl 4;            { move right 4 bits to left }
    tempChr2 := ord(AnyNumber[i+1]) and $0F;        { keep only right 4 bits }
    tempStr := tempStr + AnsiChar(tempChr1 or tempChr2); { put them together }
    inc(i,2);                                       { bump counter }
  end;
  BCD := tempStr;
end;  { function BCD }

function bcdLen(aStr: AnsiString): AnsiString;
begin
  result := bcd(zfill(str_(length(aStr)), 4));
end;

function HEXBcd(inStr: AnsiString):AnsiString;
var                                    { Compact to 8 bytes like BCD }
  i,j : byte;
  tempStr : AnsiString;
begin
  tempStr := '';
  for i := 1 to length(inStr) do
  begin
     j := ord(InStr[i]);                    { get char }
     if (j > 64)
       then tempStr := tempStr + AnsiChar(j + 9)      { if A..F add 9 so BCD works }
       else tempStr := tempStr + InStr[i];       { i.e. A = $41 + 9 =   $4A   }
  end;
  HEXBCD := bcd(tempStr);
end;

Procedure Clear_All_Out_Bits(Var In_Ptr : P_ISO);
Var i: Integer;
begin
with In_Ptr^ do
begin
  FillChar(Out_Bit_Map_16, SizeOf(Out_Bit_Map_16), 0);
  FillChar(Out_Bit_Map_08, SizeOf(Out_Bit_Map_08), 0);
  for i := 1 to (Max_BitMaps * 64) do
  begin
    Bit_Data_Out[i] := '';
    Bit_Data_OutTrunc[i] := '';
  end;
  FillChar(Bit_Data_Out_Set, SizeOf(Bit_Data_Out_Set), False);
end;
end;

{ * }

Procedure Cancel_Out_Bit(Var In_Ptr: P_ISO; Out_Bit: Integer);
begin
with In_Ptr^ do
begin
  if Range(Out_Bit) then
    Bit_Data_Out_Set[Out_Bit] := False;
end;
end;

{ * }

Procedure Set_Bits_Required(Var In_Ptr : P_ISO; B1, B2, B3, B4, B5, B6, B7, B8, B9, BA, BB, BC, BD, BE, BF : Integer);
begin
with In_Ptr^ do
begin
  if Range(B1) then Bit_Data_Out_Set[B1] := True;
  if Range(B2) then Bit_Data_Out_Set[B2] := True;
  if Range(B3) then Bit_Data_Out_Set[B3] := True;
  if Range(B4) then Bit_Data_Out_Set[B4] := True;
  if Range(B5) then Bit_Data_Out_Set[B5] := True;
  if Range(B6) then Bit_Data_Out_Set[B6] := True;
  if Range(B7) then Bit_Data_Out_Set[B7] := True;
  if Range(B8) then Bit_Data_Out_Set[B8] := True;
  if Range(B9) then Bit_Data_Out_Set[B9] := True;
  if Range(BA) then Bit_Data_Out_Set[BA] := True;
  if Range(BB) then Bit_Data_Out_Set[BB] := True;
  if Range(BC) then Bit_Data_Out_Set[BC] := True;
  if Range(BD) then Bit_Data_Out_Set[BD] := True;
  if Range(BE) then Bit_Data_Out_Set[BE] := True;
  if Range(BF) then Bit_Data_Out_Set[BF] := True;
end;
end;

{ * }

Procedure Set_Bit_Data(Var In_Ptr : P_ISO; Out_Bit : Integer; In_Str : AnsiString);
Var
  ByteToTest,
  BitToTest   : Integer;
  tmpStr      : AnsiString;
  varLen: AnsiString;
begin
with In_Ptr^ do
begin
  if (Range(Out_Bit) = True) then
    begin
      ByteToTest := ((Out_Bit-1) shr 3) + 1;   { Div 8, Which of 8 bytes is it in }
      BitToTest  := Out_Bit mod 8;             { bit 1 to 8 of the hex byte       }
      if (BitToTest = 0) then
        BitToTest := 8;                        { fix for:  bit8 mod 8 = 0         }

      if (Bit_Format[Out_Bit] = Structure_Unknown) then
        begin
          SM('Bit('+Str_(Out_Bit)+') is undefined for data >'+In_Str+'<');
          Exit;
        end;

      Out_Bit_Map_08[ByteToTest] := Out_Bit_Map_08[ByteToTest] or HexBit[BitToTest];

      if (Bit_Format[Out_Bit] = Structure_BitMap) then
        begin
          DList('Bit Map Bit '+Str_(Out_Bit)+' Set');
          Exit;
        end;

      Bit_Data_Out_Set[Out_Bit] := True;
      Bit_Data_Out[Out_Bit] := ''; //JGS-A

      if (Bit_Format[Out_Bit] = Structure_Fixed)    or
         (Bit_Format[Out_Bit] = Structure_FixedBcd) then     // TSL-B
        begin
          if (Length(In_Str) <> Bit_Min_Len[Out_Bit]) then   // compare before packing
            SM('****WARNING(fmt_8583): Bit('+Str_(Out_Bit)+') Length('+ Str_(Length(In_Str))
              +') is not of required size='+Str_(Bit_Min_Len[Out_Bit]))
          else
            if (Bit_Format[Out_Bit] = Structure_Fixed) then  // TSL-B
              Bit_Data_Out[Out_Bit] := In_Str
            else
              begin                                           // FixedBcd
                if (Bit_Min_Len[Out_Bit] mod 2 <> 0) then     // odd len?
                  Bit_Data_Out[Out_Bit] :=  Bcd('0' + In_Str) // yes, make even
                else
                  Bit_Data_Out[Out_Bit] :=  Bcd(In_Str);      // no, OK
              end;
        end
      else
        if (Bit_Format[Out_Bit] in Structure_VarSet) then
        begin
          if (Length(In_Str) < Bit_Min_Len[Out_Bit]) or (Length(In_Str) > Bit_Max_Len[Out_Bit]) then
            SM('****WARNING(fmt_8583): Bit('+Str_(Out_Bit)+') Length('+ Str_(Length(In_Str))
              +') is out of size limits (min/max)='+Str_(Bit_Min_Len[Out_Bit])
              +'/'+Str_(Bit_Max_Len[Out_Bit]))
          else
          begin
            case Bit_Format[Out_Bit] of
              Structure_Var_L   : varLen := ZFill(Str_(Length(In_Str)), 1);
              Structure_Var_LL  : varLen := ZFill(Str_(Length(In_Str)), 2);
              Structure_Var_LLL : varLen := ZFill(Str_(Length(In_Str)), 3);
              Structure_Var_LLLL: varLen := ZFill(Str_(Length(In_Str)), 4);
            end;
            Bit_Data_Out[Out_Bit] := varLen + In_Str;
          end;
        end
        else
          if (Bit_Format[Out_Bit] in Structure_BcdSet) then  // TSL-B
            begin
              if (Length(In_Str) < Bit_Min_Len[Out_Bit]) or (Length(In_Str) > Bit_Max_Len[Out_Bit]) then
                SM('****WARNING(fmt_8583): Bit('+Str_(Out_Bit)+') Length('+ Str_(Length(In_Str))
                  +') is out of size limits (min/max)='+Str_(Bit_Min_Len[Out_Bit])
                  +'/'+Str_(Bit_Max_Len[Out_Bit]))
              else
                begin
                  case Bit_Format[Out_Bit] of
                    Structure_Var_LLBcd: Bit_Data_Out[Out_Bit] := Bcd(Zfill(Str_(Length(In_Str)), 2)) + Bcd(In_Str);
                    Structure_Var_LLLBcd: Bit_Data_Out[Out_Bit] := Bcd(Zfill(Str_(Length(In_Str)), 4)) + Bcd(In_Str);
                    Structure_Var_LLLsBcd:
                      begin
                        tmpStr := Bcd(In_Str);
                        Bit_Data_Out[Out_Bit] :=
                              Bcd(Zfill(Str_(Length(tmpStr)), 4)) + tmpStr;
                      end;
                    else  { same as Structure_Var_LLLBcd }
                      Bit_Data_Out[Out_Bit] := Bcd(Zfill(Str_(Length(In_Str)), 4)) + Bcd(In_Str);
                  end;
                end;
            end
          else
          if (Bit_Format[Out_Bit] in Structure_LenBcdSet) then
          begin
            if (Length(In_Str) < Bit_Min_Len[Out_Bit]) or (Length(In_Str) > Bit_Max_Len[Out_Bit]) then
              SM('****WARNING(fmt_8583): Bit('+Str_(Out_Bit)+') Length('+ Str_(Length(In_Str))
                +') is out of size limits (min/max)='+Str_(Bit_Min_Len[Out_Bit])
                +'/'+Str_(Bit_Max_Len[Out_Bit]))
            else
            begin
              if (Bit_Format[Out_Bit] = Structure_LLOnlyBcd)
                then Bit_Data_Out[Out_Bit] := Bcd(Zfill(Str_(Length(In_Str)), 2)) + In_Str
                else Bit_Data_Out[Out_Bit] := Bcd(Zfill(Str_(Length(In_Str)), 4)) + In_Str;
            end;
          end
          else
          if (Bit_Format[Out_Bit] = Structure_HexBcd) then
              Bit_Data_Out[Out_Bit] := HexBcd(In_Str)
          else
          begin
            SM('****WARNING(fmt_8583): Bit '+Str_(Out_Bit)+'has invalid structure');
            Exit;
          end;

      if (Bit_Data_Out[Out_Bit] = '') then
      begin
        SM('****WARNING(fmt_8583): Bit('+Str_(Out_Bit)+') has been set to NULL data');
        Bit_Data_OutTrunc[Out_Bit] := '';
      end
      else
      begin
        if (Bit_Format[Out_Bit] in Structure_VarSet)
          then Bit_Data_OutTrunc[Out_Bit] := truncIfNeededVar(In_Ptr,Out_Bit,varLen,In_Str)
          else Bit_Data_OutTrunc[Out_Bit] := truncIfNeeded(In_Ptr,Out_Bit,In_Str);
        Dlist('Setting Bit('+Str_(Out_Bit)+')=>'+Bit_Data_OutTrunc[Out_Bit]+'<');   { TSL-C }
      end;
  end;
  In_Str := StringOfChar(' ', length(In_Str));      // for PCI blank out incoming data
end;
end;

{ * }

Function  Bit_Is_Required(Var In_Ptr: P_ISO; Out_Bit: Integer) : Boolean;
begin
with In_Ptr^ do
begin
  if Range(Out_Bit)
    then Bit_Is_Required := Bit_Data_Out_Set[Out_Bit]
    else Bit_Is_Required := False;
end;
end;

{ Make a string out of a Character array }

Function  A_Copy(Var In_Char; Len : Integer) : AnsiString;
Type
  In_Array = Array[1..65535] of AnsiChar;
Var
  Indx : Integer;
begin
  result := '';
  for Indx := 1 to Len do
    result := result + In_Array(In_Char)[Indx];
end;

Function A_Int(Var In_Char : AnsiChar; Len : Integer) : Integer; { Make an Integer out of a character array }
Var tmp: AnsiString;
begin
  tmp  := A_Copy(In_Char, Len);
  result := MyVal(tmp);
end;

{ * }

Function  Convert_Out_Bit_Maps(Var In_Ptr: P_ISO) : AnsiString;
Var
  Num_Bit_Maps,
  I,
  J        : Integer;
  Temp_Str : AnsiString;
  tmpMaps  : AnsiString;
begin
with In_Ptr^ do
begin
  Num_Bit_Maps := 1;
  I := 1;
  while (I <= Max_BitMaps-1) do                     { Need to set next bit map present bits }
  begin
    { Check for all bytes of Next bit maps for "anything" }
    J := ((Max_BitMaps-I) * 8); { Length beyond this bit map }
    if (A_Copy(Out_Bit_Map_08[(I * 8)+1], J) <> String_(J, AnsiChar(0))) then
    begin
      for J := 1 to 64 do  { Look for "next bit map" bit }
      begin
        if (Bit_Format[((I-1)*64)+J] = Structure_BitMap) then
        begin
          Set_Bit_Data(In_Ptr, ((I-1)*64)+J, '');
          Inc(Num_Bit_Maps);
          Break;
        end;
      end;
    end;

    Inc(I);
  end;

   if (Bit_Map_Type = Bit_Map_Display) then
   begin
     for I := 1 to (Max_BitMaps * 8) do      { Convert to display }
     begin
       Temp_Str := Hex_Set[(Out_Bit_Map_08[I] shr 4)+1] + Hex_Set[(Out_Bit_Map_08[I] and $0F) + 1];
       Out_Bit_Map_16[((I-1) shr 3)+1] := Out_Bit_Map_16[((I-1) shr 3) + 1] + Temp_Str;
     end;

     tmpMaps := 'Outgoing Bit Maps(Chr):';  { log the maps }
     for i := 1 to Num_Bit_Maps do
       tmpMaps := tmpMaps + ' ' + str_(i) + '>' + Out_Bit_Map_16[i] + '<';
     DList(tmpMaps);

     result := '';
     for I := 1 to Num_Bit_Maps do                { Assemble into returned string }
       result := result + Out_Bit_Map_16[I];
   end
   else
     result := A_Copy(Out_Bit_Map_08, (Num_Bit_Maps * 8));
end;
end;   { Convert_Out_Bit_Maps }

Function  Format_8583(Var In_Ptr : P_ISO; Var Out_Data : B_Record;
                      Var PrintData: arrayByte) : Boolean;
Var
  Format_Status : Boolean;
  I,
  Chr_Ptr       : Integer;
  Temp_Str      : AnsiString;

  Procedure Append_To_Output(aBit: integer; In_Str : AnsiString);
  var i: integer;
  begin
    for i := 1 to Length(In_Str) do
    begin
      Out_Data.B_Data[Chr_Ptr + i - 1] := ord(In_Str[i]);
      if (aBit = 0) then
        PrintData[Chr_Ptr + i - 1] := ord(In_Str[i])
      else
      begin
        if (i <= length(In_Ptr.Bit_Data_OutTrunc[aBit]))
          then PrintData[Chr_Ptr + i - 1] := ord(In_Ptr.Bit_Data_OutTrunc[aBit][i])
          else PrintData[Chr_Ptr + i - 1] := ord(' ');
      end;
    end;
    Inc(Chr_Ptr, Length(In_Str));
    In_Str := StringOfChar(' ', length(In_Str));
  end;

begin
with Out_Data do
begin
with In_Ptr^ do
begin
  Format_Status := True;
  Chr_Ptr := B_Len + 1;                         { Use his offset }
  FillChar(Out_Data, SizeOf(Out_Data), 0);

  if (MsgType_Structure = MsgDisplay) then      { TSL-B }
    Append_To_Output(0,Msg_Type)
  else
  begin
    Temp_str := BCD(Msg_Type);
    Append_To_Output(0,Temp_Str);
  end;

  Temp_Str := Convert_Out_Bit_Maps(In_Ptr);
  Append_To_Output(0,Temp_Str);

  for I := 1 to (Max_BitMaps * 64) do
    if Bit_Data_Out_Set[I] then
    begin
      if (Bit_Format[I] in Structure_DataSet) then
      begin
        if (Length(Bit_Data_Out[I]) > 0) then
           Append_To_Output(i, Bit_Data_Out[I])
        else
        begin
          SM('****WARNING(fmt_8583): Bit='+Str_(I)+' is set but data length=0');
          Format_Status := False;
        end;
      end
      else
      if (Bit_Format[I] <> Structure_BitMap) then
      begin
        SM('****WARNING(fmt_8583): Bit='+Str_(I)+' has undefined structure='+Str_(Bit_Format[I]));
        Format_Status := False;
      end;
    end;

  B_Len := Chr_Ptr - 1;
  Format_8583 := Format_Status;
end;
end;
end;    { Format_8583 }

{ Unit init }

initialization
  ExtendedLog('Fmt_8583 Initialization');
finalization
  ExtendedLog('Fmt_8583 Finalization');

end.


