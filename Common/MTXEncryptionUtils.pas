unit MTXEncryptionUtils;
// 7 Aug 2012 JMR: moved non-sensitive constants & methods out of MTXEncryption.pas and put here (to minimize dcu mismatch errors)

{$I OpenEPS_Def.inc}

interface

uses
  FinalizationLog,
  Classes, DBClient, SysUtils, MTX_Constants, MTX_Utils, MTX_Lib,
  TypInfo,
  Types, DateUtils,
  {$IFNDEF WOLF}
  SBConstants, SBX509, SBCustomCertStorage, SBPEM, SBPublicKeyCrypto, //SSL related
  SBUtils, SBSHA2, {$IFNDEF SBB9} SBTypes, {$ENDIF}
  LbCipher, LbString, LbProc, LbClass, LbRSA, { TurboPower Lockbox encryption components}
  LbUtils, // GetHash
  DCPcrypt2, DCPblockciphers, DCPdes,
//  Base64,
  {$ENDIF}
  uKEK3,
  MdMsg,
  MTX_Types,
  XSBuiltIns;   { for IBM encryption/decryption }

type
  TMTX_CertFile = (cfUnknown=0, cfEngine, cfOpenEPS, cfRS, cfFtp, cfTermMX, cfOpenIP, cfTermHYC, cfTermIngenico, cfTermHYCPCIv3, cfWebEPS);
  TMTX_CertFiles = set of TMTX_CertFile;

//JMR: Do not use ServerEPSConstants or any other file from the OpenEps directory in this unit!!! Any no project conditional defines!!!
(*
procedure MakeKEK3EncryptionKey; // for writing/reading the KEK3 in the jrnl/spool
procedure MakeMsgEncryptionKey; { msgs between wineps & openeps }
procedure MakeMsgEncryptionKeyTH; { msgs between OpenIP engine & TermHandler }
procedure MakeMsgEncryptionKeyOld; // old openeps trx files
procedure MakeFileEncryptionKeyForCSV; { key to encrypt customer CSV key }
procedure MakeFileEncryptionKeyForOperatorXML; { key to encrypt operator.xml file }
procedure MakeCSVDecryptionKey;
procedure DecryptUpgradeFile(infile, outfile: string); { decrypts old files that were encrypted during upgrade }
function EncryptDecrypterKey(s:string) : string;
function DecryptDecrypterKey(s:string) : string;
*)

// these are simply wrappers that call the actual methods in MTXEncryptionPrivate.pas
function Base64Encode(const s:AnsiString): AnsiString;
function Base64Decode(const s:AnsiString): AnsiString;
function EncryptString(var s:AnsiString): AnsiString;
function DecryptString(s:AnsiString): AnsiString;
function DecryptFileKeyWithVersion(sKey,sVersion: AnsiString; newKek3: AnsiString = ''): AnsiString; // used by RecFile when reading
function KeyVerificationStringOK(verification: AnsiString; sKey: AnsiString): boolean; // used by RecFile & MTX_Lib when reading
function EncryptFileData(s: AnsiString; sKey: AnsiString): AnsiString; // used by RecFile when writing
function DecryptFileData(s: AnsiString; sKey: AnsiString): AnsiString; // used by RecFile when reading
function GetEncryptedKeyVerificationString(sKey: AnsiString): AnsiString; // used by RecFile when writing
function EncryptFileKey(sKey:AnsiString; newKek3: AnsiString = ''; sVersion: AnsiString=KEY_VERSION): AnsiString; // used by RecFile when writing
procedure MakeKeyEncryptionKey(sKey: AnsiString = ''); // AKA: KEK - for encrypting/decrypting file keys
function EncryptKEK3(aKEK3: AnsiString): AnsiString; // used by engine & openeps to encrypt KEK3 for writing to the spool/jrnl
{$IFDEF TEST}
  function DecryptKEK3(aKEK3: AnsiString): AnsiString; // used by QA tool for getting the KEK3 from the spool/jrnl
{$ENDIF}
function GetServerEPSKey(const KeyType: TServerEPSKeyType; IsPublixHost: boolean=false): AnsiString;
function AES128EncryptCBC(Key,Value: AnsiString): AnsiString; overload;
function AES128DecryptCBC(Key,Value: AnsiString): AnsiString; overload;
{$IFDEF WOLF}
function AES128Encrypt(Key: TMtxKey128; Value: AnsiString): AnsiString; overload; // non-CBC, used for MX terminal
function AES128EncryptCBC(Key: TMtxKey128; Value: AnsiString): AnsiString; overload;
function AES128DecryptCBC(Key: TMtxKey128; Value: AnsiString): AnsiString; overload;
{$ENDIF}
function Encrypt3DES(aKeyType: TEncryptionKey; s: AnsiString; aIsECBMode: boolean=false): AnsiString;
function Decrypt3DES(aKeyType: TEncryptionKey; s: AnsiString; aIsECBMode: boolean=false): AnsiString;
{$IFNDEF WOLF}
function DecryptStringOld(s:AnsiString): AnsiString; // 3DES
function SBHashSHA256(Filename: string): AnsiString;         // new version using 1024 size chunks
function LoadCertificateToStorage(aCertFile: TMTX_CertFile;
    var aMemoryCertStorage: TElMemoryCertStorage; aServerKind: TMTX_ServerKind=skUnknown): boolean;
{$ENDIF}
function SetSessionKey(aKeyType: TEncryptionKey; sKey: AnsiString): boolean;
function LoadCustomCertificate(aCertFile: TMTX_CertFile; aCertFileName: string): boolean;
//function EncryptStringOld(s:AnsiString): AnsiString; // 3DES - this should be removed
function StringToKey128(s:AnsiString): TMtxKey128;
//function DecryptCSVKey(s:AnsiString): AnsiString;
//function EncryptCSVKey(s:AnsiString): AnsiString;
//procedure EncryptCSVFile(InStream: TStream; outfile: AnsiString; Key: TKey128); { uses customer key }
//function DecryptStringIBM(aInData:AnsiString): AnsiString; // 3DES - only used for IBM
//function DecryptStringDSI(aInData:AnsiString): AnsiString; // 3DES - only used with ScanMaster
//procedure EncryptXMLFile(InStream: TStream; OutFile: AnsiString);
//function LoadStreamFromEncryptedXMLFile(cds: TClientDataset; FileName: String): Boolean;
//function SaveStreamToEncryptedXMLFile(cds: TClientDataset; FileName: String): Boolean;
function KEKHasChanged: boolean;
//function GetPasswordResetAnswer(question: AnsiString): AnsiString;

{$IFDEF OPENIP}
function EncryptStringTH(s:AnsiString) : AnsiString; // OpenIP TermHandler
function DecryptStringTH(s:AnsiString) : AnsiString; // OpenIP TermHandler
{$ENDIF}
//function GetMD5Hash(S: AnsiString): AnsiString; // No longer supported - use Sha256Hash instead
function GetCryptoHash(const S: AnsiString): AnsiString; // uses Sha256Hash
//function Sha256Hash(const s: AnsiString): AnsiString;
(*
function MaskCertPassword(aSource: string): string;
function Encrypt3DESWithKey(const aKey; aKeySize: integer; aInData: string; aIsNilIV: boolean): string;
function EncryptStringIBM(aInData:string): string; // 3DES - only used for IBM
function EncryptStringDSI(aInData:string): string; // 3DES - only used with ScanMaster
function Decrypt3DESWithKey(const aKey; aKeySize: integer; aInData: string; aIsNilIV: boolean): string;
*)
function GetPublicKey(aKeyType: TEncryptionKey) : AnsiString;

function GetCertificateName(aKeyType: TEncryptionKey) : string;
function GetMTXEncryptionVersion: string;
function GetMTXEncryptionFileVersion: string;
(*
function GetServerEPSKeyPart1(const KeyType: TServerEPSKeyType): string;
function GetServerEPSKeyPart2(const KeyType: TServerEPSKeyType): string;
function AES128DecryptCBC(Key, Value: string): string;
*)
//function GetHash(aStr: AnsiString): AnsiString;
//function GetHashSHA256(aStr: AnsiString): AnsiString;

function RetrieveValue(aValue: AnsiString; const aConvertBinary: boolean = true): AnsiString;
function StoreValue(aValue: AnsiString): AnsiString;
function RetrieveMdMsgTrack2(var aMdMsg: MdMsgRec): string;
procedure StoreMdMsgTrack2(var aMdMsg: MdMsgRec; aTrack2: string);
function RetrieveMdMsgPersonalAccountNum(var aMdMsg: MdMsgRec): string;
procedure StoreMdMsgPersonalAccountNum(var aMdMsg: MdMsgRec; aAccountNum: string);
function TruncAcctNoForReports(var aMdMsg: MdMsgRec; MaskFields: boolean; isMaskPCI: boolean = true): string;
//function HexDigest(SHA1Digest: TSHA1Digest): AnsiString;
//function SBHashSHA256_Old(Filename: string): AnsiString;     // adapted from MTX_Utils version..  uses SB
//function HashSHA(Filename: string): AnsiString;   //JTG Dev 25063    LockBox version
procedure ClearVISARegFields(var inRec: mdmsgRec);  //unit test in Test_MTX_Lib
procedure ClearVISARegFieldsIfNeeded(var inRec: MdMsgRec);
procedure WriteFirst6Last4(var inRec: mdMsgRec);
//function GetRSAKey(akeyKind: TMTX_KeyKind; aCertFile: TMTX_CertFile; aServerKind: TMTX_ServerKind): string;

function DecryptFromSCAT(dataIn: AnsiString): AnsiString; // decrypt track data from SCAT
function EncryptToSCAT(dataIn: AnsiString): AnsiString;
function ConvertAsciiHexToBinary(aHexData: AnsiString): AnsiString;
procedure GenerateRandomKey(var Key; KeySize : Integer);

{ WebEPS }
function SignContent(const ContentToSign: AnsiString; KeyType: TMTX_CertFile): AnsiString;
function VerifySignature(const Content, Signature: AnsiString; KeyType: TMTX_CertFile; UseXMLKey: boolean=false): boolean;

{$IFDEF TEST}
function AESEncryptCBC(const key: TMtxKey256; clearText: AnsiString): AnsiString;       // Wolf
function AESDecryptCBC(const key: TMtxKey256; cipherText: AnsiString): AnsiString;   // Wolf
{$ENDIF}

const
  ENCRYPTION_KEY_LENGTH = 32;

implementation

uses
  AnsiStrings,
  MTXEncryptionPrivate;

var
/////////////// start Asymetric Encryption ////////////////////////////////////////////////
  WasConfirmedToShowAccountNoPCI: boolean = false;  // used by TruncAcctNoForReports

const
  CERT_NAME_HYPERCOM = 'Request Manager Default';
  CERT_NAME_HYPERCOM_PCIv3 = 'Terminal-Equinox';

//  CertificateNames: array[Low(TEncryptionKey)..High(TEncryptionKey)] of string = ('Hypercom-Terminal');
  CertificateNames: array[Low(TEncryptionKey)..High(TEncryptionKey)] of AnsiString = (CERT_NAME_HYPERCOM, CERT_NAME_HYPERCOM_PCIv3);

/////////////// end Asymetric Encryption ////////////////////////////////////////////////

///////////////////////////////// start SCAT related code /////////////////////////////////

function ConvertAsciiHexToBinary(aHexData: AnsiString): AnsiString;
var i: integer;
begin
  result := '';
  try
    if not IsValidHexString(aHexData) then
      exit;
    i := 1;
    while (i < length(aHexData)) do
    begin
      result := result + AnsiChar(HexToInt(copy(aHexData, i, 2)));
      inc(i, 2);
    end;
  except
    on e: exception do
      begin
      result := '';
      SM(Format('Exception in MTXEncryption.ConvertAsciiHexToBinary: %s', [e.Message]));
      end;
  end;
end;

function DecryptFromSCAT(dataIn: AnsiString): AnsiString;
var i,j: integer;
    tempData: cardArray;
    binaryStr: AnsiString;
    tempStr: AnsiString;
begin
  result := '';
  try
    if (length(dataIn) mod 8) <> 0 then
      exit;
    i := 1;
    tempStr := AnsiString(StringOfChar(' ', 8)); // since we Move below
    binaryStr := ConvertAsciiHexToBinary(dataIn);
    while (i < length(binaryStr)) do
    begin
      move(binaryStr[i], tempData[0], 4);
      move(binaryStr[i + 4], tempData[1], 4);
      MTXEncryptionPrivate.Decode(tempData);
      move(tempData[0], tempStr[1], 4);
      move(tempData[1], tempStr[5], 4);
      for j := 1 to 8 do
        result := result + AnsiChar(ord(tempStr[j]));
      inc(i, 8);
    end;
    while (result[length(result)] = #$00) do
      delete(result, length(result), 1);
  except
    on e: exception do
      begin
      result := '';
      SM(Format('Exception in MTXEncryption.DecryptFromSCAT: %s', [e.Message]));
      end;
  end;
end;

function EncryptToSCAT(dataIn: AnsiString): AnsiString;
var i,j: integer;
    tempData: cardArray;
    inData: AnsiString;
    tmp1, tmp2: array[0..3] of byte;
begin
  result := '';
  try
    i := length(dataIn) mod 8;
    if i <> 0
      then inData := dataIn + AnsiString(stringOfChar(chr(0), 8 - i))  // pad with hex00 to length div by 8z
      else inData := dataIn;
    i := 1;
    while (i < length(inData)) do
    begin
      move(inData[i], tempData[0], 4);
      move(inData[i + 4] , tempData[1], 4);
      MTXEncryptionPrivate.Code(tempData);
      move(tempData[0], tmp1[0], 4);
      move(tempData[1], tmp2[0], 4);
      for j := 0 to 3 do
        result := Format('%s%.2x',[result, tmp1[j]]);
      for j := 0 to 3 do
        result := Format('%s%.2x',[result, tmp2[j]]);
      inc(i, 8);
    end;
  except
    on e: exception do
      begin
      result := '';
      SM(Format('Exception in MTXEncryption.EncryptToSCAT: %s', [e.Message]));
      end;
  end;
end;

///////////////////////////////// end SCAT related code /////////////////////////////////

function HexToInt(aHex: AnsiString): integer;
begin
  aHex := Zfill(aHex, 4);
  result := ((pos(aHex[4],HexChrSet) - 1)      +
                     (pos(aHex[3],HexChrSet) - 1)*16   +
                     (pos(aHex[2],HexChrSet) - 1)*256  +
                     (pos(aHex[1],HexChrSet) - 1)*4096);
end;   { HexToInt }

function GetCryptoHash(const s: AnsiString): AnsiString;
begin
  Result := MTXEncryptionPrivate.GetCryptoHash(S);
end;

function GetPublicKey(aKeyType: TEncryptionKey) : AnsiString;
begin
  case aKeyType of
    ekHypercom     :  result := ExtractPublicKey(cfTermHYC);
    ekHypercomPCIv3:  result := ExtractPublicKey(cfTermHYCPCIv3);
    else              result := '';
  end;
end;

function GetCertificateName(aKeyType: TEncryptionKey) : string;
begin
  result := CertificateNames[aKeyType];
end;

function GetMTXEncryptionVersion: string;  // used for HYC SCAT
begin
  result := '525.1.0.10';
end;

function RetrieveValue(aValue: AnsiString; const aConvertBinary: boolean = true): AnsiString;
begin
  if Trim(aValue) = '' then // avoid AV error
    result := ''
  else
  try
    if aConvertBinary
      then result := DecryptFromSCAT(ConvertBinaryToAsciiHex(aValue))  // this is the default
      else result := DecryptFromSCAT(aValue);
  except
    result := '';
  end;
  aValue := StringOfChar(' ', length(aValue));
end;

function StoreValue(aValue: AnsiString): AnsiString;
begin
  result := ConvertAsciiHexToBinary(EncryptToSCAT(aValue));
  aValue := AnsiString(StringOfChar(' ', length(aValue)));
end;

function RetrieveMdMsgTrack2(var aMdMsg: MdMsgRec): string;
begin
  if (trim(aMdMsg.Track2Data) = '') then
    result := ''
  else
  if aMdMsg.EncryptedTrack
    then result := RetrieveValue(string(aMdMsg.Track2Data))
    else result := aMdMsg.Track2Data;
end;

procedure StoreMdMsgTrack2(var aMdMsg: MdMsgRec; aTrack2: string);
begin
  aTrack2 := trim(aTrack2);
  if (aTrack2 <> '')
    then aTrack2 := StoreValue(aTrack2);
  aMdMsg.EncryptedTrack := true;
  aMdMsg.Track2Data := aTrack2;
end;

function RetrieveMdMsgPersonalAccountNum(var aMdMsg: MdMsgRec): string;
begin
  if (trim(aMdMsg.AcctNo) = '') then
    result := ''
  else
  if aMdMsg.EncryptedTrack then
  begin
    result := RetrieveValue(string(aMdMsg.AcctNo));
    if Copy(result, 1,1) = '*' then // DEV-27635: if truncated by engine
      result := MaskAcctNoPCI(aMdMsg);
  end
  else
    result := aMdMsg.AcctNo;
end;

procedure StoreMdMsgPersonalAccountNum(var aMdMsg: MdMsgRec; aAccountNum: string);
begin
  aAccountNum := trim(aAccountNum);
  if (aAccountNum <> '')
    then aAccountNum := StoreValue(aAccountNum);
  aMdMsg.EncryptedTrack := true;
  aMdMsg.AcctNo := aAccountNum;
end;

// the optional isMaskPCI tells us what kind of masking if MaskFields is true;
function TruncAcctNoForReports(var aMdMsg: MdMsgRec; MaskFields: boolean; isMaskPCI: boolean = true): string;
begin
  if MaskFields then
    if isMaskPCI
      then result := MaskAcctNoPCI(aMdMsg)
      else result := truncMdMsgAcctNo(aMdMsg)
  else
  begin
    result := RetrieveMdMsgPersonalAccountNum(aMdMsg);  // no masking at all...\
    {$IFDEF RPT} // DEV-22694 <
    if SameText(result, 'ERR') then
    begin
      if NOT WasConfirmedToShowAccountNoPCI then
      begin
        WarnMessage('Failed to decrypt account number.' + Chr(13) + 'Access Denied.');
        WasConfirmedToShowAccountNoPCI := true;
      end;
      result := MaskAcctNoPCI(aMdMsg);
    end;
    {$ENDIF} // DEV-22694 >
  end;
end;

(*                                                                                   // 828.5
function HexDigestSB(Digest: TMessageDigest256): AnsiString;
var
  HexBinary: TXSHexBinary;
  M: TByteDynArray;
begin
  try
    result := '';
    HexBinary := TXSHexBinary.Create;
    try
      SetLength(M,sizeof(Digest));
      move(Digest,M,sizeof(Digest));
      HexBinary.AsByteArray := M;
      result := HexBinary.HexBinaryString;
      SetLength(M,0);
    finally
      HexBinary.Free;
    end;
  except on e: exception do
    begin
    SM(format('HexDigestSB exception (%s)',[e.message]));
    result := '';
    end
  end;
end;
*)

{$IFNDEF WOLF}
function SBHashSHA256(Filename: string): AnsiString;
const
  CHUNK_SIZE = 1024;  // JMR: I experimented with larger chunks, but it did not speed it up.
var
  SHA2Digest:  TMessageDigest256;
  Context : TSHA256Context;
  aByteArray: array[1..32] of byte;
  i,Count: integer;
  TZero: TDateTime;
  Buffer: array [0..CHUNK_SIZE-1] of byte;
  Stream: TFileStream;
begin
  result := '';
  TZero := Now;
  if FileExists(Filename) then
    try
      try
        Stream := TFileStream.Create(Filename, fmOpenRead or fmShareDenyNone);
      except on EFOpenError do
        Stream := nil;
      end;
      if Stream <> nil then
        try
          InitializeSHA256(Context);
          Count := Stream.Read(Buffer, SizeOf(Buffer));
          while (Count > 0) do
          begin
            HashSHA256(Context, @Buffer, Count);
            Count := Stream.Read(Buffer, SizeOf(Buffer));
          end;
        finally
          Stream.Free;
        end;
      SHA2Digest := FinalizeSHA256(Context);
      move(SHA2Digest, aByteArray[1], SizeOf(SHA2Digest));     //MTX_Util method
      for i := 1 to 32 do                                      //MTX_Util method
        result := format('%s%.2x',[result,aByteArray[i]]);    //MTX_Util method
      SM(format('SBHashSHA256 digest for %s: %s (%d ms)',[Filename,result,MillisecondsBetween(Now,TZero)]));
    except on e: exception do
      begin
      SM(format('SBHashSHA256 exception (%s) hashing file %s',[e.message,Filename]));
      result := '';
      {$IFDEF TEST}
      result := format('SBHashSHA256 exception (%s) hashing file %s',[e.message,Filename]);
      {$ENDIF}
      end
    end
  else
    SM(format('HashSHA256 unable to locate [%s]',[Filename]));
end;
{$ENDIF}

procedure ClearVISARegFields(var inRec: mdmsgRec);  //unit test in Test_MTX_Lib
begin
  { This routine simply clears any fields that VISA says can't be stored }
  if inRec.ReqCodeN <> SettleTotReqN then
    with inRec do
    begin  { keep track2 for voids on these tenders per Scott 06-27-05 and 05-16-06 }
      if not (inRec.ReqCodeN in User_1_Set + WirelessSet + User_2_Set - [User_2_ReturnN]) then
        StoreMdMsgTrack2(InRec, '');
      PIN  := StringOfChar(' ', 16);
      DukptKeySerialNumber := StringOfChar(' ', 20);
      CVV2 := StringOfChar(' ', 5);
      if DraconFlag then    { for Hy-Vee PCI requirements }
      begin
        StoreMdMsgTrack2(InRec, '');
        StoreMdMsgPersonalAccountNum(InRec, TruncMdMsgAcctNo(Inrec));
        ExpDate := TruncExpDate(ExpDate);
      end;
    end;
end;

procedure ClearVISARegFieldsIfNeeded(var inRec: MdMsgRec);
var tmpData : string;
begin
  if (length(inRec.TrxFinalDisp) < 1) then
    inRec.TrxFinalDisp := ' ';
  if not (inRec.TrxFinalDisp[1] in [DispOffApp,DispFwdHeld]) and  { if not in offline file }
     (trim(inRec.TORType) = '')                              and  { and not a TOR }
     (trim(inRec.TrxFinalDisp) <> '')                        then { and there is a final disp }
  begin
    if (inRec.ReqCodeN in EBT_Set)  then  { save PIN for voids }
    begin                                 { which we send as manual }
      tmpData := inRec.PIN;
      ClearVISARegFields(inRec);
      inRec.PIN := tmpData;
    end
    else
    if (trim(inRec.HostSuffixCode) = 'BYL')                    and
       (inRec.ReqCodeN in [dbPurchN, dbReturnN] + prop_db_set) then
    begin
      tmpData := inRec.Track2Data;   { keep track for these }
      ClearVISARegFields(inRec);
      inRec.Track2Data := tmpData;
    end
    else
    if (inRec.HostSuffixCode = 'ATL') or     { keep approved preAuth Data }
       (inRec.HostSuffixCode = 'BYL') or     { for these hosts until }
       (inRec.HostSuffixCode = 'ACI') or     { AuthComp is resolved }
       (inRec.HostSuffixCode = 'SVD') or
       (inRec.HostSuffixCode = 'LYN') then
    begin
      if (inRec.ReqCodeN in PreAuth_Set) then                { if preAuth }
      begin
        if (inRec.TrxFinalDisp[1] in DeclinedDispSet) then  { and declined }
          ClearVISARegFields(inRec);                         { clear fields }
      end
      else
        ClearVISARegFields(inRec);                           { clear any other trx }
    end
    else
      ClearVISARegFields(inRec);
  end;
end;

// write first6 and last4 to mdmsgrec (from acct No)
procedure WriteFirst6Last4(var inRec: mdMsgRec);  // unit test in Test_MTX_Lib in SaveAcctNo
var
  lMove : integer;
  tempAcctNo: string;
begin
  tempAcctNo := trim(RetrieveMdMsgPersonalAccountNum(inRec));
  if (length(tempAcctNo) > 0) and (tempAcctNo[1] <> '*') then  // don't copy if it is truncated
  begin
    if (length(tempAcctNo) < 6) then
      lMove := length(tempAcctNo)
    else
      lMove := 6;
    move(tempAcctNo[1], inRec.AcctNoFirst6[1], lMove);
    if (length(tempAcctNo) < 4) then
      lMove := length(tempAcctNo)
    else
      lMove := 4;
    move(tempAcctNo[length(tempAcctNo) + 1 - lMove], inRec.acctNoLast4[1], lMove);
    tempAcctNo := StringOfChar(' ',22);
  end;
end;

function Base64Encode(const s:AnsiString): AnsiString;
begin
  result := MTXEncryptionPrivate.Base64Encode(s);
end;

function Base64Decode(const s:AnsiString): AnsiString;
begin
  result := MTXEncryptionPrivate.Base64Decode(s);
end;

function EncryptString(var s:AnsiString): AnsiString;
begin
  result := MTXEncryptionPrivate.EncryptString(s);
end;

function DecryptString(s:AnsiString): AnsiString;
begin
  result := MTXEncryptionPrivate.DecryptString(s);
end;

function DecryptFileKeyWithVersion(sKey,sVersion: AnsiString; newKek3: AnsiString = ''): AnsiString; // used by RecFile when reading
begin
  result := MTXEncryptionPrivate.DecryptFileKeyWithVersion(sKey, sVersion, newKek3);
end;

function KeyVerificationStringOK(verification: AnsiString; sKey: AnsiString): boolean; // used by RecFile & MTX_Lib when reading
begin
  result := MTXEncryptionPrivate.KeyVerificationStringOK(verification, sKey);
end;

function EncryptFileData(s: AnsiString; sKey: AnsiString): AnsiString; // used by RecFile when writing
begin
  result := MTXEncryptionPrivate.EncryptFileData(s, sKey);
end;

function DecryptFileData(s: AnsiString; sKey: AnsiString): AnsiString; // used by RecFile when reading
begin
  result := MTXEncryptionPrivate.DecryptFileData(s, sKey);
end;

function GetEncryptedKeyVerificationString(sKey: AnsiString): AnsiString; // used by RecFile when writing
begin
  result := MTXEncryptionPrivate.GetEncryptedKeyVerificationString(sKey);
end;

function EncryptFileKey(sKey:AnsiString; newKek3: AnsiString = ''; sVersion: AnsiString=KEY_VERSION): AnsiString; // used by RecFile when writing
begin
  result := MTXEncryptionPrivate.EncryptFileKey(sKey, newKek3, sVersion);
end;

procedure MakeKeyEncryptionKey(sKey: AnsiString = ''); // AKA: KEK - for encrypting/decrypting file keys
begin
  MTXEncryptionPrivate.MakeKeyEncryptionKey(sKey);
end;

function EncryptKEK3(aKEK3: AnsiString): AnsiString; // used by engine & openeps to encrypt KEK3 for writing to the spool/jrnl
begin
  result := MTXEncryptionPrivate.EncryptKEK3(aKEK3);
end;

{$IFDEF TEST}
function DecryptKEK3(aKEK3: AnsiString): AnsiString;
begin
  result := MTXEncryptionPrivate.DecryptKEK3(aKEK3);
end;
{$ENDIF}

{$IFNDEF WOLF}
function DecryptStringOld(s:AnsiString) : AnsiString; // 3DES
begin
  result := MTXEncryptionPrivate.DecryptStringOld(s);
end;
{$ENDIF}

function GetServerEPSKey(const KeyType: TServerEPSKeyType; IsPublixHost: boolean=false): AnsiString;
begin
  result := MTXEncryptionPrivate.GetServerEPSKey(KeyType, IsPublixHost);
end;

function AES128EncryptCBC(Key,Value: AnsiString): AnsiString;
begin
  result := MTXEncryptionPrivate.AES128EncryptCBC(Key,Value);
end;

function AES128DecryptCBC(Key,Value: AnsiString): AnsiString;
begin
  result := MTXEncryptionPrivate.AES128DecryptCBC(Key,Value);
end;

{$IFDEF WOLF}
function AES128Encrypt(Key: TMtxKey128; Value: AnsiString): AnsiString;
begin
  result := MTXEncryptionPrivate.AES128Encrypt(Key,Value);
end;

function AES128EncryptCBC(Key: TMtxKey128; Value: AnsiString): AnsiString;
begin
  result := MTXEncryptionPrivate.AES128EncryptCBC(Key,Value);
end;

function AES128DecryptCBC(Key: TMtxKey128; Value: AnsiString): AnsiString;
begin
  result := MTXEncryptionPrivate.AES128DecryptCBC(Key,Value);
end;
{$ENDIF WOLF}

function Encrypt3DES(aKeyType: TEncryptionKey; s: AnsiString; aIsECBMode: boolean=false): AnsiString;
begin
  result := MTXEncryptionPrivate.Encrypt3DES(aKeyType, s, aIsECBMode);
end;

function Decrypt3DES(aKeyType: TEncryptionKey; s: AnsiString; aIsECBMode: boolean=false): AnsiString;
begin
  result := MTXEncryptionPrivate.Decrypt3DES(aKeyType, s, aIsECBMode);
end;

{$IFNDEF WOLF}
function LoadCertificateToStorage(aCertFile: TMTX_CertFile;
    var aMemoryCertStorage: TElMemoryCertStorage; aServerKind: TMTX_ServerKind=skUnknown): boolean;
begin
  result := MTXEncryptionPrivate.LoadCertificateToStorage(aCertFile, aMemoryCertStorage, aServerKind);
end;
{$ENDIF}

function SetSessionKey(aKeyType: TEncryptionKey; sKey: AnsiString): boolean;
begin
  result := MTXEncryptionPrivate.SetSessionKey(aKeyType, sKey);
end;

function LoadCustomCertificate(aCertFile: TMTX_CertFile; aCertFileName: string): boolean;
begin
  result := MTXEncryptionPrivate.LoadCustomCertificate(aCertFile, aCertFileName);
  if result and (aCertFile = cfTermHYCPCIv3) then
  begin
    CertificateNames[ekHypercomPCIv3] := CERT_NAME_HYPERCOM; // dev & prod terminal are using different cert name
    SM('LoadCustomCertificate - set certificate name for HypercomPCIv3: ' + CertificateNames[ekHypercomPCIv3]);
  end;
end;

function StringToKey128(s:AnsiString): TMtxKey128;
begin
  result := MTXEncryptionPrivate.StringToKey128(s);
end;

function KEKHasChanged: boolean;
begin
  result := MTXEncryptionPrivate.KEKHasChanged;
end;

{$IFDEF OPENIP}
function EncryptStringTH(s:AnsiString) : AnsiString; // OpenIP TermHandler
begin
  result := MTXEncryptionPrivate.EncryptStringTH(s);
end;
{$ENDIF}

{$IFDEF OPENIP}
function DecryptStringTH(s:AnsiString) : AnsiString; // OpenIP TermHandler
begin
  result := MTXEncryptionPrivate.DecryptStringTH(s);
end;
{$ENDIF}

function GetMTXEncryptionFileVersion: string;
begin
  result := MTXEncryptionPrivate.MTXEncryptionFileVersion;
end;

function SignContent(const ContentToSign: AnsiString; KeyType: TMTX_CertFile): AnsiString;
begin
  result := MTXEncryptionPrivate.SignContent(ContentToSign, KeyType);
end;

function VerifySignature(const Content, Signature: AnsiString; KeyType: TMTX_CertFile; UseXMLKey: boolean=false): boolean;
begin
  result := MTXEncryptionPrivate.VerifySignature(Content, Signature, KeyType, UseXMLKey);
end;

// taken from Lockbox
procedure GenerateRandomKey(var Key; KeySize : Integer);
var
  I: Integer;
begin
  Randomize;
  for I := 0 to KeySize - 1 do
    TByteArray(Key)[I] := System.Random(256);
end;

{$IFDEF TEST}
function AESEncryptCBC(const key: TMtxKey256; clearText: AnsiString): AnsiString;       // Wolf
begin
  result := MTXEncryptionPrivate.AESEncryptCBC(key, clearText);
end;

function AESDecryptCBC(const key: TMtxKey256; cipherText: AnsiString): AnsiString;   // Wolf
begin
  result := MTXEncryptionPrivate.AESDecryptCBC(key, cipherText);
end;
{$ENDIF}

initialization
  ExtendedLog('MTXEncryptionUtils Initialization');
finalization
  ExtendedLog('MTXEncryptionUtils Finalization');

end.
