// (c) MTXEPS, Inc. 1988-2008
{*********************************************************************}
{                                                                     }
{                       Delphi XML Data Binding                       }
{                                                                     }
{         Generated on: 10/25/2007 12:32:08 PM                        }
{       Generated from: Z:\824.0\UpdateXMLFiles\UpdateXMLConfig.xml   }
{                                                                     }
{*********************************************************************}
unit UpdateXMLConfigXML;

interface

uses
  FinalizationLog,
  xmldom, XMLDoc, XMLIntf;

type

{ Forward Decls }

  IXMLUpdateXMLConfigType = interface;
  IXMLActionsType = interface;
  IXMLActionType = interface;

{ IXMLUpdateXMLConfigType }

  IXMLUpdateXMLConfigType = interface(IXMLNode)
    ['{33EE6949-2C16-4ACC-B7A4-06F3ECF6057A}']
    { Property Accessors }
    function GetVersion: string;
    function GetLastModified: string;
    function GetActions: IXMLActionsType;
    procedure SetVersion(Value: string);
    procedure SetLastModified(Value: string);
    { Methods & Properties }
    property Version: string read GetVersion write SetVersion;
    property LastModified: string read GetLastModified write SetLastModified;
    property Actions: IXMLActionsType read GetActions;
  end;

{ IXMLActionsType }

  IXMLActionsType = interface(IXMLNodeCollection)
    ['{9C4DAAAD-476C-4F8A-ABF7-598DC1B9A310}']
    { Property Accessors }
    function GetAction(Index: Integer): IXMLActionType;
    { Methods & Properties }
    function Add: IXMLActionType;
    function Insert(const Index: Integer): IXMLActionType;
    property Action[Index: Integer]: IXMLActionType read GetAction; default;
  end;

{ IXMLActionType }

  IXMLActionType = interface(IXMLNode)
    ['{EC08AE62-54BF-4DB5-ADEB-14C1735667F8}']
    { Property Accessors }
    function GetType_: string;
    function GetFileName: string;
    function GetXMLPath: string;
    function GetNodeValue_: string;
    function GetNodeType_: string;
    function GetNodeName_: string;
    function GetSeq: string;
    function GetAttrName: string;
    function GetAttrValue: string;
    procedure SetType_(Value: string);
    procedure SetFileName(Value: string);
    procedure SetXMLPath(Value: string);
    procedure SetNodeValue_(Value: string);
    procedure SetNodeType_(Value: string);
    procedure SetNodeName_(Value: string);
    procedure SetSeq(Value: string);
    procedure SetAttrName(Value: string);
    procedure SetAttrValue(Value: string);
    { Methods & Properties }
    property Type_: string read GetType_ write SetType_;
    property FileName: string read GetFileName write SetFileName;
    property XMLPath: string read GetXMLPath write SetXMLPath;
    property NodeValue_: string read GetNodeValue_ write SetNodeValue_;
    property NodeType_: string read GetNodeType_ write SetNodeType_;
    property NodeName_: string read GetNodeName_ write SetNodeName_;
    property Seq: string read GetSeq write SetSeq;
    property AttrName: string read GetAttrName write SetAttrName;
    property AttrValue: string read GetAttrValue write SetAttrValue;
  end;

{ Forward Decls }

  TXMLUpdateXMLConfigType = class;
  TXMLActionsType = class;
  TXMLActionType = class;

{ TXMLUpdateXMLConfigType }

  TXMLUpdateXMLConfigType = class(TXMLNode, IXMLUpdateXMLConfigType)
  protected
    { IXMLUpdateXMLConfigType }
    function GetVersion: string;
    function GetLastModified: string;
    function GetActions: IXMLActionsType;
    procedure SetVersion(Value: string);
    procedure SetLastModified(Value: string);
  public
    procedure AfterConstruction; override;
  end;

{ TXMLActionsType }

  TXMLActionsType = class(TXMLNodeCollection, IXMLActionsType)
  protected
    { IXMLActionsType }
    function GetAction(Index: Integer): IXMLActionType;
    function Add: IXMLActionType;
    function Insert(const Index: Integer): IXMLActionType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLActionType }

  TXMLActionType = class(TXMLNode, IXMLActionType)
  protected
    { IXMLActionType }
    function GetType_: string;
    function GetFileName: string;
    function GetXMLPath: string;
    function GetNodeValue_: string;
    function GetNodeType_: string;
    function GetNodeName_: string;
    function GetSeq: string;
    function GetAttrName: string;
    function GetAttrValue: string;
    procedure SetType_(Value: string);
    procedure SetFileName(Value: string);
    procedure SetXMLPath(Value: string);
    procedure SetNodeValue_(Value: string);
    procedure SetNodeType_(Value: string);
    procedure SetNodeName_(Value: string);
    procedure SetSeq(Value: string);
    procedure SetAttrName(Value: string);
    procedure SetAttrValue(Value: string);
  end;

{ Global Functions }

function GetUpdateXMLConfig(Doc: IXMLDocument): IXMLUpdateXMLConfigType;
function LoadUpdateXMLConfig(const FileName: WideString): IXMLUpdateXMLConfigType;
function NewUpdateXMLConfig: IXMLUpdateXMLConfigType;

implementation

{ Global Functions }

function GetUpdateXMLConfig(Doc: IXMLDocument): IXMLUpdateXMLConfigType;
begin
  Result := Doc.GetDocBinding('UpdateXMLConfig', TXMLUpdateXMLConfigType) as IXMLUpdateXMLConfigType;
end;
function LoadUpdateXMLConfig(const FileName: WideString): IXMLUpdateXMLConfigType;
begin
  Result := LoadXMLDocument(FileName).GetDocBinding('UpdateXMLConfig', TXMLUpdateXMLConfigType) as IXMLUpdateXMLConfigType;
end;

function NewUpdateXMLConfig: IXMLUpdateXMLConfigType;
begin
  Result := NewXMLDocument.GetDocBinding('UpdateXMLConfig', TXMLUpdateXMLConfigType) as IXMLUpdateXMLConfigType;
end;

{ TXMLUpdateXMLConfigType }

procedure TXMLUpdateXMLConfigType.AfterConstruction;
begin
  RegisterChildNode('Actions', TXMLActionsType);
  inherited;
end;

function TXMLUpdateXMLConfigType.GetVersion: string;
begin
  Result := AttributeNodes['Version'].Text;
end;

procedure TXMLUpdateXMLConfigType.SetVersion(Value: string);
begin
  SetAttribute('Version', Value);
end;

function TXMLUpdateXMLConfigType.GetLastModified: string;
begin
  Result := AttributeNodes['LastModified'].Text;
end;

procedure TXMLUpdateXMLConfigType.SetLastModified(Value: string);
begin
  SetAttribute('LastModified', Value);
end;

function TXMLUpdateXMLConfigType.GetActions: IXMLActionsType;
begin
  Result := ChildNodes['Actions'] as IXMLActionsType;
end;

{ TXMLActionsType }

procedure TXMLActionsType.AfterConstruction;
begin
  RegisterChildNode('Action', TXMLActionType);
  ItemTag := 'Action';
  ItemInterface := IXMLActionType;
  inherited;
end;

function TXMLActionsType.GetAction(Index: Integer): IXMLActionType;
begin
  Result := List[Index] as IXMLActionType;
end;

function TXMLActionsType.Add: IXMLActionType;
begin
  Result := AddItem(-1) as IXMLActionType;
end;

function TXMLActionsType.Insert(const Index: Integer): IXMLActionType;
begin
  Result := AddItem(Index) as IXMLActionType;
end;


{ TXMLActionType }

function TXMLActionType.GetType_: string;
begin
  Result := AttributeNodes['type'].Text;
end;

procedure TXMLActionType.SetType_(Value: string);
begin
  SetAttribute('type', Value);
end;

function TXMLActionType.GetFileName: string;
begin
  Result := AttributeNodes['FileName'].Text;
end;

procedure TXMLActionType.SetFileName(Value: string);
begin
  SetAttribute('FileName', Value);
end;

function TXMLActionType.GetXMLPath: string;
begin
  Result := AttributeNodes['XMLPath'].Text;
end;

procedure TXMLActionType.SetXMLPath(Value: string);
begin
  SetAttribute('XMLPath', Value);
end;

function TXMLActionType.GetNodeValue_: string;
begin
  Result := AttributeNodes['NodeValue'].Text;
end;

procedure TXMLActionType.SetNodeValue_(Value: string);
begin
  SetAttribute('NodeValue', Value);
end;

function TXMLActionType.GetNodeType_: string;
begin
  Result := AttributeNodes['NodeType'].Text;
end;

procedure TXMLActionType.SetNodeType_(Value: string);
begin
  SetAttribute('NodeType', Value);
end;

function TXMLActionType.GetNodeName_: string;
begin
  Result := AttributeNodes['NodeName'].Text;
end;

procedure TXMLActionType.SetNodeName_(Value: string);
begin
  SetAttribute('NodeName', Value);
end;

function TXMLActionType.GetSeq: string;
begin
  Result := AttributeNodes['Seq'].Text;
end;

procedure TXMLActionType.SetSeq(Value: string);
begin
  SetAttribute('Seq', Value);
end;

function TXMLActionType.GetAttrName: string;
begin
  Result := AttributeNodes['AttrName'].Text;
end;

procedure TXMLActionType.SetAttrName(Value: string);
begin
  SetAttribute('AttrName', Value);
end;

function TXMLActionType.GetAttrValue: string;
begin
  Result := AttributeNodes['AttrValue'].Text;
end;

procedure TXMLActionType.SetAttrValue(Value: string);
begin
  SetAttribute('AttrValue', Value);
end;

initialization
  ExtendedLog('UpdateXMLConfigXML Initialization');
finalization
  ExtendedLog('UpdateXMLConfigXML Finalization');

end.
