unit TokenData;

interface

uses
  Classes, SysUtils,
  MTX_Constants;

type
  TTokenType = (
      toEnterprise      = 101,
      toLoyalty         = 102,
      toCoupon          = 103,
      toFuterUse        = 104,
      toOpenEPSOffline  = 105,
      toTemporary       = 106
    );

  TTokenData = class
    FTokenType: TStringList;
    FTokenValue: TStringList;
    FTokenExpDate: TStringList;
    FFilter: string; // token filter ex) 101,103
    function GetCount: integer;
    function GetTokenTypes: AnsiString;
    function GetTokenDataEx(aFilter: string=''): AnsiString;
    function GetFilteredTokenData: AnsiString;
    function GetTokenData: AnsiString;
    procedure SetTokenData(Value: AnsiString);
    function GetToken(Index: integer): AnsiString;
    procedure SetToken(Index: integer; const Value: AnsiString);
    procedure ParseToken(const aToken: AnsiString; var aTokenType: AnsiString; var aTokenValue: AnsiString; var aTokenExpDate: AnsiString);
  public
    constructor Create(aTokenData: AnsiString); overload;
    destructor Destroy; override;
    function Add(aTokenType, aTokenValue, aTokenExpDate: AnsiString): boolean; overload;
    function Add(aTokenData: AnsiString): boolean; overload;
    function Clear: boolean;
    function Exists(aTokenType: string): boolean;
    function TokenValueByType(aTokenType: string): string;
    function TokenExpDateByType(aTokenType: string): string;
    function TokenByType(aTokenType: string): string;
    class function ConvertToArray500c(aSource: AnsiString): Array500c;
    procedure PrintTokens(aTokenData: AnsiString);
    property Count: integer read GetCount;
    property Filter: string read FFilter write FFilter;
    property Token[Index: integer]: AnsiString read GetToken write SetToken;
    property TokenData: AnsiString read GetTokenData write SetTokenData;
    property FilteredTokenData: AnsiString read GetFilteredTokenData;
    property TokenTypes: AnsiString read GetTokenTypes;
  end;

const
  OFFLINE_TOKEN_TYPE = '105';
  ESTORE_TOKEN_TYPE  = '107'; // TFS-166790 - Target - Mobile Wallet
  TEMP_TOKEN_TYPE = '301';
  WFM_PURCHASE_TOKEN_TYPE = '121'; // CPCLIENTS-11227
  WFM_RETURN_TOKEN_TYPE = '122';   // CPCLIENTS-11227
  WFM_AMAZON_PAY_TOKEN_TYPE = '203'; // CPCLIENTS-13209

implementation

uses
  GeneralUtilities,
  MTX_Lib;

const
  NOT_FOUND = -1;

constructor TTokenData.Create(aTokenData: AnsiString);
begin
  inherited Create;
  FTokenType := TStringList.Create;
  FTokenValue := TStringList.Create;
  FTokenExpDate := TStringList.Create;
  FTokenType.Delimiter := TOKEN_DATA_DELIMITER;
  TokenData := aTokenData;
end;

destructor TTokenData.Destroy;
begin
  FreeAndNil(FTokenValue);
  FreeAndNil(FTokenType);
  FreeAndNil(FTokenExpDate);
  inherited;
end;

function TTokenData.GetCount: integer;
begin
  result := FTokenType.Count;
end;

procedure TTokenData.ParseToken(const aToken: AnsiString; var aTokenType: AnsiString; var aTokenValue: AnsiString; var aTokenExpDate: AnsiString);
var
  //tmpPos: integer;
  tmpList: TStringList;
begin
  {
  tmpPos := Pos(TOKEN_DELIMITER, aToken);
  aTokenType := Trim(Copy(aToken, 1, tmpPos-1));
  aTokenValue := Trim(Copy(aToken, tmpPos+1, Length(aToken)-tmpPos));
  }
  tmpList := TStringList.Create;
  try
    tmpList.Delimiter := TOKEN_DELIMITER;
    SplitStr(aToken, TOKEN_DELIMITER, tmpList);
    if tmpList.Count >= 1 then aTokenType := Trim(tmpList[0]);
    if tmpList.Count >= 2 then aTokenValue := Trim(tmpList[1]);
    if tmpList.Count >= 3 then aTokenExpDate := Trim(tmpList[2]);
  finally
    FreeAndNil(tmpList);
  end;
end;

function TTokenData.Add(aTokenType, aTokenValue, aTokenExpDate: AnsiString): boolean;
var i: integer;
begin
  i := FTokenType.IndexOf(aTokenType);
  if i = NOT_FOUND then
  begin
    if (aTokenType = IntToStr(Ord(toOpenEPSOffline))) and (FTokenType.IndexOf(IntToStr(Ord(toEnterprise))) > NOT_FOUND) then // drop 105 tmp token if 101 exists
    begin
      SM(Format('TTokenData.Add - Enterprise token (101) already exists - [TokenType: %s] dropped', [aTokenType]));
      Exit;
    end;
    if (aTokenType = TEMP_TOKEN_TYPE) and (aTokenExpDate = '') then // drop if temp token has no expdate.
    begin
      SM(Format('TTokenData.Add - [TokenType: %s] Temporary token has no expiration date - dropped', [TEMP_TOKEN_TYPE]));
      Exit;
    end;
    FTokenType.Add(aTokenType);
    FTokenValue.Add(aTokenValue);
    FTokenExpDate.Add(aTokenExpDate);
  end
  else
  begin
    if FTokenValue[i] <> aTokenValue then // if different, update
    begin
      SM(Format('[WARNING] TTokenData.Add - [TokenType: %s] not match: >%s< overwritten with >%s<', [aTokenType, FTokenValue[i], aTokenValue]));
      FTokenValue[i] := aTokenValue;
    end
    else
      SM(Format('TTokenData.Add - [TokenType: %s] already exists.', [aTokenType]))
  end;
end;

procedure TTokenData.PrintTokens(aTokenData: AnsiString);
var i: integer;
begin
  SM('------------< Token List >------------');
  for i := 0 to FTokenType.Count -1 do
    SM(Format('[%d] Token[%s] %s %s', [i, FTokenType[i], FTokenValue[i], FTokenExpDate[i]]));
end;

function TTokenData.Add(aTokenData: AnsiString): boolean;
var
  i: integer;
  TokenList: TStringList;
  TokenType, TokenValue, TokenExpDate: AnsiString;
begin
  TokenList := TStringList.Create;
  try
    TokenList.Delimiter := TOKEN_DATA_DELIMITER;
    SplitStr(aTokenData, TOKEN_DATA_DELIMITER, TokenList);
    for i := 0 to TokenList.Count -1 do
    begin
      ParseToken(TokenList[i], TokenType, TokenValue, TokenExpDate);
      Add(TokenType, TokenValue, TokenExpDate);
    end;
  finally
    FreeAndNil(TokenList);
  end;
end;

function TTokenData.Clear: boolean;
begin
  result := false;
  FTokenType.Clear;
  FTokenValue.Clear;
  FTokenExpDate.Clear;
  result := Count = 0;
end;

function TTokenData.Exists(aTokenType: string): boolean;
begin
  result := FTokenType.IndexOf(aTokenType) > NOT_FOUND;
end;

function TTokenData.TokenValueByType(aTokenType: string): string;
var idx: integer;
begin
  result := '';
  idx := FTokenType.IndexOf(aTokenType);
  if (idx > NOT_FOUND) then
    result := FTokenValue[idx];
end;

function TTokenData.TokenExpDateByType(aTokenType: string): string;
var idx: integer;
begin
  result := '';
  idx := FTokenType.IndexOf(aTokenType);
  if (idx > NOT_FOUND) then
    result := FTokenExpDate[idx];
end;

function TTokenData.TokenByType(aTokenType: string): string;
var idx: integer;
begin
  result := '';
  idx := FTokenType.IndexOf(aTokenType);
  if (idx > NOT_FOUND) then
  begin
    result := FTokenType[idx] + TOKEN_DELIMITER + FTokenValue[idx];
    if FTokenExpDate[idx] <> '' then
      result := result + TOKEN_DELIMITER + FTokenExpDate[idx];
  end;
end;

class function TTokenData.ConvertToArray500c(aSource: AnsiString): Array500c;
begin
  FillChar(result, SizeOf(result), 0);
  Move(aSource[1], result, Length(aSource));
end;

function TTokenData.GetTokenTypes: AnsiString;
begin
  result := FTokenType.DelimitedText;
  //result := DelimitedText(FTokenType);
end;

function TTokenData.GetTokenDataEx(aFilter: string=''): AnsiString; 
var
  i: integer;
  tmpList: TStringList;
  FilterList: TStringList;
begin
  result := '';
  tmpList := TStringList.Create;
  FilterList := TStringList.Create;
  try
    tmpList.Delimiter := TOKEN_DATA_DELIMITER;
    SplitStr(aFilter, ',', FilterList);
    for i := 0 to Count-1 do
    begin
      if FilterList.Count > 0 then
      begin
        if FilterList.IndexOf(FTokenType[i]) > NOT_FOUND then
          tmpList.Add(Token[i]);
      end
      else
        tmpList.Add(Token[i]); // return all tokens
    end;
    result := tmpList.DelimitedText;
    //result := DelimitedText(tmpList);
  finally
    FreeAndNil(FilterList);
    FreeAndNil(tmpList);
  end;
end;

function TTokenData.GetFilteredTokenData: AnsiString;
begin
  result := GetTokenDataEx(Filter);
end;

function TTokenData.GetTokenData: AnsiString;
begin
  result := GetTokenDataEx;
end;

procedure TTokenData.SetTokenData(Value: AnsiString);
begin
  Clear;
  Add(Value);
end;

function TTokenData.GetToken(Index: integer): AnsiString;
begin
  result := '';
  if (Index < 0) or (Index >= Count) then
  begin
    SM(Format('TTokenData.GetToken - Index: %d is out of range (Count=%d)', [Index, Count]));
    Exit;
  end;
  result := Format('%s:%s', [FTokenType[Index], FTokenValue[Index]]);
  if FTokenExpDate[Index] <> '' then
    result := result + TOKEN_DELIMITER + FTokenExpDate[Index];
end;

procedure TTokenData.SetToken(Index: integer; const Value: AnsiString);
var TokenType, TokenValue, TokenExpDate: AnsiString;
begin
  if (Index < 0) or (Index >= Count) then
  begin
    SM(Format('TTokenData.SetToken - Index: %d is out of range (Count=%d)', [Index, Count]));
    Exit;
  end;
  ParseToken(Value, TokenType, TokenValue, TokenExpDate);
  if (TokenType <> '') and (TokenValue <> '') then
  begin
    FTokenType[Index] := TokenType;
    FTokenValue[Index] := TokenValue;
    FTokenExpDate[Index] := TokenExpDate;
  end
  else
    SM(Format('TTokenData.SetToken - Invaliad Token %s:%s', [TokenType, TokenValue]));
end;

end.
