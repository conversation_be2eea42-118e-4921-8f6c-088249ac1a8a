//(***********************************)
//(*  DEFINES FOR XE2 SPECIFIC       *)
//{ Need to define DXE2 (VER230 +) for changing unit names for delphi XE2 }   ////////// MCD
//
//{$IFDEF VER230}
//  {$DEFINE DXE2}
//{$ENDIF}
//
//{$IFDEF VER240}
//  {$DEFINE DXE2}
//  {$DEFINE DXE3}
//{$ENDIF}
//
//{$IFDEF VER250}
//  {$DEFINE DXE2}
//  {$DEFINE DXE3}
//  {$DEFINE DXE4}
//{$ENDIF}
//
//{$IFDEF VER260}
//  {$DEFINE DXE2}
//  {$DEFINE DXE3}
//  {$DEFINE DXE4}
//  {$DEFINE DXE5}
//{$ENDIF}
//
//{$IFDEF VER270}
//  {$DEFINE DXE2}
//  {$DEFINE DXE3}
//  {$DEFINE DXE4}
//  {$DEFINE DXE5}
//  {$DEFINE DXE6}
//{$ENDIF}
//
//{$IFDEF VER280}
//  {$DEFINE DXE2}
//  {$DEFINE DXE3}
//  {$DEFINE DXE4}
//  {$DEFINE DXE5}
//  {$DEFINE DXE6}
//  {$DEFINE DXE7}
//{$ENDIF}
//
//{$IFDEF VER290}
//  {$DEFINE DXE2}
//  {$DEFINE DXE3}
//  {$DEFINE DXE4}
//  {$DEFINE DXE5}
//  {$DEFINE DXE6}
//  {$DEFINE DXE7}
//  {$DEFINE DXE8}
//{$ENDIF}
