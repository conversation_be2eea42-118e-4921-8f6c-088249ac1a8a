// (c) MTXEPS, Inc. 1988-2008

//{$I OpenEPS_XE2.inc}

Unit Base8583; { Common 8583 }
(*
v826.2 08-02-10 TSL-H increase bit 45 to 80 chars.
v852.2 01-21-09 TSL-G add   Structure_Var_LLLL    = 13;
v825.0 01-21-08 JMR Departmemt field.
v815.3 04-28-05 TSL-F add TruncType for truncating certains vars in printBug
v815.2 03-14-05 TSL-E add Structure_Var_LLLsBcd
v814.1 06-24-04 Add Bit_Length array
v813.1 02-02-04 TSL-H Add truncation of acctno, track and expDate
v811 07-29-03 TSL-G Increase buffer to IpMsgLen
v810 12-05-02 TSL-F Increase buffer for TCP/IP
** 03-30-99 TSL-E Add new record defined as LongRec
** 03-03-99 TSL-D Change some default lengths
** 02-10-00 TSL-C Change type of Msg_Type to string from string[4]
** 01-04-00 TSL-B Add BCD to structure types
** 11-01-99 TSL-A Take out references to all but windows
*)
{ ================================================================= }
Interface
{ ================================================================= }
Uses
     FinalizationLog,
     {$IFDEF MSWINDOWS}
     Windows,
     {$ENDIF}
     SysUtils,
     MTX_Constants,
     MTX_Utils,
     MTXEncryptionUtils,
     MTX_Lib;

Const
  Max_BitMaps = 3;
Type
  LongRec  = Record
               Len    : integer;
               Data   : Array[1..IpMsgLen] of Byte;  { TSL-G }
             End;

  Chr_Bit_Map   = Array[1..Max_BitMaps] of String[16];
  Bin_Bit_Map   = Array[1..(Max_BitMaps * 8)] of Byte;


  ISO_Rec = Record
              Bit_Map_Type      : Byte;

                  Msg_Type      : AnsiString{[4]};         { TSL-C }

            Out_Bit_Map_08,
             In_Bit_Map_08      : Bin_Bit_Map;

            Out_Bit_Map_16,
             In_Bit_Map_16      : Chr_Bit_Map;

           { What bits are...how long min/max }

               Bit_Format       : Array[1..(Max_BitMaps * 64)] of Byte;
               Bit_Min_Len      : Array[1..(Max_BitMaps * 64)] of Integer;
               Bit_Max_Len      : Array[1..(Max_BitMaps * 64)] of Integer;
               Bit_Length       : Array[1..(Max_BitMaps * 64)] of Integer;
               Bit_TruncType    : Array[1..(Max_BitMaps * 64)] of Integer;

               { The actual data as unformatted from incoming messages }

               Bit_Data_In      : Array[1..(Max_BitMaps * 64)] of AnsiString;

               { The actual data as formatted for outgoing messages }

               Bit_Data_Out     : Array[1..(Max_BitMaps * 64)] of AnsiString; { Data Strings }
               Bit_Data_OutTrunc: Array[1..(Max_BitMaps * 64)] of AnsiString; { Data Strings }

               { Note: Below goes from 0 so unused ones can stay 0 and not get range error }

               Bit_Data_Out_Set : Array[0..(Max_BitMaps * 64)] of Boolean;  { Bits set     }

  End;

  P_ISO = ^ISO_Rec;

Const
  Hex_Set : String[16] = '0123456789ABCDEF';

  HexBit  : Array[1..8] of Byte = (128, 64, 32, 16, 8, 4, 2, 1);

  MsgDisplay = 1;                            { TSL-B }
  MsgBcd     = 2;                            { TSL-B }

  Bit_Map_Display   = 1;
  Bit_Map_Hex       = 2;

  Structure_Unknown    = 0;
  Structure_Fixed      = 1;
  Structure_Var_LL     = 2;
  Structure_Var_LLL    = 3;
  Structure_Var_LLLL   = 13;      // TSL-G added for OpenEPS
  Structure_FixedBcd   = 4;       // TSL-B
  Structure_Var_LLBcd  = 5;       // TSL-B  Entire Var is BCD
  Structure_Var_LLLBcd = 6;       // TSL-B  Entire var is BCD
  Structure_HexBcd     = 7;       // TSL-B  Bit maps, PIN
  Structure_Var_L      = 8;       //SL-A, add for Big Y project
  Structure_BitMap     = 9;
  Structure_LLOnlyBcd  = 10;      // TSL-B  Only the length LL is BCD
  Structure_LLLOnlyBcd = 11;      // TSL-B  Only the length LLL is BCD
  Structure_Var_LLLsBcd= 12;     // TSL-B  Entire var is BCD, len bytes are ACTUAL bytes after BCD (for BioPay }

  Structure_DataSet = [Structure_Fixed, Structure_Var_LL, Structure_Var_LLL, Structure_Var_LLLL,
                       Structure_FixedBcd, Structure_Var_LLBcd,  // TSL-B
                       Structure_Var_LLLBcd, Structure_HexBcd,   // TSL-B
                       Structure_Var_L,  //SL-A, for convinient reason
                       Structure_LLOnlyBcd, Structure_LLLOnlyBcd,
                       Structure_Var_LLLsBcd];
  Structure_VarSet = [Structure_Var_LL, Structure_Var_LLL, Structure_Var_L, Structure_Var_LLLL];
  Structure_BcdSet = [Structure_Var_LLBcd, Structure_Var_LLLBcd,Structure_Var_LLLsBcd]; // TSL-B
  Structure_LenBcdSet = [Structure_LLOnlyBcd, Structure_LLLOnlyBcd]; // TSL-B

  TruncTypeNone   = 0;
  TruncTypeTrack1 = 1;
  TruncTypeTrack2 = 2;
  TruncTypeExp    = 3;
  TruncTypeAcctNo = 4;
  TruncTypeString = 5;    { truncates the entire string - like for the PIN }
  TruncTypeTrack1Encrypted = 7;
  TruncTypeTrack2Encrypted = 8;
  TruncTypeAcctNoEncrypted = 9;
  TruncTypeTrack2TRN = 10;
  TruncTypeReceiptName = 11;

  Default_Structure : Array[1..(Max_BitMaps * 64)] of Byte = (
                                  9,2,1,1,1,1,1,1, { 001-008 }
                                  1,1,1,1,1,1,1,1, { 009-016 }
                                  1,1,1,1,1,1,1,1, { 017-024 }
                                  1,1,1,1,1,1,2,2, { 025-032 }
                                  2,2,2,3,1,1,1,1, { 033-040 }
                                  1,1,1{2},2,2,3,3,3, { 041-048 }    { TSL-D }
                                  1,1,1,1,2,3,3,1, { 049-056 }
                                  1,2,3,3,3,3,3,1, { 057-064 }

                                  9,3,1,1,1,1,1,3, { 065-072 }
                                  1,1,1,1,1,1,1,1, { 073-080 }
                                  1,1,1,1,1,1,1,1, { 081-088 }
                                  1,1,1,1,2,2,2,3, { 089-096 }
                                  1,1,2,2,2,2,2,3, { 097-104 }
                                  1,1,1,1,2,2,3,3, { 105-112 }
                                  3,3,3,3,3,3,3,3, { 113-120 }
                                  3,3,3,3,3,3,3,1, { 121-128 }

                                  0,0,0,0,0,0,0,0, { 129-136 }
                                  0,0,0,0,0,0,0,0, { 137-144 }
                                  0,0,0,0,0,0,0,0, { 145-152 }
                                  0,0,0,0,0,0,0,0, { 153-160 }
                                  0,0,0,0,0,0,0,0, { 161-168 }
                                  0,0,0,0,0,0,0,0, { 169-176 }
                                  0,0,0,0,0,0,0,0, { 177-184 }
                                  0,0,0,0,0,0,0,0);{ 185-192 }

  Default_Min  : Array[1..(Max_BitMaps * 64)] of Integer = (
                   16,  1,  6, 12, 12, 12, 10,  8, { 001-008 }
                    8,  8,  6, 12,  4,  4,  4{6},  4, { 009-016 }    { TSL-D }
                    4,  4,  3,  3,  3, 3{12},  3,  3, { 017-024 }    { TSL-D }
                    2{4},  4,  1,  6,  3, 24,  1,  1, { 025-032 }    { TSL-D }
                    1,  1,  1,  1, 12,  6,  2{3},  20, { 033-040 }    { TSL-D }
                    8, 15,  40{1},  1,  1,  1,  1,  1, { 041-048 }   { TSL-D }
                    3,  3,  3,  16{8},  1,  1,  1, 35, { 049-056 }   { TSL-D }
                    3,  1,  1,  1,  1,  1,  1,  8, { 057-064 }

                   16,  1,  2,  3,  3,  3,  8,  1, { 065-072 }
                    6, 10, 10, 10, 10, 10, 10, 10, { 073-080 }
                   10, 10, 10, 10, 10, 16, 16, 16, { 081-088 }
                   16, 10,  3,  3,  1,  1,  1,  1, { 089-096 }
                   17, 25,  1,  1,  1,  1,  1,  1, { 097-104 }
                   16, 16, 10, 10,  1,  1,  1,  1, { 105-112 }
                    1,  1,  1,  1,  1,  1,  1,  1, { 113-120 }
                    1,  1,  1,  1,  1,  1,  1,  8, { 121-128 }

                    0,  0,  0,  0,  0,  0,  0,  0, { 129-136 }
                    0,  0,  0,  0,  0,  0,  0,  0, { 137-144 }
                    0,  0,  0,  0,  0,  0,  0,  0, { 145-152 }
                    0,  0,  0,  0,  0,  0,  0,  0, { 153-160 }
                    0,  0,  0,  0,  0,  0,  0,  0, { 161-168 }
                    0,  0,  0,  0,  0,  0,  0,  0, { 169-176 }
                    0,  0,  0,  0,  0,  0,  0,  0, { 177-184 }
                    0,  0,  0,  0,  0,  0,  0,  0);{ 185-192 }

  Default_Max  : Array[1..(Max_BitMaps * 64)] of Integer = (
                   16, 99,  6, 12, 12, 12, 10,  8, { 001-008 }
                    8,  8,  6, 12,  4,  4,  4{6},  4, { 009-016 }    { TSL-D }
                    4,  4,  3,  3,  3, 3{12},  3,  3, { 017-024 }    { TSL-D }
                    2{4},  4,  1,  6,  3, 24, 99, 11, { 025-032 }    { TSL-D }
                   11, 28, 40,104, 12,  6,  2{3},  20, { 033-040 }    { TSL-D }
                    8, 15, 40{99}, 99, 80,204,999,999, { 041-048 }   { TSL-D }
                    3,  3,  3,  16{8}, 48,120,255, 35, { 049-056 }   { TSL-D }
                    3, 11,999,999,999,999,999,  8, { 057-064 }

                   16,204,  2,  3,  3,  3,  8,999, { 065-072 }
                    6, 10, 10, 10, 10, 10, 10, 10, { 073-080 }
                   10, 10, 10, 10, 10, 16, 16, 16, { 081-088 }
                   16, 10,  3,  3, 11, 11, 99,999, { 089-096 }
                   17, 25, 11, 11, 17, 28, 28,100, { 097-104 }
                   16, 16, 10, 10, 84, 84,999,999, { 105-112 }
                  999,999,999,999,999,999,999,999, { 113-120 }
                  999,999,999,999,999,999,999,  8, { 121-128 }

                    0,  0,  0,  0,  0,  0,  0,  0, { 129-136 }
                    0,  0,  0,  0,  0,  0,  0,  0, { 137-144 }
                    0,  0,  0,  0,  0,  0,  0,  0, { 145-152 }
                    0,  0,  0,  0,  0,  0,  0,  0, { 153-160 }
                    0,  0,  0,  0,  0,  0,  0,  0, { 161-168 }
                    0,  0,  0,  0,  0,  0,  0,  0, { 169-176 }
                    0,  0,  0,  0,  0,  0,  0,  0, { 177-184 }
                    0,  0,  0,  0,  0,  0,  0,  0);{ 185-192 }

  Default_truncType : Array[1..(Max_BitMaps * 64)] of Byte = (
                                  0,4,0,0,0,0,0,0, { 001-008 }
                                  0,0,0,0,0,3,0,0, { 009-016 }
                                  0,0,0,0,0,0,0,0, { 017-024 }
                                  0,0,0,0,0,0,0,0, { 025-032 }
                                  0,0,2,0,0,0,0,0, { 033-040 }
                                  0,0,0,0,1,0,0,0, { 041-048 }
                                  0,0,0,5,0,0,0,0, { 049-056 }
                                  0,0,0,0,11,0,0,0, { 057-064 }

                                  0,5,0,0,0,0,0,0, { 065-072 } //CPCLIENTS-1628 - mask data of Raw MICR (bit 66)
                                  0,0,0,0,0,0,0,0, { 073-080 }
                                  0,0,0,0,0,0,0,0, { 081-088 }
                                  0,0,0,0,0,0,0,0, { 089-096 }
                                  0,0,0,0,0,0,0,0, { 097-104 }
                                  0,0,0,0,0,0,5,0, { 105-112 }
                                  0,0,0,0,0,0,0,0, { 113-120 }
                                  0,5,0,0,0,0,0,0, { 121-128 }

                                  0,0,0,0,0,0,0,0, { 129-136 }
                                  0,0,0,0,0,0,0,0, { 137-144 }
                                  0,0,0,0,0,0,0,0, { 145-152 }
                                  0,0,0,0,0,0,0,0, { 153-160 }
                                  0,0,0,0,0,0,0,0, { 161-168 }
                                  0,0,0,0,0,0,0,0, { 169-176 }
                                  0,0,0,0,0,0,0,0, { 177-184 }
                                  0,0,0,0,0,0,0,0);{ 185-192 }

var
  MsgType_Structure: integer = MsgDisplay;  { TSL-B }

procedure InitBitData(var In_Ptr: P_ISO);
Procedure Init_8583(Var In_Ptr : P_ISO);
Function  Range(In_Bit : Integer) : Boolean;
Procedure Set_Bit_Mask(Var In_Ptr : P_ISO; In_Bit, Bit_Struc, Bit_Min, Bit_Max : Integer);
procedure Set_TruncType(Var In_Ptr : P_ISO; In_Bit, In_TruncType: integer);
//Procedure Clear_Bit_Mask(Var In_Ptr : P_ISO; In_Bit : Integer); // XE: Remove WinEPS - not for OpenEPS
//Procedure Clear_All_Bit_Masks(Var In_Ptr : P_ISO); // XE: Remove WinEPS - not for OpenEPS
Procedure Set_Bit_Default(Var In_Ptr : P_ISO; In_Bit : Integer);
Procedure Set_All_Defaults(Var In_Ptr : P_ISO);
Function  TestBit(Var In_Map : Bin_Bit_Map; In_Bit : Integer) : Integer;
function  truncIfNeeded(Var In_Ptr : P_ISO; aBit: integer; tmpStr: AnsiString): AnsiString;
function  truncIfNeededVar(Var In_Ptr : P_ISO; aBit: integer; varLen, tmpStr: AnsiString): AnsiString;
function GetIsoBitDescription(bit: integer): string;

{$IFDEF TEST}          //only need to expose these for unit testing
function EmbeddedMask(iStart: integer; tmpStr: AnsiString; FullMask: boolean=false): AnsiString;  // works on tmpStr, given the start, goes to 4 before the end
function MaskSSNAndCheckinBitIfExists(aBit: integer; tmpStr: AnsiString): AnsiString;      //55685
function MaskNameinBitIfExists(aBit: integer; S: AnsiString): AnsiString;          //68539
{$ENDIF}

var
  Dr_LicenseTRN: AnsiString;

{ ================================================================= }
Implementation
{ ================================================================= }

{$IFDEF HOSTSIM}
uses smToDisplay;
{$ENDIF HOSTSIM}
uses AnsiStrings;

const
  FID_MISC_FIELDS = 62;        //68539
  FID_FIRSTNAME = 120;
  FID_LASTNAME = 121;

  IsoBitDescriptions: array[1..128] of string =
  (
    '',
    'PAN',                                                    // 2
    'processing code',                                        // 3
    'total trx amount (in cents)',                            // 4
    '',
    '',
    'transmission datetime MMddHHmmss',                       // 7
    '',
    '',
    'OpenEPS version',                                        //	10
    'MTX sequence number',                                    //	11
    'local transaction time',                                 //	12
    'local transaction date',                                 //	13
    'expiration date',                                        //  14
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    'entry mode (11:manual, 21:swipe, 61:chip, 63:fallback)', //	22
    '',
    'request code + lane type',                               //	24
    'condition code',                                         //	25
    '',
    '',
    '',
    '',
    '',
    'KEK3 timestamp',                                         //	31
    '',
    '',
    '',
    'track2 data',                                            //  35
    '',
    'retrieval reference number (host assigned)',             //  37
    'authorization number',                                   //	38
    'response code',                                          //	39
    'department',                                             //	40
    'lane number',                                            //	41
    'store number',                                           //	42
    'user ID',                                                //	43
    'response display text',                                  //	44
    'track1 data',                                            //	45
    'POS echo field',                                         //	46
    '',
    'private data',                                           //	48
    '',
    'OpenEPS response code and term action',                  //	50
    '',
    'PIN data',                                               //	52
    '',
    'additional amounts',                                     //	54
    '',
    '',
    '',
    '',
    '',
    'check auth',                                              //	60
    'manager or voucher number',                               //	61
    'extra data',                                              //	62
    'sig cap data',                                            //	63
    '',
    '',
    'raw micr data',                                           //	66
    '',
    'host response code',                                      //	68
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    'original sequence number',                                //	90
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    'ECC product code',                                        //	101
    'eWIC merchant discount',                                  //	102
    'FSA amount',                                              //	103
    'specific FSA amounts',                                    //	104
    'fleet required fields',                                   //	105
    'fleet odometer',                                          //	106
    'vehicle ID',                                              //	107
    'driver ID',                                               //	108
    'fleet data',                                              //	109
    '',
    'encrypted track data from terminal',                      //	111
    '',
    '',
    '',
    '',
    '',
    '',
    'PIN pad serial number',                                   //	118
    'blackhawk original STAN',                                 //	119
    'lane status XML',                                         //	120
    'cashier ID',                                              //	121
    'customer info',                                           //	122
    'eWIC Rx',                                                 //	123
    'working key',                                             //	124
    '',
    'Reference Id',                                            // 126 //CPCLIENTS-19007
    'Original Reference Id',                                   // 127 //CPCLIENTS-19007
    'token data'                                               //	128
  );

function GetIsoBitDescription(bit: integer): string;  // used by TrxLogView utility
begin
  result := '';
  if bit in [Low(IsoBitDescriptions)..High(IsoBitDescriptions)] then
  begin
    result := IsoBitDescriptions[bit];
  end;
end;

procedure InitBitData(var In_Ptr: P_ISO);
var i: integer;
begin
  try
    for i := 1 to Max_BitMaps * 64 do
    begin
      In_Ptr.Bit_Data_In[i] := AnsiStrings.DupeString{StringOfChar}(' ', length(In_Ptr.Bit_Data_In[i]));
      In_Ptr.Bit_Data_Out[i] := AnsiStrings.DupeString{StringOfChar}(' ', length(In_Ptr.Bit_Data_Out[i]));
      In_Ptr.Bit_Data_OutTrunc[i] := In_Ptr.Bit_Data_Out[i];
    end;
  except on e: exception do                         //JTG 31788 - hardened
    SM('Try..Except Base8583.InitBitBata - ' + e.message);
  end;
end;

Procedure Init_8583(Var In_Ptr: P_ISO);
Begin
  try
    With In_Ptr^ Do
    Begin
      FillChar(Bit_Format, SizeOf(Bit_Format), 0);
      FillChar(Bit_Min_Len, SizeOf(Bit_Min_Len), 0);
      FillChar(Bit_Max_Len, SizeOf(Bit_Max_Len), 0);
      InitBitData(In_Ptr);
      Bit_Map_Type := Bit_Map_Display;
    End;
  except on e: exception do
    SM('Try..Except Base8583.Init_8583 - ' + e.message);
  end;        
End;

Function  Range(In_Bit : Integer) : Boolean;
Begin
  If (In_Bit >= 0)                  and
     (In_Bit <= (Max_BitMaps * 64)) Then { Make sure in range of 1 to whatever }
    Range := True
  Else
  Begin
    SM('****WARNING! Bit out of range '+Str_(In_Bit));
    Range := False;
  End;
End;

{ * }

Procedure Set_Bit_Mask(Var In_Ptr : P_ISO; In_Bit, Bit_Struc, Bit_Min, Bit_Max : Integer);
Begin
  With In_Ptr^ Do
  Begin
    If (Range(In_Bit) = True) Then
    Begin
      If (Bit_Min > 0)        and
         (Bit_Min <= Bit_Max) Then     { Make sure Min/Max viable }
      Begin
        If (Bit_Struc = Structure_BitMap) Then  { Checks for bit map indicator }
        Begin
          If (In_Bit = ((Max_BitMaps-1) * 64)+1) Then   { Last "bit 1" cannot be bit map }
            SM('****WARNING! LAST MAP"S BIT 1 CANNOT BE A BIT MAP INDICATOR');

          If (In_Bit > 1)               and  { Must be a "1" bit }
             (((In_Bit-1) mod 64) <> 0) Then
            SM('****WARNING! BIT MAP INDICATOR MUST BE 1 OR MULTIPLE OF 64');
        End
        Else
          If (Bit_Struc = Structure_Fixed) and
             (Bit_Min <> Bit_Max)          Then
            SM('****WARNING! BIT MAP '+Str_(In_Bit)+' IS FIXED AND MIN<>MAX');
        Bit_Format[In_Bit]  := Bit_Struc;
        Bit_Min_Len[In_Bit] := Bit_Min;
        Bit_Max_Len[In_Bit] := Bit_Max;
      End
      Else
        SM('****WARNING! BIT '+Str_(In_Bit)+' Min>Max or Min=0');
    End
    Else
      SM('****WARNING! YOU HAVE TRIED TO DEFINE BIT '+Str_(In_Bit)+' WHICH IS INVALID');
  End;
End;

procedure Set_TruncType(Var In_Ptr : P_ISO; In_Bit, In_TruncType: integer);
begin
  with In_Ptr^ do
    Bit_TruncType[In_Bit] := In_TruncType;
end;    { Set_TruncType }

{ * }
{ // XE: Remove WinEPS - not for OpenEPS
Procedure Clear_Bit_Mask(Var In_Ptr : P_ISO; In_Bit : Integer);
Begin
  With In_Ptr^ Do
  Begin
    If (Range(In_Bit) = True) Then
    Begin
      Bit_Format[In_Bit]  := Structure_Unknown;
      Bit_Min_Len[In_Bit] := 0;
      Bit_Max_Len[In_Bit] := 0;
    End
    Else
      SM('****WARNING! YOU HAVE TRIED TO DELETE BIT '+Str_(In_Bit)+' WHICH IS INVALID');
  End;
End;
}

{ // XE: Remove WinEPS - not for OpenEPS
Procedure Clear_All_Bit_Masks(Var In_Ptr : P_ISO);
Var
  I : Integer;
Begin
  With In_Ptr^ Do
    For I := 1 to 64 * Max_BitMaps Do
      Clear_Bit_Mask(In_Ptr, I);
End;
}

Procedure Set_Bit_Default(Var In_Ptr : P_ISO; In_Bit : Integer);
Begin
With In_Ptr^ Do
Begin
  If (Range(In_Bit) = True) Then
  Begin
    Bit_Format[In_Bit]  := Default_Structure[In_Bit];
    Bit_Min_Len[In_Bit] := Default_Min[In_Bit];
    Bit_Max_Len[In_Bit] := Default_Max[In_Bit];
    Bit_TruncType[In_Bit] := Default_TruncType[In_Bit];
  End
  Else
    SM('****WARNING! BIT '+Str_(In_Bit)+' INVALID IN DEFAULT ROUTINE!');
End;
End;

{ * }

Procedure Set_All_Defaults(Var In_Ptr : P_ISO);
Var
  I : Integer;
Begin
  With In_Ptr^ Do
    For I := 1 to 64 * Max_BitMaps Do
      Set_Bit_Default(In_Ptr, I);
End;

{ * }

Function  TestBit(Var In_Map : Bin_Bit_Map; In_Bit : Integer) : Integer;
Var
  ByteToTest,
  BitToTest   : Integer;
Begin
  ByteToTest := ((In_Bit-1) shr 3) + 1;    { Div 8, Which of 8 bytes is it in }
  BitToTest  := In_Bit mod 8;              { bit 1 to 8 of the hex byte       }
  If (BitToTest = 0) Then
    BitToTest := 8;                        { fix for:  bit8 mod 8 = 0         }

  If ((In_Map[ByteToTest] and HexBit[BitToTest]) = HexBit[BitToTest]) then
    TestBit := In_Bit
  Else
    TestBit := 0;
End;    { TestBit }

//just masks a single substring at a time.. 
function EmbeddedMask(iStart: integer; tmpStr: AnsiString; FullMask: boolean=false): AnsiString;  // works on tmpStr, given the start, goes to 4 before the end
var
  i,PosNextFS,EndMask: integer;
begin
  result := tmpStr;
  PosNextFS := iStart+3;
  for i := iStart to length(tmpStr) do  //55685 - find the end of the string.. expect an SSN but who knows if input is valid
    if tmpStr[i] = FS then
      begin
      PosNextFS := i;
      break;
      end;
  if FullMask
    then EndMask := PosNextFS-1
    else EndMask := PosNextFS-5;       //55685 - leave the last 4 chars as is
  for i := iStart to EndMask do
    result[i] := AnsiChar(MASKCHAR);
end;

function MaskNameinBitIfExists(aBit: integer; S: AnsiString): AnsiString;    //68539
const
  FULLMASK = true;
  TargetFullName: AnsiString = FS + 'U';
  TargetFirstName: AnsiString = FS + 'x';
  TargetLastName: AnsiString = FS + 'y';
  TargetZip: AnsiString = FS + 'V';
  TargetCVV: AnsiString = FS + 'X';


  procedure DoMask(NameTarget: AnsiString);
  var
    iPos: integer;
  begin
    iPos := pos(NameTarget,result);            // in Bit62 we see it as <FS>UJOHN DOE<FS>
    if iPos > 0 then
      result := EmbeddedMask(iPos+length(NameTarget),result,FULLMASK);
    //SM(format('DEBUG 68539 - Base8583.MaskNameinBitIfExists: Bit[%d] Orig[%s] Masked[%s] Pos[%d]',[aBit,S,result,iPos]));
  end;

begin
  try
    result := S;
    case aBit of
      FID_MISC_FIELDS:
        begin
        DoMask(TargetFullName);            // in Bit62 we see it as <FS>UJOHN DOE<FS>
        DoMask(TargetFirstName);
        DoMask(TargetLastName);
        DoMask(TargetZip);
        DoMask(TargetCVV);
        end;
      FID_FIRSTNAME:
        DoMask(TargetFirstName);
      FID_LASTNAME:
        DoMask(TargetLastName);
    end;

  except on e:exception do
    SM(format('Base8583.MaskNameinBitIfExists EXCEPTION: [%s]',[e.Message]));
  end;
end;

function MaskSSNAndCheckinBitIfExists(aBit: integer; tmpStr: AnsiString): AnsiString;      //55685
const
  SSN_Fid_62: AnsiString = FS + 'r';
  SSN_Fid_60_B: AnsiString = FS + 'BSS';   //55685.. use this way if 'SS' not reliable.. SSN could come in B or C
  SSN_Fid_60_C: AnsiString = FS + 'CSS';   //55685.. use this way if 'SS' not reliable..
  CHECK_Fid_60_B: AnsiString = FS + 'B';   // CPCLIENTS-1628
var
  iPos: integer;
begin
  result := tmpStr;
  if aBit = 60 then
  begin
    iPos := pos('SS',uppercase(result));        // in Bit60 we see it as SS1233444...
    if iPos = 0 then
      iPos := pos(CHECK_Fid_60_B, uppercase(result)); // CPCLIENTS-1628
  end
  else
    iPos := pos(SSN_Fid_62,result);            // in Bit62 we see it as <FS>r123345..
  //SM(format('DEBUG 55685: Base8583.MaskSSNinBitIfExists Target Bit[%d] Position[%d]',[aBit,iPos]));
  if iPos > 0 then
  begin
    if aBit = 60 then    //we have an 'SS'.. but could be a BSS or CSS or both
    begin
      iPos := pos(SSN_Fid_60_B,uppercase(result));
      if iPos > 0
        then result := EmbeddedMask(iPos+length(SSN_Fid_60_B),result)
      else
      begin
        iPos := pos(CHECK_Fid_60_B + idMICR,uppercase(result));
        if iPos > 0 then
          result := EmbeddedMask(iPos+length(CHECK_Fid_60_B + idMICR),result, True) //CPCLIENTS-1628
        else
        begin
          iPos := pos(CHECK_Fid_60_B + idMICRString,uppercase(result));
          if iPos > 0 then
            result := EmbeddedMask(iPos+length(CHECK_Fid_60_B + idMICRString),result, True) //CPCLIENTS-1628
        end;
      end;

      iPos := pos(SSN_Fid_60_C,uppercase(result));
      if iPos > 0
        then result := EmbeddedMask(iPos+length(SSN_Fid_60_C),result);
    end
    else
      result := EmbeddedMask(iPos+length(SSN_Fid_62),result);
    //SM(format('DEBUG 55685: Base8583.MaskSSNinBitIfExists Bit[%d] - Orig[%s] Masked[%s]',[aBit,tmpStr,result]));
  end;
end;

function truncIfNeeded(Var In_Ptr : P_ISO; aBit: integer; tmpStr: AnsiString): AnsiString;
var
  i, len: Integer;
begin                  { these trunc functions are in MTX_LIB unit }
  if (aBit = 0) then
    result := tmpStr
  else if aBit in [60,FID_MISC_FIELDS] then   //55685 - Bit 62 is a "Misc Fields" bit and may or not contain SSN, which has to be masked if it does, bit 60 similar
    begin
    result := MaskSSNandCheckinBitIfExists(aBit,tmpStr);
    if aBit = FID_MISC_FIELDS then                    //68539
      result := MaskNameinBitIfExists(aBit,result);   //68539
    end
  else if aBit in [FID_FIRSTNAME,FID_LASTNAME] then  //68539
    result := MaskNameinBitIfExists(aBit,result)
  else
  with In_Ptr^ do
  begin
    case bit_TruncType[aBit] of
      TruncTypeNone   : result := tmpStr;
      TruncTypeTrack1 : result := truncTrack1String(tmpStr);
      TruncTypeTrack2 : result := truncTrack2String(tmpStr);
      TruncTypeExp    : result := truncExpDate(tmpStr);
      TruncTypeAcctNo : result := truncAcctNo(tmpStr);
      TruncTypeString : result := truncAnyString(tmpStr);
      TruncTypeTrack1Encrypted: result := MTX_Utils.truncTrack1String(MTXEncryptionUtils.RetrieveValue(tmpStr));
      TruncTypeTrack2Encrypted: result := MTX_Utils.truncTrack2String(MTXEncryptionUtils.RetrieveValue(tmpStr));
      TruncTypeAcctNoEncrypted: result := MTX_Utils.truncAcctNo(MTXEncryptionUtils.RetrieveValue(tmpStr));
      TruncTypeTrack2TRN :                                                        
        begin
          i := Pos(Trim(Dr_LicenseTRN), tmpStr);
          if i > 0 then
          begin
            len := Length(Trim(Dr_LicenseTRN));
            result := Copy(tmpStr, 1, i-1) +
                      TruncTrack2String(Trim(Dr_LicenseTRN)) +
                      Copy(tmpStr, i+len, Length(tmpStr)-i-len+1);
          end;
        end;                                                                      
      else result := tmpStr;
    end;
  end;
end;    { truncIfNeeded }

function  truncIfNeededVar(Var In_Ptr : P_ISO; aBit: integer; varLen, tmpStr: AnsiString): AnsiString;
var
  i, len: Integer;
begin                  { these trunc functions are in MTX_LIB unit }
  if (aBit = 0) then
    result := tmpStr
  else if aBit in [60,FID_MISC_FIELDS] then   //55685 - Bit 62 is a "Misc Fields" bit and may or not contain SSN, which has to be masked if it does, bit 60 similar
    begin
    result := varLen + MaskSSNandCheckinBitIfExists(aBit,tmpStr);    //55685
    if aBit = FID_MISC_FIELDS then                                 //68539
      result := varLen + MaskNameinBitIfExists(aBit,result);   //68539
    end
  else if aBit in [FID_FIRSTNAME,FID_LASTNAME] then  //68539
    result := MaskNameinBitIfExists(aBit,result)
  else
  with In_Ptr^ do
    case bit_TruncType[aBit] of
      TruncTypeNone   : result := varLen + tmpStr;
      TruncTypeTrack1 : result := varLen + truncTrack1String(tmpStr);
      TruncTypeTrack2 : result := varLen + truncTrack2String(tmpStr);
      TruncTypeExp    : result := varLen + truncExpDate(tmpStr);
      TruncTypeAcctNo : result := varLen + truncAcctNo(tmpStr);
      TruncTypeString : result := varLen + truncAnyString(tmpStr);
      TruncTypeTrack1Encrypted: result := varLen + mtx_utils.truncTrack1String(RetrieveValue(tmpStr));
      TruncTypeTrack2Encrypted: result := varLen + mtx_utils.truncTrack2String(RetrieveValue(tmpStr));
      TruncTypeAcctNoEncrypted: result := varLen + mtx_utils.truncAcctNo(RetrieveValue(tmpStr));
      TruncTypeTrack2TRN :                                                        
        begin
          i := Pos(Trim(Dr_LicenseTRN), tmpStr);
          if i > 0 then
          begin
            len := Length(Trim(Dr_LicenseTRN));
            result := Copy(tmpStr, 1, i-1) +
                      TruncTrack2String(Trim(Dr_LicenseTRN)) +
                      Copy(tmpStr, i+len, Length(tmpStr)-i-len+1);
          end;
        end;                                                                      
      TruncTypeReceiptName:
        begin
          i := Pos('Name:', tmpStr);
          if (i = 0) then
            i := Pos('Nom:', tmpStr);
          if (i = 0) then
            i := Pos('You:', tmpStr);
          if (i = 0) then
            i := Pos(':', tmpStr);
          if (i > 0) then
          begin
            result := varLen + copy(tmpStr,1,i + 3);
            i := Pos(#$0A, copy(tmpStr,i + 4, 60));
            result := result + DupeString{StringOfChar}('*', pred(i)) + copy(tmpStr, i, length(tmpStr));
          end
          else
            result := varLen + tmpStr;

        end;
    end;
end;    { truncIfNeeded }

{ Unit init }

initialization
  ExtendedLog('Base8583 initialization');
finalization
  ExtendedLog('Base8583 finalization');

End.
