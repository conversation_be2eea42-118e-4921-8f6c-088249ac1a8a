﻿// (c) MTXEPS, Inc. 1988-2008
unit MTX_Utils;
(*******************************************************************************
This unit contains methods that many different projects might want to use.
This unit is safe in the sense that it can be "used" by any unit and not cause circular reference problems.
This unit will not contain any code in its initialization and finalization sections.
*******************************************************************************)

interface

{$UNDEF ENGINE_OR_TEST}    // JTG this effectively creates an 'or' conditional define

{$IFDEF TEST}
  {$DEFINE ENGINE_OR_TEST}
{$ENDIF ENGINE}

uses
  FinalizationLog,
  {$IFDEF MSWINDOWS}
  Windows,
  Registry,
  TlHelp32,
  {$ELSE}
  Libc,
  IdGlobal,
  {$ENDIF}
  SysUtils,StrUtils,DateUtils,Types,Classes,

  {$IFDEF ENGINE_OR_TEST}
  MRTypes,
  {$ENDIF ENGINE_OR_TEST}

  MTX_Constants,
  MTX_XMLClasses,
  {$IFDEF MTXEPSDLL}
  epsTrace,
  {$ENDIF}
  Compression,
  XMLIntf, XMLDoc,
  MdMsg,
  AbZipKit, ZLib; //CPCLIENTS-9346

type
  TCardType = (ctUndetermined, ctMasterCard, ctVisa, ctAmex, ctDiscover); // DEV-53815

const
  NOT_FOUND = -1;

{$IFDEF MSWINDOWS}
// put all methods here that contain Windows specific code (not safe for Linux)
function GetVersionString(Filename,VerStr: string): string;
function RegKeyExists(root: hkey; dir, keyName: string): boolean;
function GetStrKeyFromRegistry(root: hkey; dir, keyName: string): string;
function GetIntKeyFromRegistry(root: hkey; dir, keyName: string; Default: integer=NOT_FOUND): integer;
function SaveIntKeyToRegistry(root: hkey; dir, keyName: string; Value: integer): integer; // returns success values
function SaveStrKeyToRegistry(root: hkey; dir, keyName: string; Value: string): integer; // returns success values
//function GetProcessId(FileName: string): Cardinal; // DOEP-29392
function GetModuleInfo(FileName: string; InfoType: string='Path'): string; // DOEP-29392

{$ENDIF MSWINDOWS}

// put all methods here that are safe for both Windows & Linux
function ABlackhawkGiftActivation(InRec: MdMsgRec): boolean;
function ABlackhawkOrIDTGiftActivation(InRec: MdMsgRec): boolean;
function CheckForNewEPSBINFileAndSetName(const doUpdate: boolean = true): string;
function ConvertBinaryToAsciiHex(aBinary: AnsiString): AnsiString;
function FixCashbackN(aCashBackN: integer): integer;
function IsCardBlackhawk(aProgramID: string): boolean;
function IsCardBlackhawkOrIDT(aProgramID: string): boolean;
function IsDiscover(aAcctNo: string): boolean;
function IsDLLUpdateNeeded(aDllName, aUpdName, aDir: string): boolean;
function IsSkipSigNewWay(aSkipOnline: string): boolean;
function IsPANLengthValid(aLen: byte): boolean;
function IsPANValid(testPAN: string; var badIndex: integer; var goodLength: boolean): boolean;
function IsValuLinkProgID(aProgID: string): boolean;
function IsINCProgIDforSVD(aProgID, aSuffix: string): boolean;
function IsValuLink(aProgID: string; aReqCodeN: byte): boolean;
function IsWholeFoodsMarketProgID(aProgID: string): boolean;     //57775
procedure Delay(MS: integer);
function IsTGTorSBUX(aProgID: string; aReqCodeN: byte): boolean;
procedure FreeDiscoverPrefixes;
function GetExpDateFromTrack2(var aTrack2: string): string;
function GetMaskedTrack1Data(tmpStr: string): string;
function GetMaskedExpirationDate(tmpStr: string): string;                       
function LPad(S: string; L: integer; PadChar: char = ' '): string;
Function MakeBYLATLIssuerCode(ReqN: byte): string2; // CPCLIENTS-5179 Changed to have meaningful name that applies that ATL too
function MaskAcctNoPCI(var aMdMsg:MdMsgRec): string;
function MaskTrack2StringPCI(ATrack2Str: string): string;
function SetCheckTypeForOpenEPS(aCkType: string; aOpenEPSTender: integer): string;
procedure SetFirst6Last4(var aMdMsg: MdMsgRec; aAccount: string);
function SigNotRequired(aAmount: integer; aResponseCode: string; aSkipOffline: boolean; aSkipOnline: string;
                        aSkipAmount: integer; aPromptOnManual: string; IsManual: boolean): boolean;
function TheTenderNameByIndex(aTender: integer): string;
function TruncAnyString(tmpStr: AnsiString): AnsiString;
function TruncAcctNoForReportsOpenEPS(var aAcctNo: string): string;
function TruncAcctNoForValuLink(tmpStr: string): string;
function TruncAcctNo(tmpStr:AnsiString): AnsiString;
function TruncExpDate(tmpDate: string4; MaskFields: boolean = true): string4;
function TruncTrack2String(tmpStr: AnsiString): AnsiString;
function TruncTrack2ForPOS(F6,L4: string; PanLen,TrackLen: integer): string;     // first 6 last 4
function TruncTrack2ExceptLast4(L4: string; PanLen,TrackLen: integer): string;
function TruncMdMsgAcctNo(var aMdMsg: MdMsgRec): string;
function TruncReceiptName(tmpStr: string): string;
function TruncTrack1String(tmpStr: AnsiString): AnsiString;
//function UpTime(var aCutoverNow: boolean; aStartTime,aStopTime: integer): Boolean; // JRM-11 : According to the start/stop times, // XE: Remove WinEPS
function UseLatestDll(aDllName, aUpdName, aOldName, aDir: string): string;
function ValidateBlackHawkUPC(aTrack1Data, aUPCCode: string): string;
function DateFromYYYYMMDD(DateTimeStr: string): TDateTime;   //JTG added as result of Micah 2007-02-28 request
function OldDate: TDateTime;
procedure SetDiscoverPrefixes;
function TruncMdMsgAcctNoEx(var aMdMsg: MdMsgRec): string; // TFS-25878
function StripTagFromEMVData(const aTag: string; const aEMVTagData: string): string;
function BlockFieldReceiptData(aBlockFieldReceiptDataTraceRsp: AnsiString): AnsiString; // CPCLIENTS-7257 To remove redundant data and make more readable

{$IFDEF MSWINDOWS}
function TimeZoneBiasInHours: double;
function EasyCreateProcess(cmdLine: string; const Wait: Boolean = false; const TimeoutInterval: Cardinal = INFINITE): Boolean;
function EasyCreateProcessEx(cmdLine: string; var aHandle: THandle; const Wait: Boolean = false; const TimeoutInterval: Cardinal = INFINITE): Boolean;
function WinExecAndWait32(FileName:String; Visibility : integer):integer;
function IsDirectoryWritable(const Dir: String): Boolean;
{$ENDIF}
function ReadWriteAccessOK(Filename: TFilename): boolean;   //JTG 32641
function ExtractFolderName(Path: string): string;
function RemoveLeadingZeroes(s: string): string;

{$IFDEF ENGINE_OR_TEST}
//function MdMsg2MR(aMdMsg: MdMsgRec): TFldByTran;
{$ENDIF}
function HexToInt(aHex: AnsiString): integer;
function IsValidHexString(s: AnsiString): boolean;

{$IFDEF LINUX}
function ExecuteCmd(aCmd: string): boolean;
{$ENDIF}

function SecureDeleteFile(FileName: string): boolean;
function SecureDeleteFileEx(FileName: string): boolean;
function DeleteFileReadOnly(aFileName: string): boolean;
function IsBase64(s: string): boolean;
function FirstLastStr(aStr: string; aLen: integer=4): string;
function MakeEmbossedPAN(aPAN: string): string;
function TranslateFleetProductCode(aCode: integer): integer;   //57776
function FleetProductCodeTranslate(S: string): string;  //57776 replaces 1st 4 chars with new code if it exists
procedure ParseTrackData(TrackData: string; Delimiter: char; var Track1,Track2: string); overload;
procedure ParseTrackData(TrackData: AnsiString; Delimiter: char; var Track1,Track2: AnsiString); overload;
function MaskPANForBarcodeActivationStr(s : string): string; // DOEP-67674
function HexToDec(Str: string): Integer; // XE: Remove WinEPS not for OpenEPS but keeep - moved from scat2
function CalcCrc(s:ansistring):word; // TFS-112056
function IsValidCrc(s : ansistring): boolean; // TFS-112056
function Min(A, B : LongInt) : LongInt;
function Max(A, B : LongInt) : LongInt;
function IsLocalAuth(AuthCode: string; LaneDigits: integer): boolean;   // CPCLIENTS-4873
function GetXmlAttributeText(Node: IXMLNode; AttrName: string): string; // 4642
function IsSCATMsg(msg: string): boolean; // 5878
function UnwrapSCATMsg(msg: string): string; // 5878
function GetCardCodeFromCardType(aCardType: string): string; // 6332
function ExtractFileFromTGZ(TGZFileName: string; var EMVContactXMLFilePath: string): boolean; //CPCLIENTS-9346
procedure SetCardProcIDAndCardName(var FInRec: MdMsgRec; FMRCardType: string);
function ReplaceJsonValue(const request, key, newValue: string): string; // 17670

const
  OLDYEAR = 1900;
  GZIP_HEADER = 10;
  GZIP_1 = AnsiChar($1F);
  GZIP_2 = AnsiChar($8B);
  HexChrSet = AnsiString('0123456789ABCDEF');
  REGISTRY_WRITE_SUCCESS = 1;
  REGISTRY_WRITE_FAILURE = -1;
  Base64Chars: AnsiString = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';

  EMV_CONTACT_XML = 'EMVCONTACT.XML'; //CPCLIENTS-9346

{$IFDEF TEST}       //need to expose this for unit test
var
  lastUpTime: boolean = false;
{$ENDIF}

implementation

uses
  {$IFDEF MSWINDOWS}
  //madBasic,
  {$IFNDEF OpenIP}
  madKernel,
  {$ENDIF}
  {$ENDIF}
  {$IFDEF ENGINE_OR_TEST}
  FormatUtils,
  {$ENDIF ENGINE_OR_TEST}
  Math,
  MTXEncryptionUtils,
  GeneralUtilities,
  MTX_Lib,  // linux uses MTX_Lib.GetVersionString
  System.IOUtils; //CPCLIENTS-3145 Roll from 828.7

var
  DS3Digits: TStringList;
  DS4Digits: TStringList;

  {$IFNDEF TEST}
  lastUpTime: boolean = false;
  {$ENDIF}

procedure ParseTrackData(TrackData: string; Delimiter: char; var Track1,Track2: string);
var
  i: integer;
begin
  Track1 := '';
  Track2 := '';
  i := pos(Delimiter,TrackData);
  if i > 0 then
    begin
    Track1 := LeftStr(TrackData,pred(i));
    System.delete(TrackData,1,i);
    i := pos(Delimiter,TrackData);
    if i = 0
      then Track2 := TrackData
      else Track2 := LeftStr(TrackData,pred(i));
    end;
end;

procedure ParseTrackData(TrackData: AnsiString; Delimiter: char; var Track1,Track2: AnsiString); overload;
var
  i: integer;
  s: string;
begin
  Track1 := '';
  Track2 := '';
  i := pos(Delimiter,TrackData);
  if i > 0 then
    begin
    Track1 := AnsiString(LeftStr(TrackData,pred(i)));
    System.delete(TrackData,1,i);
    i := pos(Delimiter,TrackData);
    if i = 0
      then Track2 := AnsiString(TrackData)
      else Track2 := AnsiString(LeftStr(TrackData,pred(i)));
    end;
end;

function ReadWriteAccessOK(Filename: TFilename): boolean;   //JTG 32641 unit tested in TestMTXUtils
var
  TestFileStream: TFileStream;           // CPCLIENTS-3145 Roll from 828.7 (use more modern file techniques that are atomic and not global like FileMode)
begin
  result := false;
  if TFile.Exists(FileName, True) then
  begin
    try
      TestFileStream := TFile.Open(Filename, TFileMode.fmOpen, TFileAccess.faReadWrite);
      TestFileStream.Free;
      result := True;
    except
      result := False;
    end;
  end
  else
    SM('ReadWriteAccessOK unable to find '+Filename);  //JTG 37468-37087 (also a good idea anyway)
  if not result then
    SM('ReadWriteAccessOK FAILED for '+Filename);  //JTG 37468-37087 (also a good idea anyway)
end;

{$IFNDEF LINUX}
// put all methods here that contain Windows specific code (not safe for Linux)

{$IFDEF MSWINDOWS}
function TimeZoneBiasInHours: double;
var
  ATimeZone: TTimeZoneInformation;
begin
  case GetTimeZoneInformation(ATimeZone) of
    TIME_ZONE_ID_DAYLIGHT:
      result := ATimeZone.Bias + ATimeZone.DaylightBias;
    TIME_ZONE_ID_STANDARD:
      result := ATimeZone.Bias + ATimeZone.StandardBias;
    TIME_ZONE_ID_UNKNOWN:
      result := ATimeZone.Bias;
    else
      result := -1440;  // results in an error indication of -24 hours...
  end;
  result := -result/60.0;
end;
{$ENDIF MSWINDOWS}

function GetVersionString(Filename,VerStr: string): string;
var
  Size,Handle: dword;
  Len: uint;
  Buffer,Value: pchar;
  TransNo: pLongInt;
  SFInfo: string;
begin
  result := '';
  Size := GetFileVersionInfoSize(pChar(FileName),Handle);
  if Size > 0 then
  begin
    Buffer := AllocMem(Size);
    try
      GetFileVersionInfo(pChar(FileName),0,Size,Buffer);
      VerQueryValue(Buffer, PChar('VarFileInfo\Translation'),Pointer(TransNo),Len);
      SFInfo := format('%s%.4x%.4x%s%s%',['StringFileInfo\',LoWord(TransNo^),HiWord(Transno^),'\',VerStr]);
      if VerQueryValue(Buffer,PChar(SFInfo),Pointer(Value),Len)
        then result := Value;
    finally
      if Assigned(Buffer) then
        FreeMem(Buffer,Size);    // always release memory that's hard-allocated
    end;
  end;
end;

function RegKeyExists(root: hkey; dir, keyName: string): boolean;
{$IFDEF MSWINDOWS}
var reg : TRegistry;
{$ENDIF}
begin
  result := false;
{$IFDEF MSWINDOWS}
  try
    reg := TRegistry.create;
    try
      reg.Access := KEY_EXECUTE;         // read-only access
      reg.RootKey := root;
      if reg.OpenKey(dir, false) then
      begin
        try
          result := reg.ValueExists(keyName);
        finally
          reg.CloseKey;
        end;
      end;
    finally
      reg.free;
    end;
  except
    on e : Exception do
      SM('Try..Except RegKeyExists - ' + e.message);
  end;
{$ENDIF}
end;

function GetStrKeyFromRegistry(root: hkey; dir, keyName: string): string;
{$IFDEF MSWINDOWS}
var reg : TRegistry;
{$ENDIF}
begin
  result := '';
{$IFDEF MSWINDOWS}  
  try
    reg := TRegistry.create;
    try
      reg.Access := KEY_EXECUTE;         // read-only access
      reg.rootkey := root;
      if (reg.openkey(dir, false)) then
        try
          if reg.ValueExists(keyName)
            then result := Reg.ReadString(keyName)
            else SM('GetStrKeyFromRegistry - key not found: ' + keyName);
        finally
          Reg.CloseKey;
        end
      else
        SM('GetStrKeyFromRegistry - failed to open key: ' + dir);
    finally
      reg.free;
    end;
  except
    on e : Exception do
      SM('GetStrKeyFromRegistry exception.  ' + e.message);
  end;
{$ENDIF}  
end;

function GetIntKeyFromRegistry(root: hkey; dir, keyName: string; Default: integer=NOT_FOUND): integer;
{$IFDEF MSWINDOWS}
var reg : TRegistry;
{$ENDIF}
begin
  result := Default;
{$IFDEF MSWINDOWS}
  try
    reg := TRegistry.create;
    try
      reg.Access := KEY_EXECUTE;         // read-only access
      reg.rootkey := root;
      if (reg.openkey(dir, false)) then
        try
          if reg.ValueExists(keyName)
            then result := Reg.ReadInteger(keyName)
            else SM('GetIntKeyFromRegistry - key not found: ' + keyName);
        finally
          Reg.CloseKey;
        end
      else
        SM('GetIntKeyFromRegistry - failed to open key: ' + dir);
    finally
      reg.free;
    end;
  except
    on e : Exception do
      SM('Try..Except GetIntKeyFromRegistry - ' + e.Message);
  end;
{$ENDIF}  
end;

function SaveIntKeyToRegistry(Root: hkey; Dir,KeyName: string; Value: integer): integer; // returns success values
var
  R: TRegistry;
begin
  result := 0;  // nothing happened yet
  try
    R := TRegistry.create;
    try
      R.RootKey := Root;
      R.Access := KEY_WRITE;
      if R.OpenKey(Dir,true) then
        begin
        R.WriteInteger(KeyName,Value);
        R.CloseKey;
        result := REGISTRY_WRITE_SUCCESS;  // positive indication of success
        end;
    finally
      R.free;
    end;
  except on e: exception do
    begin
      result := REGISTRY_WRITE_FAILURE;      // unable to write to key, probably due to access restrictions
      SM('Try..Except SaveIntKeyToRegistry - ' + E.Message);
    end;
  end;
end;

function SaveStrKeyToRegistry(root: hkey; dir, keyName: string; Value: string): integer; // returns success values
var
  R: TRegistry;
begin
  result := 0;  // nothing happened yet
  try
    R := TRegistry.create;
    try
      R.RootKey := Root;
      R.Access := KEY_WRITE;
      if R.OpenKey(Dir,true) then
        begin
        R.WriteString(KeyName,Value);
        R.CloseKey;
        result := REGISTRY_WRITE_SUCCESS;  // positive indication of success
        end;
    finally
      R.free;
    end;
  except on e: exception do
    begin
      result := REGISTRY_WRITE_FAILURE;      // unable to write to key, probably due to access restrictions
      SM('Try..Except SaveStrKeyToRegistry - ' + E.Message);
    end;
  end;
end;

{$ENDIF LINUX}
// put all methods here that are safe for both Windows & Linux -----------------

function OldDate: TDateTime;
begin
  result := StartOfAYear(OLDYEAR);  // return a really OLD date...
end;

function LPad(S: string; L: integer; PadChar: char = ' '): string;
begin
  result := S;
  while length(result) < L do
    result := PadChar + result;
end;

procedure Delay(MS: integer); // DEV-42915: moved from MTX_Lib
//var TZero: TDateTime;
begin
  GeneralUtilities.Delay(MS);
//  try
//    //WaitForSingleObject(DummyEvent, MS); // WaitForSingleObject doesn't process message in the queue with SSL on
//    TZero := Now;
//    repeat
//      Sleep(50);
//      {$IFDEF MSWINDOWS}
//        {$IFNDEF ENGINE}
//          // Application.ProcessMessages;
//          LocalProcessMessages;
//        {$ENDIF ENGINE}
//      {$ENDIF MSWINDOWS}
//    until MillisecondSpan(TZero, Now) > MS;
//  except
//    on e: exception do
//      SM('Try..Except MTX_Utils.Delay - ' + e.message);
//  end;
end;

function GetExpDateFromTrack2(var aTrack2: string): string;
var aPos: integer;
begin
  result := '';
  aPos := pos('=', aTrack2);
  if (aPos > 0) then
    result := Copy(aTrack2, succ(aPos), 4);
end;

function IsDLLUpdateNeeded(aDllName, aUpdName, aDir: string): boolean;
var
  dllVersion: string;
  updVersion: string;
begin
  result := false;
  if fileExists(aDir + aUpdName) then
  begin
    updVersion := GetVersionString(aDir + aUpdName, 'FileVersion');
    dllVersion := GetVersionString(aDir + aDllName, 'FileVersion');
    if (updVersion <> dllVersion) then
      result := true;
  end;
end;

function UseLatestDll(aDllName, aUpdName, aOldName, aDir: string): string;
var
  dllVersion: string;
  updVersion: string;
begin
  result := '';   // default
  {$IFNDEF LINUX}
  if fileExists(aDir + aUpdName) then
  begin
    updVersion := GetVersionString(aDir + aUpdName, 'FileVersion');
    dllVersion := GetVersionString(aDir + aDllName, 'FileVersion');
    result := format('UseLatestDLL Versions: %s[%s] %s[%s]',[aUpdName,updVersion,aDllName,dllVersion]);
    if (updVersion <> dllVersion) then
    begin
      result := result + ' >> UPDATING';
      deleteFile(pchar(aDir + aOldName));
      sleep(100);
      renameFile(aDir + aDllName, aDir + aOldName);
      sleep(100);
      copyFile(pchar(aDir + aUpdName), pchar(aDir + aDllName),false);
      sleep(100);
    end
    else
      result := result + ' >> NO UPDATE';
  end
  else
    result := format('UseLatestDLL: File %s does not exist',[aDir+aUpdName]);
  {$ENDIF LINUX}
end;

{ ------------------------------------------------------------------------------
Name:           GetMaskedTrack1Data
Description:    Masked Track 1 data should mask all data EXCEPT data between the two ��^��
                The masking character is the asterisk.

                For example, track 2 data of:
                B4012111122223333^MEIER/SCOTT JOHN^4015451545415
                Should yield:
                ************************^MEIER/SCOTT JOHN^******************
}
function GetMaskedTrack1Data(tmpStr: string): string;
var hatPos1, hatPos2: Integer;
begin
  try
    Result := tmpStr;
    hatPos1 := Pos('^', tmpStr);
    if (hatPos1 > 0)
      then hatPos2 := hatPos1 + Pos('^', copy(tmpStr, succ(hatPos1), length(tmpStr)))
      else hatPos2 := 0;
    if (hatPos1 > 5) and (hatPos2 > hatPos1) then
      result := stringOfChar(MASKCHAR, hatPos1 - 5) + copy(tmpStr,hatPos1-4, 4) +
                copy(tmpStr, hatPos1, hatPos2-hatPos1+1) +
                stringOfChar(MASKCHAR, length(tmpStr) - hatPos2)
    else
      result := TruncAnyString(tmpStr);
    tmpStr := StringOfChar(' ', length(tmpStr));
  except
    on e: exception do
      //sm('****TRY..EXCEPT: MTX_Lib.GetMaskedTrack1Data - ' + e.message);
  end;
end;   { GetMaskedTrack1Data }

{ ------------------------------------------------------------------------------
Name:           GetMaskedExpirationDate
Description:    Masked Exp date should mask all characters of year and month with asterisk:

                For example an expiration data of 4912, would yield ****
}
function GetMaskedExpirationDate(tmpStr: string): string;
var
  I: Integer;
begin
  try
    Result := tmpStr;
    for I := 1 to Length(Result) do
      if Result[I] in ['0'..'9'] then
        Result[I] := MASKCHAR;
  except
    on e: exception do
      //sm('****TRY..EXCEPT: MTX_Lib.GetMaskedExpirationDate - ' + e.message);
  end;
end;   { GetMaskedTrack1Data }

// BEGIN Engine utilities

procedure SetFirst6Last4(var aMdMsg: MdMsgRec; aAccount: string);
var i,aPos,len: integer;
    tmpStr: string;
begin              // pass in track2 or account number
  aAccount := trim(aAccount);
  if (aAccount <> '') then
  try
    aPos := pos('=', aAccount);
    tmpStr := '';
    if (aPos > 0) then        // must be track data, so get PAN
    begin
      for i := 1 to aPos - 1 do
        tmpStr := tmpStr + aAccount[i];
    end
    else
      tmpStr := aAccount;
    len := length(tmpStr);
    if (len > 3) then
      for i := 1 to 4 do
        aMdMsg.AcctNoLast4[i] := tmpStr[len - 4 + i];
    if (len >= 6) then
      for i := 1 to 6 do
        aMdMsg.AcctNoFirst6[i] := tmpStr[i];
    if (len >= 8) then
      for i := 1 to 8 do
        aMdMsg.LookupPrefix[i] := AnsiChar(tmpStr[i]);
    if Trim(aMdMsg.AcctNo) = '' then
    begin
      aMdMsg.AcctNo := MTXEncryptionUtils.StoreValue(aAccount);  // DEV-43107: need this for prefix search
      aMdMsg.EncryptedTrack := true;
    end;
    aMdMsg.PanLen := length(tmpStr);
    aAccount := StringOfChar(' ', 40);
    tmpStr := aAccount;
  except
    on e: exception do
      ;
  end;
end;

Function MakeBYLATLIssuerCode(ReqN: byte): string2; // CPCLIENTS-5179 Changed to have meaningful name that applies that ATL too
begin
  result := '00';   { set a default value }
  Case ReqN of
    DbPurchN,
    DbPreAuthN,                      { TSL-27 }
    DbPreAuthCompN,
    ConnectPayPurchN,
    ConnectPayReturnN,
    DbReturnN : result := 'DC';

    CrPurchN,
    CrReturnN,
    CrPreAuthN,                      { TSL-27 }
    CrPreAuthCompN,
    CrVoiceAuthN :  result := 'CC';

    PropDbPurchN,
    PropDbReturnN,
    PropDbPreAuthN, // CPCLIENTS-12016
    PropDbPreAuthCompN : result := 'AC';

    eWicBalInqN,
    eWicForceN,
    eWicPreAuthN,
    eWicPreAuthCompN : result := 'EW';

    PropCrPurchN,
    PropCrReturnN,
    PropCrVoiceAuthN,
    PropCrPreAuthN,
    PropCrPreAuthCompN : result := 'PP';

    CheckPurchN,
    CheckOverRideN,
    CheckVoiceAuthN :
      begin
        if assigned(HostBuf)
          then result := HostBuf.ChkCode   { set in read host file }
          else result := '';
        if (length(trim(result)) < 2) then
          result := 'MC';                 { default is manta check or buycheck }
      end;

    EBT_FS_PurchN,
    EBT_FS_ReturnN,
    EBT_FS_VoiceN,
    User_2_ReturnN,
    EBT_Cash_PurchN,
    EBT_Cash_VoiceN,
    EBT_FS_BalinqN,
    EBT_Cash_BalinqN : result := 'EB';

    User_1_PurchN,                      { Gift card purchase }
    User_1_ReturnN,
    User_1_VoiceN,
    User_1_ActivationN,
    User_1_DeactivateN,
    User_1_PreAuthN,
    User_1_PreAuthCompN,
    User_1_RechargeN,
    User_1_BalInqN : result := 'SV';

    User_2_PurchN,             { phone card activation }
    User_2_VoiceN,             { phone card deactivation }
    User_2_OverrideN,          { phone card recharge }
    wireLessActivationN,
    wireLessDeactivateN,
    wireLessPreAuthN:  result := 'PH';     // CPCLIENTS-11904
  end;
end;

function SetCheckTypeForOpenEPS(aCkType: string; aOpenEPSTender: integer): string;
begin
  result := '';
  if (aOpenEPSTender = ttCheckAuthorization) then
    result := aCkType;
end;

function FixCashbackN(aCashBackN: integer): integer;
begin
  if (aCashBackN = BALANCE_MISSING)
    then result := 0
    else result := aCashBackN;
end;

function ValidateBlackHawkUPC(aTrack1Data, aUPCCode: string): string;
var aPos, bPos, i: integer;
    aStr: string;
    tmpUPC, PosUPC: string;
begin
  result := '';    // default, all is OK
  tmpUPC := '';
  if (trim(aTrack1Data) <> '') then
  begin
    PosUPC := aUPCCode;
    aPos := pos('^', aTrack1Data);          // first ^
    if (aPos > 0) then
    begin
      aStr := rightStr(aTrack1Data, length(aTrack1Data) - aPos);
      aPos := pos('^', aStr);              // second ^
      if (aPos > 0) then
      begin
        aStr := leftStr(aStr, aPos - 1);   // get chars between ^ and ^
        aPos := Pos('$', aStr);            // first $
        bPos := LastDelimiter('$', aStr);  // last $
        if (bPos > aPos) then              // if we have two $
        begin
          i := aPos - 1;
          while (i > 0) and (aStr[i] <> '/') do  // UPC is chars from first $, back to beginning or to a /
          begin
            tmpUPC := aStr[i] + tmpUPC;          // we are going backwards so add it to the front
            dec(i);
          end;
          while ((Length(tmpUPC) > 0) and (tmpUPC[1] = '0')) do              { delete leading zeros }
            delete(tmpUPC, 1, 1);
          while ((Length(PosUPC) > 0) and (PosUPC[1] = '0')) do              { delete leading zeros }
            delete(PosUPC, 1, 1);
          if (tmpUPC <> PosUPC) then
            result := TrxDecBlackHawkUPC;
        end;
      end;
    end;
  end;
end;

function BoolStr(aBool: boolean): string; // XE: Remove WinEPS - not for OpenEPS but keep
begin
  if aBool
    then result := 'true'
    else result := 'false';
end;

function IsSkipSigNewWay(aSkipOnline: string): boolean;
begin
  result := sameText(aSkipOnline, 'Y') or sameText(aSkipOnline, 'N'); 
end;

function SigNotRequiredOldWay(aAmount: integer; aResponseCode: string): boolean;
begin
 result := (DSProcBuf.DSProcSigLineYN = 'Y') and (aAmount <= strToIntDef(DSProcBuf.DSProcSigLineAmt, 0)*100) and
           (copy(aResponseCode,2,1) <> 'B');
  {$IFDEF MTXEPSDLL}
  //showTrace(idVT,'SigNotRequiredOldWay:  SigLineYN(ProcFile) = ' + DSProcBuf.DSProcSigLineYN +
  //   '  RespCode >' + aResponseCode + '<  SkipAmt/TranAmt ' + DSProcBuf.DSProcSigLineAmt + '00' +
  //   '/' + intToStr(aAmount) + '  so result is ' + boolStr(result));
  {$ENDIF}
end;

function SigNotRequired(aAmount: integer; aResponseCode: string; aSkipOffline: boolean; aSkipOnline: string;
                        aSkipAmount: integer; aPromptOnManual: string; IsManual: boolean): boolean;
begin
  result := false;
  if not IsManual or SameText(aPromptOnManual, 'N') then
  begin
    if IsSkipSigNewWay(aSkipOnline) then
    begin
      if (copy(aResponseCode,1,1) = 'A') then     // only for approved trx
      begin
        if (copy(aResponseCode,2,1) = 'B')        // i.e. if approved offline
          then result := aSkipOffline and (aAmount <= aSkipAmount*100)
          else result := sameText(aSkipOnline, 'Y') and (aAmount <= aSkipAmount*100);
      end;
      {$IFDEF MTXEPSDLL}
      //showTrace(idVT,'SigNotRequired:  RspCode >' + aResponseCode + '<  SkipOffline/SkipOnline = ' + boolStr(aSkipOffline) +
      //   '/' + aSkipOnline +  '  SkipAmt/TranAmt ' + intToStr(aSkipAmount*100) +
      //   '/' + intToStr(aAmount) + '  so result is ' + boolStr(result));
      {$ENDIF}
    end
    else
      result := MTX_Utils.SigNotRequiredOldWay(aAmount, aResponseCode);
  end;
  {$IFDEF MTXEPSDLL}
  //showTrace(idVT, format('SigNotRequired:  RspCode >%s<  SkipOffline/SkipOnline %s/%s, SkipAmt/TranAmt %d/%d, ' +
  //  'IsManual/PromptOnManual %s/%s, so result is %s',
  //  [aResponseCode,boolStr(aSkipOffline),aSkipOnline,aSkipAmount*100,aAmount,boolStr(IsManual),
  //   aPromptOnManual, boolStr(result)]));
  {$ENDIF}
end;

function ConvertBinaryToAsciiHex(aBinary: AnsiString): AnsiString;
var i: integer;
begin
  for i := 1 to length(aBinary) do
    result := Format('%s%.2x',[result, ord(aBinary[i])]);
end;

function CheckForNewEPSBINFileAndSetName(const doUpdate: boolean = true): string;
var sr: TSearchRec;
    fileSpec: string;
    newName: string;
    currentName: string;
    oldName: string;
    {$IFDEF MTXEPSDLL}
    debugTrace: boolean;
    {$ENDIF}

  procedure setName(aFileName: string);
  begin
    if sameText(ExtractFileExt(sr.Name),'.new') then
    begin
      if (newName <> '')
        then SysUtils.DeleteFile(sr.Name)  // delete if more than one file
        else newName := sr.name;
    end
    else
    if sameText(ExtractFileExt(sr.Name),'.xml') then
    begin
      if (currentName <> '')
        then SysUtils.DeleteFile(sr.Name)  // delete if more than one file
        else currentName := sr.Name;
    end
    else
    if sameText(ExtractFileExt(sr.Name),'.old') then
    begin
      if (oldName <> '')
        then SysUtils.DeleteFile(sr.Name)  // delete if more than one file
        else oldName := sr.Name;
    end;
  end;

  function boolStr(aValue: boolean): string;
  begin
    if aValue
      then result := 'true'
      else result := 'false';
  end;

  procedure debugMsg(aMsg: string);
  begin
    {$IFDEF MTXEPSDLL}
    if debugTrace then
      showTrace(idIN, aMsg);
    {$ENDIF MTXEPSDLL}
  end;

begin
  result := '';
  try
    //debugTrace := false;
    fileSpec := defaultDir + binFilePrefix + '*.*';
    newName := '';
    oldName := '';
    currentName := '';
    //debugMsg('****DEBUG: Search for BIN files: ' + fileSpec);
    if (FindFirst(fileSpec, 0, sr) = 0) then
    try
      setName(sr.Name);
      while (FindNext(sr) = 0) do
        setName(sr.Name);
    finally
      SysUtils.FindClose(SR);
    end;

    //debugMsg('****DEBUG: doUpdate = ' + boolStr(doUpdate) + '  FileNames:  newName/currentName/oldName >' +
    //  newName + '/' + currentName + '/' + oldName + '<');

    if doUpdate and (newName <> '') then // only do this if we have a new file
    begin
      SysUtils.DeleteFile(defaultDir + oldName);      // get rid of previous old file if there
      if fileExists(defaultDir + currentName) then    // rename current file to old
      begin
        oldName := LeftStr(currentName, length(currentName) - 3) + 'old';   // change extension
        {$IFDEF MSWINDOWS} // L825.1
        if CopyFile(PChar(defaultDir + currentName), PChar(defaultDir + oldName), false) then  // false = overwrite is OK
        {$ELSE}
        if IdGlobal.CopyFileTo(PChar(defaultDir + currentName), PChar(defaultDir + oldName)) then
        {$ENDIF}
        begin
          debugMsg('copy OK: ' + defaultDir + currentName + ' to ' + oldName);
          SysUtils.DeleteFile(defaultDir + currentName);      // get rid of current file
        end
        else
          debugMsg('copy FAILED(Err ' + IntTostr(GetLastError) +'): ' + defaultDir +
            currentName + ' to ' + oldName)
      end
      else
       debugMsg('file currentName >' + currentName + '< does not exist');

      currentName := LeftStr(newName,length(newName) - 3) + 'xml';  // change extension from new to xml
      {$IFDEF MSWINDOWS}
      if CopyFile(PChar(defaultDir + newName), PChar(defaultDir + currentName), false) then  // false = overwrite is OK
      {$ELSE}
      if IdGlobal.CopyFileTo(PChar(defaultDir + newName), PChar(defaultDir + currentName)) then
      {$ENDIF}
      begin
        debugMsg('copy OK: ' + defaultDir + newName + ' to ' + currentName);
        SysUtils.DeleteFile(defaultDir + newName);      // get rid of new file
      end
      else
        debugMsg('copy FAILED(Err ' + IntToStr(GetLastError) +'): ' + defaultDir + newName + ' to ' +
          currentName);
    end;
    result := lowercase(currentName); // HD-8632: lowercase
  except
    on e: exception do
      debugMsg('Try..Except CheckForNewEPSBINFileAndSetName ' + e.message);
  end;
end;  // CheckForNewEPSBINFileAndSetNam

{ // XE: Remove WinEPS
function UpTime(var aCutoverNow: boolean; aStartTime,aStopTime: integer): Boolean; // JRM-11 : According to the start/stop times,
var THH,TMM,TSS,TMS: Word;
    CurrentTime: integer;
begin // should the lanes be up?
  result := True; // Assume o.k. to start, or start time = stop time
  if (aStartTime <> aStopTime) then
   begin
     DecodeTime(Time, THH, TMM, TSS, TMS);
     CurrentTime := (3600 * LongInt(THH)) + (60 * LongInt(TMM)) + LongInt(TSS);
     if (aStartTime < aStopTime) then
     begin
       if (CurrentTime <  aStartTime) or (CurrentTime >= aStopTime) then
         result := False;
     end
     Else
     begin
       if (CurrentTime >= aStopTime) and (CurrentTime < aStartTime) then
         result := False;
     end;
   end;
  if (not result) and LastUpTime then
    aCutoverNow := true; // We just hit the cutover time.
  LastUpTime := result;
end; // UpTime
}
// END Engine utilities

procedure FreeDiscoverPrefixes;
begin
  if Assigned(DS3Digits) then
    FreeAndNil(DS3Digits);
  if Assigned(DS4Digits) then
    FreeAndNil(DS4Digits);                                                     
end;

function IsCardBlackhawk(aProgramID: string): boolean;
begin
  aProgramID := trim(aProgramID);   // DOEP-43226
  result := SameText(aProgramID, SWAY) or SameText(aProgramID, SPP)  or
            SameText(aProgramID, BHWK) or SameText(aProgramID, BHK) or SameText(aProgramID, BHN);
end;

function ABlackhawkGiftActivation(InRec: MdMsgRec): boolean;
begin
  result := (InRec.ReqCodeN = User_1_ActivationN) and  IsCardBlackhawk(InRec.ProgramID);
end;

function IsCardBlackhawkOrIDT(aProgramID: string): boolean;
begin
  result := SameText(aProgramID, SWAY) or SameText(aProgramID, SPP)  or SameText(aProgramID, IDT)  or
            SameText(aProgramID, BHWK) or SameText(aProgramID, BHK);
end;

function ABlackhawkOrIDTGiftActivation(InRec: MdMsgRec): boolean;
begin
  result := (InRec.ReqCodeN = User_1_ActivationN) and  IsCardBlackhawkOrIDT(InRec.ProgramID);
end;

function IsDiscover(aAcctNo: string): boolean;
begin
  // Shmilo - added try..finally to ensure we free TStringList objects
  result := false;
  SetDiscoverPrefixes;
  try
    if (Length(aAcctNo) >= 2) then // no need to define DS2Digits
      result := (DS3Digits.IndexOf(Copy(aAcctNo, 1, 2)) >= 0); // DEV-49874
    if NOT result and (Length(aAcctNo) >= 3) then
      result := (DS3Digits.IndexOf(Copy(aAcctNo, 1, 3)) >= 0);
    if NOT result and (Length(aAcctNo) >= 4) then
      result := (DS4Digits.IndexOf(Copy(aAcctNo, 1, 4)) >= 0);
    if NOT result and (Length(aAcctNo) >= 5) then
      result := (DS3Digits.IndexOf(Copy(aAcctNo, 1, 5)) >= 0);
  finally
    FreeDiscoverPrefixes;
  end;
end;

function IsPANLengthValid(aLen: byte): boolean;
begin
  result := aLen in [9..25];
end;

function IsPANValid(testPAN: string; var badIndex: integer; var goodLength: boolean): boolean;
var i: integer;
begin
  result := false;
  badIndex := 0;
  goodLength := IsPANLengthValid(length(testPAN));
  if goodLength then
  begin
    for i := 1 to length(testPAN) do
    begin
      result := testPAN[i] in ValidChars;
      if not result then
      begin
        badIndex := i;
        break;
      end;
    end;
  end
  else
    result := false;
  testPAN := stringOfChar(' ', 22);
end;

function MakeEmbossedPAN(aPAN: string): string;
var
  i: integer;
begin
  i := pos('=', aPAN);
  if i > 0
    then result := copy(aPAN,i+13,1) + copy(aPAN,i+15,2) + copy(aPAN,7,9) + rightStr(aPAN,4)
    else result := '';
end;

function IsWholeFoodsMarketProgID(aProgID: string): boolean;     //57775
begin
  result := SameText('WFM', trim(aProgID));
end;

function IsValuLinkProgID(aProgID: string): boolean;
begin
  result := SameText('VAL', trim(aProgID));
end;

function IsINCProgIDforSVD(aProgID, aSuffix: string): boolean;
begin
  result := SameText(aProgID, 'INC') and SameText(aSuffix, 'SVD');
end;

function IsValuLink(aProgID: string; aReqCodeN: byte): boolean;
begin
  result := IsValuLinkProgID(aProgID) and (aReqCodeN in user_1_set);
end;

function IsTGTorSBUX(aProgID: string; aReqCodeN: byte): boolean;
begin
  result := ((aProgID = 'TGT') or (aProgID = 'SBUX')) and (aReqCodeN in user_1_set);
end;

function MaskTrack2StringPCI(ATrack2Str: string): string;

var
  PosOfEqual,PanLen: integer;
  sPan,sDsc: string; // PAN, Discretionary data
begin
  try
  // Parse
  PosOfEqual := Pos('=',ATrack2Str);
  if PosOfEqual > 0 then
    begin
    sPan := copy(ATrack2Str,1,PosOfEqual - 1);
    sDsc := StringOfChar(MASKCHAR,length(ATrack2Str) - PosOfEqual);
    end
  else
    begin
    sPan := ATrack2Str;
    sDsc := '';
    end;

  // Mask PAN
  PanLen := length(sPan);
  if PanLen <= 10 then
    result := sPAN
  else
    result := copy(sPAN, 1, 6) + StringOfChar(MASKCHAR,PanLen - 10) + copy(sPAN, PanLen - 3, 4);
  sPan := StringOfChar(' ', length(sPan));
  ATrack2Str := StringOfChar(' ', length(ATrack2Str));
  // Mask discretionary data
  if PosOfEqual > 0 then
    result := result + '=' + sDsc;
  except on e: exception do
    SM('MaskTrack2StringPCI EXCEPTION: '+e.Message);
  end;
end;

// fixed to return correct tender names for MTX tender type bit index (3-20)
function TheTenderNameByIndex(aTender: integer): string; // XE: Remove WinEPS - not for OpenEPS but OEN
begin
  Case aTender of
    1 : result := tnDebit;
    2 : result := tnCredit;
    3 : result := tnEBT_FS;
    4 : result := tnEBT_CA;
    5 : result := tnPrivateDebit;
    6 : result := tnPrivateCredit;
    9 : result := tnCheck;
    13 : result := tnGiftCard;
    14 : result := tnPhoneCard;
    15 : result := tnFleet;
    16 : result := tnWirelessPhone;
    17 : result := tnACH;
    20 : result := tnConnectPay;
    else result := '*UNKNOWN*';
  end;
end;

function TruncAnyString(tmpStr: AnsiString): AnsiString;
begin
  try
    if (length(tmpStr) > 0)
      then result := StringOfChar(MASKCHAR, length(tmpStr))
      else result := '';
    tmpStr := StringOfChar(' ', length(tmpStr));
  except
    on e: exception do
      //sm('****TRY..EXCEPT: TruncAnyString - ' + e.message);
  end;
end;   { TruncAnyString }

// mask all but first 6 & last 4
function MaskAcctNoPCI(var aMdMsg: MdMsgRec): string;
var MaskLen: integer; 
begin
  MaskLen := aMdMsg.PanLen - 10; // DEV-27156
  if MaskLen < 1 then
    MaskLen := 4;
  result := aMdMsg.AcctNoFirst6 + StringOfChar(MASKCHAR, MaskLen) + aMdMsg.AcctNoLast4; // DEV-24066
end;

// mask only last 4
function TruncAcctNoForValuLink(tmpStr: string): string;
begin
  result := leftStr(tmpStr, length(tmpStr) - 4) + stringOfChar(MASKCHAR, 4);
end;

// mask all but last 4.
function TruncAcctNo(tmpStr: AnsiString): AnsiString;
begin
  tmpStr := trim(tmpStr);
  if length(tmpStr) > 4
    then result := stringOfChar(MASKCHAR,length(tmpStr)-4) + RightStr(tmpStr, 4)
    else result := TruncAnyString(tmpStr);
  tmpStr := StringOfChar(' ', length(tmpStr));
  result := trim(result);                                                       
end;

function TruncAcctNoForReportsOpenEPS(var aAcctNo: string): string;
begin
  if (length(aAcctNo) > 10)
    then result := leftStr(aAcctNo, 6) + StringOfChar(MASKCHAR, length(aAcctNo) - 10) + rightStr(aAcctNo, 4)
    else result := aAcctno;
end;

function TruncExpDate(tmpDate: string4; MaskFields: boolean = true): string4;
begin
  if MaskFields then
    result := stringOfChar(MASKCHAR, length(tmpDate))
  else
  begin
    result := tmpDate;
    {$IFDEF RPT} // DEV-22694 <
    if SameText(result, 'ERR') then
      result := '****';
    {$ENDIF} // DEV-22694 >
  end;
end;

function TruncTrack2String(tmpStr: AnsiString): AnsiString;
var aLen, eqPos: integer;
    beforeEq, afterEq: AnsiString;
begin
  result := '';
  try
    aLen := length(tmpStr);
    eqPos := Pos('=', tmpStr);
    if (aLen > 8) and (eqPos > 8) then     // if not 8 chars and 8 chars before = sign, just mask whole thing.
    begin
      if (eqPos > 0) then
      begin
        beforeEq := copy(tmpStr, 1, eqPos - 1);                  // get account num
        afterEq := stringOfChar(MASKCHAR, aLen + 1 - eqPos);     // mask from eq sign to end
      end
      else
      begin
        beforeEq := tmpStr;                                      // no =, just use entire input string
        afterEq := '';
      end;
      result := stringOfChar(MASKCHAR, length(beforeEq) - 4) + rightStr(beforeEq, 4) + afterEq;
    end
    else
      result := stringOfChar(MASKCHAR, aLen);
    beforeEq := StringOfChar(' ', length(beforeEq));
    afterEq := StringOfChar(' ', length(afterEq));
    tmpStr := StringOfChar(' ', length(tmpStr));
  except
    on e: exception do
      //sm('****TRY..EXCEPT: TruncTrack2String - ' + e.message);
  end;
end;   { truncTrack2String }

function TruncTrack2ForPOS(F6,L4: string; PanLen,TrackLen: integer): string;     // first 6 last 4
begin
  if (length(L4) > 4) then          // below assumes L4 is length 4
    L4 := leftStr(L4, 4);
  if (length(L4) < 4) then
    L4 := stringOfChar(MASKCHAR, 4 - length(L4)) + L4;
  if (PanLen = TrackLen)
    then result := F6 + stringOfChar(MASKCHAR, PanLen - 10) + L4
    else result := F6 + stringOfChar(MASKCHAR, PanLen - 10) + L4 + '=' + StringOfChar(MASKCHAR, TrackLen - PanLen - 1);
end;

function TruncTrack2ExceptLast4(L4: string; PanLen,TrackLen: integer): string;
begin
  if (PanLen = 0) then
    PanLen := 16;
  if (TrackLen = 0) then
    TrackLen := 32;
  if (length(L4) > 4) then          // below assumes L4 is length 4
    L4 := leftStr(L4, 4);
  if (length(L4) < 4) then
    L4 := stringOfChar(MASKCHAR, 4 - length(L4)) + L4;

  if (PanLen = TrackLen)
    then result := stringOfChar(MASKCHAR, PanLen - 4) + L4
    else result := stringOfChar(MASKCHAR, PanLen - 4) + L4 + StringOfChar(MASKCHAR, TrackLen - PanLen);
end;

function TruncMdMsgAcctNo(var aMdMsg: MdMsgRec): string;
const
  MIN_PAN = 4;      //Dev-43372 - needs to be at least 4 since we subtract 4 from it
  MAX_PAN = 20;
var
  L,i: integer;
  sLast4: string;
begin                               //Dev-43372 - don't return crap if MdMsg is empty
  try
    L := max(min(aMdMsg.PanLen,MAX_PAN),MIN_PAN);  //keeps L between MIN_PAN and MAX_PAN
    for i := 1 to sizeof(aMdMsg.AcctNoLast4) do                 //Dev-43372
      if aMdMsg.AcctNoLast4[i] in NUMERICS                      //Dev-43372
        then sLast4 := sLast4 + aMdMsg.AcctNoLast4[i]           //Dev-43372
        else sLast4 := sLast4 + '?';                            //Dev-43372
    result := StringOfChar(MASKCHAR,L-MIN_PAN) + sLast4;
  except on e: exception do
    result := '????';
  end;
end;

// TFS-25878 - fix account# masking for payroll check receipt
function TruncMdMsgAcctNoEx(var aMdMsg: MdMsgRec): string;
var
  tmpAccNo: AnsiString;
begin
  Result := TruncMdMsgAcctNo(aMdMsg);
  if (pos('?', Result) > 0) and (aMdMsg.EncryptedTrack) then
  begin
    tmpAccNo := MTXEncryptionUtils.RetrieveValue(aMdMsg.AcctNo);
    result := TruncAcctNo(tmpAccNo);
    tmpAccNo := '';
  end
end;


function TruncReceiptName(tmpStr: string): string;
var aPos: integer;
    i: byte;
    done: boolean;
    aTag: string;
begin
  result := tmpStr;              // default is no truncation
  i := 1;
  done := false;
  while not done and (i < 4) do
  begin
    case i of
      1: aTag := 'Name:';        // see receiptClass.pas for tags before name
      2: aTag := 'Nom:';
      3: aTag := 'You:';
    end;
    aPos := POS(aTag, tmpStr);
    done := (aPos > 0) and
            not((HostBuf.Suffix = 'CBS') and (ContainsText(tmpStr, 'App Name:'))); // CPCLIENTS-14760 - To not to replace '*' when Printing App Name for BAMS(CBS) host.
    if done
      then result := Copy(tmpStr, 1, pred(aPos)) + aTag + ' ' + StringOfChar('*', 20)
      else inc(i);
  end;
end;

function TruncTrack1String(tmpStr: AnsiString): AnsiString;
var
  hatPos  : integer;
begin
  try
    result := tmpStr;
    hatPos := Pos('^', result);
    if (hatPos > 5) then
      result := stringOfChar(MASKCHAR, hatPos-5) + copy(tmpStr, hatPos-4, 4) +
                stringOfChar(MASKCHAR, length(tmpStr) + 1 - hatPos)
    else
    if (Pos('=', result) > 0) then    { MPS puts track2 in bit 45 - normally track1 }
      result := TruncTrack2String(tmpStr)
    else
      result := TruncAnyString(tmpStr);
    tmpStr := StringOfChar(' ', length(tmpStr));
  except
    on e: exception do
      //sm('****TRY..EXCEPT: TruncTrack1String - ' + e.message);
  end;
end;   { truncTrack1String }

function DateFromYYYYMMDD(DateTimeStr: string): TDateTime;   //JTG added as result of Micah 2007-02-28 request
const
  LenYYYYMMDD = 8;
var
  AYear,AMonth,ADay: Word;
  DT: TDateTime;
begin
  result := OldDate;    // if we die prematurely, this gets returned
  DateTimeStr := trim(dateTimeStr);

  // die if the lengths don't match
  if length(dateTimeStr) <> LenYYYYMMDD then exit;

  // now try to encode all the values
    try
      AYear := StrToIntDef(copy(DateTimeStr,1,4),0);    // AYear has to be a word value
      AMonth := StrToIntDef(copy(DateTimeStr,5,2),0);
      ADay := StrToIntDef(copy(DateTimeStr,7,2),0);
      if TryEncodeDateTime(AYear,AMonth,ADay,0,0,0,0,DT)
        then result := DT
        else result := OldDate;
    except
      result := OldDate;  // probably not necessary
    end;
end;

procedure SetDiscoverPrefixes;
begin      // remember to call FreeDiscoverPrefixes!
  if not assigned(DS3Digits) then
    DS3Digits := TStringList.Create;
  DS3Digits.Add('622');
  DS3Digits.Add('650');
  DS3Digits.Add('955');
  if not assigned(DS4Digits) then
    DS4Digits := TStringList.Create;
  DS4Digits.Add('6011');
  DS4Digits.Add('6245');
  DS4Digits.Add('6388');
  DS4Digits.Add('6858');
  DS4Digits.Add('9400');
  DS4Digits.Add('9688');
  DS4Digits.Add('9843');
  DS4Digits.Add('9988');

  // DEV-49874
  DS3Digits.Add('36');
  DS3Digits.Add('38');
  DS3Digits.Add('39');
  DS3Digits.Add('65');

  DS3Digits.Add('300');
  DS3Digits.Add('301');
  DS3Digits.Add('302');
  DS3Digits.Add('303');
  DS3Digits.Add('304');
  DS3Digits.Add('305');
  DS3Digits.Add('622');
  DS3Digits.Add('624');
  DS3Digits.Add('625');
  DS3Digits.Add('626');
  DS3Digits.Add('644');
  DS3Digits.Add('645');
  DS3Digits.Add('646');
  DS3Digits.Add('647');
  DS3Digits.Add('648');
  DS3Digits.Add('649');

  DS4Digits.Add('3095');
  DS4Digits.Add('6011');
  DS4Digits.Add('6282');
  DS4Digits.Add('6283');
  DS4Digits.Add('6284');
  DS4Digits.Add('6285');
  DS4Digits.Add('6286');
  DS4Digits.Add('6287');
  DS4Digits.Add('6288');

  DS3Digits.Add('65559');
  DS3Digits.Add('65560');
  DS3Digits.Add('65561');
  DS3Digits.Add('65562');
  DS3Digits.Add('65563');
  DS3Digits.Add('65564');
  DS3Digits.Add('65565');
  DS3Digits.Add('65566');
end;

{$IFDEF ENGINE_OR_TEST}
(* // XE: Remove WinEPS
function MdMsg2MR(aMdMsg: MdMsgRec): TFldByTran;
var mr: TFldByTran;
    i: integer;

  function SetMRTenderFromReqCode: TsTenderTypePOS;
  Var
    i,tmp: byte;
    tmpMap: string;
    BitsOn: array[0..64] of Byte;
  begin
    if (aMdMsg.ReqCodeN in db_Set) then i := ttDebit
    else if (aMdMsg.ReqCodeN in ConnectPay_Set) then i := ttDebit  // host uses debit
    else if (aMdMsg.ReqCodeN in Cr_Set) then i := ttCredit
    else if (aMdMsg.ReqCodeN in Prop_Db_Set) then i := ttPrivateDebit
    else if (aMdMsg.ReqCodeN in Prop_Cr_Set) then i := ttPrivateCredit
    else if (aMdMsg.ReqCodeN in Ach_Set) then i := ttACH
    else if (aMdMsg.ReqCodeN in Check_Set) then i := ttCheckAuthorization
    else if (aMdMsg.ReqCodeN in WirelessSet) then i := ttWirelessPhone
    else if (aMdMsg.ReqCodeN in EBT_FS_Set) then i := ttEBT_FS
    else if (aMdMsg.ReqCodeN in EBT_Cash_Set) then i := ttEBT_CA
    else if (aMdMsg.ReqCodeN in User_1_Set) then i := ttGiftCard
    else if (aMdMsg.ReqCodeN in PhoneCard_Set) then i := ttPhoneCard
    else if (aMdMsg.ReqCodeN in Fleet_Set) then i := ttFleet
    else
    begin
      //sm('****ERROR SetMRTenderFromReqCode - invalid ReqCodN >' + str_(aMdMsg.ReqCodeN) +
      //   '< credit assumed');
      i := ttCredit;           // set something so we don't get an error
    end;
    Fillchar(BitsOn, SizeOf(BitsOn), 0);     { init 0..128 byte array   }
    BitsOn[i]  := 1;                         { set tender flag in array }

    tmpMap := '';
    i := 1;              { start at 1 & go to 64 - not more tenders than that now }
    while (i < 62) do    { 0 element skipped }
    begin
      Tmp := 8 * BitsOn[I + 0] +
             4 * BitsOn[I + 1] +
             2 * BitsOn[I + 2] +
             1 * BitsOn[I + 3] + 1;
      tmpMap := tmpMap + HexChrSet[Tmp];        // get 16 chars as bit map
      Inc(i, 4);
    end;
    result := tmpMap + stringOfChar('0', 16);   // need 32 chars for bit map
  end;

  function IsLynkCompletionWithOfflinePreAuth: boolean;
  begin
    result := SameText(HostBuf.Suffix, 'LYN') and
              (aMdMsg.PreAuthTrxFinalDisp[1] in [DispOffApp,DispFwdApp,DispFwdHeld]) and
              (aMdMsg.ReqCodeN in AuthComp_Set);
  end;

  function IsATLZeroDollarCompletionTOR: boolean;
  begin                                       // for any host, not just ATL
    result := (aMdMsg.ReqType = TORNoTermRsp) {and (aMdMsg.HostSuffixCode = 'ATL')} and (aMdMsg.TrxAmtN = 0);
  end;

  function SetMRTransactionTypeFromReqCode: integer;
  begin
    if IsLynkCompletionWithOfflinePreAuth then result := trtPurchase
    else if aMdMsg.ReqCodeN in All_Return_Trxs then result := trtReturn
    else if aMdMsg.ReqCodeN in All_Voice_Trxs then result := trtForce
    else if aMdMsg.ReqCodeN in BalInq_Set then result := trtBalanceInquiry
    else if aMdMsg.ReqCodeN in Activation_Set then result := trtActivation
    else if aMdMsg.ReqCodeN in [PropCrRechargeN,User_1_RechargeN] then result := trtRecharge
    else if aMdMsg.ReqCodeN = User_2_OverrideN then result := trtRefresh
    else if aMdMsg.ReqCodeN in Deactivate_Set then result := trtDeactivate
    else if aMdMsg.ReqCodeN in PreAuth_Set then result := trtPreAuth
    else if aMdMsg.ReqCodeN in AuthComp_Set then
    begin
      if IsATLZeroDollarCompletionTOR  // have to reverse it as a preAuth, cust cancelled
        then result := trtPreAuth
        else result := trtPreAuthCompletion;
    end
    else if aMdMsg.ReqCodeN = User_1_PreActivationN then result := trtPreActivation
    else if aMdMsg.ReqCodeN = EBT_FS_VoiceN then result := trtForce
    else if aMdMsg.ReqCodeN = User_2_ReturnN then result := trtReturnVoucher
    else result := trtPurchase;
  end;

begin    { MdMsg2MR }
    try
      InitializeTFldByTranRecord(mr);
      mr.HostSuffix := HostBuf.Suffix;
      if IsATLZeroDollarCompletionTOR
        then mr.PurchaseAmount := StrToIntDef(aMdMsg.preAuthAmt, 0)
        else mr.PurchaseAmount := aMdMsg.TrxAmtN - aMdMsg.CashBackN - aMdMsg.FeeAmtN;
      mr.CashBackAmount := aMdMsg.CashBackN;
      mr.FeeAmount := aMdMsg.FeeAmtN;
      mr.TenderTypeMTX := SetMRTenderFromReqCode;
      mr.TransactionType := SetMRTransactionTypeFromReqCode;
      mr.TransactionDate := copy(aMdMsg.TrxDate, 3, 4) + copy(aMdMsg.TrxDate, 1, 2); // must be mmddyy
      mr.TransactionTime := aMdMsg.TrxTime;
      mr.MTXSequenceNumber := aMdMsg.SeqNo;
      mr.HostResponseCode   := aMdMsg.SwRspCode;
      mr.AuthorizationNumber := aMdMsg.AuthCode;
      mr.PostTransactionNumber := aMdMsg.PostTransactionNumber;
      mr.HostRetrievalReferenceNumber := aMdMsg.RetrievalRefNo;
      mr.OverrideFlag := (aMdMsg.ReqCodeN in Override_Set);
      if (aMdMsg.VoidCode = 'V') or (aMdMsg.ReqType[1] in TORSet) then    //    DOEP-55428
        mr.SeqNumToVoid := aMdMsg.OldSeq
      else if (mr.TransactionType = trtPreAuthCompletion) then            //    DOEP-55428
        mr.SeqNumToVoid := aMdMsg.ACIPreAuthSeqNo;
      mr.TrainingTransaction := aMdMsg.Trx_Is_Training;
      mr.isVoid := (aMdMsg.VoidCode = 'V');
      if (aMdMsg.ReqType <> '') then
      begin
        if (length(aMdMsg.ReqType) > 0)
          then mr.IsTOR := (aMdMsg.ReqType[1] in TORSet)
          else mr.IsTOR := false;
        mr.IsOfflineFwd := (aMdMsg.ReqType = OfflineFwd);
        mr.IsOnline := (aMdMsg.ReqType = NormalTrx);
        mr.IsDeclinedAdvice := aMdMsg.DeclinedAdvice;
      end
      else
      begin
        mr.IsTOR  := false;
        mr.IsOfflineFwd := false;
      end;

//b
//      mr.Track1Data   not available from mdMsgRec
      mr.Track2Data := aMdMsg.Track2Data;           // encrypted already
      mr.PersonalAccountNumber := aMdMsg.AcctNo;    // encrypted already
      mr.ExpirationDate := aMdMsg.ExpDate;
      mr.PrimaryIDType := aMdMsg.primaryIDType;
      mr.SecondaryIDType := aMdMsg.SecondaryIDType;
      mr.ManualEntryMICRFlag := (aMdMsg.Entry_Check = 'M');
      mr.ManualEntryTrack2Flag := (aMdMsg.Entry_Track2 = 'M');
      mr.ManualEntryIDFlag := (aMdMsg.Entry_DL = 'M');
      mr.IsBarCodeScan  := aMdMsg.BarCodeScan;
      mr.RFID  := aMdMsg.RFID;
      mr.UPCCode := aMdMsg.UPCCode;
      mr.AccountType := aMdMsg.AcctInd;
      mr.CVV2 := aMdMsg.CVV2;
      mr.CreditToDebitFailed := (aMdMsg.CrToDbCode = CrToDbToCr);
      mr.CrToDbFlag := (aMdMsg.CrToDbCode = CrToDb);
      mr.CardProcessingID := aMdMsg.CardProcID;
      mr.CardType := aMdMsg.CardName;
//c
      mr.CheckTransitRoutingNumber := aMdMsg.Check_Field_3;
      mr.CheckAccountNumber := aMdMsg.Check_Field_2;
      mr.CheckNumber := aMdMsg.Check_Num;
      mr.CheckType := aMdMsg.Check_Type;
      mr.ECCProductCode := aMdMsg.ECCProductCode;
      if (aMdMsg.ReqCodeN in Check_Set) then
      begin
        i := 1;
        while (i <= high(aMdMsg.fleetData)) and (ord(aMdMsg.fleetData[i]) <> 0) do
        begin
          inc(mr.RawMICRSize);     // first hex zero is end of data
          inc(i);
        end;
        if (mr.RawMICRSize > 0) then
          move(aMdMsg.fleetData[1], mr.RawMICR, mr.RawMICRSize)
      end;
      mr.EChkCapable := aMdMsg.EChkCapable;
//d
      mr.TipAmount := StrToIntDef(aMdMsg.Tip, 0);
//      mr.SettlementTotals     not used
      mr.CurrencyCode := aMdMsg.CurrencyCode;
      mr.FsaAmount := aMdMsg.FsaAmount;
      mr.RxAmount  := aMdMsg.RxAmount;
      mr.MedicalAmount := aMdMsg.MedicalAmount;
      mr.VisionAmount := aMdMsg.VisionAmount;
      mr.DentalAmount := aMdMsg.DentalAmount;
//e
      mr.PrimaryID := aMdMsg.Check;
      mr.StateCode := aMdMsg.St_Code;
      mr.SecondaryID := aMdMsg.Dr_License;
//f
      mr.Odometer := aMdMsg.fleetOdometer;
      if (aMdMsg.fleetVehicleID <> '') then
        mr.VehicleID := stringOfChar('0', high(mr.VehicleID) - length(aMdMsg.fleetVehicleID)) + aMdMsg.fleetVehicleID;
      mr.DriverID  := aMdMsg.fleetDriverID;
      move(aMdMsg.fleetData[1], mr.FleetData[0], aMdMsg.fleetLen);
      mr.FleetCardType := aMdMsg.fleetCardType;
//g
      mr.LaneNumber := aMdMsg.LaneNo;
      mr.CashierID := aMdMsg.Cashier;
      mr.ManagerID := aMdMsg.ManagerID;
      mr.VoucherNumber := aMdMsg.EBT_Voucher_Num;
      mr.PONumber := aMdMsg.PONumber;
      mr.LaneType := aMdMsg.EPSLaneType;
      mr.UserID := aMdMsg.UserID;
//      mr.Department      comes from OpenEPS sign on not used here
      mr.CommandSequence := aMdMsg.CmdSequence;
      if (pos(Chr_SigCapture, aMdMsg.CmdSequence) > 0) then
        mr.ERCSequence := Chr_SigCapture;
      if (pos(Chr_RcptCapture , aMdMsg.CmdSequence) > 0) then
        mr.ERCSequence := mr.ERCSequence + Chr_RcptCapture ;
//      mr.ManagerNumberPOS         not used
      mr.ActivationBatch := aMdMsg.BatchNumber;
//      mr.TransactionPath    not used
      mr.UUID := aMdMsg.UUID;
      mr.BioMetricsTran := (aMdMsg.BioMetricsTran = 'Y');
      mr.IsCardFSA := (aMdMsg.FSACard = 'Y');
      mr.ProgramCode := aMdMsg.ProgramID;
//h
      mr.CustomerName := aMdMsg.Customer_Name;
      mr.CustomerAddress := aMdMsg.CustomerAddress;
      mr.CustomerCity  := aMdMsg.CustomerCity;
      mr.CustomerState := aMdMsg.CustomerState;
      mr.ZipCode := aMdMsg.ZipCode;
      mr.PhoneNumber := aMdMsg.PhoneNumber;
      mr.DateOfBirth := aMdMsg.DOB;
//i
      mr.PIN := aMdMsg.PIN;
      mr.DukptKeySerialNumber := aMdMsg.DukptKeySerialNumber;
      mr.PinPadSerialNumber := aMdMsg.PinPadSerialNumber;
      mr.AmountChangeAllowed := sameText(aMdMsg.HostApproveLowerAmount,'Y');
      result := mr;
    except
      on e: exception do
        ;//MsgLog('TRY..EXCEPT in MdMsg2MR: ' + e.message);
    end;
end;    { MdMsg2MR }
*)
{$ENDIF ENGINE_OR_TEST}

function HexToInt(aHex: AnsiString): integer;
begin
  try
    result := StrToInt('$'+aHex);  //JTG: one line replaces faulty complexity below
  except on e:EConvertError do
    result := 0;
  end;

// JTG: old way below failed for everything except strings exactly 4 chars long
//  result := ((pos(aHex[4],HexChrSet) - 1)      +
//                     (pos(aHex[3],HexChrSet) - 1)*16   +
//                     (pos(aHex[2],HexChrSet) - 1)*256  +
//                     (pos(aHex[1],HexChrSet) - 1)*4096)
end;

function IsValidHexString(s: AnsiString): boolean;
var
  i: integer;
begin
  result := true;  // in case S is empty
  for i := 1 to Length(s) do
    begin
    result := pos(s[i],HexChrSet) > 0;
    if not result
      then break
    end;
end;

{$IFDEF MSWINDOWS}
function EasyCreateProcessEx(cmdLine: string; var aHandle: THandle; const Wait: Boolean = false; const TimeoutInterval: Cardinal = INFINITE): Boolean;
// This proc takes care of all the tedious parameters of the CreateProcess API call.
var
  dw             : integer ;
  lb             : longbool;
  lp             : pointer ;
  ts             : TStartupInfo;
  tp             : TProcessinformation;
begin
  result := false;
  try
    dw:=0;
    lb:=false;
    lp:=nil;
    fillchar(ts, sizeof(ts), 0);
    fillchar(tp, sizeof(tp), 0);
    ts.dwflags     := STARTF_USESHOWWINDOW;
    ts.wShowWindow := SW_HIDE;
    Result := CreateProcess(nil,pchar(cmdLine),nil,nil,lb,dw,lp,nil,ts,tp);
    if Wait and Result then
      WaitForSingleObject(tp.hProcess, TimeoutInterval);
    aHandle := tp.hProcess;
    CloseHandle(tp.hProcess);
    CloseHandle(tp.hThread);
  except
    on E: Exception do
      ;//SM('Exception in MTX_Lib.EasyCreateProcessEx: ' + E.Message);
  end;
end;
{$ENDIF}

{$IFDEF MSWINDOWS}
function EasyCreateProcess(cmdLine: string; const Wait: Boolean = false; const TimeoutInterval: Cardinal = INFINITE): Boolean; { JMR-N: moved from StartupU }
var aHandle: THandle;
begin
  result := false;
  try
    result := EasyCreateProcessEx(cmdLine, aHandle, Wait, TimeoutInterval);
  except
    on E: Exception do
      ;//SM('Exception in MTX_Lib.EasyCreateProcess: ' + E.Message);
  end;
end;
{$ENDIF}

{$IFDEF LINUX} 
function ExecuteCmd(aCmd: string): boolean;
var
  res : integer;
  a_thread    : pthread_t;
  thread_Attr : pthread_attr_t;

  procedure ThreadProc(s : pchar);
  var r: integer;
  begin
    r := Libc.system(s);
    pthread_exit(nil);
  end;
begin
  result := false;
  res := pthread_attr_init(thread_attr);
  if res <> 0 then
  begin
    //msgLog('attribute creation failed');
    exit;
  end;
  res := pthread_attr_setdetachstate(thread_attr, PTHREAD_CREATE_DETACHED);
  if res <> 0 then
  begin
    //msgLog('setting detached attribute failed');
    exit;
  end;
  res := pthread_create(a_thread, @thread_attr, @ThreadProc, pchar(aCmd));
  if res <> 0 then
  begin
    //msgLog('thread creation failed');
    exit;
  end
  else
    result := true;
end;
{$ENDIF}

{$IFDEF MSWINDOWS}
function WinExecAndWait32(FileName:String; Visibility : integer):integer;
 { returns -1 if the Exec failed, otherwise returns the process' exit
   code when the process terminates }
 var
   zAppName:array[0..512] of char;
   zCurDir :array[0..255] of char;
   WorkDir :String;
   StartupInfo:TStartupInfo;
   ProcessInfo:TProcessInformation;
 begin
   StrPCopy(zAppName,FileName);
   GetDir(0,WorkDir);
   StrPCopy(zCurDir,WorkDir);
   FillChar(StartupInfo,Sizeof(StartupInfo),#0);
   StartupInfo.cb := Sizeof(StartupInfo);
   StartupInfo.dwFlags := STARTF_USESHOWWINDOW;
   StartupInfo.wShowWindow := Visibility;
   if not CreateProcess(nil,
     zAppName, { pointer to command line string }
     nil, { pointer to process security attributes}
     nil, { pointer to thread security attributes }
     false, { handle inheritance flag }
     CREATE_NEW_CONSOLE or { creation flags }
     NORMAL_PRIORITY_CLASS,
     nil, { pointer to new environment block }
     nil, { pointer to current directory name }
     StartupInfo, { pointer to STARTUPINFO }
     ProcessInfo) then { pointer to PROCESS_INF }
     Result := -1
   else
   begin
      //Application.ProcessMessages;          // JTG - syntax error, must comment out
      Result := WaitforSingleObject(ProcessInfo.hProcess,INFINITE);
      //GetExitCodeProcess(ProcessInfo.hProcess,Result);
   end;
end;
{$ENDIF}

function SecureDeleteFile(FileName: string): boolean;
var
  RetryCount: integer;
begin
  RetryCount := 0;
  repeat
    result := SecureDeleteFileEx(FileName);
    if NOT result then
    begin
      Delay(500);
      Inc(RetryCount);
      result := SecureDeleteFileEx(FileName);
    end;
  until result or (RetryCount = 3);
end;

function SecureDeleteFileEx(FileName: string): boolean;
const
  BUFFER_SIZE = 1024;
var
  f: file;
  tmpBuf: array[1..BUFFER_SIZE] of Byte;
  i, tmpSize, BlockCount, RemainSize: integer;
  tmpFileName: string;
begin
  result := false;
  FillChar(tmpBuf, SizeOf(tmpBuf),0);
  try
    if FileExists(FileName) then
    begin
      FileSetAttr(FileName, FileGetAttr(FileName) and not faReadOnly);
      AssignFile(f, FileName);
      Reset(f, 1);
      try
        tmpSize := FileSize(f);
        BlockCount := tmpSize div BUFFER_SIZE;
        RemainSize := tmpSize mod BUFFER_SIZE;
        for i := 1 to BlockCount do
          BlockWrite(f, tmpBuf, BUFFER_SIZE);
        if RemainSize > 0 then
          BlockWrite(f, tmpBuf, RemainSize);
      finally
        CloseFile(f);
      end;
      tmpFileName :=  ExtractFilePath(FileName) + '$$$.tmp';
      if NOT RenameFile(FileName, tmpFileName) then
      begin
        SM('SecureDeleteFile - failed to rename to ' + tmpFileName);
        Exit;
      end;
      if NOT DeleteFile(tmpFileName) then
      begin
        SM('SecureDeleteFile - failed to delete file: ' + tmpFileName);
        Exit;
      end;
    end;
    result := true;
    SM(FileName + ' deleted securely');
  except
    on e : Exception do
      SM('Try..Except SecureDeleteFile -  ' + e.Message);
  end;
end;

function DeleteFileReadOnly(aFileName: string): boolean;
begin
  FileSetAttr(aFileName, FileGetAttr(aFileName) and not faReadOnly);
  result := SysUtils.DeleteFile(aFileName);
end;

function IsBase64(s: string): boolean; 
var i: integer;
begin
  result := false;
  for i := 1 to Length(s) do
    if (Pos(s[i], Base64Chars + '=') < 1) then
    begin
      {$IFDEF SECURITY_DEBUG}
      SM(Format('IsBase64: Invalid Base64 index=%d, s=%s', [i, PrintableStr(s, rtHex)]));
      {$ENDIF}
      exit;
    end;
  result := true;
end;

function FirstLastStr(aStr: string; aLen: integer=4): string;
begin
  if Length(aStr) <= aLen * 2 then
    result := aStr
  else
    result := Copy(aStr, 1, aLen) + '...' + Copy(aStr, Length(aStr)-aLen+1, aLen);
end;

{$IFDEF MSWINDOWS}
function GetModuleInfo(FileName: string; InfoType: string='Path'): string; // DOEP-29392
begin
  result := '';
  {$IFNDEF OpenIP}
  try
    if SameText(InfoType, 'Path') then
      result := Module(FileName).FileName
    else if SameText(InfoType, 'BaseAddr') then
      result := Format('%x', [Cardinal(Module(FileName).Memory)]);
  finally
  end;
  {$ENDIF OpenIP}
end;
{$ENDIF MSWINDOWS}

{$IFDEF MSWINDOWS}
function IsDirectoryWritable(const Dir: String): Boolean;
var
  TempFile: array[0..MAX_PATH] of Char;
begin
  if GetTempFileName(PChar(Dir), '~', 0, TempFile) <> 0 then
    Result := Windows.DeleteFile(TempFile)
  else
    Result := False;
end;
{$ENDIF MSWINDOWS}

function ExtractFolderName(Path: string): string; // DOEP-29392
var
  I: Integer;
begin
  // take out last back slash
  if Path[Length(Path)] = '\' then
    Path := Copy(Path, 1, Length(Path) -1);
  I := LastDelimiter(PathDelim + DriveDelim, Path);
  Result := Copy(Path, I + 1, MaxInt);
end;

// JMR: remove leading zeroes from a string. The string does not need to be a number.
// Unit test: Test_RemoveLeadingZeroes in Test_MtxUtils.pas
function RemoveLeadingZeroes(s: string): string;
begin
  while (Copy(s,1,1) = '0') do
    Delete(s,1,1);
  result := s;
end;

function ExtractProductCodePair(aInput: string; Delimiter: char; var Code1,Code2: integer): boolean;
var
  i: integer;
  sCode1,sCode2: string;
begin
  result := false;  // assume function fails
  Code1 := 0;
  Code2 := 0;
  i := pos(Delimiter,aInput);
  if i > 0 then
    begin
    sCode1 := trim(LeftStr(aInput,i-1));  //extract strings to variables so we can trim so as to be forgiving on white space
    delete(aInput,1,i);
    sCode2 := trim(aInput);

    if not TryStrToInt(sCode1,Code1) then exit;
    if not TryStrToInt(sCode2,Code2) then exit;
    result := true;      // we are happy
    end;
end;

function TranslateFleetProductCode(aCode: integer): integer;   //57776
const
  STARTTAG = '[StartFleetData]';
  ENDTAG = '[EndFleetData]';
var
  Filename,S: string;
  FoundTag: boolean;
  f: TextFile;
  Code1,Code2: integer;
begin
  try
    result := aCode;  // default in case no conversion found;

    Filename := DefaultDir+OpenEPSIni;
    if FileExists(Filename) then
      begin
      assignfile(f,Filename);     //uses the OpenEPS.ini file
      reset(f);  //reset file; read all lines between [StartFleetData] and [EndFleetData] into string list and sort, use colon as delimiter
      try
        FoundTag := false;
        while not (eof(f) or FoundTag) do
          begin
          readln(f,S);
          S := trim(S);
          FoundTag := SameText(STARTTAG,S);
          end;
        if FoundTag then
          while not eof(f) do
            begin
            readln(f,S);
            S := trim(S);
            if SameText(ENDTAG,S) then
              break;  // just leave

            if ExtractProductCodePair(S,':',Code1,Code2) then
              begin
              if aCode = Code1 then          // if we find a match, then return the result
                begin
                result := Code2;
                break;                       // leave as soon as we get a match
                end;
              end
            else
              SM(format('MTX_Utils.TranslateFleetProductCode failure to decode [%s]',[S]));
            end;
      finally
        closefile(f);
      end;
      end;
  except on e: exception do
    SM('Exception: MTX_Utils.TranslateFleetProductCode: ' + e.Message);
  end;
end;

function FleetProductCodeTranslate(S: string): string;  //57776 replaces 1st 4 chars with new code if it exists
const
  CODELEN = 4;
  FLEET_DATA_LEN = 34;
var
  OldCode,NewCode: integer;
  sOldCode,sNewCode: string;
  FleetData, tmpS: string;
begin
  SM('FleetProductCodeTranslate: Full FleetData=' + S);
  result := S;   // return same string as default in case something goes wrong, or if there is no change
  try
    if SameText(mtx_lib.Reg_Lookup(DefaultDir + OpenEPSIni,FLEETPRODUCTACI,False),'Y') then
      begin
      result := '';
      tmpS := S;
      while Length(tmpS) > 0 do
        begin
        FleetData := Copy(tmpS, 1, FLEET_DATA_LEN);
        tmpS := Copy(tmpS, FLEET_DATA_LEN + 1, Length(tmpS)-FLEET_DATA_LEN);
        SM('FleetProductCodeTranslate FleetData=' + FleetData + '/ Remain=' + tmpS);
        sOldCode := LeftStr(FleetData,CODELEN);
        if TryStrToInt(sOldCode,OldCode) then
          begin
          NewCode := TranslateFleetProductCode(OldCode);
          if NewCode <> OldCode then
            begin
            sNewCode := format('%4.4d',[NewCode]);
            result := result + sNewCode + RightStr(FleetData,length(FleetData)-CODELEN);  //TFS 8328
            SM(format('FleetProductCodeTranslate [%4.4d] -> [%s]',[OldCode,sNewCode]));
            //SM(format('DEBUG 57776: Prev/New Fleet Field [%s] -> [%s]',[S,result]));
            end
          else
            result := result + FleetData;
          end
        else
          begin
          result := result + FleetData;
          SM(format('FleetProductCodeTranslate FAILED TO EXTRACT the left %d chars from [%s] (%s)',[CODELEN,S,sOldCode]));
          end;
        end;
      end;
    SM('FleetProductCodeTranslate: result=' + result);
  except on e: exception do
    SM('Exception: MTX_Utils.FleetProductCodeTranslate: ' + e.Message);
  end;
end;

// DOEP-67674 - Rolled Forward: DOEP-67673 - TGT: Log Barcode Activation String with Masked Pan
function MaskPANForBarcodeActivationStr(s : string): string;
const
  DELIM = ',';
var
  BarcodeActivationStrList: TStringList;
begin
  Result := s;
  BarcodeActivationStrList := TStringList.Create;
  try
    BarcodeActivationStrList.Delimiter := DELIM;
    BarcodeActivationStrList.DelimitedText := '"' + StringReplace(s, DELIM,
      '"' + DELIM + '"', [rfReplaceAll]) + '"';
    if BarcodeActivationStrList.Count >= 3 then
      BarcodeActivationStrList[3] := TruncAcctNo(BarcodeActivationStrList[3]);
    Result := BarcodeActivationStrList.DelimitedText;
  finally
    FreeAndNil(BarcodeActivationStrList);
  end;
end;

function HexToDec(Str: string): Integer;  // XE: Remove WinEPS not for OpenEPS but keeep - moved from scat2
var
  i, M: Integer;
begin
  result:=0;
  M:=1;
  Str:=AnsiUpperCase(Str);
  for i := length(Str) downto 1 do
    begin
    case Str[i] of
      '1'..'9': inc(result,(Ord(Str[i])-Ord('0'))*M);
      'A'..'F': inc(result,(Ord(Str[i])-Ord('A')+10)*M);
    end;
    M := M shl 4;
    end;
end;

////////////// CRC routines for TFS-112056 ////////////////////
procedure ByteCrc(data: byte; var crc: word);
var
  i: Byte;
begin
  for i := 0 to 7 do
  begin
    if ((data and $01) xor (crc and $0001) <> 0) then
    begin
      crc := crc shr 1;
      crc := crc xor $8408;
    end
    else
      crc:=crc shr 1;
    data := data shr 1;
  end;
end;

function CalcCrc(s:ansistring):word;
var
  len, i: Integer;
begin
  result := $FFFF;
  len := length(s);
  for i := 1 to len do
    bytecrc(ord(s[i]), result);
end;

function IsValidCrc(s : ansistring): boolean;
var
  wCrc : Word;
  len, i: Integer;
  CrcHexStr : ansistring;
begin
  result := False;
  if Length(s) < 2 then
    Exit;
  wCrc := CalcCrc(Copy(s, 1, Length(s)-2));
  CrcHexStr := IntToHex(wCRC, 4);
  result := (s[Length(s)-1] = AnsiChar(StrToIntDef('$'+Copy(CrcHexStr,1,2),0))) and
      (s[Length(s)] = AnsiChar(StrToIntDef('$'+Copy(CrcHexStr,3,2),0)));
end;

function Min(A, B : LongInt) : LongInt;
begin
  if A < B then
    Result := A
  else
    Result := B;
end;

function Max(A, B : LongInt) : LongInt;
begin
  if A > B then
    Result := A
  else
    Result := B;
end;

// Given: a tag and a string of EMV data
// Return: a string without the given tag or that tag's associated data
// Unit tests: OpenEpsCommonTests.TestStripTagFromEMVData
function StripTagFromEMVData(const aTag: string; const aEMVTagData: string): string;
var
  PosOfTag: integer;
begin
  Result := aEMVTagData;
  PosOfTag := Pos(aTag+'-', Result);
  if PosOfTag > 0 then
  begin
    while ((PosOfTag <= Length(Result)) and (AnsiChar(Result[PosOfTag]) <> '|')) do  // delete tag  & contents
      Delete(Result, PosOfTag, 1);
  end;
  if Pos('||', Result) > 0 then  // if this created a || then delete one |
    Delete(Result, Pos('||', Result), 1);
  Result := Result.Trim(['|']);
end;

// CPCLIENTS-7257 To remove redundant data and make more readable
function BlockFieldReceiptData(aBlockFieldReceiptDataTraceRsp: AnsiString): AnsiString;
begin
  result := StringReplace(aBlockFieldReceiptDataTraceRsp, '"{\"DataFields\":{\"Receipt\":', '',[rfReplaceAll]);
  result := StringReplace(result, '}"}', '',[rfReplaceAll]);
  result := StringReplace(result, '\"', '"',[rfReplaceAll]);
end;


function IsLocalAuth(AuthCode: string; LaneDigits: integer): boolean;  // CPCLIENTS-4873
begin
  SM('authcode:'+authcode+' authcode starts with..'+booltostr(AuthCode.StartsWith(OffAppHdr))+' Lanedigits:'+inttostr(Lanedigits)+' Authcode.startswith'+booltostr(AuthCode.StartsWith(OffAppHdr[1])));
  Result := AuthCode.StartsWith(OffAppHdr) or ((LaneDigits > 2) and AuthCode.StartsWith(OffAppHdr[1]));
end;

function GetXmlAttributeText(Node: IXMLNode; AttrName: string): string;
var AttrNode: IXMLNode;
begin
  result := '';
  AttrNode := Node.AttributeNodes[AttrName];
  if Assigned(AttrNode) then
    result := AttrNode.Text;
end;

function IsSCATMsg(msg: string): boolean; // 5878
begin
  result := (msg.Length >= 3) and msg.StartsWith(STX) and (msg[msg.Length-1] = ETX); // TODO: LRC check?
end;

function UnwrapSCATMsg(msg: string): string; // 5878
begin
  result := msg;
  if IsSCATMsg(msg) then
    result := msg.Substring(1, msg.Length-3);
end;

function GetCardCodeFromCardType(aCardType: string): string; // 6332
begin
  Result := aCardType.Substring(0, 2).ToUpper;
end;

function ExtractFileFromTGZ(TGZFileName: string; var EMVContactXMLFilePath: string): boolean; //CPCLIENTS-9346
var
  I: integer;
  Zip: TAbZipKit;
  DestDir: string;
  InStream: TFileStream;
  OutStream: TMemoryStream;
  DecompressionStream: TDecompressionStream;
begin
  result := false;
  if FileExists(TGZFileName) then
  begin
    try
      DestDir := IncludeTrailingPathDelimiter(ExtractFilePath(TGZFileName));
      EMVContactXMLFilePath := DestDir + EMV_CONTACT_XML; //CPCLIENTS-9346
      InStream := TFileStream.Create(TGZFileName, fmOpenRead);
      OutStream := TMemoryStream.Create;
      try
        DecompressionStream := TDecompressionStream.Create(InStream, 15 + 16); // 31 bit wide window = gzip only mode
        OutStream.CopyFrom(DecompressionStream, 0);
        OutStream.Position := 0;

        Zip:= TAbZipKit.Create(nil);
        try
          Zip.BaseDirectory:= DestDir;
          Zip.Stream := OutStream;
          I := Zip.FindFile(EMV_CONTACT_XML);
          if I > 0 then
          begin
            Zip.ExtractAt(I, EMVContactXMLFilePath);
            result := true;
            SM(Format('ExtractFileFromTGZ - %s extracted from %s', [EMV_CONTACT_XML, TGZFileName]));
          end
          else
            SM(Format('ExtractFileFromTGZ - %s does not contain %s', [TGZFileName, EMV_CONTACT_XML]));
        finally
          FreeAndNil(Zip);
        end;
      finally
        FreeAndNil(OutStream);
        FreeAndNil(InStream);
      end;
    except
      on e: exception do
        SM('Try..Except ExtractFileFromTGZ - ' + e.message);
    end;
  end
  else
    SM(Format('ExtractFileFromTGZ - File not exists: %s', [TGZFileName]));

end;

procedure SetCardProcIDAndCardName(var FInRec: MdMsgRec; FMRCardType: string);
begin
  if not String.IsNullOrEmpty(FMRCardType) then
  begin
    FInRec.CardProcID := Copy(FMRCardType, 1, 2);
    FInRec.CardName   := Copy(FMRCardType, 3, 16);
  end;
end;

// This function searches the key in the JSON request string, replaces its value and returns the modifed request.
function ReplaceJsonValue(const request, key, newValue: string): string; // 17670
var
  startIdx, endIdx : integer;
  jsonStr : string;
begin
   startIdx := Pos('"' + key + '":', request);
   if startIdx > 0 then
   begin
     startIdx := startIdx + Length(key) + 3;
     endIdx := PosEx(',',request,startIdx);

     if endIdx = 0 then
       endIdx := PosEx('}', request, startIdx);

     jsonStr := Copy(request, startIdx, endIdx - startIdx);
     jsonStr := StringReplace(jsonStr, jsonStr, '"' + newValue + '"', []);

     Result := Copy(request,1,startIdx - 1) + jsonStr + Copy(request, endIdx, MaxInt);
   end
   else
     Result := request; // return the original request if key not found.
end;

initialization
  ExtendedLog('MTX_Utils Initialization');
finalization
  ExtendedLog('MTX_Utils Finalization');

end.
