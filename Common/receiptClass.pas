// (c) MTXEPS, Inc. 1988-2008
Unit receiptClass;

{ -- Mod Log --
ver   Date      Who    Change
v825.2 01-28-09 TSL     Add ValuLink special printing/truncation of acct no on receipt
v825.0 1210-07  TSL     Change this to a TReceipt = class
v823.0 03-26-07 TSL-15  Don't print Credit_Verbage on ECC receipts, add ECC receipts for
                        Lynk host (Certegy)
v820.0 07-05-06 TSL-14  Stand alone Receipt was not printing sig line, no sig line on declines,
                        Set rDir = Default ifdef MTXEPSDLL, no Gift card bal for voided MPS trans
v819.0 04-26-06 TSL-Z  Add a sig line to the ECC receipt
v818.0 02-08-06 TSL-X  Add DECLINED to all declined integrated receipts
v817.0 03-14-06 TSL-Y  Per <PERSON>, make two receipts if integrated and declined.
v817.0 11-29-05 TSL-13 Don't print sig line on cust_copy
v816.1 10-03-05 TSL-12 add Pub<PERSON>R<PERSON>eipt routine
v815.2 03-21-05 TSL-11 add Msg to receipt for Electronic Check Conversion
v815.0 11-10-04 TSL-10 In AddWMsgToReceipt, just send prefix, not exp date from track2 data
v814.0 04-09-04 TSL-09 Patch from 813.1 for franking (Shop N Save), add TenderIndex
v814.0 02-23-04 TSL-10 Add PreActivate descrip on receipt, add check for Z tac in
                       integrated to print declined receipt
v813.1 07-09-04 TSL-10 Add option from process.eft to not print sig line
v813.1 01-29-04 TSL-08 Add PrintSigLine var so Vital host can call Build_ReceiptNoSig
                       for orders <= $25.00, add procedure AddWMsg for wireless recpt
v813.0 01-09-04 TSL-07 On Gift card deactivate, make sure an amount prints
v812.0 09-25-03 TSL-06 A little clean up of comments
v811.1 09-02-03 TSL-05 Add Check Franking if Check tender, printer 150/500 and franking TAC there.
v811.0 07-09-03 TSL-04 Truncate Exp Date if Acct No is truncated
v810   06-19-03 TSL-03 Don't print begin bal on declined gift card receipts, say DeActive
v810   01-09-03 TSL-02 Don't print receipt for pre-auth's
v810   12-18-02 TSL-01 Add Rcpt_WinEPSSeq_Line, use OldSeq if SeqNo=000000
v810   11-27-02 TSL-Z Truncate the Debit receipt drawer copy
v810   10-23-02 TSL-Y Linux changes, fix SeqNo on receipt if appr offline
v809.1 10-02-02 TSL-X Add DECLINED to User_1 declined receipts
v809 08-14-02 TSL-W  Add Tip, take out ebt_case_num, look for FTip
v809 08-01-02 TSL-V  Only print decl receipts for ND, NO, NR
v808 06-04-02 TSL-U  Fix receipt printing
v808 05-09-02 TSL-T  Print all receipts for Gift card (including declines)
v808 03-14-02 TSL-S  Let NM and NC return a declined receipt
v807 08-03-01 TSL-R  Print EBT declined receipt for TermRspCode NW
v807 07-25-01 TSL-Q  Do cust AND drawer receipts for EBT declined, integrated
v807 04-20-01 TSL-P  Fix %I  - should be $I
   ---------------------------Rel 806-------------------------------------------
   03-29-01  TSL    TSL-O     Print EBT declined receipt if TermRspcode= NR
   ---------------------------Prior to 806--------------------------------------
   12-11-00  TSL    TSL-N     For EBT declined receipt, add extra cash pad msg
   11-15-00  TSL    TSL-M     if TrxAmt = 0 on Gift card Activate use FS_Balance
   11-07-00  TSL    TSL-L     Change Checker Messages to Extra_EBT_Msg
   10-30-00  TSL    TSL-K     Change Void description for Lynk host
   10-26-00  TSL    TSL-J     Add printing of extra lines from host if necessary
                              Line1,2,3 prints at top of header if <> ''
                              Line4 prints after 'total' line if <> '', after
                                Other_bal_line for EBT
                              Line5,Line6,Line7 print before Blank_to_sig1 if <> ''
                                and before No_dispense_case for EBT
   10-12-00  TSL    TSL-I     User_1_ Avail balance wasn't printing
   09-28-00  TSL    TSL-H     Don't print sig block on User_1: gift card
-------------------------------service pack 09 ----------------------------------

   08-25-00  TSL    TSL-G     Make sure BalInq receipt prints OK for USER_1
   08-17-00  TSL    TSL-F     Print Balance inquiry receipt for other than EBT
                              Don't print sig line on Gift Card Activation,make
                              compatible with mtx_pos.dll, remove DSHostbuf, add Rdir
                              add USES sysutils
   03-16-99  JGS    JGS-B     Don't "x" out drawer copy of account number Foodland
   10-05-98  JGS    JGS-A     Original in reg receipt went 1->9 left 0 on 10
   09-24-98  JGS    JGS-X     Only use SwSeqNo if sent to modem set Y
   09-11-98  JGS    JGS-W     Change below to only x out if array entry set to "y"
   06-03-98  JGS    JGS-V     X out every card...
   05-21-98  JGS    JGS-U     Take off change ribbon color if not 250/900, allow for 1,2,5,7 as printer types
   05-21-98  JGS    JGS-T     20 dig acct no was truc to 19 and lost last dig
   05-20-98  JGS    JGS-S     Leave 1E on front of receipt
   05-08-98  TSL    TSL-E     Add str_voice for receipt tran type descrip
   04-13-98  TSL    TSL-D     Take out DOS for NT
   10-30-97  JGS              Build to put in host modules
   10-30-97  JGS              Rework startup code so it decides whether to build or exit quickly
   10-23-97  JGS              Fix 38 char and 2 0x00s
   06-16-97  JGS              Put in mod below
   05-20-97  TSL    TSL-C     Put Approved on EBT receipt, shorten it, put on funding
   03-19-97  JGS    JGS-S     EBT Voice would not print c/b
   11-21-96  JGS    JGS-R     Make sure ExpDt is not null in receipt
   11-14-96  JGS    JGS-Q     Move length was 8 instead of Length() in AuthCode EBT, got x'00', trunc print
   05-20-96  JGS    JGS-P     Cashier 0 was an endless loop
   04-12-96  JGS    JGS-O     Print "I AGREE" BS on bottom of credit receipt
   04-12-96  JGS    JGS-O     Make Card Number Mxxxxx if Manual (PayPoint)
   02-23-96  JGS    JGS-N     Do not print a drawer copy for Check, see end_copy at bottom of prog
   01-13-96  JGS    JGS-M     Sig lines and name for integrated with printer 150/250
   10-27-95  JGS    JGS-L     JGS-A should have been 0-9 was 1-9
   10-25-95  JGS    JGS-K     Add support for terminal types '7', '8'
   10-02-95  JGS    JGS-J     Change below to XXXXXXXXXXXX1234 on receipt
   04-03-95  JGS    JGS-I     Only put 4 digits of card number on receipt
   04-03-95  JGS    JGS-H     CrVoiceAuth of Disc w CB printed as TOTAL
   04-03-95  JGS    JGS-G     Fix receipt says checkingfrom primary
   03-27-95  JGS    JGS-F     Add 150 sup to not print sig, do print name, Blank after other_bal gone
   03-09-95  JGS    JGS-E     Remove Mikes AnyToLst Messages
   12-11-94  JGS    JGS-D     Rewrite to have both receipts and trigger on input from terminal module
   11-22-94  JGS    JGS-C     Re-Order this list so last mod is on top, since we read top down in USA
   11-22-94  JGS    JGS-C     Make sure amounts are at least 0.xx inc 0.00
   11-11-94  JGS    JGS-B     SwRspCode = '' AND MTXRspCode = ''
   11-03-94  TSL    TSL-B     Fix so zero balance returned will print
   10-28-94  TSL    TSL-A     Fix void receipt on stand alone
   10-25-94  JGS    JGS-A     Remove any numerics from the front of FCT Cardname
   10-24-94  JGS   10-24-94   Rewrite datasets each time, don't scratch on command
*********************************************************************************** }

interface

uses
  MTX_Lib,MTX_Utils,UPLUMessageFile,Classes,sysutils,strUtils,MTX_Constants,MdMsg,micrValuesClass,stringUtils,
  MTXEncryptionUtils,
  {$IFDEF MTXEPSDLL}
  SCATConstants,
  DllTypes,
  MRTypes,
  {$ENDIF}
  UXMLCommon,
  ReceiptCommon,
  Receipts.Json              // CPCLIENTS-5932
  , BarTabU                  // CPCLIENTS-10531
  ;

{$V-}

type
  TReceipt = class
    FIntOrSBReceipt: AnsiChar;
    End_Copy: Integer;
    // Host_TruncateAcctNo: array[1..10] of string[1];
    FUseThisOne: Boolean;
    FInLineNum: integer;
    FInrec: MdMsgRec;
    FIsReceiptRequired: boolean;
    Char_Pos: Integer;
    Amt_Buf: String16;
    Temp_Line: String41;
    Last_Was: AnsiChar;      { Last Receipt Size Char }
    FrDir: string;
    FHostLine1: string50;
    FHostLine2: string50;
    FHostLine3: string50;
    FHostLine4: string50;
    FHostLine5: string50;
    FHostLine6: string50;
    FHostLine7: string50;
    FLynkBatch: string;
    FLynkReceiptData: string;
    FMerchLanguage: string;
    FTruncate:  string;
    Printer_Type: AnsiChar;
    Small_or_Large: AnsiChar;
    XingVar: Boolean;
    FTip: integer;
    FReceiptWithNoSig: boolean;
    FisTimedOutTrx: Boolean;
    FPhoneCardPIN: string;
    FPhoneCardMsg: TStrings;
    FPrintWhichCopy: boolean;
    FCalcStartingBal: boolean;
    FCustReceipt: TStringList;
    FDrwrReceipt: TStringList;
    FDoingCustomerCopy: boolean;
    FwirelessPIN:       string;
    FwirelessTracking:  string;
    FwirelessAccess:    string;
  strict private
    // function IsHostOneOf(aHostList: string): boolean;   // Moved to MTX_Lib
  private
    // DOEP-42647
    // FIsEMVC33CidDeclined : Boolean;
    // FIsEMVC33GenericError: Boolean;
    FEMVC31ErrorCode : Integer;
    FIsContEMVGenericError: boolean;
    FIsContEMVCidDeclined: boolean;
    FReceiptCommon: TReceiptCommon;
    FAIDPrinted: Boolean; // CPCLIENTS-2788, 2959
    {$HINTS OFF}
    function  IsBlackhawkDecline: boolean;
    {$HINTS ON}
    procedure CheckForBlackhawkMessage(Which_Copy: integer);   // CPCLIENTS-5932
    procedure WriteRLine(aMsg: string);
    procedure readProcFile;
    procedure Format_Amount(In_Amt : LongInt; Var Out_Amt : String16);
    function  IsApprovedOffline: boolean;
    function  IsDeclinedOffline: boolean;
    function  IsECCTermRspCode: boolean;
    function  IsHost(aHostCode: string): boolean;
    function  IsBYLATLECC: boolean;  // CPCLIENTS-5020
    function  IsFLCECC: boolean;
    function  IsLYNECC: boolean;
    function  IsProgID(aProgID: string): boolean;
    function  IsXPIEnglish: boolean;
    function  IsNOVECC: boolean;
    function  IsDCC: boolean;     //11758 - is this a DCC transaction/receipt?
    procedure Init_For_Copy(Which_One: Integer);
    procedure InitHostLines;
    procedure printBlankLines(aNum: integer);
    procedure PrintCopy(aMsg: string);
    procedure PrintSignatureLine;
    procedure PrintECCSignatureLine;
    procedure PrintTDBSignatureLine;
    procedure ECCReceiptMsg(aLane: string; aReceiptType: integer);
    procedure BuildBuypassECCReceipt(Truncate: string);
    procedure BuildNovaECCReceipt;
    procedure BuildCertegyECCReceipt;
    procedure BuildTDBReceipt;
    function  BYLATLReceiptMerchTerm(aIssuerCode: string): string50; // CPCLIENTS-5179 To have meaningful name for BYLATLReceiptMerchTerm as it is implemented for ATL too
    function LynkRetrievalData: string;
    function LynkMerchantID: string;
//    function LynkDeviceID(RecN: byte) : string1; // CPCLIENTS-1508: remove DevId
    function LynkTermIDDeviceID: string;
    function LynkFNSNumberOrOdometer: string;
    function LynkVoidSeqNum: string;
    function LynkBatchandReceipt: string;
    function EngOrFrenchPurch: string;
    function EngOrFrenchCashback: string;
    function GetCid: AnsiString;
    function MakeTDTranDescrip:string;
    function  MakeServerEPSAcctNoForChecks(TmpAcctNo: string): string;
    function  MakeValuLinkAcctNoForReceipt: string;
    procedure SetEndCopy;
    procedure SetReceiptRequired;
    procedure SetSmallOrLargeReceipt;
    function  TranApproved: boolean;
    Procedure SetXingVar(Truncate: string);
    procedure PrintReturnCheckFee(aECCReceiptInfo: TECCReceiptInfo); // DEV-8179
    function publixHostDefined(HostCode: string): boolean;
    procedure WritePhoneCardLines(Which_Copy: integer);    // CPCLIENTS-5932
    function IsPrintMinimalReceipt: Boolean;
    function IsMACOrChipDecline: Boolean;
    function GetMaskedAcctNo: string;
    function IsBYL_LYN_Fallback: Boolean;
    function IsCidOfflineDecline(cid: AnsiString): boolean;
    function Is_EMV_VNT_BYL_LYN: boolean;
    procedure MakeEMVRcpt;
    procedure PrintAID(Which_Copy: integer);   // CPCLIENTS-2788, 2959 // CPCLIENTS-5932
    procedure SetJsonReceiptBlock(Which_Copy: integer; aGeneralReceiptBlock: TGeneralReceiptBlock); overload; // CPCLIENTS-5932
    procedure SetJsonReceiptBlock(aReceiptBlock: TReceiptBlock); overload;               // CPCLIENTS-5932
    procedure SetICforATL;    //8710 ATL Issuer Code to be based on Af field
    procedure CreateBAMSRRNLine(tmpSeqLineNo:Byte);     // CPCLIENTS-13141
    procedure PrintEBTChipData(Which_Copy: integer); //CPCLIENTS-19133
  public
  {$IFDEF MTXEPSDLL}
    FRspMMR: TServerEPSResponse;
  {$ENDIF}
    constructor Create(Inrec: MdMsgRec);
    destructor  Destroy; override;
    procedure AddWMsgToReceipt(wPIN, wTracking, wAccess: string; const aCompany: string = '');
    procedure Build_Receipt(Host_PONumberUsed : string1; Truncate: string);
    procedure Build_ReceiptNoSig(Host_PONumberUsed : string1; Truncate: string);
    procedure Build_ReceiptH(Host_PONumberUsed: string1;
                             ExtraLine1: string50;
                             ExtraLine2: string50;
                             ExtraLine3: string50;
                             ExtraLine4: string50;
                             ExtraLine5: string50;
                             ExtraLine6: string50;
                             ExtraLine7: string50;
                             Truncate: string;
                             const aStore: string = '');
    procedure Build_ReceiptForHost(const aStore: string = ''; const aMerchLanguage: string = '');
    //procedure ClearCustReceipt; // XE: Remove WinEPS - not in use
    procedure SetINCPhoneCardVars(aPIN, aMsg: string);
    procedure SetLynkVars(aBatch: string; aReceiptData: string);
    //procedure ClearDrwrReceipt; // XE: Remove WinEPS - not in use
    function fnDOB(aDOB: string): string;
    function fnPosTracking(aTracking: string): string;
    procedure SetFInRec(Inrec: MdMsgRec);
    function PrintTipLineOnReceipt: Boolean; // CPCLIENTS-1942
    procedure PrintCopyLine(Which_Copy: integer); // CPCLIENTS-5933
    property rDir: string read FrDir write FrDir;
    property isReceiptRequired: boolean read FIsReceiptRequired;
    property isTimedOutTrx: boolean read FisTimedOutTrx write FisTimedOutTrx;
    property PrintWhichCopy: boolean read FPrintWhichCopy write FPrintWhichCopy;
    property CustReceipt: TStringList read FCustReceipt;
    property DrwrReceipt: TStringList read FDrwrReceipt;
    property wirelessPIN: string read FwirelessPIN write FwirelessPIN;
    property wirelessTracking: string read FwirelessTracking write FwirelessTracking;
    property wirelessAccess: string read FwirelessAccess write FwirelessAccess;
    property CalcStartingBal: boolean read FCalcStartingBal write FCalcStartingBal;
    // property IsEMVC33CidDeclined: boolean read FIsEMVC33CidDeclined write FIsEMVC33CidDeclined;
    // property IsEMVC33GenericError: Boolean read FIsEMVC33GenericError write FIsEMVC33GenericError;
    property EMVC31ErrorCode : Integer read FEMVC31ErrorCode write FEMVC31ErrorCode;
    property IsContEMVGenericError: boolean read FIsContEMVGenericError write FIsContEMVGenericError;
    property IsContEMVCidDeclined: boolean read FIsContEMVCidDeclined write FIsContEMVCidDeclined;
  end;

const
  MAXARRAYSIZE = 10;
  MAXRECEIPTLINES = 999;
  FRENCH_DECIMAL_SEPARATOR = ',';
  DEFAULT_DECIMAL_SEPARATOR = '.';
type
  TStrArray10 = array[1..MAXARRAYSIZE] of string[80];  // fixed structure, easier to handle

function LineToStrArray(S: string; var FreeFormLines: TStrArray10): integer;  //exposed for unit testing
function ArrayToStr(Arr: array of char): string;

implementation

uses
{$IFDEF MTXEPSDLL}
  ServerEPS,
{$ENDIF}
  FinalizationLog,
  MTX_XMLClasses, hostname,
  EMVManager,                // TFS-15026
  HostFunctions,
  MTX.Receipt.Utils,
  MTX_EPS_IProcs;            // TFS-99999

Const
  {$I LANERCPT.INC}

var
  EBT_Map_Lines,
  Rcpt_Map_Lines,
  Rcpt_Line_Len,
  Max_Name_Len   : Byte;

  Defs           : Rcpt_Layout;
  EBT_Defs       : EBT_Rcpt_Layout;

  Receipt_A      : Array[1..S_Rcpt_Map_Lines] of String[L_Rcpt_Line_Len];
  EBT_Rcpt       : Array[1..S_EBT_Map_Lines]  of String[L_Rcpt_Line_Len];

  Str_Declined0,              { Change_Ribbon_Color + ' ---  DECLINED  ---'  }
  Str_Declined1,              { Change_Ribbon_Color + ' *******************' }
  Str_Declined2,              { Change_Ribbon_Color + ' *  DO NOT USE AS  *' }
  Str_Declined3,              { Change_Ribbon_Color + ' *PROOF OF PURCHASE*' }
  Str_Declined4  : String[Len_Str_Declined4];{ Change_Ribbon_Color + ' *******************' }

  Str_Training   : String[Len_Str_Training];{ 'Training Receipt -- Not Valid Purchase'                }

const
  SACr_Receipt      = '0';
  SA_Receipt        = '3';
  Int_Receipt       : set of char = ['5','9'];

  Asterisk: string2 = '**';        // DOEP-44922 not related but done at same time - save some memory, was string255
  Cust_Copy         =  1 ;
  Drawer_Copy       =  2 ;
  MAXLINELEN = 40; // 38;  // TFS-21105
  TDBLINELEN = 32;

  ChrOffset = 32;
  SIG_CAPTURED_ELECTRONIC = '   Signature Captured Electronically';
  SIG_NOT_REQUIRED        = 'No Signature Required';
  SIGNATURE               = 'Signature';
  PIN_VERIFIED            = '   PIN VERIFIED';
  c31PANMISMATCH          = 28; // DOEP-44837
  c31EXPMISMATCH          = 29;
  SIG_NOT_REQUIRED_FRENCH = 'Signature non requise';
  SIG_CAPTURED_ELECTRONIC_FRENCH = '   Signature électronique';
  ATLRspCode_ChkElectronic = '99';     //8710 for Electronic check
  ATLRspCode_ChkPaper      = '95';     //8710 for Paper check
  Paper_Chk                = 'PAPER CHECK';  //8710 introduced const for paper check

  // DOEP-44922  and DEV-44924 - Lynk Host take out imprint message for manual transactions

function TReceipt.LynkRetrievalData: string;
begin
  if (trim(FInrec.LynkRetrievalData) <> '')
    then result := ' RefData ' + Copy(FInrec.LynkRetrievalData, 1, 15)
    else result := '';
end;

function TReceipt.LynkMerchantID: string;
begin
  result := ' Mrch ' + rpad(Copy(HostBuf.MerchantNum, 1, 15), 15);
end;

//Function TReceipt.LynkDeviceID(RecN: byte) : string1;
//begin
//  If (RecN + ChrOffset > 125) then { then it's non printable lane num's 95-99 aren't allowed }
//    result := ''
//  else
//  if (RecN + ChrOffset >= 44)
//    then result := chr(RecN + ChrOffset + 1)   { skip the comma - Lynk can not handle it }
//    else result := chr(RecN + ChrOffset); { rec number + 32 = 1 printable char }
//end; {MakeDeviceID}

function TReceipt.LynkTermIDDeviceID: string;
begin
  result := ' Term ' + rpad(copy(HostBuf.StoreNumber, 1, 6), 6); // + ' DevID' + LynkDeviceID(FInrec.RecNoN); // CPCLIENTS-1508: remove DevID
end;

function TReceipt.LynkFNSNumberOrOdometer: string;
begin
  if (FInrec.ReqCodeN in Fleet_Set) then
  begin
    if (FInrec.fleetVehicleID <> '')
      then result := ' Vehicle#: ' + FInrec.fleetVehicleID + '  Odometer: ' + FInrec.fleetOdometer
      else result := ' Odometer: ' + FInrec.fleetOdometer;
  end
  else
  begin
    if (trim(FInRec.EBT_FNS_Number) <> '') and (FInRec.ReqCodeN in EBT_FS_Set)
      then result := ' EBTFNS# ' + FInRec.EBT_FNS_Number
      else result := '';

    if (trim(FInrec.EBT_Voucher_Num) <> '') then
      result := result + ' Vch# ' + FInrec.EBT_Voucher_Num;
  end;
end;

function TReceipt.LynkVoidSeqNum: string;
begin
  if (FinRec.VoidCode = 'V')
    then result := ' Original WinEPS Sequence # ' + FinRec.OldSeq
    else result := '';
end;   { VoidSeqNum }

function TReceipt.LynkBatchandReceipt: string;
begin
  if (FLynkBatch <> '') or (FLynkReceiptData <> '')
    then result := ' Batch# ' + FLynkBatch + ' ' + FLynkReceiptData
    else result := '';
end;

function TReceipt.IsBlackhawkDecline: boolean;
begin
  result := ABlackhawkOrIDTGiftActivation(FInRec) and (copy(FInRec.TermRspCode, 1, 1) <> 'A');
end;

procedure TReceipt.CheckForBlackhawkMessage(Which_Copy: integer);      // CPCLIENTS-5932
var aText: string;
    DoMsg: boolean;
begin
  DoMsg := ((FInrec.TermRspCode = rcApprovedByWinEPS) and ABlackhawkOrIDTGiftActivation(FInRec)) or
     ((IsTGTorSBUX(FInRec.ProgramID,FInRec.ReqCodeN) or IsINCProgIDforSVD(FInRec.ProgramID, FInRec.HostSuffixCode)) and
      (FInRec.ReqCodeN = User_1_ActivationN) and (FInRec.MTXRspCodeN = TrxAppOffN));
  if DoMsg then
  begin
    SetJsonReceiptBlock(Which_Copy, grbAgreements);              // CPCLIENTS-5932
    {
    If FDoingCustomerCopy then
    begin
      case FInRec.languageID of
        1: aText := MTX_XMLClasses.DSProcBuf.DSProcOfflineBlackhawkReceiptVerbiage;
        2: aText := MTX_XMLClasses.DSProcBuf.DSProcOfflineBlackhawkReceiptVerbiageLang2;
        3: aText := MTX_XMLClasses.DSProcBuf.DSProcOfflineBlackhawkReceiptVerbiageLang3;
        else
          aText := MTX_XMLClasses.DSProcBuf.DSProcOfflineBlackhawkReceiptVerbiage;
      end;
    end
    else
    }
      aText := MTX_XMLClasses.DSProcBuf.DSProcOfflineBlackhawkReceiptVerbiage;
    WriteRLine(CenterText(aText,MAXLINELEN));
  end;
end;

procedure TReceipt.WriteRLine(aMsg: string);
begin
  if (length(aMsg) > 1) and (ord(aMsg[1]) < 32) then
    aMsg[1] := ' ';
  If FDoingCustomerCopy then
  begin
    if (CustReceipt.Count <= MAXRECEIPTLINES)
      then CustReceipt.Add(aMsg)
      else sm(format('****WARNING - Customer Receipt:  At max Lines of %d, could not add this line >%s<',
        [MAXRECEIPTLINES, aMsg]));
  end
  else
  begin
    if (DrwrReceipt.Count <= MAXRECEIPTLINES)
      then DrwrReceipt.Add(aMsg)
      else sm(format('****WARNING - Merchant Receipt:  At max Lines of %d, could not add this line >%s<',
        [MAXRECEIPTLINES, aMsg]));
  end;
  JsonReceipt.AddLineToBlock(aMsg); // CPCLIENTS-5932
end;

procedure TReceipt.readProcFile;
begin
  with TXMLProcess.Create do
    try
      GetRecord(MTX_XMLClasses.DSProcBuf);
    finally
      Free;
    end;
end;    { readProcFile }

procedure TReceipt.Format_Amount(In_Amt : LongInt; Var Out_Amt : String16);
Var Temp_Dollar : String[8];
begin
  Temp_Dollar := Str_(In_Amt);
  if (Length(Temp_Dollar) < 3) then  { JGS-C }
    Temp_Dollar := ZFill(Temp_Dollar, 3);
  Out_Amt := Copy(Temp_Dollar, 1, Length(Temp_Dollar)-2)   { Create 0000000.00  }
                + '.'
                + Copy(Temp_Dollar, Length(Temp_Dollar)-1, 2);

  While (Out_Amt[1] = '0')    and
        (Length(Out_Amt) > 4) Do       { Lop of leading 0's JGS-C }
    Delete(Out_Amt, 1, 1);
  While (Length(Out_Amt) < 8) Do
    Out_Amt := ' ' + Out_Amt;

end;

function  TReceipt.IsApprovedOffline: boolean;
begin
  result := (FInRec.MTXRspCodeN in TrxAppOffSet) or
    (SameText(FInRec.TermRspCode, rcApprovedByWinEPS)); // DOEP-43472
end;

function TReceipt.IsDeclinedOffline: boolean;
begin
  result := ((FInRec.MTXRspCodeN > 200) and (not (FInRec.MTXRspCodeN in TrxAppOffSet + [TrxAppSigCapN]))) //DOEP-44519
    or ((FInRec.MTXRspCodeN in [TrxDecGenN, TrxDecExpDateN, TrxDecCustomerCancelN,
    TrxDecTrxNotAlwdN, TrxDecVoidNotAllowedN]) and (FInrec.TDBHostResponse = '')); // DOEP-43016, 44135
  if Result then                                                                   // TFS-20397
  begin                                                                            // TFS-20397
    if (FInRec.TermRspCode = 'AB') and ((EMVMgr.Gen2EMV9F27Tag = '00') or EMVMgr.WeSentY3) then   // TFS-26299
      Result := False;                                                           // TFS-26299
  end;                                                                             // TFS-20397
  MsgDebug('IsDeclinedOffline FInRec.MTXRspCodeN=' + FInRec.MTXRspCodeN.ToString + ' FInrec.TDBHostResponse=' + FInrec.TDBHostResponse);
end;

function TReceipt.IsECCTermRspCode: boolean;
begin
  result := (FInrec.TermRspCode = rcApprovedByECC) or (FInrec.TermRspCode = rcDeclineND);
end;

function TReceipt.IsHost(aHostCode: string): boolean;
begin
  result := SameText(FInrec.HostSuffixCode, aHostCode);
end;

function TReceipt.IsBYLATLECC: boolean;  // CPCLIENTS-5020 Changed method name so to be more descriptive it should generate ECC receipt for ATL too
begin
  //8710 For ATL Af field value of '99' in SE_RECV to be considered instead of HostBuf.ChkCode
  result := (FInrec.ReqCodeN in check_Set) and IsECCTermRspCode and
            ((IsHost(ATL) and (FInrec.SwRspCode = ATLRspCode_ChkElectronic )) or
            ((IsHostOneOf(FInrec.HostSuffixCode,'BYL/JET') and (HostBuf.ChkCode = 'TE'))));    // CPCLIENTS-5020 For ConcordEPC receipts
end;

function TReceipt.IsFLCECC: boolean;
begin
  result := IsHost('FLC') and (FInrec.ReqCodeN in check_Set) and IsECCTermRspCode;
end;

function TReceipt.IsLYNECC: boolean;
begin
  result := IsHost('LYN') and (FInrec.ReqCodeN in check_Set) and
    (HostBuf.ChkAuthType = CHECK_AUTH_TYPE_ECC) and IsECCTermRspCode;
end;

function TReceipt.IsNOVECC: boolean;
begin
  result := IsHost('NOV') and (FInrec.ReqCodeN in check_Set) {and (FInrec.Check_Type <> ctOther)};
end;

function  TReceipt.IsDCC: boolean;     //11758 - is this a DCC transaction/receipt?
begin
  result := FInrec.isDCCTrans and (FInrec.DCC_ExchangeRate > 0);   //and IsHost('RPD') ...     // TFS-22487
  MsgLog(format('TReceipt.IsDCC[%s] for Host[%s] ',[sTF[result],FInrec.HostSuffixCode]));
end;

procedure TReceipt.Init_For_Copy(Which_One: Integer);
begin
  FDoingCustomerCopy := (which_One = cust_Copy);
  FInLineNum := 0;                               // Starting line-1 for mapping
end;

procedure TReceipt.InitHostLines;
begin
  FHostLine1 := '';
  FHostLine2 := '';
  FHostLine3 := '';
  FHostLine4 := '';
  FHostLine5 := '';
  FHostLine6 := '';
  FHostLine7 := '';
end;

procedure TReceipt.printBlankLines(aNum: integer);
var i: integer;
begin
  for i := 1 to aNum do   { put in blank lines }
    WriteRLine(' ');
end;    { printBlankLines }

procedure TReceipt.PrintCopy(aMsg: string);
begin   // center this line
  aMsg := StringOfChar(' ', (MAXLINELEN - length(aMsg)) div 2) + aMsg;
  WriteRLine(' ');
  WriteRLine(aMsg);
  WriteRLine(' ');
end;

procedure TReceipt.PrintSignatureLine;
begin
  printBlankLines(3);
  if FInRec.IsSigElectronic then
    WriteRLine(SIG_CAPTURED_ELECTRONIC)
  else
  begin
    WriteRLine(' X__________________________');
    WriteRLine('     Customer Signature');
  end;
end;    { printSignatureLine }

procedure TReceipt.PrintECCSignatureLine;
begin
  printBlankLines(3);
  if FInRec.IsSigElectronic then
    WriteRLine(SIG_CAPTURED_ELECTRONIC)
  else
  begin
    WriteRLine(' Signature: __________________________');
    WriteRline(' ');
  end;
end;

{ printSignatureLine }

procedure TReceipt.PrintTDBSignatureLine;
begin
  printBlankLines(3);
  // DOEP-42998 - Need to print electronic signature when PIN verified
  if FInRec.IsSigElectronic and FInRec.TDBPINVerified then
  begin
    if IsXPIEnglish then    // English
      WriteRLine(SIG_CAPTURED_ELECTRONIC)
    else                    // French
      WriteRLine(SIG_CAPTURED_ELECTRONIC_FRENCH); // DOEP-53096, 54551
  end
  else if FInRec.TDBSigNotRequired then
  begin
    if (not FInRec.TDBPINVerified) then
      if IsXPIEnglish then    // English
        WriteRLine(SIG_NOT_REQUIRED)
      else                   // French
        WriteRLine(SIG_NOT_REQUIRED_FRENCH); // DOEP-53096, 54551
  end
  else
  begin
    WriteRLine('X_______________________________');
    WriteRLine(CenterText('Signature',TDBLINELEN));
    WriteRLine(' ');
    if IsXPIEnglish then    // English
    begin
      WriteRLine(CenterText('I agree to pay the above total',TDBLINELEN));
      WriteRLine(CenterText('amount according to the card',TDBLINELEN));
      WriteRLine(CenterText('issuer agreement.',TDBLINELEN));
    end
    else                               // French
    begin
      WriteRLine(CenterText('Je consens à payer le total',TDBLINELEN));  // DOEP-53005, 54556
      WriteRLine(CenterText('précité selon la convention de',TDBLINELEN));   // DOEP-53005, 54556
      WriteRLine(CenterText('l''émetteur.',TDBLINELEN)); // DOEP-53005, 54556
    end;
  end;
end;    { printSignatureLine }

procedure TReceipt.PrintReturnCheckFee(aECCReceiptInfo: TECCReceiptInfo); // DEV-8179
const
  MAX_MSG = 'Max Amt by Law';              //JTG 32149
  RETURN_FEE_AMOUNT_MSG = 'Return Fee Amount';   //32149 - make a constant out of a multiply-used phrase (duplicated in DLLTypes)
var
  ReturnCheckFee: Currency;
begin
  if Assigned(aECCReceiptInfo) then
  begin
    // DOEP-56119, if there is a return check fee replacement flag in the agreement, then don't bother printing return check fee on its own line
    if (not IsHost('FLC')) or (Pos('~F', aECCReceiptInfo.AcceptanceAgreement.Text) = 0) then
    begin
      sm(format('PrintReturnCheckFee FInrec.ReturnCheckFee=[%s], ECCReceiptInfo.ReturnCheckFee=[%d]',[FInrec.ReturnCheckFee,aECCReceiptInfo.ReturnCheckFee]));
      printBlankLines(1);
      if IsHost('NOV') and (trim(FInrec.NOVServiceFeeData) <> '') then     // i.e. if Elavon host
        WriteRLine(' NSF ServiceFee = ' + trim(FInrec.NOVServiceFeeData))
      else
      begin
        // DOEP-56119, If FLC host and ECC receipt info text has a ~F, then don't bother writing this line?
        ReturnCheckFee := StrToCurrDef(FInrec.ReturnCheckFee, 0);
        if ReturnCheckFee = 0 then
          ReturnCheckFee := (aECCReceiptInfo.ReturnCheckFee / 100);

        if ReturnCheckFee = 0 then
        begin
          WriteRLine(' '+RETURN_FEE_AMOUNT_MSG+': '+MAX_MSG);
          //WriteRLine('     Maximum fee allowed by state law.');
        end
        else
          WriteRLine(' '+RETURN_FEE_AMOUNT_MSG+'  $  ' + Format('%8.2f', [ReturnCheckFee]));
      end;
      printBlankLines(1);
    end else
      SM('PrintReturnCheckFee, skipped since ECC agreement text already contains the return check fee');
  end;
end;

function TReceipt.publixHostDefined(HostCode: string): boolean;
begin
  if (HostCode = 'PBL') or       { is it a Publix host? }
     (HostCode = 'PB2') or
     (HostCode = 'PB3')
    then result := true
    else result := false;
end;   { publixHostDefined }

procedure TReceipt.WritePhoneCardLines(Which_Copy: integer);    // CPCLIENTS-5932
var i: integer;
begin
  if FDoingCustomerCopy and ((FInrec.ReqCodeN in PhoneCard_Set) or IsHost(INCHOST)) then  // CPCLIENTS-12067
  begin
    SetJsonReceiptBlock(Which_Copy, grbAgreements);              // CPCLIENTS-5932
    if (FPhoneCardPIN <> '') then
      WriteRLine('PIN: ' + FPhoneCardPIN);
    if FPhoneCardMsg.Count > 0 then                              // CPCLIENTS-12067
    begin                                                        // CPCLIENTS-12067
      WriteRLine(' ');                                           // CPCLIENTS-12067
      for i := 0 to FPhoneCardMsg.Count - 1 do
        WriteRLine(FPhoneCardMsg[i]);
    end;                                                         // CPCLIENTS-12067
  end;
end;

procedure TReceipt.SetJsonReceiptBlock(Which_Copy: integer; aGeneralReceiptBlock: TGeneralReceiptBlock);   // CPCLIENTS-5932
begin
  if Which_Copy = Cust_Copy then
  begin
    case aGeneralReceiptBlock of
      grbStoreData:      JsonReceipt.SetCurrentBlock(rbCustStoreData);
      grbTransType:      JsonReceipt.SetCurrentBlock(rbCustTransType);
      grbCardInfo:       JsonReceipt.SetCurrentBlock(rbCustCardInfo);
      grbEMVData:        JsonReceipt.SetCurrentBlock(rbCustEMVData);
      grbTipInfo:        JsonReceipt.SetCurrentBlock(rbCustTipInfo);
      grbCashbackAmount: JsonReceipt.SetCurrentBlock(rbCustCashbackAmount);
      grbSubtotalAmount: JsonReceipt.SetCurrentBlock(rbCustSubtotalAmount);
      grbTotalAmount:    JsonReceipt.SetCurrentBlock(rbCustTotalAmount);
      grbSigLines:       JsonReceipt.SetCurrentBlock(rbCustSigLines);
      grbAgreements:     JsonReceipt.SetCurrentBlock(rbCustAgreements);
      grbCopy:           JsonReceipt.SetCurrentBlock(rbCustCopy);
    end;
  end
  else
  begin
    case aGeneralReceiptBlock of
      grbStoreData:      JsonReceipt.SetCurrentBlock(rbMerchStoreData);
      grbTransType:      JsonReceipt.SetCurrentBlock(rbMerchTransType);
      grbCardInfo:       JsonReceipt.SetCurrentBlock(rbMerchCardInfo);
      grbEMVData:        JsonReceipt.SetCurrentBlock(rbMerchEMVData);
      grbTipInfo:        JsonReceipt.SetCurrentBlock(rbMerchTipInfo);
      grbCashbackAmount: JsonReceipt.SetCurrentBlock(rbMerchCashbackAmount);
      grbSubtotalAmount: JsonReceipt.SetCurrentBlock(rbMerchSubtotalAmount);
      grbTotalAmount:    JsonReceipt.SetCurrentBlock(rbMerchTotalAmount);
      grbSigLines:       JsonReceipt.SetCurrentBlock(rbMerchSigLines);
      grbAgreements:     JsonReceipt.SetCurrentBlock(rbMerchAgreements);
      grbCopy:           JsonReceipt.SetCurrentBlock(rbMerchCopy);
    end;
  end;
end;

procedure TReceipt.SetJsonReceiptBlock(aReceiptBlock: TReceiptBlock); // CPCLIENTS-5932
begin
  JsonReceipt.SetCurrentBlock(aReceiptBlock);
end;

procedure TReceipt.CreateBAMSRRNLine(tmpSeqLineNo: Byte); // CPCLIENTS-13141
var
  Which_Copy: Integer;
begin
  if (FInLineNum = tmpSeqLineNo + 1) and (FInrec.HostSuffixCode = CBS) and (trim(FInrec.RetrievalRefNo) <> '') then // CPCLIENTS-13624 - To not to print RRN for Void transactions
  begin
    SetJsonReceiptBlock(Which_Copy, grbCardInfo);
    WriteRLine(' RRN  #  ' + trim(FInrec.RetrievalRefNo));
  end;
end;

function TReceipt.IsPrintMinimalReceipt: Boolean;
begin
  // DOEP-50952 - OpenEPS - Displaying min receipt for gift cards (user_1_set) and phone cards (user_2_set)
  Result := False;
  if (FInrec.ReqCodeN in User_1_Set + User_2_Set) then
    Result := FInRec.PrintMinimalReceipt = 'Y';
  msgNotice('IsPrintMinimalReceipt = %s',[BoolStr(Result)]);
end;

function TReceipt.IsMACOrChipDecline: Boolean;
begin
  Result := (FInRec.MTXRspCodeN = TrxDecCustomerCancelN) or (FInRec.MTXRspCodeN = TrxDecTrxNotAlwdN);
end;

procedure TReceipt.ECCReceiptMsg(aLane: string; aReceiptType: integer);
var ECCReceiptInfo: TECCReceiptInfo; // DEV-8179
begin
  printBlankLines(1);
  if (IsHost('NOV') or IsHost('DEM') or IsHost('LML') or IsHost('TRN')) and
     (FInrec.ReqCodeN in check_Set) and (FInrec.TermRspCode = rcApprovedByECC) then
  begin
    WriteRLine('~E');
    ECCReceiptInfo := TECCReceiptInfo.Create(FInrec.HostSuffixCode); // DEV-8179 <
    try
      PrintReturnCheckFee(ECCReceiptInfo);
    finally
      FreeAndNil(ECCReceiptInfo);
    end; // DEV-8179 >
  end
  else
  begin
    WriteRLine(' I authorize the merchant to convert ');
    WriteRLine(' my check to an Electronic Funds     ');
    WriteRLine(' Transfer or paper draft, and to     ');
    WriteRLine(' debit my account for the amount of  ');
    WriteRLine(' the transaction.  In the event that ');
    WriteRLine(' my draft or EFT is returned unpaid, ');
    WriteRLine(' I agree that a fee of $25.00 or the ');
    WriteRLine(' maximum allowed by law may be       ');
    WriteRLine(' charged to my account via draft     ');
    WriteRLine(' of EFT.');
  end;
end;   { ECCReceiptMsg }

constructor TReceipt.Create(Inrec: MdMsgRec);
var                                            // CPCLIENTS-14975
  LaneIsHospitality: boolean;                  // CPCLIENTS-14975
begin
  //inherited;
  FCustReceipt := TStringList.Create;
  FDrwrReceipt := TStringList.Create;
  FPhoneCardPIN := '';
  FPhoneCardMsg := TStringList.Create;

  FReceiptWithNoSig := false;       // TFS-121544 / 122439  / 124994
  FPrintWhichCopy := false;
  FDoingCustomerCopy := false;
  if FileExists('REGISTRY.MTX')           // this is for the engine
    then FCalcStartingBal := SameText(Reg_Lookup('REGISTRY.MTX','OLDREC', False),'Y')
    else FCalcStartingBal := false;    { this is the new way, now default for engine, but OpenEPS sets it from config }
  Last_Was := 'N';                     { Init to something useless }
  {$IFDEF MTXEPSDLL}
  rDir := DefaultDir;
  {$ELSE MTXEPSDLL}
  rDir := WinEPSDir;              { Init to default dir for WinEPS }
  {$ENDIF MTXEPSDLL}
  {$IFNDEF GUI}
  if (MTX_XMLClasses.DSProcBuf.DSProcAuto <> 'Y') and (MTX_XMLClasses.DSProcBuf.DSProcAuto <> 'N') then
    readProcFile;
  {$ENDIF GUI}
  FInrec := Inrec;
  FInrec.CashbackN := mtx_utils.FixCashbackN(FInrec.CashBackN);
  FIntOrSBReceipt := FInrec.Build_Rcpt_Data[1];
  Small_or_Large := FInrec.Build_Rcpt_Data[2];
  Printer_Type   := FInrec.Build_Rcpt_Data[3];
  FTip := strToIntDef(FInrec.tip, 0);        { TSL-W }
  if (FIntOrSBReceipt in ['5','7','8']) then  { JGS-K }
    FIntOrSBReceipt := '5';

  if (FIntOrSBReceipt = SACr_Receipt) then
    FIntOrSBReceipt := SA_Receipt;
  InitHostLines;
  SetSmallOrLargeReceipt;
  SetEndCopy;
  SetReceiptRequired;
  FIsContEMVCidDeclined {FIsEMVC33CidDeclined} := False; // DOEP-42647
  FIsContEMVGenericError {FIsEMVC33GenericError} := False; //DOEP-42852
  FEMVC31ErrorCode := 0; // DOEP-44387
  if EMVMgr.cid.Length > 0 then
    sm('cid in TReceiptClass.Create value=' + IntToStr(Ord(EMVMgr.cid[1])));

  // TFS-124994
  // From the original TFS-122427: Bug: HOSP: WorldPay Host Adjustment receipt includes signature line EMV or MSR & Online or Offline
  // We have to check that:
  //   - it is Hospitality
  //   - it is WorldPay
  //   - it is trtPreAuthCompletion or trtAdjustment, or, using the information in InRec: CrPreAuthCompN or CrAdjustmentN
  LaneIsHospitality := GetLaneType(SignOnSet.LaneNumber) = DSLaneHospitality;            // CPCLIENTS-14975
  if (IsHost('LYN') and LaneIsHospitality and ((InRec.ReqCodeN = CrPreAuthCompN) or (InRec.ReqCodeN = CrAdjustmentN)))   // CPCLIENTS-14975
      OR     ((LaneIsHospitality and signOnSet.ReceiptHospitalityIncludeTip) and (FInrec.ReqCodeN in All_Return_Trxs) // CPCLIENTS-14975
         and (POS(Chr_SigCapture, CmdSequence) = 0)) then  // TFS-121544 / 122430 124240 // CPSupport-1517 Don't Print Sig line for Hosp Return receipt unless O Tac is present
    begin
      FReceiptWithNoSig := true;    // TFS-121544 122430 124994
    end;
end;

destructor TReceipt.Destroy;
begin
  inherited;
  FreeAndNil(FCustReceipt);
  FreeAndNil(FDrwrReceipt);
  FreeAndNil(FPhoneCardMsg);
  if Assigned(FReceiptCommon) then
    FreeAndNil(FReceiptCommon);
end;

function TReceipt.fnPosTracking(aTracking: string): string;
begin
  if (trim(aTracking) <> '')
    then result := ' Freedom ID#: ' + aTracking
    else result := '';
end;

function TReceipt.fnDOB(aDOB: string): string;
begin
  if (trim(aDOB) <> '') then
    begin
    if (strToIntDef(aDOB, 0) <> 0) then  { we have a date }
      result := ' Birth Date ' + copy(aDOB,1,2) + '/' + copy(aDOB,3,2) + '/' +
                                    copy(aDOB,5,2)
    else
    if sameText(ADOB, 'Accept') then     { the date was just accepted }
      result := ' Birth Date Accepted'
    else
      result := ' Birth Date ' + aDOB;   { we don't know what we have }
    end
  else
    result := '';
end;

procedure TReceipt.AddWMsgToReceipt(wPIN, wTracking, wAccess: string;
          const aCompany: string = '');
var
  aMessage : TStrings;
  i        : integer;
  tData    : string;
begin
  with FInrec do
  try
    aMessage := TStringList.Create;
    try
      tData := MTXEncryptionUtils.RetrieveMdMsgTrack2(FInrec);
      i := pos('=', tData);
      if (i = 0) then
        i := pos(' ', tData);
      if (i > 0) then
        tData := copy(tData,1,i-1);

      if UPLUMessageFile.GetPLUMessage(trim(tData), aMessage) then
      begin
        WriteRLine(' ');
        WriteRLine(' Tracking# ' + wTracking);
        WriteRLine(' Access# ' + wAccess);
        WriteRLine(' PIN ' + wPIN);

        for i := 1 to aMessage.Count do
          WriteRLine(aMessage[i - 1]);
      end
      else
        sm('****WARNING: Did not find PLU >' + trim(tData) + '< in ' + SPLUMessageFileName + ' file');
    finally
      FreeAndNil(aMessage);
      tData := stringOfChar(' ', 22);
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: AddWMsgToReceipt ' + e.message);
  end;
end;    { AddWMsgToReceipt }

Procedure TReceipt.SetXingVar(Truncate: string);
Var
  Pos1 : Byte;
begin
  FTruncate := Truncate;
  Pos1 := mtx_lib.tenderIndex(FInrec);      { TSL-09 }
  if (Pos1 = maxTerms) then
  begin
    SM('***WARNING! TRX TYPE FAILURE IN SETXINGVAR/RECEIPT.PAS, ASSUMING TRUE');
    XingVar := true;
  end
  else
    XingVar := GetArrayText(FTruncate,Pos1,1) = 'Y';
end;    { SetXingVar }

procedure TReceipt.BuildBuypassECCReceipt(Truncate: string);
var
  I, Which_Copy: Integer;
  isVoid: Boolean;
  tmpStr, tmpLine, ReturnCheckFeeNote: string;
  foundSpc: Boolean;
  ECCReceiptInfo: TECCReceiptInfo;
const
  Rcpt_Acct_Pos = 20;
begin
  {            1         2         3         Actual  }
  {   ********90********90********90******** Position}
  {           1         2         3          RcptBuf }
  {   234567890********90********90********9 Position}
  {   ' Trace ID       ********90********9012'       }
  ECCReceiptInfo := TECCReceiptInfo.Create(FInrec.HostSuffixCode);
  try
    try
      SetXingVar(Truncate);
      isVoid := (FInrec.VoidCode = 'V');
      for which_Copy := cust_Copy to end_Copy do
      begin
        Init_For_Copy(which_Copy);
        if FInrec.Trx_Is_Training then
          WriteRLine(' ' + Str_Training);
         // TFS-25878 - rolled forward certegy payroll check receipt format
        if (FInrec.TermRspCode[1] = 'N') and (SameText(HostBuf.CertegyPayroll, 'Y')) and (FInRec.Check_Type = ctPayroll) then
        begin
          // DOEP-70277, any Certgy check decline other than specific cases above will create the following receipt
          WriteRLine(' ');
          WriteRLine(Format(' %s/%s/%s %s:%s ',
                            [Copy(FInrec.TrxDate,3,2), Copy(FInrec.TrxDate,5,2), Copy(FInrec.TrxDate,1,2),
                             Copy(FInrec.TrxTime,1,2), Copy(FInrec.TrxTime,3,2)]));
          WriteRLine(' ');
          WriteRLine(' CHECK AMOUNT:    $' + Trim(Format('%9.2f', [FInrec.TrxAmtN/100])));
          WriteRLine(' BANK:            ' + Copy(FInrec.Check, 2, 9));
          WriteRLine(' Acct:            ' + TruncMdMsgAcctNoEx(FInrec));
          WriteRLine(' CHK #:           ' + FInrec.Check_Num);
          WriteRLine(' CEY ID  ' + FInrec.TraceID);
          WriteRLine(' ');
          WriteRLine(' We are sorry that we could not cash the');
          WriteRLine(' check you presented.');
          WriteRLine(' We value your business and regret the');
          WriteRLine(' inconvenience.');
          WriteRLine(' Our decision was based in part on');
          WriteRLine(' information provided');
          WriteRLine(' by Certegy Check Services, Inc.');
          WriteRLine(' ("Certegy").');
          WriteRLine(' ');
          WriteRLine(' Certegy provides authentication and');
          WriteRLine(' risk management services');
          WriteRLine(' to merchants and businesses');
          WriteRLine(' nationwide.  You have the right');
          WriteRLine(' (i) to a free copy of your consumer');
          WriteRLine(' report if you request it from Certegy');
          WriteRLine(' no later than sixty (60) days after you');
          WriteRLine(' received this notice, and (ii) to');
          WriteRLine(' dispute any inaccurate or incomplete');
          WriteRLine(' information in that consumer report');
          WriteRLine(' with Certegy.');
          WriteRLine(' ');
          WriteRLine(' For more information, call Certegy toll');
          WriteRLine(' free at **************');
          WriteRLine(' Or write to Certegy Check Services,');
          WriteRLine(' Post Office Box 30046, Tampa, Florida');
          WriteRLine(' 33630-3046.');
          WriteRLine(' ');
          WriteRLine(' Inquiries can be made online at:');
          WriteRLine(' https://www.askcertegy.com');
          WriteRLine(' ');
          WriteRLine(' For employee training and evaluation');
          WriteRLine(' purposes,');
          WriteRLine(' your telephone call may be monitored or');
          WriteRLine(' recorded.');

          Exit; // DOEP-70277, any further receipt text below is NOT for FLC/FL2 and Certegy Payroll, so get out of here
        end else
        if (FInrec.TermRspCode = rcDeclineND) then
        begin
          WriteRLine(' Denial Record # ' + FInrec.DenialRecordNum);
          WriteRline(' ');
          WriteRLine(' Check Amount   $' + BLFill(Format('%m',[FInrec.trxAmtN/100]),15));
          WriteRline(' ');
        end
        else
        begin
          WriteRLine('           ELECTRONIC ' + iif(isVoid, 'VOID', 'CHECK'));
          WriteRline(' ');
          if not isVoid then
          begin
            WriteRLine(' Purchase       $' + BLFill(Format('%8.2f',[(FInrec.TrxAmtN-FInrec.CashBackN-FInrec.FeeAmtN)/100]),15));
            if (FInRec.CashBackN > 0) then
            begin
              WriteRLine(' Cash Back      $' + BLFill(Format('%8.2f',[FInrec.CashBackN/100]),15));
              WriteRLine('                 ---------------');
              WriteRLine(' Total          $' + BLFill(Format('%8.2f',[FInrec.trxAmtN/100]),15));
            end;
          end
          else
            WriteRLine(' VoidPrch       $' + BLFill(Format('%8.2f',[FInrec.trxAmtN/100]),15));
          WriteRline(' ');
        end;

        // CPCLIENTS-5350 Account # should not be prefixed with 'S', 'M' if void by seq #
        if IsHost('ATL') then
        begin
          if (FInrec.VoidCode = 'V') and ((StrtoIntDef(FInRec.SeqNo,0) - 1) <>  StrtoIntDef(FInRec.OldSeq,0)) then
            tmpStr := StringOfChar('X', FInRec.PanLen - 4) + ArrayToStr(FInrec.AcctNoLast4)
          else
            tmpStr := iif((FInrec.Entry = CEV_MANUAL_POS), CEV_MANUAL_POS, CEV_SWIPED_CUST) + StringOfChar('X', FInRec.PanLen - 4) + ArrayToStr(FInrec.AcctNoLast4);
          end
        else
          tmpStr := iif((FInrec.Entry = CEV_MANUAL_POS), CEV_MANUAL_POS, CEV_SWIPED_CUST) + StringOfChar('X', FInRec.PanLen - 4) + ArrayToStr(FInrec.AcctNoLast4);                       // 828.5

        WriteRline(' Check Auth #   ' + tmpStr);
        WriteRline(' Auth # ' + FInrec.AuthCode + '  Check # ' + FInrec.Check_Num);
        WriteRline(' Lane # ' + RPad(FInrec.LaneNo, 8) + 'Cashier # ' + IntToStr(StrToIntDef(FInrec.Cashier,0)));
        if (FInrec.SeqNo = '000000')
          then tmpStr := FInrec.OldSeq
          else tmpStr := FInrec.SeqNo;
        WriteRline(Format(' %s/%s/%s %s:%s ',
            [Copy(FInrec.TrxDate,3,2), Copy(FInrec.TrxDate,5,2), Copy(FInrec.TrxDate,1,2),
             Copy(FInrec.TrxTime,1,2), Copy(FInrec.TrxTime,3,2)])
             + 'Ref/Seq # ' + tmpStr);
        WriteRline(' Trace ID #     ' + FInrec.TraceID);
        if IsHost('FLC') then
        begin
          // DOEP-56119/56114, FLC wants merchant ID here
          //if usingServerEPSHost then                           //69739
          WriteRline(' Merchant ID # ' + HostBuf.MerchantNumber); // server EPS uses MerchantNumBER XML tag
          //else                                                 //69739
          //  WriteRline(' Merchant ID # ' + HostBuf.MerchantNum); // WinEPS uses MerchantNum XML tag
        end;

        if not ((FInrec.ReqCodeN in Check_Set) and (Printer_Type in ['1', '5'])) then
          if (FInrec.SeqNo = '000000') or (Trim(FInrec.SeqNo) = '')
            then tmpStr := FInrec.OldSeq
            else tmpStr := FInrec.SeqNo;
        WriteRline(' EPS Sequence # ' + tmpStr);
        // CPCLIENTS-5020 For ConcordEPC receipts to display MId/TId/IC
        if IsBYLHostGroup(FInrec.HostSuffixCode) then
          WriteRline(BYLATLReceiptMerchTerm(FInrec.BYLATLIssuerCode)); // CPCLIENTS-5179 To have meaningful name for BYLATLIssuerCode/BYLATLReceiptMerchTerm as it is implemented for ATL too
        WriteRline(' ');

        if (FInrec.TermRspCode = rcDeclineND) then
        begin
          // CPCLIENTS-5020 To display signatory text for Denial receipts
          if IsBYLHostGroup(FInrec.HostSuffixCode) then
            WriteRline('~E')
          else
          begin
            WriteRline(' You can obtain more information on');
            WriteRline(' this transaction by calling Telecheck');
            WriteRline(' at ' + ECCReceiptInfo.PhoneNumber + '.');
            WriteRline(' ');
            WriteRline(' Having the record number noted');
            WriteRline(' above along with your check and ID');
            WriteRline(' will expedite your call.');
            WriteRline(' ');
          end
        end
        else
        begin
          if not isVoid then
          begin
            WriteRline('~E');
            PrintReturnCheckFee(ECCReceiptInfo); // DEV-8179
            ReturnCheckFeeNote := '';
            tmpStr := Trim(FInrec.ReturnCheckFeeNote);
            if  tmpStr <> '' then
            begin
              while Length(tmpStr) > MAXLINELEN do
              begin
                tmpLine := Copy(tmpStr, 1, MAXLINELEN);
                tmpStr := Copy(tmpStr, succ(MAXLINELEN), Length(tmpStr)-MAXLINELEN);
                foundSpc := False;
                for I := MAXLINELEN downto 1 do
                  if (tmpLine[I] = ' ') then
                  begin
                    WriteRLine(Copy(tmpLine, 1, I));
                    tmpStr := Copy(tmpLine, I+1, MAXLINELEN-I) + tmpStr;
                    foundSpc := True;
                    Break;
                  end;
                if not foundSpc then
                  WriteRLine(tmpLine);
              end;
              if Length(Trim(tmpStr)) <= MAXLINELEN then
                WriteRLine(tmpStr);
              WriteRline(' ');
            end;

            if (Which_Copy = drawer_Copy) then
              PrintECCSignatureLine;
          end;
          if (Which_Copy = cust_Copy) or not isVoid then
            WriteRline('     Questions? Call ' + ECCReceiptInfo.PhoneNumber);
        end;
      end;
    except
      on e: exception do
        sm('Try..Except: BuildBuypassECCReceipt ' + e.Message);
    end;
  finally
    FreeAndNil(ECCReceiptInfo);
  end;
end;

//break out this functionality to make it easily unit testable.
function LineToStrArray(S: string; var FreeFormLines: TStrArray10): integer;
var
  i,LineLen: integer;
begin
  result := 1;
  LineLen := 0;
  fillchar(FreeFormLines,sizeof(FreeFormLines),0);
  for i := 1 to length(S) do
    begin
    inc(LineLen);
    case S[i] of
      '/': begin
           inc(result);
           LineLen := 0;
           end;
      '\': FreeFormLines[result] := FreeFormLines[result] + ' ';
      else FreeFormLines[result] := FreeFormLines[result] + S[i];
    end;
    if LineLen > MAXLINELEN then
      begin
      inc(result);
      LineLen := 0;
      end;
    if result > MAXARRAYSIZE then   // lose the remaining chars if they screw up formatting...
      begin
      result := MAXARRAYSIZE;
      break;
      end;
    end;
end;

function ArrayToStr(Arr: array of char): string;
var
  ch: Char;
begin
  Result := '';
  for ch in Arr do
    Result := Result + ch;
end;

procedure TReceipt.BuildNovaECCReceipt;
var
  Which_Copy,NumFreeFormLines,i: Integer;
  isVoid: Boolean;
  tmpStr: string;
  ECCReceiptInfo: TECCReceiptInfo; // DEV-8179
  FreeFormLines: TStrArray10;   // DEV 12823
const
  Rcpt_Acct_Pos = 20;
begin
(*
      ECC Certification Test Store
            1234 Some Lane
        Somewhere, CA  12345
    (123) 456-7890

ELECTRONIC CHECK CONVERSION

Purchase        $     14.00     Note:  No cash back allowed

Transit #       ********0       Different: Transit and acct separated
Account #       XXXXXXX7890     Account masking shows last 4 of acct
Auth # 123456           Check #  0102       Check number could be up to 8 chars

Lane #  01          Cashier #  101      Checker ID could be up to 9 chars
9/13/06  9:35       WinEPS Seq  # 010117
Record # 123            Trace # 12345       Bit 48, pos 4-6;  Bit 63, pos 13-18

Bank ID #           001054
Terminal  ID #      ****************    Lead digits taken from host cfg, last 2 are lane #
Tran Ref #          ********90      Bit 63, pos 19-28
Reference #         ********        Bit 63, pos 1-8
Tran ID #               ********9012345     Bit 63, pos 29-43

      AUTHORIZATION AGREEMENT

I AUTHORIZE THE MERCHANT TO USE THE From this line to the last auth text line -
INFORMATION FROM MY CHECK TO INITIATE   read from ECC Rec Info XML file, NOV section
…






BY STATE OR FEDERAL LAW.


Signature: _________________________        Drawer copy only

    Thank you for shopping at our store
              and have a nice day!
*)

  {            1         2         3         Actual  }
  {   ********90********90********90******** Position}
  {           1         2         3          RcptBuf }
  {   234567890********90********90********9 Position}
  {   ' Trace ID       ********90********9012'       }

  isVoid := (FInrec.VoidCode = 'V');
  for which_Copy := cust_Copy to end_Copy do
  begin
    Init_For_Copy(which_Copy);
    if FInrec.Trx_Is_Training then
      WriteRLine(' ' + Str_Training);

    if isVoid
      then WriteRLine('        ELECTRONIC CHECK REVERSAL       ')
      else WriteRLine('       ELECTRONIC CHECK CONVERSION      ');
    WriteRline(' ');

    if (FInrec.TermRspCode = rcDeclineND) then
      WriteRLine(' Declined Amount$' + BLFill(Format('%m',[FInrec.trxAmtN/100]),15))
    else
    begin
      if not isVoid then
      begin
        WriteRLine(' Purchase       $' + BLFill(Format('%8.2f',[(FInrec.TrxAmtN-FInrec.CashBackN-FInrec.FeeAmtN)/100]),15));
        if (FInrec.CashBackN <> 0) then
        begin
          WriteRLine(' Cash Back      $' + BLFill(Format('%8.2f',[FInrec.CashBackN/100]),15));
          WriteRLine('                ---------------');
          WriteRLine(' Total          $' + BLFill(Format('%8.2f',[FInrec.trxAmtN/100]),15));
        end;
      end
      else
        WriteRLine(' VoidPrch       $' + BLFill(Format('%8.2f',[FInrec.trxAmtN/100]),15));
    end;
    WriteRline(' ');
    WriteRline(' Transit #      ' + Copy(FInrec.Check,1,9));

    tmpStr := iif((FInrec.Entry = CEV_MANUAL_POS), 'M', 'S') + StringOfChar('X', FInRec.PanLen - 4) + ArrayToStr(FInRec.AcctNoLast4);    // 828.5

    WriteRLine(' Account #      ' + tmpStr);
    WriteRLine(' Auth # ' + RPad(FInrec.AuthCode, 8) + '  Check # ' + FInrec.Check_Num);
    WriteRLine(' ');
    WriteRLine(' Lane # ' + RPad(FInrec.LaneNo, 8) + 'Cashier # ' + IntToStr(StrToIntDef(FInrec.Cashier,0)));

    if (FInrec.SeqNo = '000000')
      then tmpStr := FInrec.OldSeq
      else tmpStr := FInrec.SeqNo;
    WriteRLine( Format(' %s/%s/%s %s:%s ',
        [Copy(FInrec.TrxDate,3,2), Copy(FInrec.TrxDate,5,2), Copy(FInrec.TrxDate,1,2),
         Copy(FInrec.TrxTime,1,2), Copy(FInrec.TrxTime,3,2)])
         + 'WinEPS Seq # ' + tmpStr);
    WriteRLine(' Record # ' + rpad(FInrec.NOVSequenceNum,6) +       // bit48, pos 4-6
               'Trace # ' + FInrec.NOVtraceNum);
    WriteRLine(' ');
    WriteRLine(' Bank ID #      ' + HostBuf.StoreNumber);
    WriteRLine(' Terminal ID #  ' + Trim(HostBuf.MerchantNum) + Trim(FInrec.LaneNo));
    WriteRLine(' Tran Ref #     ' + FInrec.NOVtranRefNum);
    WriteRLine(' Reference #    ' + FInrec.NOVreferenceNum);
    WriteRLine(' Tran ID #      ' + Finrec.NOVtranID);

    WriteRLine(' ');

    if FInrec.TermRspCode = rcDeclineND then
    begin
      WriteRLine('                DECLINED             ');
      WriteRLine(' ');
      FInrec.NOVRspSource := rpad(FInrec.NOVRspSource, 1);   // don't want errors, needs to be length 1
      if FInrec.NOVRspSource = 'A' then // 3rd party bank -> long form
      begin
        WriteRLine(' YOUR CHECK CANNOT BE ACCEPTED FOR THE');
        WriteRLine(' POS CHECK SERVICE AT THIS TIME. THE');
        WriteRLine(' DECISION TO DENY YOUR CHECK IS BASED');
        WriteRLine(' UPON THE INFORMATION PROVIDED TO US');
        WriteRLine(' BY');
        WriteRLine(' ');

        NumFreeFormLines := LineToStrArray(FInrec.NOVfreeFormData,FreeFormLines);
        for i := 1 to NumFreeFormLines do
          WriteRLine(FreeFormLines[i]);

        WriteRLine('');
        WriteRLine(' YOU HAVE THE RIGHT TO OBTAIN A FREE');
        WriteRLine(' COPY OF THIS INFORMATION FROM THE');
        WriteRLine(' COMPANY LISTED ABOVE, IF YOU REQUEST');
        WriteRLine(' IT FROM THAT COMPANY WITHIN 60 DAYS.');
        WriteRLine(' YOU ALSO HAVE THE RIGHT TO DISPUTE');
        WriteRLine(' THE ACCURACY OR COMPLETENESS OF THE');
        WriteRLine(' INFORMATION THEY PROVIDE YOU.');
      end
      else if FInrec.NOVRspSource[1] in ['4','5'] then // member bank -> short format
      begin
        WriteRLine(' YOUR CHECK CANNOT BE ACCEPTED FOR ');
        WriteRLine(' THE POS CHECK SERVICE AT THIS TIME.');
        WriteRLine(' THE DECISION TO DENY YOUR CHECK IS');
        WriteRLine(' BASED ON INFORMATION PROVIDED TO US');
        WriteRLine(' BY YOUR BANK. FOR MORE INFORMATION');
        WriteRLine(' REGARDING THIS TRANSACTION,');
        WriteRLine(' CALL YOUR BANK.');
      end;
      WriteRLine(' ');
    end
    else
    begin
      if not isVoid then
      begin
        WriteRLine('        AUTHORIZATION AGREEMENT   ');
        WriteRLine(' ');
        WriteRLine('~E');
        ECCReceiptInfo := TECCReceiptInfo.Create(FInrec.HostSuffixCode); // DEV-8179 <
        try
          PrintReturnCheckFee(ECCReceiptInfo);
        finally
          FreeAndNil(ECCReceiptInfo );
        end; // DEV-8179 >
        if (Which_Copy = drawer_Copy) then
          PrintECCSignatureLine;
      end
      else
      begin
        WriteRLine('            REVERSAL COMPLETE    ');
      end;
    end;
  end;
end;

procedure TReceipt.BuildCertegyECCReceipt;
var
  Which_Copy: Integer;
  ECCReceiptInfo: TECCReceiptInfo; // DEV-8179
begin
(*         Certigy ECC Receipt format:

************************APPROVED RECEIPT*****************

           Receipt header - merchant address

CHECK:           $100.00
SALE:            $100.00

EFT REF:         ********9
BANK:            ********9
Certegy UID#:    xxxxxxxxxxxxxxx
ACT:             ******1234
CHK #:           1234
FINANCIAL INST.: SUNTRUST BANK,
                 TAMPA BAY

  -------------CERTEGY APPROVED DISCLAIMER HERE - SEE BELOW --------

  ___________________________________________
               SIGNED

            THANK YOU,
    ANY QUESTIONS, CALL CERTEGY
       1-800-539-3577

       MERCHANT (OR CUSTOMER) COPY

***********************DECLINED RECEIPT********************

           Receipt header - merchant address

CHECK:          $100.00
SALE:           $100.00
CERTEGY UID:    XXXXXXXXXXXXXX
BANK:           ********9
Acct:           ********1234
CHK #:          1234

  ------------CERTEGY DECLINED DISCLAIMER HERE-----------
*)
  for which_Copy := cust_Copy to end_Copy do
    begin
    Init_For_Copy(which_Copy);
    if FInrec.Trx_Is_Training then
      WriteRLine(' ' + Str_Training);
    if (FInrec.TermRspCode = rcApprovedByECC) then
      begin
      WriteRline(' ');
      WriteRline(Format(' %s/%s/%s %s:%s ',
        [Copy(FInrec.TrxDate,3,2), Copy(FInrec.TrxDate,5,2), Copy(FInrec.TrxDate,1,2),
         Copy(FInrec.TrxTime,1,2), Copy(FInrec.TrxTime,3,2)]));
      WriteRLine(' ');
      WriteRLine(' CHECK:           $' + Trim(format('%9.2f',[FInrec.TrxAmtN/100])));
      WriteRLine(' SALE:            $' + Trim(format('%9.2f',[(FInrec.TrxAmtN - FInrec.CashBackN)/100])));
      WriteRLine(' ');
      WriteRLine(' EFT REF:         ' + FInrec.SwRspCode);
      WriteRLine(' BANK:            ' + Copy(FInrec.Check,1,9));
      WriteRLine(' Certegy UID#:    ' + FInrec.SwRspCode);
      WriteRLine(' TERMINAL ID:     ' + FInrec.StoreID);
      WriteRLine(' Acct:            ' + TruncMdMsgAcctNo(FInrec));
      WriteRLine(' CHK #:           ' + FInrec.Check_Num);
      WriteRLine(' FINANCIAL INST.: ' + FInrec.SwRspCode);
      WriteRLine(' ');
      WriteRLine('~E');
      ECCReceiptInfo := TECCReceiptInfo.Create(FInrec.HostSuffixCode); // DEV-8179 <
      try
        PrintReturnCheckFee(ECCReceiptInfo);
      finally
        FreeAndNil(ECCReceiptInfo);
      end; // DEV-8179 >

      if (Which_Copy = drawer_Copy) then
        begin
        WriteRLine(' ');
        WriteRLine(' ');
        WriteRLine(' ');
        WriteRLine(' ' + stringOfChar('_', 36));
        WriteRLine(' ' + stringOfChar(' ',15) + 'SIGNED');
        WriteRLine(' ');
        WriteRLine(' ' + stringOfChar(' ',13) + 'THANK YOU,');
        WriteRLine(' ' + stringOfChar(' ',5) + 'ANY QUESTIONS, CALL CERTEGY');
        WriteRLine(' ' + stringOfChar(' ',11) + '1-800-539-3677');
        WriteRLine(' ');
        WriteRLine(' ' + stringOfChar(' ',11) + 'MERCHANT COPY');
      end
      else begin
        WriteRLine(' ');
        WriteRLine(' ' + stringOfChar(' ',5) + 'ANY QUESTIONS, CALL CERTEGY');
        WriteRLine(' ' + stringOfChar(' ',11) + '1-800-539-3677');
        WriteRLine(' ');
      end;
    end
  else if (FInrec.TermRspCode = rcDeclineND) then
      begin
      WriteRLine(' ');
      WriteRLine(Format(' %s/%s/%s %s:%s ',
        [Copy(FInrec.TrxDate,3,2), Copy(FInrec.TrxDate,5,2), Copy(FInrec.TrxDate,1,2),
         Copy(FInrec.TrxTime,1,2), Copy(FInrec.TrxTime,3,2)]));
      WriteRLine(' ');
      WriteRLine(' CHECK:           $' + Trim(format('%9.2f',[FInrec.TrxAmtN/100])));
      WriteRLine(' SALE:            $' + Trim(format('%9.2f',[(FInrec.TrxAmtN - FInrec.CashBackN)/100])));
      WriteRLine(' BANK:            ' + Copy(FInrec.Check,1,9));
      WriteRLine(' Certegy UID#:    ' + FInrec.SwRspCode);
      WriteRLine(' TERMINAL ID:     ' + FInrec.StoreID);
      WriteRLine(' Acct:            ' + TruncMdMsgAcctNo(FInrec));
      WriteRLine(' CHK #:           ' + FInrec.Check_Num);
      WriteRLine(' ');
      WriteRLine(' We apologize for not accepting your');
      WriteRLine(' check. Our decision was based in');
      WriteRLine(' part on information provided by');
      WriteRLine(' Certegy Check Services, Inc.  You');
      WriteRLine(' have the right to a free copy of');
      WriteRLine(' your consumer report if you request');
      WriteRLine(' it from Certegy no later than');
      WriteRLine(' sixty (60) days after you received');
      WriteRLine(' this notice, and to dispute any');
      WriteRLine(' inaccurate or incomplete information');
      WriteRLine(' in that consumer report with Certegy.');
      WriteRLine(' ');
      WriteRLine(' Question? Call Certegy toll free at');
      WriteRLine('          1-800-248-7939');
      end;
    end;
end;

function  TReceipt.IsProgID(aProgID: string): boolean;
begin
  result := SameText(FInRec.ProgramID, aProgID);
end;

function TReceipt.IsXPIEnglish: boolean;   // this function defaults either setting to english
begin
  if FDoingCustomerCopy and (EMVMgr.language <> '') then
    Result := EMVMgr.language <> LANG_FRENCH // cust copy: language is from card or customer set it
  else
    Result := FMerchLanguage <> EMV_LANGUAGES[2]; // DOEP-43946 - use store language for Void by seq customer copy receipt
end;

function TReceipt.EngOrFrenchPurch: string;
begin
  if IsXPIEnglish
    then result := 'Purchase'
    else result := 'Achat';
end;

function TReceipt.EngOrFrenchCashBack: string;
begin
  if IsXPIEnglish
    then result := 'Cash back'
    else result := 'Retrait'; //DOEP-43353
end;

function TReceipt.MakeTDTranDescrip: string;
begin
  result := TDCC.EngOrFrenchTranDesc(True, FInRec);  // CPCLIENTS-12067 not using French at this time
  (*
  result := '';
  if IsXPIEnglish then
  begin
    if (FInRec.ReqCodeN in All_Return_Trxs) then
      result := 'Refund'
    else if (FInRec.ReqCodeN in PreAuth_Set) then
      result := 'Pre-Auth'
    else if (FInRec.ReqCodeN in PreAuth_Set) then
      result := 'Pre-Auth Completion'
    else if (FInRec.ReqCodeN in Activation_Set) then
      result := 'Activation'
    else if (FInRec.ReqCodeN = CrPaymentOnAcctN) then
      result := 'Payment'
    else if (FInRec.ReqCodeN = BenefitItemQualificationN) then   // CPCLIENTS-10788 - To Print Item Qualification on Receipt
      result := 'Item Qualification'
    else
      result := 'Purchase';
  end
  else
  begin
    if (FInRec.ReqCodeN in All_Return_Trxs) then
      result := 'Remboursement'
    else if (FInRec.ReqCodeN in PreAuth_Set) then
      result := 'Préautorisation Préaut.'
    else if (FInRec.ReqCodeN in PreAuth_Set) then
      result := 'Compl. de préaut.'
    else if (FInRec.ReqCodeN in Activation_Set) then
      result := 'Activation'
    else if (FInRec.ReqCodeN = CrPaymentOnAcctN) then
      result := 'Paiement'
    else
      result := 'Achat';
  end;

  if (FInRec.VoidCode = 'V') then
  begin
    // DOEP-43766 - DR4 - Need to Correct Receipt verbiage for voids
    if FInRec.ReqCodeN <> 0 then
    begin
      if (FInRec.ReqCodeN in All_Return_Trxs) then
      begin
        if IsXPIEnglish then
          result := 'Return Correction'
        else
          result := 'Correction De Remise';
      end
      else
      begin
        if IsXPIEnglish then
          result := 'Purchase Correction'
        else
          result := 'Correction de L''achat'; // DOEP-51037, 56073
      end;
    end
    else
      result := 'Correction';
  end;
  *)
end;

procedure TReceipt.BuildTDBReceipt;
var i: integer;

  procedure WriteRLineIfNotBlank(aLine: string);
  begin
    if (aLine <> '') then
      WriteRLine(aLine);
  end;

  function MakeEntry: string;
  begin
    result := '';
    if (FInRec.Entry <> '') then
    begin
      if IsApprovedOffline or IsDeclinedOffline then // DOEP-42857
        case FInRec.Entry[1] of
          CEV_MANUAL_POS,CEV_MANUAL_CUST: result := 'MO';    // manual POS, manual Customer
          CEV_SWIPED_CUST,CEV_SWIPED_POS: result := 'SO';    // swipe customer, swipe POS
          CEV_FALLBACK:                   result := 'SC';       // fallback // DOEP-34370
          // DOEP-41721 - TDMS: Printing wrong card entry method on offline chip transactions receipt
          //'E': result := 'CR';      // chip card inserted
          CEV_CHIPCARD:                   result := 'CO';        // chip card inserted
          CEV_RFID:                       result := 'RF';        // RFID
          CEV_BARCODE:                    result := 'B';        // Barcode
        end
      else
        case FInRec.Entry[1] of
          CEV_MANUAL_POS,CEV_MANUAL_CUST: result := 'M';    // manual POS, manual Customer
          CEV_SWIPED_CUST,CEV_SWIPED_POS:
          begin
            if (FInRec.TDBSigNotRequired) and (not FInRec.TDBPINVerified) and
              (not (FInrec.ReqCodeN in All_Return_Trxs)) and (FInrec.VoidCode <> 'V') then // DOEP-43978
              result := 'SN' // DOEP-43417
            else
              result := 'S';    // swipe customer, swipe POS
          end;
          CEV_FALLBACK: result := 'SC';       // fallback // DOEP-34370
          CEV_CHIPCARD: result := 'C';        // chip card inserted
          CEV_RFID:     result := 'RF';        // RFID
          CEV_BARCODE:  result := 'B';        // Barcode
        end;
    end;
  end;

  function MakeAccount: string;
  begin
    result := '';
    if (TruncMdMsgAcctNo(FInrec) <> '') then
    begin
      if IsXPIEnglish
        then result := 'Acct #    '
        else result := 'No compte ';
      if IsHost('TDB') or (Assigned(HostBuf) and (HostBuf.Suffix = 'TDB')) then                                    // TFS-8220 this is adding ???? to the account #
      begin
        if FDoingCustomerCopy
          then result := format('%s%s %s',[result,TruncMdMsgAcctNo(FInrec),MakeEntry])
          else result := format('%s%s %s',[result,MaskAcctNoPCI(FInrec),MakeEntry]);
      end
      else
        result := format('%s%s %s',[result,MaskAcctNoPCI(FInrec),MakeEntry]);
    end;
  end;

  function MakeAccountType: string;
  begin
    result := '';
    if (FInRec.ReqCodeN in db_set) then
    begin
      if (EMVMgr.accountType = SAVINGS2) then
      begin
        If IsXPIEnglish
          then result := 'Savings'
          else result := 'Épargne'; //DOEP-43194, 52302,54559
      end
      else if (EMVMgr.accountType = CHECKING1) then
      begin
        If IsXPIEnglish
          then result := 'Chequing'
          else result := 'Chèque'; //DOEP-43194, 52302, 54559
      end;
    end;
  end;

  function MakeExpDateOrCardType: string;
  begin
    result := '';
    if IsXPIEnglish then
    begin
      if (FInRec.ReqCodeN in db_set) or FDoingCustomerCopy then
      begin
        if (FInRec.CardProcID <> '') then
          result := lpad('Card Type  ' + FInRec.CardProcID, TDBLINELEN);
      end
      else
      begin
        if (FInRec.ExpDate <> '')
          then result := format('Exp Date %s/%s', [copy(FInRec.ExpDate,3,2),copy(FInRec.ExpDate,1,2)])
          else result := '';
        if (FInRec.CardProcID <> '') then
          result := format('%s     Card %s',[rpad(result, 14),FInRec.CardProcID]);
      end;
    end
    else
    begin
      if (FInRec.ReqCodeN in db_set) or FDoingCustomerCopy  then
      begin
        if (FInRec.CardProcID <> '') then
          result := lpad('Type carte ' + FInRec.CardProcID, TDBLINELEN);
      end
      else
      begin
        if (FInRec.ExpDate <> '')
          then result := format('Date exp. %s/%s',[copy(FInRec.ExpDate,3,2),copy(FInRec.ExpDate,1,2)])
          else result := '';
        if (FInRec.CardProcID <> '') then
          result := format('%s    Carte %s',[rpad(result, 15),FInRec.CardProcID]);
      end;
    end;
  end;

  function MakeName: string;
  begin
    result := '';
    if (FInRec.Customer_Name <> '') and (FInRec.PrintNameOnRcpt <> 'N') then
    begin
      if IsXPIEnglish
        then result := 'Name: ' + copy(FInRec.Customer_Name, 1, TDBLINELEN - 6)   // don't exceed line length
        else result := 'Nom:  ' + copy(FInRec.Customer_Name, 1, TDBLINELEN - 6);
    end;
  end;

  procedure WriteAppIDTrace;   // writes 4 lines - the same for all receipts
  var
    rID : string;
  begin
    // DOEP-41898 - The AID is only needed on all EMV transactions excluding non-debit returns
    if (EMVMgr.AppID <> '') and (FInRec.Entry = CEV_CHIPCARD) then // DOEP-44817 -  Need to print AID on all chip transactions
      WriteRLine(EMVMgr.AppID); // DOEP-42268 - print full AID and App Label following it in a seperate line
    WriteRLine(EMVMgr.AppLabel);
    WriteRLine('');
    if Assigned(HostBuf) then
      WriteRLine('TG' + HostBuf.MerchantNumber + FInRec.LaneNo);
    // DOEP-46032 - Mask all but the last 4 of the retailer ID on the receipt for discover
    if FInrec.CardProcID = 'DS' then
      rID := TruncAcctNo(FInRec.TDBRetailerID)
    else
      rID := FInRec.TDBRetailerID;
    WriteRLine(rID);
  end;

  procedure WriteTCCInvAuthNum;
  var tmpLine: string;
  begin
    if (not FDoingCustomerCopy) and (FInRec.ReqCodeN in cr_set + db_set) then
    begin
      if (EMVMgr.tcc <> '')
        then tmpLine := format('TCC %d',[StrToIntDef(EMVMgr.tcc,0)])
        else tmpLine := '';
      if (EMVMgr.tcd <> '') then
        tmpLine := format('%s           TCD %d',[tmpLine,StrToIntDef(EMVMgr.tcd, 0)]);
      WriteRLineIfNotBlank(tmpLine);
    end;
    if IsXPIEnglish then
    begin
      WriteRLine('Inv. # ' + FInRec.SeqNo);
      if (trim(FInRec.AuthCode) = '') then
        tmpLine := ''
      else if (FInRec.ReqCodeN in All_Voice_Trxs) then
        tmpLine := format('Auth # %sFP',[rpad(FInRec.AuthCode,8)])
      else
        tmpLine := format('Auth # %s',[rpad(FInRec.AuthCode,8)]);
      if (FInRec.RetrievalRefNo <> '') then
        tmpLine := format('%s  RRN %s',[tmpLine,FInRec.RetrievalRefNo]);
      WriteRLineIfNotBlank(tmpLine);
    end
    else
    begin
      WriteRLine('No facture ' + FInRec.SeqNo);
      if (trim(FInRec.AuthCode) = '') then
        tmpLine := ''
      else if (FInRec.ReqCodeN in All_Voice_Trxs) then
        tmpLine := format('No aut %sFP',[rpad(FInRec.AuthCode,8)])
      else
        tmpLine := format('No aut %s',[rpad(FInRec.AuthCode,8)]);
      if (FInRec.RetrievalRefNo <> '') then
        tmpLine := format('%s  RRN %s',[tmpLine,FInRec.RetrievalRefNo]);
      WriteRLineIfNotBlank(tmpLine);
    end;
  end;

  // DOEP-41665 - TDMS - Receipt Requirements
  function MakeTranStatus(IsNotCompleted: Boolean): string;
  var
    JbErrorCode : Integer;
  begin
    JbErrorCode := StrToIntDef(FInrec.SwRspCode, 0);
    // DOEP-43814 - Need to Print Kc field on Debit MSR and Private Debit MSR when returned
    if (FInRec.ReqCodeN in Db_Set + Prop_Db_Set) and (FInrec.Entry <> CEV_CHIPCARD) and
      (EMVMgr.HostReceiptMessage <> '') and (JbErrorCode = 0) and // DOEP-43413 - not on Jb Error
      (not IsMACOrChipDecline) and (not IsNotCompleted) and (not FIsContEMVCidDeclined) then // DOEP-44229 - not on MAC or Chip or C33 Cid declines
    begin
      result := EMVMgr.HostReceiptMessage;
    end
    else
    begin
      if (Copy(FInRec.TermRspCode,1,1) = 'A') then
      begin
        if IsXPIEnglish then
          result := 'Approved-Thank You'
        else
          result := 'Approuvé–Merci'; //DOEP-52302, 54559
      end
      else
      if (IsNotCompleted or IsMACOrChipDecline or (FInRec.MTXRspCodeN = TrxDecNoXPIFallBackN) or // DOEP-41804, DOEP-44719
        ((JbErrorCode <> 0) and (FInrec.TDBHostResponse = ''))) and //DOEP-43413,43817
        (EMVC31ErrorCode = 0) and (not FIsContEMVCidDeclined) and (not ((FInrec.Entry = CEV_SWIPED_CUST) and //DOEP-44837
        (FInrec.ReqCodeN in Cr_Set) and (FInRec.MTXRspCodeN = TrxDecOffAmtN)))  then // DOEP-43823, 44519
      begin
        if IsXPIEnglish then
          result := 'Not Completed'
        else
          result := 'Non complété'; //DOEP-52302, 54559
      end
      else
      begin
        if IsXPIEnglish then
          result := 'Declined'
        else
          result := 'Refusé'; //DOEP-43096, 52302, 54559
      end;
    end;
  end;

  function MakeEAFieldAndTranStatus: string;
  var
    aVal: integer;
    IsNotCompleted: Boolean;
  begin
    IsNotCompleted := False;
    // DOEP-41403 - TDMS - New Data Element on Receipt
    // DOEP-41898,35300,44135,44515 - Per Josh - Need to remove EA field from non-EMV receipts (this field should only be on the following receipts)
    // a. EMV Credit and Debit Purchase
    // b. EMV Debit Return
    // c. EMV Debit Voids
    if (FInRec.Entry = CEV_CHIPCARD) and ((FInRec.ReqCodeN in [DbPurchN, DbReturnN, DbReturnWithValidN]) or
        ((FInRec.VoidCode <> 'V') and (FInrec.ReqCodeN = CrPurchN))) then
    begin
      aVal := StrToIntDef(FInRec.TDBHostResponse, -1);
      if FIsContEMVCidDeclined or (IsMACOrChipDecline and (FInRec.TDBHostResponse='')) or // DOEP-44653
        (EMVC31ErrorCode in [c31PANMISMATCH, c31EXPMISMATCH])  then // DOEP-44837
        result := '(Z1) '
      else
      if (aVal >= 0) and (aVal <= 49) then //DOEP-54549, 53189
        result := '(00) '
      //else if (aVal >= 100) and (aVal <= 149) then
      //  result := '(05) ' // DOEP-42734
      else if (aVal >= 900) and (aVal <= 949) then
        result := '(04) '
      else if (aVal >= 0) then                     // all other response codes
        result := '(05) '
      // DOEP-35300 - TDMS Receipts - EA field
      // Y3 = Unable to go online, CID from C34 returns TC offline approval (Y3)
      //else if IsApprovedOffline then
      else if (EMVMgr.cid = '40') then // TC approval
        result := '(Y3) '
      else
      begin
        IsNotCompleted := True;
        result := '(Z3) ';                          // (Z3) not completed is the default
      end;
    end
    else
      result := StringOfChar(' ', 5);

    result := result + MakeTranStatus(IsNotCompleted);
  end;

  // DOEP-47031 - TARGET - TDMS - Missing Currency Code on Receipts
  // Last from Josh: "We do not need the Currency code for Non-EMV transactions. This can be removed."
  function MakeCurrencyName: string;
  var
    currencyCode : string;
  begin
    Result := '';
    //if EMVMgr.tcd <> '' then
      currencyCode := EMVMgr.tcd; // From EMV tag 5F2A
    //else
    //  currencyCode := mtx_lib.GetHostConfigFromXML(HostBuf.Suffix, hctCurrencyCode);
    case (StrToIntDef(currencyCode, 0)) of
      124: Result := 'CAD';
      840: Result := 'USD';
    end;
  end;

  procedure WriteTotalAndTranType;
  var
    purchText, cashbackText,
    purchAmt, cashbackAmt: string;
  begin
    WriteRLine(MakeEAFieldAndTranStatus);
    WriteRLine('');
    // DOEP-55357,57907  - TGT: French Receipts must use French Number Format
    if not IsXPIEnglish then
      SysUtils.FormatSettings.DecimalSeparator := FRENCH_DECIMAL_SEPARATOR; // set to french decimal separator (,)     // 828.5
    if (FInRec.CashBackN <> 0) then
    begin
      purchText := EngOrFrenchPurch;
      cashbackText := EngOrFrenchCashback;
      purchAmt := Trim(Format('%9.2f',[(FInrec.TrxAmtN-FInrec.CashBackN-FInrec.FeeAmtN) / 100]));
      cashbackAmt := Trim(Format('%9.2f',[FInrec.CashBackN/100]));
      if IsXPIEnglish then
      begin
        purchAmt := '$' + purchAmt;
        cashbackAmt := '$' + cashbackAmt;
      end
      else
      begin // DOEP-66756
        purchAmt := purchAmt + ' $';
        cashbackAmt := cashbackAmt + ' $';
      end;
      WriteRLine(rpad(purchText, 14) + Trim(purchAmt));
      WriteRLine(rpad(cashbackText, 14) + Trim(cashbackAmt));
      WriteRLine(StringOfChar(' ',15) + StringOfChar('-', 9));
    end;
      if IsXPIEnglish then
      WriteRLine(Format('Total         $%f%s',[FInrec.TrxAmtN/100,MakeCurrencyName]))
    else
      WriteRLine(Format('Total         %f $ %s',[FInrec.TrxAmtN/100,MakeCurrencyName]));  // DOEP-56073
    System.SysUtils.FormatSettings.DecimalSeparator := DEFAULT_DECIMAL_SEPARATOR; // reset to default decimal seperator             // 828.5
  end;

  procedure WriteTVR_CVMR;
  var tmpLine: string;
  begin
    if (EMVMgr.tvr <> '')
      then tmpLine := format('TVR %s',[rpad(EMVMgr.tvr,10)])
      else tmpLine := '';
    if (EMVMgr.cvmr <> '') and (IsApprovedOffline or FIsContEMVCidDeclined or // DOEP-35348
      IsDeclinedOffline) // DOEP-43472
      then tmpLine := format('%s       CVMR %s', [tmpLine,EMVMgr.cvmr]);
    WriteRLineIfNotBlank(tmpLine);
  end;

  procedure WriteTC;
  var
    tmpLine, tmpLineHeader, cid, tc : string;
  begin
    // DOEP-34272 - EMV: Dynamic receipt field header based on tag 9F27
    if (EMVMgr.tc <> '') then
    begin
      cid := EMVMgr.cid;
      tc := EMVMgr.tc;
      if ((FInrec.CardProcID = 'MC') or (FInrec.CardProcID = 'MT') or (FInrec.CardProcID = 'AM')) and
        (IsApprovedOffline or IsDeclinedOffline) {or IsMACOrChipDecline} then
      begin
        cid := EMVMgr.ContEMV_cid; // .c33_cid;
        tc := EMVMgr.ContEMV_tc;  //.c33_tc;
      end;
      case StrToIntDef(cid, -1) of
        00: tmpLineHeader := 'AAC';
        40: tmpLineHeader := 'TC';
        80: tmpLineHeader := 'ARQC';
      else
        tmpLineHeader := '';
      end;
      tmpLine := format('%s %s',[tmpLineHeader, rpad(tc, 16)]);
    end
    else
      tmpLine := '';
    if (EMVMgr.aip <> '') and (IsApprovedOffline or
      IsDeclinedOffline or //DOEP-43472
      FIsContEMVCidDeclined or FIsContEMVGenericError) then // DOEP-35348,42852
      tmpLine := format('%s     AIP %s', [tmpLine,EMVMgr.aip]);
    WriteRLineIfNotBlank(tmpLine);
  end;

  procedure WriteATC;
  var tmpLine: string;
  begin
    if (FInRec.Entry = CEV_CHIPCARD) and (IsApprovedOffline or
      IsDeclinedOffline or // DOEP-43472
      FIsContEMVCidDeclined or FIsContEMVGenericError) then    // DOEP-35348, 42852, 44171
    begin
      if (EMVMgr.atc <> '')
        then tmpLine := format('ATC %s', [rpad(EMVMgr.atc, 4)])
        else tmpLine := '';
      if (EMVMgr.ps <> '') then
        tmpLine := format('%s  PS %s',[tmpLine,EMVMgr.ps]);
      if (EMVMgr.up <> '') then
        tmpLine := format('%s     UP# %s',[tmpLine,EMVMgr.up]);
      WriteRLineIfNotBlank(tmpLine);
    end;
  end;

  procedure WriteMerchReceiptLines;      // only on merch copy, same for both languages
  begin
    if not FDoingCustomerCopy then
    begin
      WriteTVR_CVMR;
      WriteTC;
      WriteATC;
      if (FInRec.ReqCodeN in cr_set) then
      begin
        if (EMVMgr.iad <> '') and (IsDeclinedOffline or FIsContEMVCidDeclined{or IsMACOrChipDecline}) then //DOEP-43755
          WriteRLine('IAD ' + EMVMgr.iad);        // just show bytes 18 to 32???
        if (EMVMgr.iac_dn <> '') and (EMVMgr.iac_df <> '') and (EMVMgr.iac_ol <> '') and
           (((copy(FInRec.TermRspCode,1,1) <> 'A') and IsApprovedOffline) or IsMACOrChipDecline) then    // only print these on declined receipt
        begin
          WriteRLine(Format('IAC DN=%s  DF=%s',[EMVMgr.iac_dn, EMVMgr.iac_df]));
          WriteRLine(Format('IAC OL=%s',[EMVMgr.iac_ol]));
        end;
        // DOEP-44837
        if FEMVC31ErrorCode = c31PANMISMATCH then
        begin
          if IsXPIEnglish then
            WriteRLine('Inconsistent PAN=MISMATCH')
          else
            WriteRLine('Inconsistant PAN=disparité');
        end
        else
        if FEMVC31ErrorCode = c31EXPMISMATCH then
        begin
          if IsXPIEnglish then
            WriteRLine('Inconsistent EXP DATE=MISMATCH')
          else
            WriteRLine('Inconsistant Date d’exp.=disparité');
        end;
        if (EMVMgr.cid <> '') and (IsDeclinedOffline or FIsContEMVCidDeclined{or IsMACOrChipDecline}) then //DOEP-43755
          WriteRLine('CID ' + EMVMgr.cid);
      end;
      // DOEP-43196 - print "pin verified" only on EMV credit receipt
      if (FInRec.Entry = CEV_CHIPCARD) and (FInRec.TDBPINVerified) and (FInrec.ReqCodeN in Cr_Set) and
         (Copy(EMVMgr.tvr, 5,2) = '00') then //DOEP-43406 - print only if all bits are off in TVR Byte 3
      begin
        if IsXPIEnglish
          then WriteRLine(CenterText('PIN VERIFIED', TDBLINELEN))
          else WriteRLine(CenterText('NIP VERIFIE', TDBLINELEN));
      end;
    end;
  end;

  // TFS-8221
  { This procedure was copied in from XPIBaseTerm and needs to be moved to a common unit instead of existing twice }
  function SetTVRByteNValue(aByteNo: Byte; aTVR: string): integer;
  begin     // the TVR might come in as 10 bytes of hex, or 5 bytes of chars, converted from hex, make it work for either
    result := 0;
    if (length(aTVR) = 5) then
      result := ord(aTVR[aByteNo])
    else if (length(aTVR) = 10) then
      result := StrToIntDef('$' + copy(aTVR, (aByteNo-1) * 2 + 1, 2), 0);   // change hex string to integer for Byte N of tvr
  end;

  procedure WriteSigLines;
  { Everything between ========== was added for TFS-8221}           // TFS-8221
  { =================================================== }
  const                                                             // TFS-8221
    sigNeededSet: set of byte = [$3,$5,$43,$45,$1E,$5E,$1F,$3F];    // TFS-8221
    pinVerifiedSet: set of byte = [$1,$3,$4,$5,$41,$43,$44,$45];    // TFS-8221
  var
    aByte: byte;
    s: AnsiString;
    cvmrRequiresSig: boolean;
    tvrRequiresSig: boolean;
  begin
    cvmrRequiresSig := True;
    aByte := 0;
    if (length(EMVMgr.cvmr) > 0) then
    begin
      s := HexStrToByteString(Copy(EMVMgr.cvmr, 1, 2));
      if Length(s) > 0 then
        aByte := Ord(s[1]);
      cvmrRequiresSig := (aByte in sigNeededSet) or not (aByte in pinVerifiedSet);
    end;
    tvrRequiresSig := SetTVRByteNValue(3, EMVMgr.tvr) <> 0;
    { =================================================== }
    WriteRLine('');
    // DOEP-42998 - Need to print electronic signature when PIN verified
    if (not FDoingCustomerCopy) and (FInrec.ReqCodeN in cr_set) and {(not FInRec.TDBPINVerified) and}
      (copy(FInRec.TermRspCode, 1, 1) = 'A') and (cvmrRequiresSig or tvrRequiresSig) then // DOEP-42638  // TFS-8221
      PrintTDBSignatureLine;
  end;

  procedure WriteFooter;
  begin
    WriteRLine(' ');
    if not FDoingCustomerCopy then
    begin
      if IsXPIEnglish
        then WriteRLine(CenterText('Merchant copy ',TDBLINELEN))     // English always here for merchant
        else WriteRLine(CenterText('Copie du marchand',TDBLINELEN)); // DOEP-53005, 54556
    end
    else
    begin                                                     // this is the customer copy
      if IsXPIEnglish then
      begin
        WriteRLine('Retain this copy for your record');
        WriteRLine(CenterText('Customer Copy',TDBLINELEN));
      end
      else
      begin //DOEP-52302, 54559
        WriteRLine('Conservez cette copie au dossier');
        WriteRLine(CenterText('Copie du client',TDBLINELEN));
      end;
    end;
  end;

  function MakeTDTranDate: string;
  begin
    result := copy(FInRec.TrxDate,3,2) + '-' + copy(FInRec.TrxDate, 5,2) + '-20' + copy(FInRec.TrxDate, 1, 2);
  end;

  function MakeTDTranTime: string;
  begin
    result := copy(FInRec.TrxTime,1,2) + ':' + copy(FInRec.TrxTime,3,2) + ':' + copy(FInRec.TrxTime,5,2);
  end;

  procedure InitForTDBCreditReturn;
  begin    // these items are not to print on a credit return
    if (FInRec.ReqCodeN = CrReturnN) then
    begin
      EMVMgr.tvr := '';
      EMVMgr.tc := '';
      EMVMgr.iad := '';
      EMVMgr.cid := '';
      EMVMgr.ps := ''; //DOEP-44171
    end;
  end;

begin
  // DOEP-52560 - TGT: Override Windows localization when setting amounts
  System.SysUtils.FormatSettings.DecimalSeparator := DEFAULT_DECIMAL_SEPARATOR;                  // 828.5
  for i := cust_Copy to end_Copy do
  begin
    Init_For_Copy(i);
    InitForTDBCreditReturn;
    // DOEP-45349 - Add "Transaction Record" to all TD receipts
    //if (FInRec.ReqCodeN in db_set) then                // debit transactions for this host are Interac
    //begin
      if IsXPIEnglish
        then WriteRLine(CenterText('TRANSACTION RECORD', TDBLINELEN))
        else WriteRLine(CenterText('RELEVÉ DE LA TRANSACTION', TDBLINELEN)); //DOEP-52302, 54559
      WriteRLine('');
    //end;

    writeRLine('**** ' + CenterText(MakeTDTranDescrip, TDBLINELEN-10) + ' ****');    // all receipts start with these lines - BEGIN
    writeRLine(format('%s         %s',[MakeTDTranDate, MakeTDTranTime]));
    writeRLineIfNotBlank(MakeAccount);
    writeRLineIfNotBlank(MakeAccountType);
    WriteRLineIfNotBlank(MakeExpDateOrCardType);
    WriteRLineIfNotBlank(MakeName);
    WriteAppIDTrace;
    WriteTCCInvAuthNum;                                // all receipts start with these lines - END
    WriteMerchReceiptLines;                            // Merchant copy only
    WriteTotalAndTranType;
    WriteSigLines;
    WriteFooter;
  end;
end;   { BuildTDBReceipt }

Procedure TReceipt.Build_Receipt(Host_PONumberUsed : string1; Truncate: string);

  function isAnOffline: boolean;
  begin
    if (copy(FInrec.TermRspCode,2,1) = 'B')
      then result := true      { AB means print signature line }
      else result := false;
  end;   { isAnOffline }

  function isSignatureLineRequired: boolean;
  var sSigOnline: string;
  begin
    if (FInRec.VoidCode = 'V') then
      result := false
    else
    begin
      if FInrec.skipSigOnline
        then sSigOnline := 'Y'
        else sSigOnline := 'N';

      with FInrec do
        result := not MTX_Utils.SigNotRequired(trxAmtN, TermRspCode, skipSigOffline, sSigOnline, skipSigAmount,
                                               promptOnManual, (Entry_Track2 = 'M'));

      MsgDebug('isSignatureLineRequired:' + CRLF
             + '    POS(Chr_SigCapture, CmdSequence=' + POS(Chr_SigCapture, CmdSequence).ToString + CRLF
             + '    POS(Chr_SigCapture, ERCSequence=' + POS(Chr_SigCapture, ERCSequence).ToString + CRLF
             + '    FInRec.Entry[1] in [CEV_SWIPED_CUST, CEV_RFID, CEV_MANUAL_POS, CEV_MANUAL_CUST]='
             +                         BoolToStr(FInRec.Entry[1] in [CEV_SWIPED_CUST, CEV_RFID, CEV_MANUAL_POS, CEV_MANUAL_CUST], True) + CRLF
             + '    FInRec.ReqCodeN in All_Return_Trxs=' + BoolToStr(FInRec.ReqCodeN in All_Return_Trxs, True));

      //CPCLIENTS-4236 [MSR, RFID and  Manual Refund show Sig line on reciept when No O tac sequence]
      if result and ((POS(Chr_SigCapture, CmdSequence) = 0) and (POS(Chr_SigCapture, ERCSequence) = 0) and
          (FInRec.Entry[1] in [CEV_SWIPED_CUST, CEV_RFID, CEV_MANUAL_POS, CEV_MANUAL_CUST]) and
          (FInRec.ReqCodeN in All_Return_Trxs)) then
        result := false
    end;
  end;   { isSignatureLineRequired }

  function PrintSigLine(CustOrDrawr: integer; LogIt: boolean = False): boolean;   { TSL-14 redo the whole routine }   // CPCLIENTS-14975
  var
    FieldsRec: TFieldsRecord;

    procedure SetFieldsRec;
    begin
      FieldsRec.IsVantivReceipt := False;
      FieldsRec.IsMerchantCopy := CustOrDrawr = Drawer_Copy;
      FieldsRec.IsFallback := FInRec.Entry = CEV_FALLBACK;
      FieldsRec.IsRefund := FInRec.ReqCodeN in All_Return_Trxs;
      FieldsRec.IsEMVReceipt := False;
      FieldsRec.IsHospitality := GetLaneType(SignOnSet.LaneNumber) = DSLaneHospitality;
      FieldsRec.ErcSequence := ERCSequence;
      FieldsRec.CmdSequence := CmdSequence;
      FieldsRec.SigLineIsNeeded := result;
      FieldsRec.InRec := FInRec;
    end;

  begin
    if LogIt then                                                                                                     // CPCLIENTS-14975 start
    begin
      MsgDebug('PrintSigLine:' + CRLF
           + '    FInRec.termRspCode=' + FInRec.termRspCode + CRLF
           + '    printer_type=' + Printer_Type + CRLF
           + '    FReceiptWithNoSig=' + BoolToStr(FReceiptWithNoSig, True) + CRLF
           + '    FIntOrSBReceipt=' + FIntOrSBReceipt + CRLF
           + '    CustOrDrawr=' + CustOrDrawr.ToString + CRLF
           + '    LaneType=' + GetLaneType(SignOnSet.LaneNumber) + CRLF
           + '    ReqCodeN=' + FInRec.ReqCodeN.ToString);
    end;                                                                                                              // CPCLIENTS-14975 end

    with FInrec do
    begin
      if (copy(termRspCode,1,1) <> 'A')  or  { TSL-14 }    { if declined or }
         (printer_Type = '1')      or        { TSL - printer 150 has it's own rcpt line }
         FReceiptWithNoSig         or        { we called Build_ReceiptNoSig }
         ((FIntOrSBReceipt in Int_Receipt) and (CustOrDrawr = Cust_Copy) and (GetLaneType(SignOnSet.LaneNumber) <> DSLaneHospitality)) or   { no sig on integ cust copy unless hospitality }
         (ReqCodeN in BalInq_Set) then       { TSL-F }     { bal inq, no sig line }
        result := false
      else
      begin
        { this is the Generic print sig line rule: don't print sigs for activations or deactivations }
        if (POS(Chr_SigCapture, CmdSequence) > 0) and not (FInrec.ReqCodeN in Activation_Set+Deactivate_Set) then    { if O tac in sequence }
        begin
          if (mtx_lib.tenderIndex(FInrec) = icheck) then
          begin
            if (ChkSigLineECCOnly = 'Y')
              then result := sameText(termRspCode, rcApprovedByECC)
              else result := (copy(termRspCode,1,1) = 'A');
          end
          else
            result := isSignatureLineRequired;
        end
        else                                              { else }
        begin
          result := false;                                { assume we don't print }
          case mtx_lib.tenderIndex(FInrec) of               { now we have some specific rules by tender }
            idebit    : { no special rules } ;              { see generic rule above }
            icredit   : result := isSignatureLineRequired;        { see CR rules, above }
            ipldebit  : if isAnOffline then result := true; { print if offline }
            iplcredit : result := true;                     { always print }
            // CPCLIENTS-5179 For ATL host, signature should not be printed for Manual - Void
            icheck    : if (TermRspCode = rcApprovedByECC) and not (IsHost('ATL') and (FInRec.VoidCode = 'V')) then result := true; { always print if EChk }
            iebt_fs   : { no special rules } ;              { see generic rule above }
            iebt_ca   : { no special rules } ;              { see generic rule above }
            iuser_1   : { no special rules } ;              { see generic rule above }
            ifleet    : result := true;                     { always print }
            iuser_2   : { no special rules } ;              { see generic rule above }
            iwireless : { no special rules } ;              { see generic rule above }
            iach      : { no special rules } ;              { see generic rule above }
            iConnectPay: { no special rules} ;
            ieWIC:       { no special rules} ;
          end;
        end;
      end;
    end;

    //CPCLIENTS-18975
    if EMVMgr.IsAlwaysPrintSigLineNeeded(FInRec) then
    begin
      SetFieldsRec;
      result := TDCC.MustPrintSignatureLine(FieldsRec);
    end;
    MsgDebug('PrintSigLine Result=' + BoolToStr(Result, True));           // CPCLIENTS-13848
  end;   { PrintSigLine }

  function MakeRetrievalNo: string;
  begin
    with FInrec Do      { see PublixHostUsed in uisoio.pas - same code }
      if (ReqCodeN in Check_Set) then
        //result := rpad(copy(DSLaneMsgBuf.TrxDate,3,4) + SeqNo,12)    //JTG XE6 refactor: how can this ever work? DSLaneMsgBuf NOT set/read anywhere!
        result := rpad(copy(FInrec.TrxDate,3,4) + SeqNo,12)    //JTG XE6 refactor - will remove FInRec anyway
      else
        result := SeqNo + '-' + format('%.3u',[ACISlotNumberN]);
  end;   { MakeRetrievalNo }

  function AddSlotNo: string;
  begin
    with FInrec do
    begin
      if (pos('-', PublixHostRetrievalNo) = 0)
        then result := trim(PublixHostRetrievalNo) + '-' + format('%.3u',[ACISlotNumberN])
        else result := PublixHostRetrievalNo;
    end;
  end;   { AddSlotNo }

  function DateEtc: string;
  begin
    with FInrec do
    begin
      while (Length(Cashier) > 0) and
            (cashier[1] = '0')    do  { delete leading zeros }
        delete(cashier, 1, 1);
      result := copy(trxDate, 3, 2) + '/' + copy(trxDate, 5, 2) + '/' +
                copy(trxDate, 1, 2) + ' ' +
                copy(trxTime, 1, 2) + ':' + copy(trxTime, 3, 2) + ' ' +
                'S' + trim(StoreID) + ' ' +
                'R1' + LaneNo + ' ' +
                format('%.4u',[strToIntDef(PostTransactionNumber, 0)]) + ' ' +
                'C' + format('%.4u',[strToIntDef(trim(cashier), 0)]);       { last 4 digits }
    end;
  end;   { DateEtc }

  function SetTranDescription: string;
  begin
    with FInrec do
      if VoidCode = 'V' then       { set tran description }
        result := 'Void'
      else if (ReqCodeN in (All_Return_Trxs + [PropCrRechargeN,User_1_RechargeN])) then
        result := 'Refund'
      else
        result := 'Purchase';
  end;   { SetTranDescription }

  procedure PublixReceipt;
  var
    AcctStr    : string;
    realAmt    : real;
  begin
    with FInrec do
    begin
      //With Rcpt_Buf Do // [Hint] LANERCPT.INC(550): Variable 'Rcpt_File' is declared but never used in 'receiptClass'
      begin
        Init_For_Copy(Drawer_Copy);
        AcctStr := MakeServerEPSAcctNoForChecks('');

        if (ReqCodeN in cr_set + db_set + ebt_set) then
          WriteRLine(' PRESTO!');

        if (trim(PublixHostRetrievalNo) = '')
          then PublixHostRetrievalNo := MakeRetrievalNo   { set by UIsoio for offline }
          else PublixHostRetrievalNo := AddSlotNo;

        WriteRLine(' Reference #: ' + PublixHostRetrievalNo);
        if IsHost('PBL') then                                                             // TFS-27358
          WriteRLine(' Trace #: ' + IfThen(IsApprovedOffline, AuthCode, RetrievalRefNo))  // TFS-27358
        else                                                                              // TFS-27358
          WriteRLine(' Trace #: ' + SwSeqNo);  { set by UIsoio for offline }
        // TFS-38487 removed two lines
        WriteRLine(' Acct #: ' + AcctStr);
        WriteRLine(' Auth #: ' + authCode);
        WriteRLine(' ');
        WriteRLine(' ' + SetTranDescription + ' ' + CardName);
        printBlankLines(3);
        realAmt := trxAmtN/100.0;
        WriteRLine(stringOfChar(' ',13) + '*** Amount: ' + format('%m',[realAmt]) + '***');
        if PrintSigLine(Drawer_Copy) then
          printSignatureLine;
        WriteRLine(' ');
        WriteRLine(' ' + DateEtc);
        WriteRLine(' ');
        AcctStr := StringOfChar(' ', length(AcctStr));
      end;
    end;
  end;    { PublixReceipt }

  function IsAuthCompletion(FInrec: MdMsgRec): boolean;  //57903
  begin
    result := FInrec.ReqCodeN in AuthComp_Set;
  end;

  function UseOldSeq(FInrec: MdMsgRec): boolean;
  var
    PublixOrBYL: boolean;
  begin
    result := (FInrec.SeqNo = '000000') or (FInrec.VoidCode = 'V') {or IsAuthCompletion(FInRec){57903};    //50269  57570: revert to original logic here..
    PublixOrBYL := IsHost(BYL) or IsHost(PBL) or IsHost(PB2);
    SM(format('ReceiptClass.Build_Receipt.UseOldSeq %s, ReqCodeN[%d] SeqNo[%s] OldSeq[%s] PreAuthSeqNo[%s] MTX_SeqNo[%s] VoidCode[%s] PublixOrBYL[%s]',
              [sTF[result],FInrec.ReqCodeN,FInrec.SeqNo,FInrec.OldSeq,FInrec.ACIPreAuthSeqNo,FInrec.MTX_SeqNo,FInrec.VoidCode,sTF[PublixOrBYL]]));

  end;

  function IsPrintBalance: boolean;        // TSL 3-9-10 take out DB, don't want checking acct bal shown, too personal
  begin
    result := (FInRec.ReqCodeN in User_1_Set {+ db_set} + cr_set) and (rtrim(FInRec.EBT_FS_Balance) <> '')
  end;

  // DOEP-42506 - TGT - Minimal InComm Phone Card Activation Receipt
  procedure WriteInCommPhoneCardLines;
  var i: integer;
  begin
    if FDoingCustomerCopy  then
    begin
      if (FPhoneCardPIN <> '') then
        WriteRLine('PIN: ' + FPhoneCardPIN);
      if FPhoneCardMsg.Count > 0 then              // CPCLIENTS-12067
      begin
        WriteRLine(' ');
        for i := 0 to FPhoneCardMsg.Count - 1 do
          WriteRLine(FPhoneCardMsg[i]);
      end;
    end;
  end;

  // DOEP-42109,42506,42511 - Minimal Gift & Phone Card Activation Receipt
  procedure MakeMinimalReceipt;
  var
    i: integer;
    aText: string;
    EndBalStr: string;                        // CPCLIENTS-14925
  begin
    for i := Cust_Copy to End_Copy do
    begin
      Init_For_Copy(i);
      if (FInrec.ReqCodeN in PhoneCard_Set) then
      begin
        if IsHost(INCHOST) then
        begin
          WriteRLine(' ');
          WriteInCommPhoneCardLines;
        end
        else
          WritePhoneCardLines(i);       // CPCLIENTS-5932
      end
      else
      begin

        WriteRLine(StringOfChar(' ',16) + MakeValuLinkAcctNoForReceipt);
        if (FInrec.EBT_FS_Balance <> '') and (not IsHost(INCHOST)) then
        begin
          if IsXPIEnglish then
          begin                                                                              // CPCLIENTS-14925 start
            if IsHostOneOf(FInrec.HostSuffixCode, 'JET/NPP') then
              EndBalStr := String(Str_BalRemain)
            else
              EndBalStr := String(Str_EndBal);
            aText := Format('%s    %m',[EndBalStr, FInrec.EBT_FS_BalanceN/100])
          end                                                                               // CPCLIENTS-14925 end
          else // DOEP-52302, 54559
          begin
            System.SysUtils.FormatSettings.DecimalSeparator := FRENCH_DECIMAL_SEPARATOR; // set to french decimal seperator (,)    // 828.5
            aText := Format('%s    %f $',['Solde', FInrec.EBT_FS_BalanceN/100]); // DOEP-66756
            System.SysUtils.FormatSettings.DecimalSeparator := DEFAULT_DECIMAL_SEPARATOR; // need to set back to default decimal seperator
          end;
          WriteRLine(StringOfChar(' ',16) + aText);
        end;
        // DOEP-54905, 55000 - remove for voids (SVDot host) since the card will not be active within 24 hours
        if IsApprovedOffline and (not ((FInRec.VoidCode = 'V') and IsHost(SVD))) then
        begin
          // DOEP-56988,57872 - TARGET: Suppress Offline blackhawk verbiage while in training mode
          if FInrec.Trx_Is_Training then
            aText := ''
          else
            aText := MTX_XMLClasses.DSProcBuf.DSProcOfflineBlackhawkReceiptVerbiage;
          WriteRLine(StringOfChar(' ',16) + aText);
        end;
      end;
    end;
  end;

  procedure PrintCardEntryTypeForChase(Which_Copy: integer); // DOEP-53141 // CPCLIENTS-5932
  begin
    SetJsonReceiptBlock(Which_Copy, grbCardInfo);   // CPCLIENTS-5932
    case FInrec.Entry[1] of
      CEV_SWIPED_CUST: WriteRLine(' Entry Method: Swiped');
      CEV_MANUAL_POS, CEV_MANUAL_CUST: WriteRLine(' Entry Method: Manual');  //19105
      CEV_RFID: WriteRLine(' Entry Method: RFID');
    end;
  end;

  procedure DonotPrintLineforBAMS;  // CPCLIENTS-11513
  begin
    if IsHost(CBS) then
      FUseThisOne := False;
  end;

  function IsCBSHostandVoidTransaction: boolean;  // CPCLIENTS-13624
  begin
    Result := (FInRec.HostSuffixCode = CBS) and (FInRec.VoidCode = 'V')
  end;

  function IsCBSVoid: Boolean;  // CPCLIENTS-13929
  begin
    Result := (FInrec.HostSuffixCode = CBS) and (FInrec.VoidCode = 'V');
  end;

  procedure CreateTransactionTypeLine(FWhich_Copy:Integer); // CPCLIENTS-5230	// CPCLIENTS-13624 - Moved method here to access from EBT_Receipts
  var tmpStr: string;
  begin
    with FInrec, Defs do
    begin
      if (FInLineNum = Rcpt_Trx_Type_Line) then
      begin
        if IsHost(ATL) and (ReqCodeN in check_Set) and (SwRspCode = ATLRspCode_ChkPaper) then //8710 paper check to be displayed if rspcode is 95
          WriteRLine(CenterText(Paper_Chk, MAXLINELEN));

        if (ReqCodeN in All_Return_Trxs) then
        begin
          if IsCBSHostandVoidTransaction then // CPCLIENTS-13624
            tmpStr := 'VOID REFUND'
          else
            tmpStr := 'REFUND'
        end
        else if (ReqCodeN = BenefitItemQualificationN) then   // CPCLIENTS-10788 - To Print Item Qualification on Receipt
          tmpStr := 'ITEM QUALIFICATION'
        else if (ReqCodeN in Activation_Set) then                     //CPCLIENTS-10878
          tmpStr := 'ACTIVATION'
        else if (ReqCodeN in Deactivate_Set) then                     //CPCLIENTS-10878
          tmpStr := 'DEACTIVATION'
        else if (ReqCodeN in BalInq_Set) then                         //CPCLIENTS-10878
          tmpStr := 'BALANCE INQUIRY'
        else if (ReqCodeN in [PropCrRechargeN,User_1_RechargeN]) then //CPCLIENTS-10878
          tmpStr := 'RECHARGE'
        else if (ReqCodeN in [User_1_PreactivationN,User_2_PreactivationN]) then  //CPCLIENTS-11186
          tmpStr := 'PRE ACTIVATION'
        else if (ReqCodeN in PreAuth_set) then    //CPCLIENTS-11186
        begin
          if IsCBSHostandVoidTransaction then // CPCLIENTS-13624
            tmpStr := 'VOID PRE-AUTH'
          else
            tmpStr := 'PRE-AUTH'
        end
        else if (ReqCodeN in AuthComp_Set) and (HostSuffixCode = CBS) then  // CPCLIENTS-13502
        begin
          if IsCBSHostandVoidTransaction then // CPCLIENTS-13624
            tmpStr := 'VOID PRE-AUTH COMPLETION'
          else
            tmpStr := 'PRE-AUTH COMPLETION'
        end
        else if (HostSuffixCode = CBS) and (ReqCodeN in EBT_Voucher_Set)  then  // CPCLIENTS-13624
        begin
          if VoidCode = 'V' then
            tmpStr := 'VOID VOUCHER'
          else
            tmpStr := 'VOUCHER';
        end
        else // All_Purchase_Trxs
        begin
          if IsCBSHostandVoidTransaction then // CPCLIENTS-13624
            tmpStr := 'VOID PURCHASE'
          else
            tmpStr := 'PURCHASE';
        end;
        SetJsonReceiptBlock(FWhich_Copy, grbTransType);
        WriteRLine(CenterText(tmpStr, MAXLINELEN));
      end;
    end;
  end;

  procedure CreateEntryMethodLine(FWhich_Copy:Integer); // TFS-177661 // CPCLIENTS-13669 - Moved method from nested proc of Gen_Receipt to make available for EBT_Receipt as well.
  var
    entryMethodStr: string;
    CurrentReceiptBlock: TReceiptBlock; //CPCLIENTS-5932
  begin
   with FInrec, Defs do
    if (FInLineNum = Rcpt_Acct_Line + 1) and (IsHostOneOf(FInRec.HostSuffixCode, 'MPS/CBS')) then // TFS-177661 // CPCLIENTS-13669 added BAMS as EntryMethod need to be printed in new line
    begin
      CurrentReceiptBlock := JsonReceipt.GetCurrentBlock;        // CPCLIENTS-5932
      SetJsonReceiptBlock(FWhich_Copy, grbCardInfo);              // CPCLIENTS-5932
      if (ReqCodeN in Check_Set + Ach_Set) then
      begin
        if (Entry_Check = 'M') then
          entryMethodStr := 'Manual';
      end
      else if ((Entry = 'M') or (Entry = 'C')) and not IsCBSVoid then // CPCLIENTS-13774 - To not to print as Manual for BAMS Pre-AuthCompletion  // CPCLIENTS-13929
        entryMethodStr := 'Manual'
      else if (trim(RFID) <> '') and not IsCBSVoid then // CPCLIENTS-13774 - To not to print Entrymethod for BAMS Void transactions // CPCLIENTS-13929
      begin
        if (RFID = rfTRACK_READ)
        then
        begin
          if Entry = 'E' then         // TFS-29116
            entryMethodStr := 'Chip'     // 'E' -> chip
          else
            entryMethodStr := 'Swipe';
        end
        else if (Entry = 'X') and (VoidCode = 'V') then // CPCLIENTS-18975 Print ENtry methd contactless for Entry X and void txn
          entryMethodStr := 'Cntctless'
        else entryMethodStr := 'RFID';  { means it was RFID }
      end
      else if (voidCode <> 'V') and     { just leave a void with no indicator }
               not ((HostSuffixCode = CBS) and IsAuthCompletion(FInrec) and (GetLaneType(SignOnSet.LaneNumber) = DSLaneHospitality)) then  // CPCLIENTS-13929 - To not to print EntryType for Hospitality Pre-Auth as it is not available in offline
      begin
        if BarCodeScan
          then entryMethodStr := 'Barcode'
          else entryMethodStr := 'Swipe';
      end;
      if not entryMethodStr.IsEmpty then  // CPCLIENTS-13358 - To not to print EntryMethod place holder when EntyMethod value is empty
      begin
        if FInrec.HostSuffixCode = CBS then // CPCLIENTS-15022 - To Print the label as Entry Mode for BAMS CyberSource Host
          entryMethodStr := ' Entry Mode    # ' + entryMethodStr
        else
          entryMethodStr := ' Entry Method : ' + entryMethodStr;
        WriteRLine(entryMethodStr);
        SetJsonReceiptBlock(CurrentReceiptBlock);              // CPCLIENTS-5932
      end;
    end;
  end;

  Procedure Gen_Receipt;
  Var
    Temp_Cust : String[40];
    C_Counter,
    I         : Integer;
    Which_Copy: Integer;
    //GiftBBal  : Integer;
    tmpStr    : string255;
    Last4,OrigLine: string;


  procedure PrintExtraHostLines;   //nested proc for Gen_Receipt
  begin
    SetJsonReceiptBlock(Which_Copy, grbStoreData);    // CPCLIENTS-5932
    with FInrec do
      if IsHost('FLC') then
       begin
          if (trim(FHostLine1) <> '') then WriteRLine(FHostLine1);       { extra line to print from host }
          if (trim(FHostLine2) <> '') then WriteRLine(FHostLine2);       { extra line to print from host }
          if (trim(FHostLine3) <> '') then WriteRLine(FHostLine3);       { extra line to print from host }
        end
        else
        if not (ReqCodeN in Check_Set) then  { TSL-09 }
        begin
          if (trim(FHostLine1) <> '') then WriteRLine(FHostLine1);       { extra line to print from host }
          if (trim(FHostLine2) <> '') then WriteRLine(FHostLine2);       { extra line to print from host }
          if (trim(FHostLine3) <> '') then WriteRLine(FHostLine3);       { extra line to print from host }
        end;
  end;

  procedure PrintDCCLines1(Which_Copy: integer);    //11758 // CPCLIENTS-5932
  begin
    try
      MsgDebug(format('ReceiptClass.PrintDCCLines1: isDCCTrans[%s]',[sTF[IsDCC {FInrec.isDCCTrans}]]));  // TFS-22487
      if IsDCC {FInrec.isDCCTrans} then   //11758, since DCC is ONLY for credit transactions, we can use this line!   // TFS-22487
        begin
        SetJsonReceiptBlock(Which_Copy, grbSubtotalAmount);              // CPCLIENTS-5932
        MsgLog(format('ReceiptClass.PrintDCCLines1: CurrencyCode[%s] CurrencySymbol[%s] CodeAndAmount[%s] ExchangeRate[%n]',[FInrec.DCC_CurrencyCode,FInrec.DCC_CurrencySymbol,FInrec.DCC_CodeAndAmount,FInrec.DCC_ExchangeRate]));
        WriteRLine(format(' %s',[FInrec.DCC_CurrencyName]));
        WriteRLine(format(' Exchange Rate*   %n',[FInrec.DCC_ExchangeRate]));
        WriteRLine(format(' Rate includes %n%% margin',[FInrec.DCC_ExchangeRateMarginPercentage]));
        WriteRLine(' ');
        end;
    except on e: exception do
      MsgLog(format('ReceiptClass.PrintDCCLines1 ERROR [%s]',[e.Message]));
    end;
  end;
  
  procedure CreatePurchaseAmountLine;   //nested proc for Gen_Receipt

    procedure AddLine;
    begin
        Format_Amount(FInrec.TrxAmtN-FInrec.CashBackN-FInrec.FeeAmtN-FTip, Amt_Buf);
      if Length(Amt_buf) > 0 then
      begin
        Move(Amt_Buf[1], Temp_Line[Defs.Rcpt_Purch_Amt_Pos], Length(Amt_Buf));
        end;
    end;
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_Purch_Amt_Line) then
        begin
        SetJsonReceiptBlock(Which_Copy, grbTotalAmount);               // CPCLIENTS-5932
        if (ReqCodeN in [User_1_DeActivateN]){ no bal for this }     or
           ((ReqCodeN in User_1_Set) and (voidCode = 'V') and IsHost('MPS')) then  { TSL-14 }
          EBT_FS_Balance := '';

        if IsPrintBalance then { balance for receipt }
        begin
          if (ReqCodeN in BalInq_Set) then
             TrxAmtN := EBT_FS_BalanceN
          else           { all others }
          begin
            if (ReqCodeN in [User_1_VoiceN,User_1_DeactivateN]) and (TrxAmtN = 0) then
              TrxAmtN := EBT_FS_BalanceN;
            if CalcStartingBal and (copy(TermRspCode,1,1) = 'A') then  { appr - compute beginbal } { TSL-03 }
            begin
              case ReqCodeN of
                crPurchN,crVoiceAuthN,dbPurchN,
                User_1_PreAuthN,User_1_PreAuthCompN,User_1_VoiceN,User_1_PurchN:
                  if (VoidCode = 'V')
                    then Format_Amount(EBT_FS_BalanceN - TrxAmtN, Amt_Buf)
                    else Format_Amount(EBT_FS_BalanceN + TrxAmtN, Amt_Buf);
                crReturnN,dbReturnN,User_1_ReturnN,User_1_RechargeN:
                  if (VoidCode = 'V')
                    then Format_Amount(EBT_FS_BalanceN + TrxAmtN, Amt_Buf)
                    else Format_Amount(EBT_FS_BalanceN - TrxAmtN, Amt_Buf);
              end;
              Move(Str_BeginBal[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9);
              Move(Amt_Buf[1], Temp_Line[Rcpt_Purch_Amt_Pos], Length(Amt_Buf));
              WriteRLine(Temp_Line);
            end;
          end;
        end;

        if (signOnSet.ReceiptHospitalityIncludeTip) and PrintTipLineOnReceipt then // CPCLIENTS-1942
        begin
          if (FTip = 0) and
            ((VoidCode = 'V') or (ReqCodeN in All_Return_Trxs)) then
          begin
            FUseThisOne := False
          end
          else
          begin
            SetJsonReceiptBlock(Which_Copy, grbSubtotalAmount);               // CPCLIENTS-5932
            Move(Str_SubTotal[1],Temp_Line[Rcpt_Amt_Desc_Pos],9);
            AddLine;
          end;
        end
        else if (FTip = 0) and
            (((not (ReqCodeN in All_Purchase_Trxs)) and (not (ReqCodeN in All_Voice_Trxs))) or
            (VoidCode = 'V') or
            ((CashbackN = 0) and (FeeAmtN = 0)))
            and (not (Ishost(JET) and (FeeAmtN <> 0)))  then         //15330 create purchase amount line for JET/NPP if FeeAmount is not 0
          FUseThisOne := False                       { Skip this line        }
        else
        Begin
          Temp_Line := Receipt_A[Defs.Rcpt_Purch_Amt_Line];
          SetJsonReceiptBlock(Which_Copy, grbSubtotalAmount);               // CPCLIENTS-5932
          AddLine;
        End;
      end;
  end;

  procedure CreateCashbackAmountLine;    //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_CashB_Amt_Line) then
        begin
        if ((not (ReqCodeN in All_Purchase_Trxs)) and (not (ReqCodeN in All_Voice_Trxs))) or (CashbackN = 0) then  // CPCLIENTS-2789 - Removed condition or (VoidCode = 'V')
          FUseThisOne := False                       { Skip this line        }
        else
        begin
          Format_Amount(CashbackN, Amt_Buf);
          Move(Amt_Buf[1], Temp_Line[Rcpt_Cashb_Amt_Pos], Length(Amt_Buf));
          SetJsonReceiptBlock(Which_Copy, grbCashbackAmount);               // CPCLIENTS-5932
        end;
        end;
  end;

  procedure CreateFeeAmountLine;    //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_Fee_Amt_Line) then
      begin
        if (FeeAmtN = 0) then
          FUseThisOne := False                       { Skip this line        }
        else
        begin
          Format_Amount(FeeAmtN, Amt_Buf);
          Move(Amt_Buf[1], Temp_Line[Rcpt_Cashb_Amt_Pos], Length(Amt_Buf));
          SetJsonReceiptBlock(Which_Copy, grbSubtotalAmount);               // CPCLIENTS-5932
        end;
      end;
  end;

  procedure CreateTipAmountLine;    //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_Tip_Amt_Line) then
      begin
        SetJsonReceiptBlock(Which_Copy, grbTipInfo);               // CPCLIENTS-5932
        if (FTip <> 0) or
            (signOnSet.ReceiptHospitalityIncludeTip and PrintTipLineOnReceipt and // CPCLIENTS-1942
            NOT ((VoidCode = 'V') or (ReqCodeN in All_Return_Trxs))) then
        begin
              if (FTip = 0) then
          begin
            printBlankLines(1);
            Amt_Buf := Str_BlankAmountLine;
          end
          else
          Format_Amount(FTip, Amt_Buf);
          Move(Amt_Buf[1], Temp_Line[Rcpt_Cashb_Amt_Pos], Length(Amt_Buf));
        end
        else
          FUseThisOne := False                       { Skip this line        }
      end;
  end;

  procedure PossiblySkipUnderscoreLine;   //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_Underscore_Line) then
        if ((FTip = 0)) and
           (((not (ReqCodeN in All_Purchase_Trxs)) and (not (ReqCodeN in All_Voice_Trxs))) or
           (VoidCode = 'V')                                                               or
           ((CashbackN = 0) and (FeeAmtN = 0))) and
           (not (Ishost(JET) and (FeeAmtN <> 0))) then            //15330 skip underscore line for JET/NPP if Tip and FeeAmount is 0
          FUseThisOne := False;                      { Skip this line        }
  end;

  procedure CreateTotalAmountLine;     //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_Total_Amt_Line) then
      begin
        SetJsonReceiptBlock(Which_Copy, grbTotalAmount);               // CPCLIENTS-5932
        if (ReqCodeN = User_1_DeActivateN) and (TrxAmtN = 0) then   { TSL-07 }
          TrxAmtN := EBT_FS_BalanceN;

      if signOnSet.ReceiptHospitalityIncludeTip and (FTip = 0)and (FInrec.FeeAmtN = 0) and PrintTipLineOnReceipt and // CPCLIENTS-1942  //14301 print blank line if FeeAmount is 0
            NOT ((VoidCode = 'V') or (ReqCodeN in All_Return_Trxs)) then
        begin
          printBlankLines(1);
          Move(Str_BlankAmountLine[0], Temp_Line[Rcpt_Total_Amt_Pos], Length(Str_BlankAmountLine));
        end
        else
        begin
          if (VoidCode = 'V') then
          begin
            if not (HostSuffixCode = CBS) then // CPCLIENTS-13624
            begin
              if (ReqCodeN in All_Return_Trxs) then
              Move(Str_VoidRetrn[1], Temp_line[Rcpt_Amt_Desc_Pos], 9)
              else if (ReqCodeN = User_1_ActivationN) then  { gift cards} { TSL-03 }
              Move(Str_DeActivation[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9)
              else if (ReqCodeN in [PropCrRechargeN,User_1_RechargeN]) then
              Move(Str_VoidRechg[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9)
              else if (ReqCodeN in PreAuth_Set) then
              Move(Str_VoidPreAuth[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9)
              else
              Move(Str_VoidPurch[1], Temp_line[Rcpt_Amt_Desc_Pos], 9);
            end
            else
               Move(Str_Total[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9);  // CPCLIENTS-13624
          end
          else if (ReqCodeN in All_Return_Trxs) and not(HostSuffixCode = CBS) then  // CPCLIENTS-13624
            Move(Str_Return[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9)
          else if (CashbackN <> 0) or (FeeAmtN <> 0) or (FTip <> 0) then
            Move(Str_Total[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9)
          else if (ReqCodeN in [PropCrRechargeN,User_1_RechargeN]) then
            Move(Str_Recharge[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9)
          else if (ReqCodeN in Activation_Set) then
            Move(Str_Activate[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9)
          else if (ReqCodeN = User_1_PreActivationN) then          { TSL-10 }
            Move(Str_PreActivate[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9)
          else if (ReqCodeN = User_1_ReActivationN) then
            Move(Str_ReActivate[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9)
          else if (ReqCodeN in Deactivate_Set) then
            Move(Str_Deactivation[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9)
          else if (ReqCodeN in All_Voice_Trxs) and not (Ishost(CBS) and (FInrec.ReqCodeN in EBT_Set)) then       { TSL-E } // CPCLIENTS-15316 not to print 'FrcPurch' label for CBS host
            Move(Str_Voice[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9){ TSL-E }
          else if (ReqCodeN in BalInq_Set) and not (Ishost(CBS) and (FInrec.ReqCodeN in EBT_Set)) then          { TSL-F }  // CPCLIENTS-15022
            Move(Str_Balance[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9)
          else if (ReqCodeN = User_2_OverrideN) then
            Move(Str_Refresh[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9)
          else if (ReqCodeN in PreAuth_Set) and not(HostSuffixCode = CBS) then  // CPCLIENTS-13624
            Move(Str_PreAuth[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9)
          else
            Move(Str_Total[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9); { Default to 'Total' } //CPCLIENTS-1422
        Format_Amount(TrxAmtN, Amt_Buf);
		end;
        Move(Amt_Buf[1], Temp_Line[Rcpt_Total_Amt_Pos], Length(Amt_Buf));
      end;
  end;

  procedure CreatePinUsedLine;      //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_Pin_Used_Line)   and
         not(HostSuffixCode = CBS) then // CPCLIENTS-13625 - To not to print PIN used on receipt for MSR transactions when PIN TAC configured and PIN entered
      begin
        SetJsonReceiptBlock(Which_Copy, grbSigLines);              // CPCLIENTS-5932
        if (Rtrim(PIN) <> '') then
          Move(Str_PinUsed[1],Temp_Line[Rcpt_Pin_Used_Pos], Length(Str_PinUsed))
        else
        if (ReqCodeN in Check_Set)       and   { TSL-05 }
           (Printer_Type in ['1', '5'])  then  { printers 150 & 500 skip blanks }
          FUseThisOne := False;               { Skip this line        }
      end;
   end;

  procedure CreateCardTypeLine;    //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_Card_Type_Line) then
      begin
        SetJsonReceiptBlock(Which_Copy, grbCardInfo);             // CPCLIENTS-5932
        if FInrec.Trx_Is_Training then
        begin
          WriteRLine(Str_Training);
          WriteRLine(' ');
        end;

        // HAM 11/17/2016 TFS 123453, check to see if the Card Name is missing, if it is then
        // populate with the one from the XMLCardProcessingProfile which is guarenteed to be correct
        // if that cannnot be found then default to the Card ID which is returned and is the response record.
        if length( FInRec.CardName ) = 0 then
        begin
          if assigned( XMLCardProcessingProfile ) and assigned( XMLCardProcessingProfile.FindFCT( RspMMR.CardProcessingID ))then
          begin
            FInRec.CardName := XMLCardProcessingProfile.FindFCT( RspMMR.CardProcessingID ).CardName;
          end
          else
          // Last Chance Saloon
          begin
            FInRec.CardName := RspMMR.CardProcessingID;
          end;
        end;

        if Ishost(CHS) and IsPinlessDebit then                    //18233 print pinless for chase pinless trx
          FInRec.CardName := PINLESSDEBIT;

        //  Delete NCR Tender change numerics JGS-A
        // HAM 11/17/2016 - create explicit reference to FInRec which was confusing.
        while (length(FInRec.CardName) > 0) and (FInRec.CardName[1] >= '0') and (FInRec.CardName[1] <= '9') do
        begin
          Delete( FInRec.CardName, 1, 1);
        end;

        //Move(CardName[1], Temp_Line[Rcpt_Card_Type_Pos], Length(CardName));  // added to account number below
      end;
  end;

  procedure CreateAccountLine;        //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_Acct_Line) then
      begin
        SetJsonReceiptBlock(Which_Copy, grbCardInfo);             // CPCLIENTS-5932

        if((IsApprovedOffline()  and (FInRec.ReqCodeN = CrPreAuthCompN )) or  // CPCLIENTS-14976 - To Print PAN number for offline Transactions.
          ((FInRec.ReqCodeN = CrPreAuthCompN ) and (HostSuffixCode = CBS) and (GetLaneType(SignOnSet.LaneNumber) = DSLaneHospitality)))then // CPCLIENTS-13502 - Added missing code from 8287 for which entry mode (S) is not printed on reciepts
        begin
          Temp_Line := CardName + StringOfChar(' ', ( length( Temp_Line ) + Length( CardName )));
          exit;
        end;

        PrintDCCLines1(Which_Copy);    //CPCLIENTS-5932  //11758.. print the DCC lines JUST BEFORE THE Card Type!
        //SM(format('DEBUG 57775 - ProgramID[%s]',[ProgramID]));
        if IsValuLink(programID, ReqCodeN) or IsTGTorSBUX(programID, ReqCodeN) or IsWholeFoodsMarketProgID(programID) or   //57775
           IsINCProgIDForSVD(programID, HostSuffixCode) then
          Temp_Cust := MakeValuLinkAcctNoForReceipt
        else
        begin
          //SM(format('DEBUG 10425 ReceiptClass Rcpt_Acct_Line XingVar[%s] IsTokenTransaction[%s]',[sTF[XingVar],sTF[DllTypes.IsTokenTransaction]]));
          // TFS-10656 - fixed to use SizeOf for unicode (moving 8 bytes instead of 4)
          setLength(Temp_Cust, SizeOf(FInRec.AcctNoLast4));
          move(FInRec.AcctNoLast4, Temp_Cust[1], SizeOf(FInRec.AcctNoLast4));
          (*if DLLTypes.IsTokenTransaction then                  //TFS 10425 10426
            begin
            Temp_Cust := trim(Temp_Cust);
            if Temp_Cust = '' then
              Temp_Cust := MidStr(MTXEncryptionUtils.RetrieveMDMsgPersonalAccountNum(FinRec), FInRec.PanLen - 4, 4);
            Temp_Cust := StringOfChar('X',12) + Temp_Cust;
            //S := MTXEncryptionUtils.RetrieveMDMsgPersonalAccountNum(FinRec);  //10425.. for Token Transactions these fields are empty, len 0
            //SM(format('DEBUG 10425 Rcpt_Acct_Line IsTokenTransaction: PAN[%s] PanLen[%d]',[S,FInRec.PanLen]));
            end
          else *)if XingVar then
          begin
            if IsHost('ADS') and (FInRec.ReqCodeN in Prop_Cr_Set) and (FInRec.Entry_Track2 <> 'M') then
            begin                        // don't want last digit of Track2 PAN on the receipt
              Temp_Cust := MidStr(MTXEncryptionUtils.RetrieveMDMsgPersonalAccountNum(FinRec), FInRec.PanLen - 4, 4);
              Temp_Cust := StringOfChar('X', FInRec.PanLen - 5) + Temp_Cust;
            end
            else
              Temp_Cust := GetMaskedAcctNo;
          end
          else
            begin
            Temp_Cust := Trim(MTXEncryptionUtils.RetrieveMdMsgPersonalAccountNum(FInRec));    //this is where we set the receipt line
            if FInRec.AcctNoLast4 <> '' then
              begin
              Temp_Cust := GetMaskedAcctNo;
              //SM(format('DEBUG 46336: ReceiptClass.BuildReceipt.GenReceipt: RESETTING to [%s]',[Temp_Cust]));    //46336
              end;
            end;
          if (Length(Temp_Cust) = 20) then
            Delete(Temp_Cust, 1, 1);
        end;

        if IsWholeFoodsMarketProgID(programID) then
          begin
          Temp_Cust := MakeValuLinkAcctNoForReceipt;                         //57775  delete this line
          //SM(format('DEBUG 57775: MakeValuLinkAcctNoForReceipt ProgramID[%s] PAN[%s]',[ProgramID,Temp_Cust])); //57775  delete this line
          end;

        Temp_Cust := MakeServerEPSAcctNoForChecks(Temp_Cust);
{$IFDEF MTXEPSDLL}
        if (FInRec.ReqCodeN in User_1_Set) and (FinRec.VoidCode = 'V') then // use ServerEPS response
          Temp_Cust := GetOriginalTransactionData(FRspMMR.OriginalTransactionData, 'Bf') + Temp_Cust
        else
{$ENDIF}
      if not IsHostOneOf(FInRec.HostSuffixCode, 'MPS/CBS') then // TFS-177661 // CPCLIENTS-13669 added BAMS as EntryMethod been printed in another line
      begin
        if (ReqCodeN in Check_Set + Ach_Set) then
        begin
          if (Entry_Check = 'M') then
            Temp_Cust := 'M' + Temp_Cust;
        end
        else if (Entry = CEV_MANUAL_POS) or (Entry = CEV_MANUAL_CUST) then
          Temp_Cust := 'M' + Temp_Cust
        else if (trim(RFID) <> '') then
        begin
          if (RFID = rfTRACK_READ)
            then
              begin
                if FInRec.Entry = 'E' then         // TFS-29116 / 42272
                  Temp_Cust := 'E' + Temp_Cust     // 'E' -> chip
                else
                  Temp_Cust := 'S' + Temp_Cust;
              end
            else Temp_Cust := 'R' + Temp_Cust;  { means it was RFID }
        end
        else if (voidCode <> 'V') then     { just leave a void with no indicator }
        begin
          if BarCodeScan
            then Temp_Cust := 'B' + Temp_Cust
          else if Entry = CEV_RFID then// CPCLIENTS-8463
            Temp_Cust := 'R' + Temp_Cust
          else Temp_Cust := 'S' + Temp_Cust;
        end;
      end;
        // DOEP-53098: Chase - Full card number should be printed on the receipt on the following transactions.
        if IsHost('CHS') and (ReqCodeN in [User_1_ActivationN, User_1_RechargeN]) then
          Temp_Cust := Trim(MTXEncryptionUtils.RetrieveMdMsgPersonalAccountNum(FInRec));

        // CPCLIENTS-5179 For ATL host paper transaction, swipe transaction shoule be prefixed by 'S'
        // CPCLIENTS-5350 Account # should not be prefixed with 'S' if void by seq #
        if IsHost('ATL') and (Entry_Check = 'T') and
          not ((FInrec.VoidCode = 'V') and ((StrtoIntDef(FInRec.SeqNo,0) - 1) <>  StrtoIntDef(FInRec.OldSeq,0))) then
            Temp_Cust := 'S' + Temp_Cust;

        if (FInrec.HostSuffixCode = CBS) then // CPCLIENTS-15022 - To Print CardType and CardName in different lines for BAMS CyberSource Host
        begin          
          Temp_Line := ' Card Type     #                     ';
          if length(CardName) > 0 then
            Move(CardName[1], Temp_Line[Rcpt_Auth_Pos + 9], Length(CardName));
          if (Temp_Line <> '') then
            WriteRLine(Temp_Line);

          Temp_Line := ' Card Number   #                     ';
          if length(Temp_Cust) > 0 then
            Move(Temp_Cust[1], Temp_Line[Rcpt_Auth_Pos + 9], Length(Temp_Cust));
          if (Temp_Line <> '') then
            WriteRLine(Temp_Line);

          FUseThisOne := False;
        end
        else
        begin
          Temp_Cust := trim(CardName) + ' #' + Temp_Cust;  // we now make this whole line
          // we now make this whole line
          if (length(Temp_Cust) > 0) then                                                         // CPCLIENTS-11376 begin
          begin
            if (length(Temp_Cust) > MAXLINELEN - 2) and (trim(CardName) > '') then
            begin
              // Truncate the card name to get the account number to fit on the receipt line.
              if Pos(CardName, Temp_Cust) > 0 then
                Delete(Temp_Cust, Pos(CardName, Temp_Cust) + length(CardName) - (length(Temp_Cust) - MAXLINELEN + 3),
                      (length(Temp_Cust) - MAXLINELEN + 3));
              Temp_Cust := Copy(Temp_Cust, 1, 40);                                                // CPCLIENTS-11376
            end;

          //DOEP-46336
  {$IFDEF MTXEPSDLL}
          if (FInRec.ReqCodeN = User_1_ReActivationN) then
            Last4 := FRspMMR.AccountLast4  // use ServerEPS response
          else
  {$ENDIF}
            Last4 := FInRec.AcctNoLast4;   //turn array of char into a string for easier manipulation
          Last4 := trim(Last4);
          OrigLine := Temp_Cust;
          if (FInRec.ReqCodeN in Prop_Cr_Set) and (length(Last4) = 4) and (StrToIntDef(Last4,-1) > 0) then  // only for Credit and if we have a valid last 4 from ServerEPS, has to be EXACTLY 4 digits
            // TFS-10656 - fixed to use SizeOf for unicode (moving 8 bytes instead of 4)
            //Move(Last4[1],Temp_Cust[length(Temp_Cust)-3],4);   //46336 end
            Move(Last4[1],Temp_Cust[SizeOf(Temp_Cust)-6],SizeOf(Last4));   //46336 end

          //SM(format('DEBUG 46336 - Last4[%s] Orig Line[%s] New Line[%s]',[Last4,OrigLine,Temp_Cust]));

          if (length(Temp_Cust) > 0) then
            Move(Temp_Cust[1], Temp_Line[Rcpt_Card_Type_Pos], Length(Temp_Cust));
            //Move(Temp_Cust[1], Temp_Line[Rcpt_Acct_Pos], Length(Temp_Cust));
          Temp_Cust := StringOfChar(' ', length(Temp_Cust));
        end;
      end; // end of if (FInLineNum = Rcpt_Acct_Line) then
    end;
  end;

  procedure PrintDCCLines2(Which_Copy: integer);    //11758 // CPCLIENTS-5932
  var
    sForeignAmount: string;
  begin
    try
      if IsDCC {FInrec.isDCCTrans} then   //11758, since DCC is ONLY for credit transactions, we can use this line!   // TFS-22487
        begin
        SetJsonReceiptBlock(Which_Copy, grbSubtotalAmount);              // CPCLIENTS-5932
        sForeignAmount := format('%8.*f',[FInrec.DCC_DecimalDigits,FInrec.DCC_ExchangeRate*(FInrec.TrxAmtN/100)]);
        WriteRLine(format(' Transaction Currency  %s %s',[FInrec.DCC_CurrencyCode,sForeignAmount]));
        end;
    except on e: exception do
      MsgLog(format('ReceiptClass.PrintDCCLines2 ERROR [%s]',[e.Message]));
    end;
  end;

  procedure CreateAuthLine;    //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      if FInLineNum = Rcpt_Auth_Line then
      begin
        if (HostSuffixCode = CBS) and (IsAuthCompletion(FInrec) or (VoidCode = 'V')) then // CPCLIENTS-13358 - To not to print AuthCode for Pre-Auth Completion for BAMS // CPCLIENTS-13774
          FUseThisOne := False
        else
        begin
          if (FInrec.HostSuffixCode = CBS) and ContainsText(Temp_Line, 'Auth #') then // CPCLIENTS-15022 - To Print Approval Code in place of Auth # for BAMS CyberSource host
          begin            
            Temp_Line := ' Approval Code #                     ';
            if length(AuthCode) > 0 then
              Move(AuthCode[1], Temp_Line[Rcpt_Auth_Pos + 9], Length(AuthCode))
            else begin
              Temp_Line := '';
              //exit;
            end;
          end
          else if length(AuthCode) > 0 then
            Move(AuthCode[1], Temp_Line[Rcpt_Auth_Pos], Length(AuthCode));
          if IsHost('CHS') then // DOEP-53139
            begin
            PrintCardEntryTypeForChase(Which_Copy);
            if (ReqCodeN in Db_Set) or (ReqCodeN in User_1_Set) and (Trim(FInRec.TraceID) <> '') then
              WriteRLine(' Trace Number: ' + Trim(FInRec.TraceID));
            end;
          SetJsonReceiptBlock(Which_Copy, grbCardInfo);              // CPCLIENTS-5932 in case it got changed above
          PrintDCCLines2(Which_Copy);   //11758.. this will print BEFORE the Temp_Line prints (this call only formats the Temp_Line) //CPCLIENTS-5932
        end;
      end;
  end;

  procedure CreateFromLine;  //nested proc for Gen_Receipt

  function _should_print_exp() : Boolean; register;
  const
    EMV  = 'E';
    RFID = 'R';
  begin
    result :=  (FInRec.ReqCodeN  = CrAdjustmentN)
           and (FInRec.Entry <> EMV)
           and (FInRec.Entry <> RFID)
           //and (FInRec.ReqType  <> OfflineReq)
           ;
  end;

  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_From_Line) then
      begin
        SetJsonReceiptBlock(Which_Copy, grbCardInfo);              // CPCLIENTS-5932
        if (ReqCodeN in Db_Set) then
        begin
          if (AcctInd = Acct_Check) then
            Move(Str_Checking[1], Temp_Line[Rcpt_From_Where_Pos], 9)
          else
          if (AcctInd = Acct_Save) then
            Move(Str_Savings[1], Temp_Line[Rcpt_From_Where_Pos], 9);
        end
        else
        begin
          if (ReqCodeN in Cr_Set) then
          begin
            if FInrec.HostSuffixCode = CBS then // CPCLIENTS-15022 - To not to print exp date for BAMS CyberSource host
              Exit;

            Move(Str_Blank20[1], Temp_Line[Rcpt_Exp_Date_Pos], 20); {Clear line}
            Move(Str_Blank20[1], Temp_Line[Rcpt_Exp_Date_Pos+1], 20); //CPCLIENTS-2184

            // HACK TFS#121137 - forcing this to run for Online, MSR, adjustment transactions
            if (_should_print_exp() or ((ExpDate <> '') and (ExpDate <> '0000'))) and
               (not IsHostOneOf(HostSuffixCode, 'MPS/CBS')) then // TFS-177661 // CPCLIENTS-11513 - To not to print Expiry Date for BAMS CyberGateway Host
            begin
              Move(Str_ExpDate[1], Temp_Line[Rcpt_Exp_Date_Pos], Length(Str_ExpDate));
              Move(Asterisk[1], Temp_Line[Rcpt_Exp_Date_Pos + 9], 2);
              Move(Asterisk[1], Temp_Line[Rcpt_Exp_Date_Pos + 12], 2);
            end;
          end;
        end;
      end;
  end;

  procedure CreateLaneLine;  //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_Lane_Line) then
      begin
        SetJsonReceiptBlock(Which_Copy, grbStoreData);             // CPCLIENTS-5932
        // DOEP-65125, lane # length increased, so don't use hard-coded length 2 any more. Use Length of LaneNo variable
        Move(LaneNo[1], Temp_Line[Rcpt_Lane_Pos], Length(LaneNo));
        //Move(LaneNo[1], Temp_Line[Rcpt_Lane_Pos], 2);
        DonotPrintLineforBAMS;  // CPCLIENTS-11513
      end;
  end;
		
  procedure CreateBAMS_AVS_CVV_Line;   //nested proc for Gen_Receipt  //13626 create AVS and Cvv line for CBS host(BAMS)
  var
    Which_Copy: Integer;
    tmpstr: string;
  begin
    if IsHost(CBS) and ((FInRec.Entry = CEV_MANUAL_POS) or (FInRec.Entry = CEV_MANUAL_CUST)) then
    begin
      tmpstr := '';
      if (not FRspMMR.AvsResponse.IsEmpty) then tmpstr := ' AVS  #  ' + FRspMMR.AvsResponse + '      ';
      if (not FRspMMR.CVVResponse.IsEmpty) then tmpstr := tmpstr +  ' Cvv  #  ' + FRspMMR.CVVResponse;

      with Defs do
        if (FInLineNum = Rcpt_WinEPSSeq_Line +1 ) and (not tmpstr.IsEmpty) then
        begin
          SetJsonReceiptBlock(Which_Copy, grbCardInfo);
          WriteRLine(tmpstr);
        end;
    end;
  end;

  procedure CreateBAMSHostRspMsgLine;  //nested proc for Gen_Receipt   // 13626 create host response literal line for CBS host(BAMS)
  var
    Which_Copy: Integer;
    tmpStr : string;
  begin
    if IsHost(CBS) then
    begin
      with FInrec, Defs do
        if (FInLineNum = Rcpt_Auth_Line +1 ) then
        begin
          SetJsonReceiptBlock(Which_Copy, grbCardInfo);
          if (copy(TermRspCode,1,1) = 'A') then tmpStr := ' ' + Str_Approved
          else tmpStr := ' ' + Str_Declined0;
          WriteRLine(tmpStr);
        end;
    end;
  end;

  procedure PrintEBTVoucherNumLine;                 //CPCLIENTS-15371
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_Auth_Line +1 ) and (trim(FInrec.EBT_Voucher_Num) <> '') then
      begin
        SetJsonReceiptBlock(Which_Copy, grbCardInfo);
        WriteRLine(' Voucher  #  ' + trim(FInrec.EBT_Voucher_Num));
      end;
  end;

  procedure CreateCashierLine;     //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_Cashier_Line) then
      begin
        SetJsonReceiptBlock(Which_Copy, grbStoreData);             // CPCLIENTS-5932
        While (Length(Cashier) > 0) and (Cashier[1] = '0') Do
          Delete(Cashier, 1, 1);
        Cashier := BRFill(Cashier, 9);
        if (length(Cashier) > 0) then
          Move(Cashier[1], Temp_Line[Rcpt_Cashier_Pos], Length(Cashier));
        DonotPrintLineforBAMS;  // CPCLIENTS-11513
      end;
  end;

  procedure CreateDateLine;  //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      begin
      TrxDate := rpad(TrxDate, 6);
      if (FInLineNum = Rcpt_Date_Line) then { In_Date is YYMMDD }
      begin
        SetJsonReceiptBlock(Which_Copy, grbStoreData);             // CPCLIENTS-5932
        Move(TrxDate[3], Temp_Line[Rcpt_Date_Pos+0], 2);
        Move(TrxDate[5], Temp_Line[Rcpt_Date_Pos+3], 2);
        Move(TrxDate[1], Temp_Line[Rcpt_Date_Pos+6], 2);
      end;
      end;
  end;

  procedure CreateTimeLine;     //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      begin
      TrxTime := rpad(TrxTime, 6);
      if (FInLineNum = Rcpt_Time_Line) then
      begin
        SetJsonReceiptBlock(Which_Copy, grbStoreData);             // CPCLIENTS-5932
        Move(TrxTime[1], Temp_Line[Rcpt_Time_Pos+0], 2);
        Move(TrxTime[3], Temp_Line[Rcpt_Time_Pos+3], 2);
      end;
      end;
  end;

  procedure CreateReferenceLine;    //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_Ref_Line) then
      begin
        SetJsonReceiptBlock(Which_Copy, grbStoreData);              // CPCLIENTS-5932
        if publixHostDefined(FInrec.HostSuffixCode) then
        begin
           tmpStr := SeqNo + '-0' + LaneNo;
           move(tmpStr[1], Temp_Line[Rcpt_Ref_Pos], Length(tmpStr));
        end
        else
        begin   //57903
        //SM(format('DEBUG 57903 - ReqCodeN[%d] PreAuthSeqNo[%s] SwSeqNo[%s] OldSeq[%s] SeqNo[%s] MTX_SeqNo[%s] MTXRspCodeN[%d]',[ReqCodeN,ACIPreAuthSeqNo,SwSeqNo,OldSeq,SeqNo,MTX_SeqNo,MTXRspCodeN]));
        if (SwSeqNo <> '')                   and
           (SentToModemYN = 'Y')             and   { JGS-X }
           (MTXRspCodeN <> OfflineAuthCodeN) then  { TSL-Y offline appr }
          Move(SwSeqNo[1], Temp_Line[Rcpt_Ref_Pos], Length(SwSeqNo))
        else
        begin
          if IsAuthCompletion(FInRec) then
            Move(ACIPreAuthSeqNo[1], Temp_Line[Rcpt_Ref_Pos], Length(OldSeq))
          else if UseOldSeq(FInrec) then   { TSL-Y offline appr }
            Move(OldSeq[1], Temp_Line[Rcpt_Ref_Pos], Length(OldSeq))
          else if TDCC.IsChaseDebitTrans(FInRec) and (Trim(TraceID) <> '') then     // CPCLIENTS-7092
          begin
            Move(TraceId[1], Temp_Line[Rcpt_Ref_Pos], Length(TraceId));
            if Pos('Ref/Seq', Temp_Line) > 0 then
              Temp_Line := ReplaceStr(Temp_Line, 'Ref/Seq', 'Trace  ');
          end
          else
            Move(SeqNo[1], Temp_Line[Rcpt_Ref_Pos], Length(SeqNo));
        end;
        end;
      end;
  end;

  procedure CreateWinEPSSequenceLine;   //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_WinEPSSeq_Line) then   { TSL-01 }
        begin
        SetJsonReceiptBlock(Which_Copy, grbCardInfo);              // CPCLIENTS-5932
        OldSeq := zfill(OldSeq, 6);
        if (ReqCodeN in Check_Set)       and   { TSL-09 }
           (Printer_Type in ['1', '5']) or  { printers 150 & 500 skip blanks }
           (CurrencyCode = 'CAD') or (CurrencyCode = '124') and IsHost('CHS') then // TFS-29130 - skip if canada chase host
          FUseThisOne := False                       { Skip this line        }
        else if (SeqNo = '000000') or (trim(SeqNo) = '')
          then Move(OldSeq[1], Temp_Line[Rcpt_WinEPSSeq_Pos], Length(OldSeq))
        else Move(SeqNo[1], Temp_Line[Rcpt_WinEPSSeq_Pos], Length(SeqNo));
        end;
  end;

  procedure CreateIncommDigitalProductCodeLine;         // CPCLIENTS-12067
  begin
    if (FInLineNum = Defs.Rcpt_WinEPSSeq_Line + 1) and (FInrec.HostSuffixCode = INCHOST) and not (trim(FInrec.ControlNumber) = '') then
    begin
      SetJsonReceiptBlock(Which_Copy, grbCardInfo);
      WriteRLine(' Product Code    ' + trim(FInrec.ControlNumber));
    end;
  end;

  procedure CreateSignatureLines;    //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      begin
      if (FInLineNum in [Rcpt_Blank_To_Sig_1..Rcpt_Blank_To_Sig_2]) then
      begin
        SetJsonReceiptBlock(Which_Copy, grbSigLines);              // CPCLIENTS-5932
        if (rtrim(PONumber) <> '') and (FInLineNum = Rcpt_Blank_To_Sig_1) and (Host_PONumberUsed = 'Y') then
        begin
           Move('PO/Ref #', Temp_Line[BashasPO_Pos], 8);  { Print PO on 1st line if there }
           Move(PONumber[1], Temp_Line[BashasPO_Pos+9], Length(PONumber));
        end
        else
        if not printSigLine(Which_Copy) then
          FUseThisOne := False;
      end;

      if (FInLineNum = Rcpt_Sig_Line) then
        FUseThisOne := PrintSigLine(Which_Copy, True);
      end;
  end;

  procedure CreateNameLine;   //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_Name_Line) and not FReceiptWithNoSig then  // CPSupport-1517
      begin
        SetJsonReceiptBlock(Which_Copy, grbSigLines);              // CPCLIENTS-5932
        if (Customer_Name = '')    or { if no cust name, not usable }
           (PrintNameOnRcpt = 'N') or
           (ReqCodeN in User_1_Set + WirelessSet + [User_2_PurchN, User_2_OverrideN, User_2_VoiceN]) then
          FUseThisOne := False
        else
        if printSigLine(Which_Copy) then
        begin
          Char_Pos := Length(RTrim(Customer_Name));
          if (Char_Pos > Max_Name_Len) then
            Char_Pos := Max_Name_Len;

          if (length(Customer_Name) > 0)
            then Move(Customer_Name[1], Temp_Line[Rcpt_Name_Pos], Char_Pos)
            else Temp_Line := '';
        end
        else         { Print Thank You --- Name on }
        begin        { integrated and cust copy if no no sig line }
          if (Length(RTrim(Customer_Name)) + 11 <= Rcpt_Line_Len) then
          begin
            Temp_Cust := 'Thank You: ' + RTrim(Customer_Name);
            Char_Pos := ((Rcpt_Line_Len - Length(Temp_Cust)) div 2) + 1;
            Move(Temp_Cust[1], Temp_Line[Char_Pos], Length(Temp_Cust));
          end
          else
            FUseThisOne := False;
        end;
      end;
  end;

  procedure PrintBalanceLine;       //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_Total_Amt_Line) and IsPrintBalance and not (ReqCodeN in BalInq_Set) then
      begin
        SetJsonReceiptBlock(Which_Copy, grbSubtotalAmount);             // CPCLIENTS-5932
        WriteRLine(Receipt_A[Rcpt_Underscore_Line]); { do an underline }
        Format_Amount(EBT_FS_BalanceN, Amt_Buf);
        Temp_Line := Receipt_A[Rcpt_Purch_Amt_Line];
        if IsHostOneOf(FInrec.HostSuffixCode, 'JET/NPP') then                             // CPCLIENTS-14925
          Move(Str_BalRemain[1], Temp_Line[Rcpt_Amt_Desc_Pos], Length(Str_BalRemain))     // CPCLIENTS-14925
        else                                                                              // CPCLIENTS-14925
          Move(Str_EndBal[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9);
        Move(Amt_Buf[1], Temp_Line[Rcpt_Purch_Amt_Pos], Length(Amt_Buf));

        WriteRLine(Temp_Line);
      end;
  end;

  procedure PrintEBTBalanceLine;       //nested proc for Gen_Receipt   // CPCLIENTS-15022
  var
    Str_temp : String[09];
  begin
    Str_temp := '';
    with FInrec, Defs do
      if (FInLineNum = Rcpt_Total_Amt_Line) then
      begin
        SetJsonReceiptBlock(Which_Copy, grbSubtotalAmount);          
        WriteRLine(Receipt_A[Rcpt_Underscore_Line]); { do an underline }

        Str_temp := IfThen((FInRec.HostSuffixCode = CBS), 'FS Bal:  ', Str_EBTFood);
        Format_Amount(EBT_FS_BalanceN, Amt_Buf);
        if (Trim(EBT_FS_Balance) <> '') then
        begin
          Temp_Line := Receipt_A[Rcpt_Purch_Amt_Line];                                                                              // CPCLIENTS-14925
          Move(Str_temp[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9);
          Move(Amt_Buf[1], Temp_Line[Rcpt_Purch_Amt_Pos], Length(Amt_Buf));
          WriteRLine(Temp_Line);
        end;

        Str_temp := IfThen((FInRec.HostSuffixCode = CBS), 'Cash Bal:', Str_Cash);;
        Format_Amount(EBT_Cash_BalanceN, Amt_Buf);
        if (Trim(EBT_Cash_Balance) <> '') then
        begin
          Temp_Line := Receipt_A[Rcpt_Purch_Amt_Line];                                                                              // CPCLIENTS-14925
          Move(Str_temp[1], Temp_Line[Rcpt_Amt_Desc_Pos], 9);
          Move(Amt_Buf[1], Temp_Line[Rcpt_Purch_Amt_Pos], Length(Amt_Buf));
          WriteRLine(Temp_Line);
        end;
      end;
  end;

  procedure PrintTotalAmountForHostLine;  //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_Total_Amt_Line) and (trim(FHostLine4) <> '') then
      begin
        if not (ReqCodeN in Check_Set) or ((ReqCodeN in Check_Set) and (copy(TermRspCode[1],1,1) <> 'A')) then
        begin
          SetJsonReceiptBlock(Which_Copy, grbTransType);  // CPCLIENTS-5933
          WriteRLine(FHostLine4);
        end;
      end;
  end;

  procedure PrintDateForHostLine;   //nested proc for Gen_Receipt
  begin
    with FInrec, Defs do
      if (FInLineNum = Rcpt_Date_Line) then   { TSL-J }
      begin
        SetJsonReceiptBlock(Which_Copy, grbStoreData);              // CPCLIENTS-5932
        if (Trim(FHostLine5) <> '') then
          WriteRLine(FHostLine5);

        if (trim(NOVtranID) <> '') then                  // TSL DEV-10563
          WriteRLine(' Tran ID ' + NovtranID);
        // CPCLIENTS-5179 To print Merchant Id, Terminal Id and Issuer Code
        if not (ReqCodeN in Check_Set) or IsBYLHostGroup(FInrec.HostSuffixCode) then  { TSL-09 }
        begin
          if (Trim(FHostLine6) <> '') then  // this is BYLATLReceiptMerchTerm line
            WriteRLine(FHostLine6);

          if (Trim(FHostLine7) <> '') then
            WriteRLine(FHostLine7);
        end;
      end;
  end;

  procedure PrintDCCLinesDisclaimer;  //11758
  begin
    try
      if IsDCC {FInrec.isDCCTrans} then   //11758, since DCC is ONLY for credit transactions, we can use this line!  // TFS-22487
        begin   //  ********90********90********90********9
        WriteRLine(' Please debit my account for the total');
        WriteRLine(' amount of this transaction in the');
        WriteRLine(' transaction currency shown above.');
        WriteRLine(' I acknowledge I had a choice to pay in');
        WriteRLine(' US dollars and my choice of currencies');
        WriteRLine(' is final. Currency conversion is');
        WriteRLine(' conducted by the merchant.');
        WriteRLine(' ');
        WriteRLine(' *The exchange rate used for this');
        WriteRLine(' transaction is a wholesale interbank');
        WriteRLine(' exchange rate plus an international');
        WriteRLine(' currency selection fee.');
        WriteRLine(' Rate Source:Reuters Wholesale Interbank');
        end;
    except on e: exception do
      MsgLog(format('ReceiptClass.PrintDCCLinesDisclaimer ERROR [%s]',[e.Message]));
    end;
  end;

  begin       //  Gen_Receipt main
    msgDebug('Gen_Receipt BEGIN');
    with FInrec, Defs do
    for Which_Copy := Cust_Copy to End_Copy do
    begin
      if (Which_Copy = Drawer_Copy) and (PublixHostDefined(FInrec.HostSuffixCode)) then
        PublixReceipt
      else
      begin
        if Which_Copy = Cust_Copy then                                                      // CPCLIENTS-5932 start
        begin
          JsonReceipt.Clear;
          JsonReceipt.SetCurrentBlock(rbHeader);
          for i := 1 to 4 do
            JsonReceipt.AddLineToBlock(SignOnSet.ReceiptHeaderLines[i]);
        end;                                                                                // CPCLIENTS-5932 end
        Init_For_Copy(Which_Copy);
        PrintExtraHostLines;
        FAIDPrinted := False; // CPCLIENTS-2788, 2959

        repeat
          inc(FInLineNum);                       { Next Line  }
          FUseThisOne := true;                   { Reset      }
          Temp_Line := Receipt_A[FInLineNum];    { Map Line  }

          CreateTransactionTypeLine(Which_Copy); // CPCLIENTS-5230  // CPCLIENTS-13624 - Moved this method to under Build_Receipt so thact can be access from both Gen_Receipt and EBT_Receipt
          CreatePurchaseAmountLine;        // all of these only format the Temp_Line line
          CreateCashbackAmountLine;
          CreateFeeAmountLine;
          CreateTipAmountLine;
          PossiblySkipUnderscoreLine;
          CreateTotalAmountLine;
          CreatePinUsedLine;
          CreateCardTypeLine;
          CreateAccountLine;
          CreateEntryMethodLine(Which_Copy); // TFS-177661  // CPCLIENTS-13669 - Moved this to here from bottom
          CreateAuthLine;
          CreateFromLine;
          CreateLaneLine;
          CreateCashierLine;
          CreateDateLine;
          CreateTimeLine;
          CreateReferenceLine;
          CreateWinEPSSequenceLine;
          CreateIncommDigitalProductCodeLine;      // CPCLIENTS-12067
          CreateBAMS_AVS_CVV_Line;      // 13626
          CreateBAMSHostRspMsgLine;     // 13626
          // CPCLIENTS-13502 - Added below condition to not to Print RRN for Hospitality lane as the RRN no is not received from host
          if not(FInrec.SeqNo = FInrec.RetrievalRefNo) then
            CreateBAMSRRNLine(Defs.Rcpt_WinEPSSeq_Line);  // CPCLIENTS-13141
          CreateSignatureLines;
          CreateNameLine;
          //CPCLIENTS-4287 - Print AID after WinEPS Sequence line.
          if (not FAIDPrinted) and ( FInLineNum > Rcpt_WinEPSSeq_Line ) then // CPCLIENTS-2788, 2959
          begin
            PrintAID(which_Copy);
            if (signOnSet.ReceiptHospitalityIncludeTip) and PrintTipLineOnReceipt then // CPCLIENTS-5933
              SetJsonReceiptBlock(Which_Copy, grbSubtotalAmount);
          end;

          if FUseThisOne then      { if this one is going to be used }
          begin
            for C_Counter := 1 to length(Temp_Line) do { JGS-10/23/97 Stater problem }
              if (ord(Temp_Line[C_Counter]) < 32) then
                Temp_Line[C_Counter] := ' ';
            WriteRLine(Temp_Line);
          end;

          if IsHost(CBS) and (FInRec.ReqCodeN in EBT_Set) then    // CPCLIENTS-15022
            begin
              PrintEBTVoucherNumLine;                             // CPCLIENTS-15371
              PrintEBTBalanceLine;
            end
          else
            PrintBalanceLine;       //nested proc for Gen_Receipt
          PrintTotalAmountForHostLine;
          PrintDateForHostLine;
        until (FInLineNum = Rcpt_Map_Lines);

        SetJsonReceiptBlock(Which_Copy, grbSubtotalAmount);              // CPCLIENTS-5932
        PrintDCCLinesDisclaimer;

        // JTG: this was my change for 55543 but Jeff reports it's working but with and without this change.. so I will just comment this out for now
        // if (ReqCodeN in check_Set)         and          //55543 - move this to BEFORE the print sig line..
        //   (TermRspCode = rcApprovedByECC) then         { an ECC transacton uses a }
        //  ECCReceiptMsg(LaneNo, which_Copy);

        if printSigLine(which_Copy, True) and (termRspCode <> rcApprovedByECC) then      { TSL-15 }
        begin
          SetJsonReceiptBlock(Which_Copy, grbAgreements);              // CPCLIENTS-5932
          if (Last_Was = 'L') then
            for I := 1 to L_Credit_Verbage_Lines Do
              WriteRLine(L_Credit_Verbage[I])
          else
            for I := 1 to S_Credit_Verbage_Lines Do
              WriteRLine(S_Credit_Verbage[I]);
        end;

        if IsHost('CHS') and (which_copy = Drawer_Copy) and
           (ReqCodeN in User_1_Set) and (ReqCodeN <> User_1_BalInqN) then // DOEP-53098
        begin
          SetJsonReceiptBlock(Which_Copy, grbAgreements);              // CPCLIENTS-5932
          WriteRLine('I AUTHORIZE THE TRANSFER OF FUNDS');
          WriteRLine('FOR THE AMOUNT OF THIS');
          WriteRLine('TRANSACTION');
        end;

        if (which_copy = end_copy) then  { this used to be outside which copy loop }
        begin
          SetJsonReceiptBlock(Which_Copy, grbCardInfo);              // CPCLIENTS-5932
          if (ReqCodeN in BalInq_Set) then   { TSL-F }
          begin
            if (Small_or_Large = 'L') then
            begin
              Str_Declined1 := L_Str_BalInq1;
              Str_Declined2 := L_Str_BalInq2;
            end
            else
            begin
              Str_Declined1 := S_Str_BalInq1;
              Str_Declined2 := S_Str_BalInq2;
            end;
            if (trim(Str_Declined1) <> '') then
              WriteRLine(Str_Declined1);
            if (trim(Str_Declined2) <> '') then
              WriteRLine(Str_Declined2);
          end;
        end;
        //for 55543, this was commented out and moved up, but it doesn't seem to be necessary
        if (ReqCodeN in check_Set)         and               //55543 moved this up above the signature line
           (TermRspCode = rcApprovedByECC) and               { an ECC transacton uses a }
            not (IsHost('ATL') and (FInRec.VoidCode = 'V')) then // CPCLIENTS-5179 For ATL host void transaction, signature authority text should not be displayed
          ECCReceiptMsg(LaneNo, which_Copy);

        CheckForBlackhawkMessage(Which_Copy);   // CPCLIENTS-5932
        WritePhoneCardLines(Which_Copy);        // CPCLIENTS-5932
        PrintCopyLine(Which_Copy); // CPCLIENTS-5933
      end;
      if Which_Copy = Cust_Copy then                                                      // CPCLIENTS-5932 start
      begin
        JsonReceipt.SetCurrentBlock(rbFooter);
        for i := 1 to 2 do
          JsonReceipt.AddLineToBlock(SignOnSet.ReceiptTrailLines[i]);
      end;                                                                                // CPCLIENTS-5932 end
    end;
  end; { Gen_Receipt }

  function IsAtLeastOneHipBalExists: boolean;
  begin
    if FInRec.ReqCodeN in EBT_Cash_Set then // DOEP-46785, EBT cash transaction do NOT deal with these HIP amounts
    begin
      Result := False;
    end else
    begin
      with FInrec do
        result := (EBTHIPCurBal <> BALANCE_MISSING) or (EBTHIPMtdBal <> BALANCE_MISSING);
    end;
  end;

  Procedure EBT_Receipt;
  Var
    C_Counter,
    I             : Integer;
    Temp_BalanceN : LongInt;
    Temp_Cust     : String[40];
    Other_Bal_Line,
    No_Bal        : Boolean;
    Which_Copy    : integer;
    tmpStr        : string255;

  begin
  With FInrec, EBT_Defs Do
  begin
    //SM(format('DEBUG JTG - EBT_Receipt: ReqCodeN: %d',[ReqCodeN]));
    No_Bal := False;         { Assume balance here         }
    Other_Bal_Line := false; { TSL-F }
    Temp_BalanceN := 0;      { TSL-F }

    For Which_Copy := Cust_Copy to End_Copy Do
      begin
        // CPCLIENTS-5932 Customer receipts are printed first and the JSON receipt is both customer and merchant so only clear on the customer receipt
        if Which_Copy = Cust_Copy then                                                      // CPCLIENTS-5932 start
        begin
          JsonReceipt.Clear;
          JsonReceipt.SetCurrentBlock(rbHeader);
          for i := 1 to 4 do
            JsonReceipt.AddLineToBlock(SignOnSet.ReceiptHeaderLines[i]);
        end;                                                                                // CPCLIENTS-5932 end
        Init_For_Copy(Which_Copy);
        SetJsonReceiptBlock(Which_Copy, grbStoreData);                                      // CPCLIENTS-5932
        if (trim(FHostLine1) <> '') then        { extra line to print from host }
          WriteRLine(FHostLine1);
        if (trim(FHostLine2) <> '') then        { extra line to print from host }
          WriteRLine(FHostLine2);
        if (trim(FHostLine3) <> '') then        { extra line to print from host }
          WriteRLine(FHostLine3);

        Repeat
          Inc(FInLineNum);                       { Next Line  }
          //SM(format('DEBUG JTG - EBT_Receipt: FInLineNum: %d',[FInLineNum]));
          FUseThisOne := True;                      { Reset      }
          Temp_Line    :=  EBT_Rcpt[FInLineNum]; { Map Line  }

          if (FInLineNum = EBT_Card_Type_Line) then
          begin
            if (HostSuffixCode = CBS) then  // CPCLIENTS-13624
              CreateTransactionTypeLine(Which_Copy)
            else
              Temp_Line := '';

            FUseThisOne := false;
          end;

          if (FInLineNum = EBT_Acct_Line) then
          begin
            tmpStr := StringOfChar('X', FInRec.PanLen - 4) + FInRec.AcctNoLast4;
            Move(tmpStr[1], Temp_Line[EBT_Acct_Pos], Length(tmpStr));

            if not (HostSuffixCode = CBS) then  // CPCLIENTS-13669 - To skip Entry character for EBT for CBS
            begin
              if (Entry = CEV_MANUAL_POS) or (Entry = CEV_MANUAL_CUST)
                then Temp_Line[EBT_Acct_Pos-1] := 'M'
              else if (EMVMgr.IsCardEntryFallback) then // CPCLIENTS-19133 Add entry method FS for EMV Fallback to swipe
              begin
               Temp_Line[EBT_Acct_Pos-1] := 'S';
               Temp_Line[EBT_Acct_Pos-2] := 'F';
              end
              else if (not(EMVMgr.IsCardEntryChipCard) and (FinRec.VoidCode <> 'V')) then //CPCLIENTS-19133
                Temp_Line[EBT_Acct_Pos-1] := 'S';
            end;
            SetJsonReceiptBlock(Which_Copy, grbCardInfo);     // CPCLIENTS-5932
          end;

          if (HostSuffixCode = CBS) then  // CPCLIENTS-13669 - To print Entry method for EBT for CBS
            CreateEntryMethodLine(Which_Copy);

          if (FInLineNum = EBT_Pin_Used_Line) and (Rtrim(PIN) <> '') and (VoidCode <> 'V') and
             not(HostSuffixCode = CBS) then // CPCLIENTS-13625 - To not to print PIN used on receipt for MSR transactions when PIN TAC configured and PIN entered
          begin
            Move(Str_PinUsed[1],Temp_Line[EBT_Pin_Used_Pos], Length(Str_PinUsed));
            SetJsonReceiptBlock(Which_Copy, grbCardInfo);    // CPCLIENTS-5932  this is the same line as the PAN line
          end;

          { Print Beginning Balance  1. All SA  2. Cust Copy of Integrated }

          if (FInLineNum = EBT_Begin_Bal_Line) then
            begin
              While (CardName[1] >= '0') and (CardName[1] <= '9') Do  { Delete NCR Tender change numerics JGS-O }
                Delete(CardName, 1, 1);

              if (copy(TermRspCode,1,1) = 'A') then    { TSL-C }
              begin
                tmpStr := ' ' + rtrim(CardName) + '  +++  ' + Str_Approved + '  +++';
                if (ReqCodeN in [EBT_FS_BalinqN, EBT_Cash_BalinqN]) then
                  tmpStr := tmpStr + ' BALANCE INQUIRY';
                SetJsonReceiptBlock(Which_Copy, grbCardInfo);     // CPCLIENTS-5932
                WriteRLine(tmpStr);
              end
              else
              begin
                if (RTrim(SwRspCode) <> '')             { TSL-C }
                  then tmpStr := ' ' + rtrim(CardName) + ' ' + Str_Declined0 + ' ' + SwRspCode
                  else tmpStr := ' ' + rtrim(CardName) + ' ' + Str_Declined0 + ' ' + MTXRspCode;

                SetJsonReceiptBlock(Which_Copy, grbCardInfo);    // CPCLIENTS-5932
                WriteRLine(tmpStr);
                tmpStr :=  '  ' + CashPadDisp;
                if (Rtrim(Extra_EBT_Msg) <> '') then { TSL-L 2nd decl msg }
                begin                                 { See Deluxe.pas }
                  if (Small_or_Large = 'L') then   { TSL-N }
                    tmpStr := tmpStr + ' ' + Copy(Extra_EBT_Msg, 1, 16)
                  else
                  begin
                    WriteRLine(tmpStr);
                    tmpStr := '  ' + Copy(Extra_EBT_Msg, 1, 16)
                  end;
                  WriteRLine(tmpStr);
                end;
              end;

              if (trim(FHostLine4) <> '') then
              begin
                SetJsonReceiptBlock(Which_Copy, grbTransType); // CPCLIENTS-5933
                WriteRLine(FHostLine4);
              end;

              if (ReqCodeN in EBT_FS_Set) and (EBT_FS_Balance = '') then       { TSL-B }
              begin
                FUseThisOne := False;
                No_Bal := True;
              end
              else
              if (ReqCodeN in EBT_Cash_Set) and (EBT_Cash_Balance = '') then   { TSL-B }
              begin
                FUseThisOne := False;
                No_Bal := True;
              end
              else
              if (FIntOrSBReceipt = SA_Receipt) or ((FIntOrSBReceipt in Int_Receipt) and (Which_Copy = Cust_Copy)) then
              begin
                if (ReqCodeN in EBT_FS_Set) then
                begin
                  if (copy(TermRspCode,1,1) = 'A') then
                  begin
                    if ((ReqCodeN = EBT_FS_ReturnN) and (VoidCode = ' '))                  or
                       ((ReqCodeN in [EBT_FS_PurchN, EBT_FS_VoiceN]) and (VoidCode = 'V'))
                      then Temp_BalanceN := EBT_FS_BalanceN - TrxAmtN
                      else Temp_BalanceN := EBT_FS_BalanceN + TrxAmtN; { Is NormalPurchase }
                  end
                  else
                    Temp_BalanceN := EBT_FS_BalanceN;
                  // DOEP-71726, TFS ID 7752, need to use 'SNAP' in place of EBT Food string for Target and VantivIBM
                  if IsHost('VNT') then
                  begin
                    move(Str_SNAP[1],Temp_Line[2], length(Str_SNAP));
                  end else
                    move(Str_EBTFood[1],Temp_Line[2], length(Str_EBTFood));  // JTG move the label into the receipt too...  Dev 16340
                  SetJsonReceiptBlock(Which_Copy, grbTotalAmount);     // CPCLIENTS-5932
                end
                else  { Is EBT_Cash }
                begin
                  if (copy(TermRspCode,1,1) = 'A') then
                  begin
                    if ((ReqCodeN = EBT_Cash_ReturnN) and (VoidCode = ' ')) or
                       ((ReqCodeN in [EBT_Cash_PurchN, EBT_Cash_VoiceN]) and (VoidCode = 'V'))
                      then Temp_BalanceN := EBT_Cash_BalanceN - TrxAmtN
                      else Temp_BalanceN := EBT_Cash_BalanceN + TrxAmtN;
                  end
                  else
                    Temp_BalanceN := EBT_Cash_BalanceN;
                  move(Str_Cash[1],Temp_Line[2], length(Str_Cash));  // JTG move the label into the receipt too... Dev 16340
                SetJsonReceiptBlock(Which_Copy, grbTotalAmount);     // CPCLIENTS-5932
                end;

                if CalcStartingBal then  { appr - compute beginbal } { TSL-03 }
                begin
                  Format_Amount(Temp_BalanceN, Amt_Buf);
                  Move(Amt_Buf[1], Temp_Line[EBT_Begin_Bal_Pos], Length(Amt_Buf));
                  SetJsonReceiptBlock(Which_Copy, grbTotalAmount);     // CPCLIENTS-5932
                end
                else
                  FUseThisOne := False;  // 7-16-09  Take out all beginning balances on receipt
              end
              else
                FUseThisOne := False;
            end;

          { Purchase Amount       }
          { Needs to be printed   }
          { 1. All SA             }
          { 2. All Copies of Int  }

          if (FInLineNum = EBT_Purch_Amt_Line) then
          begin
            SetJsonReceiptBlock(Which_Copy, grbTotalAmount);   // CPCLIENTS-5932
            if (HostSuffixCode = CBS) then  // CPCLIENTS-13624
                Move(Str_Total[1], Temp_Line[EBT_Purch_Type_Pos], 9)
            else if (HostSuffixCode = MPS) and (ReqCodeN in All_Purchase_Trxs) and not(VoidCode = 'V') and (TenderTypeValMTX = ttEBT_FS) then //18706  // CPCLIENTS-14663  - To Print the Purchase text as per WorldPay(MPS)
                Move(Str_MPS_EBT_Purhcase[1], Temp_Line[EBT_Purch_Type_Pos], 17)
            else
            begin
              if VoidCode = 'V' then
              begin
                if (ReqCodeN in All_Return_Trxs) then
                  Move(Str_VoidRetrn[1], Temp_line[EBT_Purch_Type_Pos], 9)
                else if (ReqCodeN in PreAuth_Set) then
                  Move(Str_VoidPreAuth[1], Temp_Line[EBT_Purch_Type_Pos], 9)
                else
                  Move(Str_VoidPurch[1], Temp_line[EBT_Purch_Type_Pos], 9);
              end
              else
              if (ReqCodeN in All_Return_Trxs) then
                Move(Str_Return[1], Temp_Line[EBT_Purch_Type_Pos], 9)                     { 'Return  '            }
              else if ReqCodeN in All_Voice_Trxs then
                Move(Str_Voice[1], Temp_Line[EBT_Purch_Type_Pos], 9) { TSL-E }
              else if (ReqCodeN in PreAuth_Set) then
                Move(Str_PreAuth[1], Temp_Line[EBT_Purch_Type_Pos], 9)
              else
                Move(Str_Purchase[1], Temp_Line[EBT_Purch_Type_Pos], 9);                 { Default to 'Purchase' }
            end;

            if VoidCode = 'V' then
              Format_Amount(TrxAmtN, Amt_Buf)                    { TSL-A }
            else
              Format_Amount(TrxAmtN-CashbackN-FeeAmtN-FTip, Amt_Buf);
            Move(Amt_Buf[1], Temp_Line[EBT_Purch_Amt_Pos], Length(Amt_Buf));
          end;

          if (FInLineNum = EBT_Purch_Amt_Line+1) and  // CPCLIENTS-14663 - To Print HIP Earned text as per WorldPay(MPS) after Puchase line
             (IsHostOneOf(HostSuffixCode,'MPS/BYL'))and (EBTHIPCurBal <> BALANCE_MISSING) and (EBTHIPCurBal > 0) and (TenderTypeValMTX = ttEBT_FS) then //18911 //18706   //15421 Added BYL (FiServ) host to the condition
          begin
            Temp_Line := '';
            SetJsonReceiptBlock(Which_Copy, grbTotalAmount);
            if (FRspMMR.TransactionType = trtReturn) or (FRspMMR.TransactionType = trtReturnVoucher) then     //17070 //16614 for return trx receipt to print 'Returned'
              WriteRLine(' Fruit/Veg Bonus Returned')
            else
              WriteRLine(' Fruit/Veg Bonus Earned');
            Temp_Line := FormatCurr(' This Transaction  $     0.00', EBTHIPCurBal/100);
            if (Temp_Line <> '') then
                WriteRLine(Temp_Line);
            //WriteRLine(' Voucher  #  ' + trim(FInrec.EBT_Voucher_Num));
          end;

          { Cashback Amount       }
          { Needs to be printed   }
          { if Purchase Trx and   }
          { Not VOID        and   }
          { CB <> $0.00           }
          { 1. All SA             }
          { 2. All Copies of Int  }
          { Means all the time    }

          if (FInLineNum = EBT_CashB_Amt_Line) then
          begin
            if ((not (ReqCodeN in All_Purchase_Trxs)) and (not (ReqCodeN in All_Voice_Trxs))) or
               (VoidCode = 'V')                                                               or
               (CashbackN = 0) then                       { Cashback = $0.00 then }
              FUseThisOne := False                       { Skip this line        }
            else
            begin
              Format_Amount(CashbackN, Amt_Buf);
              Move(Amt_Buf[1], Temp_Line[EBT_Cashb_Amt_Pos], Length(Amt_Buf));
              SetJsonReceiptBlock(Which_Copy, grbCashbackAmount);     // CPCLIENTS-5932
            end;
          end;

          { Fee Amount            }
          { Needs to be printed   }
          { if Purchase Trx and   }
          { Not VOID        and   }
          { Fee <> $0.00          }
          { 1. All SA             }
          { 2. All Copies of Int  }
          { Means all the time    }

          if (FInLineNum = EBT_Fee_Amt_Line) then
          begin
            if ((not (ReqCodeN in All_Purchase_Trxs)) and (not (ReqCodeN in All_Voice_Trxs))) or        { TSL-E }
               (VoidCode = 'V')                                                               or
               (FeeAmtN = 0)                                                                  then
              FUseThisOne := False                       { Skip this line        }
            else
            begin
              Format_Amount(FeeAmtN, Amt_Buf);
              Move(Amt_Buf[1], Temp_Line[EBT_Cashb_Amt_Pos], Length(Amt_Buf));
              SetJsonReceiptBlock(Which_Copy, grbCashbackAmount);   // CPCLIENTS-5932
            end;
          end;

          if (FInLineNum = EBT_Tip_Amt_Line) then
          begin
            if ((not (ReqCodeN in All_Purchase_Trxs)) and (not (ReqCodeN in All_Voice_Trxs))) or        { TSL-E }
               (VoidCode = 'V')                                                               or
               (FTip = 0)                                                                     then
              FUseThisOne := False                       { Skip this line        }
            else
            begin
              Format_Amount(FTip, Amt_Buf);
              Move(Amt_Buf[1], Temp_Line[EBT_Cashb_Amt_Pos], Length(Amt_Buf));
              SetJsonReceiptBlock(Which_Copy, grbTipInfo);   // CPCLIENTS-5932
            end;
          end;

          { Underscore            }
          { Needs to be printed   }
          { 1. All SA             }
          {    if bal or c/b fee  }
          { 2. Cust Copy of Int   }
          {   if bal or c/b or fee}
          { 3. Drawer Copy of Int }
          {    if CB <> $0.00     }
          {   and Fee<> $0.00     }
          { So .. NO if           }
          { Int/Drawer and CB=$0  }
          { and Fee=$0            }

          if (FInLineNum = EBT_Underscore_Line) then
          begin
            if (FIntOrSBReceipt in Int_Receipt) then
            begin
              if (Which_Copy = Drawer_Copy) then
              begin
                if (CashBackN = 0) and (FeeAmtN = 0) and (FTip = 0) then
                  FUseThisOne := False;
              end
              else  {  Cust Copy }
              if No_Bal and (CashbackN = 0) and (FeeAmtN = 0) and (FTip = 0) then
                FUseThisOne := False;
            end
            else  { SA Receipt }
            if No_Bal and (CashbackN = 0) and (FeeAmtN = 0) and (FTip = 0) or
               (VoidCode = 'V') then        { TSL-A }
              FUseThisOne := False;
            SetJsonReceiptBlock(Which_Copy, grbSubtotalAmount);    // CPCLIENTS-5932
          end;

          { end Balance               }
          { Needs to be printed       }
          { 1. All SA                 }
          { 2. Cust Copy of Int       }
          { 3. As Purch+CB+Fee=Total  }
          {    for Int/Drawer         }

          if (FInLineNum = EBT_End_Bal_Line) then
          begin
            SetJsonReceiptBlock(Which_Copy, grbTotalAmount);   // CPCLIENTS-5932
            if not No_Bal then
            begin
              //SM(format('DEBUG JTG - EBT_Receipt: EBT_End_Bal_Line (%d) NOT No_Bal',[EBT_End_Bal_Line]));
              if (FIntOrSBReceipt = SA_Receipt) or
                 ((FIntOrSBReceipt in Int_Receipt) and (Which_Copy = Cust_Copy)) then
              begin
                if (ReqCodeN in EBT_FS_Set)
                  then Format_Amount(EBT_FS_BalanceN, Amt_Buf)
                  else Format_Amount(EBT_Cash_BalanceN, Amt_Buf);
                if (ReqCodeN in EBT_FS_Set) then  //JTG DEV 16340 - add matching header to value.. do it like this for easy removal
                begin
                  // DOEP-71726, TFS ID 7752, need to use 'SNAP' in place of EBT Food string for Target and VantivIBM
                  if IsHost('VNT') then
                  begin
                    move(Str_SNAP[1],Temp_Line[EBT_Other_Bal_Hdr_Pos], length(Str_SNAP));
                  end else
                    Move(Str_EBTFood[1], Temp_Line[EBT_Other_Bal_Hdr_Pos], Length(Str_EBTFood));  //JTG DEV 16340 - add matching header to value
                end else
                  Move(Str_Cash[1], Temp_Line[EBT_Other_Bal_Hdr_Pos], Length(Str_Cash));       //JTG DEV 16340 - add matching header to value
                Move(Amt_Buf[1], Temp_Line[EBT_End_Bal_Pos], Length(Amt_Buf));
              end
              else  { Is Integrated Drawer Copy, need total Amt }
              if (CashBackN <> 0) or (FeeAmtN <> 0) or (FTip <> 0) then
              begin
                Move(Str_Total[1], Temp_Line[EBT_End_Bal_Hdr_Pos], Length(Str_Total));
                Format_Amount(TrxAmtN, Amt_Buf);
                Move(Amt_Buf[1], Temp_Line[EBT_End_Bal_Pos], Length(Amt_Buf));
              end
              else
                FUseThisOne := False;
            end
            else    { No balances }
            begin
              //SM(format('DEBUG JTG - EBT_Receipt: EBT_End_Bal_Line (%d) No_Bal TRUE',[EBT_End_Bal_Line]));
              if (CashbackN = 0) and (FeeAmtN = 0) and (FTip = 0) or
                 (VoidCode = 'V') then         { TSL-A }
                FUseThisOne := False
              else { Have either cb or fee }
              begin
                Move(Str_Total[1], Temp_Line[EBT_End_Bal_Hdr_Pos], Length(Str_Total));
                Format_Amount(TrxAmtN, Amt_Buf);
                Move(Amt_Buf[1], Temp_Line[EBT_End_Bal_Pos], Length(Amt_Buf));
                if (ReqCodeN in EBT_FS_Set) then  //JTG DEV 16340 - add matching header to value.. do it like this for easy removal
                begin
                  // DOEP-71726, TFS ID 7752, need to use 'SNAP' in place of EBT Food string for Target and VantivIBM
                  if IsHost('VNT') then
                  begin
                    move(Str_SNAP[1],Temp_Line[EBT_Other_Bal_Hdr_Pos], length(Str_SNAP));
                  end else
                    Move(Str_EBTFood[1], Temp_Line[EBT_Other_Bal_Hdr_Pos], Length(Str_EBTFood));  //JTG DEV 16340 - add matching header to value
                end else
                  Move(Str_Cash[1], Temp_Line[EBT_Other_Bal_Hdr_Pos], Length(Str_Cash));       //JTG DEV 16340 - add matching header to value
              end;
            end;
          end;

          { Other Balance Line   }
          { Always unless Drawer }

          if (FInLineNum = EBT_Other_Bal_Line) then
          begin
            SetJsonReceiptBlock(Which_Copy, grbSubtotalAmount);    // CPCLIENTS-5932
            Other_Bal_Line := True;
            if (FIntOrSBReceipt = SA_Receipt)                                  or
               ((FIntOrSBReceipt in Int_Receipt) and (Which_Copy = Cust_Copy)) then
            begin
              if (ReqCodeN in EBT_Cash_Set) then
              begin
                //SM(format('DEBUG JTG - EBT_Receipt: EBT_Other_Bal_Line (%d) ReqCodeN in EBT_Cash_Set',[EBT_Other_Bal_Line]));
                if (EBT_FS_Balance <> '') then   { TSL-B }
                begin
                  // DOEP-71726, TFS ID 7752, need to use 'SNAP' in place of EBT Food string for Target and VantivIBM
                  if IsHost('VNT') then
                  begin
                    move(Str_SNAP[1],Temp_Line[EBT_Other_Bal_Hdr_Pos], length(Str_SNAP));
                  end else
                    Move(Str_EBTFood[1], Temp_Line[EBT_Other_Bal_Hdr_Pos], Length(Str_EBTFood));         // JTG - my new version
                  //Move(Str_Foodstamp[1], Temp_Line[EBT_Other_Bal_Hdr_Pos], Length(Str_Foodstamp));   // Tom's & previous version
                  Temp_BalanceN := EBT_FS_BalanceN;
                end
                else
                begin
                  FUseThisOne := False;
                  Other_Bal_Line := False;
                end;
              end
              else
              begin
                //SM(format('DEBUG JTG - EBT_Receipt: EBT_Other_Bal_Line (%d) NOT ReqCodeN in EBT_Cash_Set',[EBT_Other_Bal_Line]));
                if (EBT_Cash_Balance <> '') then     { TSL-B }
                begin
                  Move(Str_Cash[1], Temp_Line[EBT_Other_Bal_Hdr_Pos], Length(Str_Cash));
                  Temp_BalanceN := EBT_Cash_BalanceN;
                end
                else
                begin
                  FUseThisOne := False;
                  Other_Bal_Line := False;
                end;
              end;
              Format_Amount(Temp_BalanceN, Amt_Buf);
              Move(Amt_Buf[1], Temp_Line[EBT_Other_Bal_Pos], Length(Amt_Buf));
              SetJsonReceiptBlock(Which_Copy, grbTotalAmount);    // CPCLIENTS-5932
            end
            else
              FUseThisOne := False;
          end;

          { Blank after Other JGS-F }

          if (FInLineNum = EBT_Other_Bal_Line+1) then
          begin
            if (Other_Bal_Line = False) then
              FUseThisOne := False;
          end;

          { Authorization Num }
          { Always            }

          if (FInLineNum = EBT_Auth_Line) then
          begin
            if ((HostSuffixCode = CBS) and (VoidCode = 'V')) then // CPCLIENTS-13774 - To not to Print Auth# Place holder for EBT Voids
                FUseThisOne := False
            else
            begin
              if (length(AuthCode) > 0) and (copy(TermRspCode,1,1) = 'A') then
              begin
                Move(AuthCode[1], Temp_Line[EBT_Auth_Pos], Length(AuthCode));
                SetJsonReceiptBlock(Which_Copy, grbCardInfo);    // CPCLIENTS-5932
              end;

              if FInrec.Trx_Is_Training then
              begin
                SetJsonReceiptBlock(Which_Copy, grbCardInfo);      // CPCLIENTS-5932
                WriteRLine(Str_Training);
                WriteRLine(' ');
              end;
            end;
          end;

          if (FInLineNum = EBT_Exp_Date_Line) then { JGS-R }
          begin
            if (ExpDate <> '') and (ExpDate <> '0000') and not IsHost(CBS) then // CPCLIENTS-11513 - To not to print Expiry Date for BAMS CyberGateway Host
            begin
              Move(Str_ExpDate[1], Temp_Line[EBT_Exp_Date_Pos], Length(Str_ExpDate));
              Move(Asterisk[1], Temp_Line[EBT_Exp_Date_Pos + 9], 2);
              Move(Asterisk[1], Temp_Line[EBT_Exp_Date_Pos + 12], 2);
              SetJsonReceiptBlock(Which_Copy, grbCardInfo);    // CPCLIENTS-5932
            end;
          end;

          if (FInLineNum = EBT_Exp_Date_Line) and IsHost('CHS') then // DOEP-53139
          begin
            SetJsonReceiptBlock(Which_Copy, grbCardInfo);      // CPCLIENTS-5932
            PrintCardEntryTypeForChase(Which_Copy);
            if (ReqCodeN in EBT_Set) and (Trim(FInRec.TraceID) <> '') then
              WriteRLine(' Trace Number: ' + Trim(FInRec.TraceID)); // was DenialRecordNum
          end;

          if (FInLineNum = EBT_Lane_Line) then
          begin
            // DOEP-69524, lane # length increased, so don't use hard-coded length 2 any more. Use Length of LaneNo variable
            Move(LaneNo[1], Temp_Line[EBT_Lane_Pos], Length(LaneNo));
            //Move(LaneNo[1], Temp_Line[EBT_Lane_Pos], 2);
            SetJsonReceiptBlock(Which_Copy, grbStoreData);    // CPCLIENTS-5932
            DonotPrintLineforBAMS;  // CPCLIENTS-11513
          end;

          if (FInLineNum = EBT_Cashier_Line) then
          begin
            While (Length(Cashier) > 0) and (Cashier[1] = '0') Do
              Delete(Cashier, 1, 1);
            Cashier := BRFill(Cashier, 9);
            if (length(cashier) > 0) then
            begin
              SetJsonReceiptBlock(Which_Copy, grbStoreData);    // CPCLIENTS-5932
              Move(Cashier[1], Temp_Line[EBT_Cashier_Pos], Length(Cashier));
              SetJsonReceiptBlock(Which_Copy, grbStoreData);    // CPCLIENTS-5932
            end;
            DonotPrintLineforBAMS;  // CPCLIENTS-11513
          end;

          TrxDate := rpad(TrxDate, 6);   // pad so no lenght errors
          if (FInLineNum = EBT_Date_Line) then { In_Date is YYMMDD }
          begin
            Move(TrxDate[3], Temp_Line[EBT_Date_Pos+0], 2);
            Move(TrxDate[5], Temp_Line[EBT_Date_Pos+3], 2);
            Move(TrxDate[1], Temp_Line[EBT_Date_Pos+6], 2);
            SetJsonReceiptBlock(Which_Copy, grbStoreData);    // CPCLIENTS-5932
          end;

          TrxTime := rpad(TrxTime, 6);
          if (FInLineNum = EBT_Time_Line) then
          begin
            Move(TrxTime[1], Temp_Line[EBT_Time_Pos+0], 2);
            Move(TrxTime[3], Temp_Line[EBT_Time_Pos+3], 2);
            SetJsonReceiptBlock(Which_Copy, grbStoreData);    // CPCLIENTS-5932
          end;

          if (FInLineNum = EBT_Ref_Line) then
          begin
            if publixHostDefined(FInrec.HostSuffixCode) then
            begin
               tmpStr := SeqNo + '-0' + LaneNo;
               move(tmpStr[1], Temp_Line[EBT_Ref_Pos], Length(tmpStr));
               SetJsonReceiptBlock(Which_Copy, grbStoreData);    // CPCLIENTS-5932
            end
            else
            if (SwSeqNo <> '')                   and
               (SentToModemYN = 'Y')             and    { JGS-X }
               (MTXRspCodeN <> OfflineAuthCodeN) then  { TSL-Y offline appr }
            begin
              Move(SwSeqNo[1], Temp_Line[EBT_Ref_Pos], Length(SwSeqNo));
              SetJsonReceiptBlock(Which_Copy, grbStoreData);    // CPCLIENTS-5932
            end
            else
            begin
              SetJsonReceiptBlock(Which_Copy, grbStoreData);    // CPCLIENTS-5932
              if UseOldSeq(FInrec)    { TSL-Y offline appr }
                then Move(OldSeq[1], Temp_Line[EBT_Ref_Pos], Length(OldSeq))
                else Move(SeqNo[1], Temp_Line[EBT_Ref_Pos], Length(SeqNo));
            end;
          end;

          if (FInLineNum = EBT_WinEPSSeq_Line) then    { TSL-01 }
          begin
            Move(SeqNo[1], Temp_Line[EBT_WinEPSSeq_Pos], Length(SeqNo));
            SetJsonReceiptBlock(Which_Copy, grbCardInfo);    // CPCLIENTS-5932
          end;

          if (EMVMgr.IsEBTChip) and (EMVMgr.IsCardEntryChipCard) and not (FInrec.HostSuffixCode = CBS) then
          begin
            if (FInLineNum = EBT_WinEPSSeq_Line+1) then //CPCLIENTS-19133
              PrintEBTChipData(which_Copy);
          end
          else
            CreateBAMSRRNLine(EBT_Defs.EBT_WinEPSSeq_Line); // CPCLIENTS-13141

          if (FInLineNum = EBT_WinEPSSeq_Line+1) and (IsHostOneOf(FInrec.HostSuffixCode,'CBS/BYL/ATL/JET')) and (trim(FInrec.EBT_Voucher_Num) <> '') then //CPCLIENTS-19628 // CPCLIENTS-13627 - To Print Voucher# for CyberSource
          begin
            SetJsonReceiptBlock(Which_Copy, grbCardInfo);
            WriteRLine(' Voucher          # ' + trim(FInrec.EBT_Voucher_Num));
          end;

          if (FInLineNum in [EBT_No_Disp_Cash_1..EBT_No_Disp_Cash_4]) then
          begin
            if (copy(TermRspCode,1,1) = 'A') then       { Trx is approved }
            begin
              if (FIntOrSBReceipt = SA_Receipt)                                    or
                 ((FIntOrSBReceipt in Int_Receipt) and (Which_Copy = Drawer_Copy)) then
              begin
                SetJsonReceiptBlock(Which_Copy, grbAgreements);    // CPCLIENTS-5933
                if (ReqCodeN in EBT_Cash_Set) then
                  FUseThisOne := False;
              end
              else
                FUseThisOne := False;
            end
            else { Trx is Declined }
            if (FIntOrSBReceipt = SA_Receipt)                                    or
               ((FIntOrSBReceipt in Int_Receipt) and (Which_Copy = Drawer_Copy)) then
            begin
              SetJsonReceiptBlock(Which_Copy, grbAgreements);    // CPCLIENTS-5932
              if (FInLineNum = EBT_No_Disp_Cash_1 ) then
                Temp_Line := Str_Declined1
              else
              if (FInLineNum = EBT_No_Disp_Cash_2 ) then
                Temp_Line := Str_Declined2
              else
              if (FInLineNum = EBT_No_Disp_Cash_3 ) then
                Temp_Line := Str_Declined3
              else
              if (FInLineNum = EBT_No_Disp_Cash_4 ) then
                Temp_Line := Str_Declined4;
            end
            else
              FUseThisOne := False;
          end;

          if (FInLineNum = EBT_Sig_Line) and not PrintSigLine(Which_Copy) then
          begin
            Temp_Line := ' ';                    { Create a space  }
            SetJsonReceiptBlock(Which_Copy, grbSigLines);    // CPCLIENTS-5932
          end;

          { Customer Name                      }
          { SA                                 }
          {  1. if Sig line, under             }
          {  2. if no sig, Thank You -- Name   }
          {                                    }
          { Int                                }
          {  1. Integrated with printer, under }
          { else                               }
          {  Cust_Copy - Thank You -- Name     }
          {  Drawer Copy                       }
          {   1. Sig Line there, under         }
          {   2. No sig, Thank You -- Name     }

          if (FInLineNum = EBT_Name_Line) then
          begin
            SetJsonReceiptBlock(Which_Copy, grbSigLines);    // CPCLIENTS-5932
            if (Customer_Name = '')    or
               (PrintNameOnRcpt = 'N') then
              FUseThisOne := False
            else
            if printSigLine(Which_Copy) then
            begin
              Char_Pos := Length(RTrim(Customer_Name));
              if (Char_Pos > Max_Name_Len) then
                Char_Pos := Max_Name_Len;
              if (length(Customer_Name) > 0)
                then Move(Customer_Name[1], Temp_Line[EBT_Name_Pos], Char_Pos)
                else Temp_Line := '';
            end
            else
            begin
              if (Length(RTrim(Customer_Name)) + 11 <= Rcpt_Line_Len) then
              begin
                Temp_Cust := RTrim(Customer_Name); //CPCLIENTS-19133
                EMVMgr.EBTCardHolder := Temp_Cust;
                Char_Pos := ((Rcpt_Line_Len - Length(Temp_Cust)) div 2) + 1;
                Move(Temp_Cust[1], Temp_Line[Char_Pos], Length(Temp_Cust));
              end
              else
                FUseThisOne := False;
            end;
          end;

          if FUseThisOne then      { if this one is going to be used }
          begin
            For C_Counter := 1 to length(Temp_Line) Do
            begin
              if (ord(Temp_Line[C_Counter]) < 32) then
                Temp_Line[C_Counter] := ' ';
            end;
            WriteRLine(Temp_Line);
          end;

          if (FInLineNum = EBT_Date_Line) then
          begin
            SetJsonReceiptBlock(Which_Copy, grbStoreData);    // CPCLIENTS-5932
            if (Trim(FHostLine5) <> '') then
              WriteRLine(FHostLine5);
            if (Trim(FHostLine6) <> '') then
              WriteRLine(FHostLine6);
            if (Trim(FHostLine7) <> '') and not (IsHostOneOf(FInrec.HostSuffixCode,'CBS/BYL/ATL/JET')) then //CPCLIENTS-19628
              WriteRLine(FHostLine7);
            if (trim(NovTranID) <> '') then                  // TSL DEV-10563
            begin
              SetJsonReceiptBlock(Which_Copy, grbCardInfo);    // CPCLIENTS-5932
              WriteRLine(' Tran ID ' + NOVtranID);
            end;
          end;

          //if UsingServerEPSHost then     //DEV-46471 - Receipt: Should remove EBT HIP from Receipts (HIP NOT supported in WinEPS)   //69739
            if (FInLineNum = EBT_Other_Bal_Line) and IsAtLeastOneHipBalExists then
            begin
              SetJsonReceiptBlock(Which_Copy, grbTotalAmount);    // CPCLIENTS-5932
              if not((IsHostOneOf(HostSuffixCode,'MPS/BYL')) and (TenderTypeValMTX = ttEBT_FS)) and (EBTHIPMtdBal > 0) then  //CPCLIENTS-19601 //18706  // CPCLIENTS-14663 - To not to Print HIP Amount as per WorldPay(MPS) //15421 Added BYL (FiServ) host to the condition
              begin
                Temp_Line := FormatCurr(' HIP Amount $##0.00', EBTHIPAmount/100);
                WriteRLine(Temp_Line);
              end;
              Temp_Line := '';
              if (EBTHIPCurBal <> BALANCE_MISSING) and (EBTHIPMtdBal > 0) and
                 not((IsHostOneOf(HostSuffixCode,'MPS/BYL')) and (TenderTypeValMTX = ttEBT_FS)) then //CPCLIENTS-19601 //18706 // CPCLIENTS-14663 - To not to print HIP Earned for WorldPay(MPS) as this line is printing after Purchase. //15421 Added BYL (FiServ) host to the condition
              begin
                if (ReqCodeN in [EBT_FS_ReturnN,User_2_ReturnN]) and (VoidCode <> 'V')
                  then Temp_Line := FormatCurr(' HIP Earned -$##0.00', EBTHIPCurBal/100)
                  else Temp_Line := FormatCurr(' HIP Earned $##0.00', EBTHIPCurBal/100);
              end;
              if (EBTHIPMtdBal <> BALANCE_MISSING) and (EBTHIPMtdBal > 0) then //18911
              begin
                if (IsHostOneOf(HostSuffixCode,'MPS/BYL')) and (TenderTypeValMTX = ttEBT_FS) then //18706 // CPCLIENTS-14663- To print HIP Month to Date as per WorldPay(MPS)  //15421 Added BYL (FiServ) host to the condition
                begin
                  WriteRLine(' Fruit/Veg Bonus Earned');
                  Temp_Line := FormatCurr(' Month-to-Date     $     0.00', EBTHIPMtdBal/100)
                end
                else
                  Temp_Line := Temp_Line + FormatCurr(' HIP MTD $##0.00', EBTHIPMtdBal/100);
              end;
              if (Temp_Line <> '') then
                WriteRLine(Temp_Line);
            end;
        Until (FInLineNum = EBT_Map_Lines);
        PrintCopyLine(Which_Copy); // CPCLIENTS-5933
        if Which_Copy = Cust_Copy then                                                      // CPCLIENTS-5932 start
        begin
          JsonReceipt.SetCurrentBlock(rbFooter);
          for i := 1 to 2 do
            JsonReceipt.AddLineToBlock(SignOnSet.ReceiptTrailLines[i]);
        end;                                                                                // CPCLIENTS-5932 end
      end;
  end; { FInrec , EBT_Defs }
  end; { EBT_Receipt  }

begin    { Build_Receipt }
  try
    msgDebug('Build_Receipt - IsBYL_LYN_Fallback=' + YN(IsBYL_LYN_Fallback) + '/ Is_EMV_VNT_BYL_LYN=' + YN(Is_EMV_VNT_BYL_LYN));
    TDCC.SigLinePrinted := False;
    EMVMgr.EBTCardHolder := ''; //CPCLIENTS-19133
    if IsReceiptRequired then  // if receipt not needed get out
    begin
      SetXingVar(Truncate);
      if FIsReceiptRequired and not TranApproved then
        FHostLine4 := '*DECLINED* ' + FHostLine4;
      if IsNOVECC then
        BuildNovaECCReceipt
      else if IsLYNECC then
        BuildCertegyECCReceipt
      else if (FInrec.ReqCodeN in EBT_Set) and (not IsHost(CBS)) then   // CPCLIENTS-15022
        EBT_Receipt
      else if IsPrintMinimalReceipt then
        MakeMinimalReceipt
      else if (Is_EMV_VNT_BYL_LYN or IsBYL_LYN_Fallback) and  // TFS-14778
        (FInrec.VoidCode <> 'V') then   // TFS-27263    roll forward from 828.7 126032  TFS-42272
        MakeEMVRcpt
      else
        Gen_Receipt;
    end;
  except
    on e : Exception do
      SM('****Try..Except: receipt.Build_Receipt - ' + e.message);
  end;
end;

Procedure TReceipt.Build_ReceiptNoSig(Host_PONumberUsed : string1; Truncate: string);
begin
  FReceiptWithNoSig := true;       { special flag for no sig line }
  Build_Receipt(Host_PONumberUsed, Truncate);
  FReceiptWithNoSig := false;
end;

Procedure TReceipt.Build_ReceiptH(Host_PONumberUsed : string1;
                         ExtraLine1 : string50;
                         ExtraLine2 : string50;
                         ExtraLine3 : string50;
                         ExtraLine4 : string50;
                         ExtraLine5 : string50;
                         ExtraLine6 : string50;
                         ExtraLine7 : string50;
                         Truncate: string;
                         const aStore: string ='');
begin
  FHostLine1 := ExtraLine1;
  FHostLine2 := ExtraLine2;
  FHostLine3 := ExtraLine3;
  FHostLine4 := ExtraLine4;
  FHostLine5 := ExtraLine5;
  FHostLine6 := ExtraLine6;
  FHostLine7 := ExtraLine7;
  Build_Receipt(Host_PONumberUsed, Truncate);
end;

function TReceipt.BYLATLReceiptMerchTerm(aIssuerCode: string): string50; // CPCLIENTS-5179 To have meaningful name for BYLATLIssuerCode/BYLATLReceiptMerchTerm as it is implemented
begin                    {Mrch=545644 Term=001 IC=EB }
  result :=  ' Mrch=' + HostBuf.MerchantNumber +
             ' Term=' + HostBuf.TerminalNumber +
             ' IC='   + aIssuerCode;
end;

function TReceipt.GetMaskedAcctNo: string;
begin
  if (FInRec.ReqCodeN in User_1_Set) then
  begin
    if (FinRec.VoidCode = 'V')
  {$IFDEF MTXEPSDLL}                 //64810
      then result := FRspMMR.AccountFirst6 + StringOfChar('X', FInRec.PanLen - 10) + FInRec.AcctNoLast4
      else
      begin
        if (FInRec.ReqCodeN = User_1_ReActivationN)
          then result := ReplaceString(MTX_Utils.TruncAcctNoForReportsOpenEPS(FRspMMR.PersonalAccountNumber), '*', 'X') // use ServerEPS Bd field
          else result := FInRec.AcctNoFirst6 + StringOfChar('X', FInRec.PanLen - 10) + FInRec.AcctNoLast4;
      end;
  {$ELSE}
      then result := FInRec.AcctNoFirst6 + StringOfChar('X', FInRec.PanLen - 10) + FInRec.AcctNoLast4;
  {$ENDIF}
  end
  else
  begin

    // HAM 11/17/2016 TFS 123453, check to see if the PAN Lenght has been set, if not then use
    // the PAN Length from the Response which is always guarenteed to be correct
    if( FInRec.PanLen <= 0 )then
    begin
      FInRec.PanLen := FRspMMR.PANLength;
    end;

    result := StringOfChar('X', FInRec.PanLen - 4) + FInRec.AcctNoLast4;
  end;

end;

function  TReceipt.MakeServerEPSAcctNoForChecks(TmpAcctNo: string): string;
var FMicr: TMICRValues;
    isToad: Boolean;
begin
  result := TmpacctNo;            // use what comes in, in case this tender is not check
  If (FInRec.ReqCodeN in check_Set) and (FInRec.primaryIDType <> idMICRString) then
  begin
    FMicr := TMicrValues.create;
    try
      isToad := IsHost(FLC); // DOEP-54818      //69739
      FMicr.SetMICRVars(FInrec, isToad);      // false means is not Toad format
      if (FMicr.ChkRouting <> '')
        then result := Copy(FMicr.ChkRouting,1,6) + '***' + FInRec.AcctNoLast4
        else result := Copy(FMicr.TAFormattedMICR,1,6) + '***' + FInRec.AcctNoLast4;
    finally
      FMicr.Free;
    end;
  end
  else
  if (result = '') then          // if nothing set, set normal truncation now
    result := GetMaskedAcctNo;
end;

function  TReceipt.MakeValuLinkAcctNoForReceipt: string;
var tmpTrack: string;
    tmpAcctNo: string;
    i: integer;
begin
  result := '';
  try
    tmpTrack := RetrieveMdMsgTrack2(FInRec);
    i := pos('=', tmpTrack);
    if (i > 0) and (IsProgID('TGT') or IsProgID('VAL') or IsProgID('WFM') or IsProgID('SBUX')) then     //57775
      tmpAcctNo := copy(tmpTrack,i+13,1) + copy(tmpTrack,i+15,2) + copy(tmpTrack,7,9) + rightStr(tmpTrack, 4)
    else
    begin
      tmpAcctNo := MTXEncryptionUtils.RetrieveMdMsgPersonalAccountNum(FInRec);
      if tmpAcctNo = '' then
        tmpAcctNo := tmpTrack;
    end;
    // DOEP-67285: TARGET - Not including PAN for all SVDOt GC receipts
    // Fixed to always truncate PAN for GC receipts
    result := TruncAcctNoForValuLink(tmpAcctNo);
    //if IsProgID('VAL') or IsProgID('WFM')
    //  then result := TruncAcctNoForValuLink(tmpAcctNo);
  except
    on e: exception do
      SM('****TRY..EXCEPT: MakeValuLinkAcctNoForReceipt ' + e.message);
  end;
end;

procedure TReceipt.SetEndCopy;                { Pick whether to do 2 receipts or 1 }
begin
  if (FIntOrSBReceipt = SA_Receipt)
    then End_Copy := Cust_Copy       { if Stand Alone, only 1 ( NCR PAPER ) }
    else End_Copy := Drawer_Copy;
end;

procedure TReceipt.SetReceiptRequired;
var
  ZTacPresent: boolean;
  CTacPresent: boolean;
  OTacPresent: boolean;

  function TermRspCodeNotLocal: boolean;
  begin
    result := (FInrec.TermRspCode <> 'NP') and (FInrec.TermRspCode <> 'NW') and
              (FInrec.TermRspCode <> 'NC') and (FInrec.TermRspCode <> 'NM');
  end;

begin
  ZTacPresent := (Pos(Chr_printReceipt, FInrec.CmdSequence) > 0);
  CTacPresent := (Pos(Chr_RcptCapture, FInrec.CmdSequence) > 0);
  OTacPresent := (Pos(Chr_SigCapture, FInrec.CmdSequence) > 0);
  if (Small_or_Large = 'N') then    { if no receipt in lane def, then exit }{ TSL-U }
    sm('****WARNING: Lane definition is set to NO receipt, none made')
  else
  if (FInrec.ReqCodeN in EBT_Set + User_1_Set) or  { print EBT appr or decl }
     ((FInrec.HostSuffixCode = 'TDB') and (FInrec.ReqCodeN in db_set + Prop_Db_Set)) then
    FIsReceiptRequired := true
  else
  if (FInrec.ReqCodeN in Check_Set) then             { if check auth }
  begin
    if IsHost('NOV') then
    begin
      if isTimedOutTrx then
      begin
        isTimedOutTrx := False;
        FIsReceiptRequired := false;
      end
      else
      if (FInrec.TermRspCode = rcDeclineND) then
      begin
        if (length(FInrec.NOVRspSource) > 0) and (FInrec.NOVRspSource[1] in ['A','4','5'])  // no receipt if not valid bit63:44
          then FIsReceiptRequired := true
          else FIsReceiptRequired := false;
      end
      else
      if (FInrec.Check_Type = ctOther) then
        FIsReceiptRequired := true
      else
      if (FInrec.TermRspCode = rcApprovedByECC) then   { only print if ECC }
        FIsReceiptRequired := true;
    end
    else
    if (FInrec.TermRspCode = rcApprovedByECC) then   { only print if ECC }
      FIsReceiptRequired := true
    else
    if ZTacPresent or CTacPresent or OTacPresent
      then FIsReceiptRequired := true
      else FIsReceiptRequired := false;           { reg check auth no receipt }
  end
  else  { other tenders and tran types }
  if (Finrec.ReqCodeN in BalInq_Set) then
  begin
    if ZTacPresent
      then FIsReceiptRequired := true
      else FIsReceiptRequired := false
  end
  else
  if (Finrec.ReqCodeN = User_1_ActivationN) and PublixHostDefined(Finrec.HostSuffixCode) and
     (copy(FinRec.TermRspCode,1,1) <> 'A') and (Finrec.VoidCode = 'V') then
    FIsReceiptRequired := false
  else
  if (ZTacPresent and TermRspCodeNotLocal) or TranApproved or IsHost(CBS) // TODO: what? OE not print receipt for decline??? // 13626 allow approved/declined receipt for CBS host(BAMS)
    then FIsReceiptRequired := true { print approvals or declines with Z TAC }
    else FIsReceiptRequired := false;
  if (FInRec.Entry <> '') and (FInRec.Entry[1] in [CEV_CHIPCARD, CEV_EMV_CTLS, CEV_RFID]) {and EMVMgr.IsTransCIDDeclined} {(EMVMgr.Gen2EMV9F27Tag = '00')} then     // TFS-15026   // TFS-18673
    FIsReceiptRequired := True;                                                                   // TFS-15026

  MsgDebug('SetReceiptRequired - FInRec.Entry=' + FInRec.Entry + '/ IsReceiptRequired=' + YN(IsReceiptRequired));
end;

procedure TReceipt.SetSmallOrLargeReceipt;
begin
  if (Small_or_Large <> Last_Was) then
  begin
    Last_Was := Small_or_Large;         { Set it now for check later }
    if (Small_or_Large = 'L') then
    begin
      Rcpt_Map_Lines := L_Rcpt_Map_Lines;
      Rcpt_Line_Len  := L_Rcpt_Line_Len;
      Max_Name_Len   := L_Max_Name_Len;
      EBT_Map_Lines  := L_EBT_Map_Lines;

      if (signOnSet.ReceiptHospitalityIncludeTip) and PrintTipLineOnReceipt then   // SAK TFS 198957 // CPCLIENTS-1942
      begin
        Move(L_Receipt_B, Receipt_A,  SizeOf(L_Receipt_A));
        Move(L_Defs_B,      Defs,       SizeOf(L_Defs));
      end
      else
      begin
        Move(L_Receipt_A, Receipt_A,  SizeOf(L_Receipt_A));
        Move(L_Defs,      Defs,       SizeOf(L_Defs));
      end;
      Move(L_EBT_Rcpt,  EBT_Rcpt,   SizeOf(L_EBT_Rcpt));
      Move(L_EBT_Defs,  EBT_Defs,   SizeOf(L_EBT_Defs));

      Str_Declined0 := L_Str_Declined0;
      Str_Declined1 := L_Str_Declined1;
      Str_Declined2 := L_Str_Declined2;
      Str_Declined3 := L_Str_Declined3;
      Str_Declined4 := L_Str_Declined4;

      Str_Training  := L_Str_Training;
    end
    else
    begin
      Rcpt_Map_Lines := S_Rcpt_Map_Lines;
      Rcpt_Line_Len  := S_Rcpt_Line_Len;
      Max_Name_Len   := S_Max_Name_Len;
      EBT_Map_Lines  := S_EBT_Map_Lines;

      if (signOnSet.ReceiptHospitalityIncludeTip) and PrintTipLineOnReceipt then      // SAK TFS 198957 // CPCLIENTS-1942
        begin
          Move(S_Receipt_B, Receipt_A,  SizeOf(S_Receipt_A));
          Move(S_Defs_B,      Defs,       SizeOf(S_Defs));
        end
      else
        begin
          Move(S_Receipt_A, Receipt_A,  SizeOf(S_Receipt_A));
          Move(S_Defs,      Defs,       SizeOf(S_Defs));
        end;
      Move(S_EBT_Rcpt,  EBT_Rcpt,   SizeOf(S_EBT_Rcpt));
      Move(S_EBT_Defs,  EBT_Defs,   SizeOf(S_EBT_Defs));

      Str_Declined0 := S_Str_Declined0;
      Str_Declined1 := S_Str_Declined1;
      Str_Declined2 := S_Str_Declined2;
      Str_Declined3 := S_Str_Declined3;
      Str_Declined4 := S_Str_Declined4;

      Str_Training  := S_Str_Training;
    end;
  end;
end;

function  TReceipt.TranApproved: boolean;
begin
  MsgDebug('TReceipt.TranApprove FInrec.TermRspCode=' + FInrec.TermRspCode);
  result := (FInrec.TermRspCode = rcApprovedByECC)    or
            (FInrec.TermRspCode = rcApprovedByWinEPS) or
            (FInrec.TermRspCode = rcApprovedByHost);
end;

procedure TReceipt.Build_ReceiptForHost(const aStore: string = ''; const aMerchLanguage: string = '');
var ExtraMsg,
    HostMsg,
    entryMode: string; //8463
begin
  FMerchLanguage := aMerchLanguage;
  TDCC.SigLinePrinted := False;
  IsLoggedInfoAlready := False; // CPCLIENTS-18975
  msgNotice('Build_ReceiptForHost: Merchant Language >%s<',[FMerchLanguage]);

  if MR.IsHoldTransaction and TranApproved then                                              // CPCLIENTS-10531 Start
  begin
    SetCardProcIDAndCardName(FInRec, MR.CardType);
    BarTabStuff.WriteHoldTabData(FInRec.SeqNo, FInrec);
  end
  else if MR.IsReleaseTransaction then
  begin
    BarTabStuff.ReadHoldTabData(FInRec.SeqNo, FInRec);
    Set_WinEPSCardType(1, RPad(FInRec.CardProcID, 2) + Rpad(FInRec.CardName, 16));
  end;                                                                                       // CPCLIENTS-10531 End
  MsgDebug(Format('FInRec.CardProcId=%s FInRec.CardName=%s', [FInRec.CardProcId, FInRec.CardName]));

  if FInRec.ReqCodeN = CrAdjustmentN then  // 8463
  begin
    entryMode := GetOriginalTransactionData(RspMMR.OriginalTransactionData, 'Bf');
    msgDebug(Format('Build_ReceiptForHost: entryMode=%s', [entryMode]));
    if entryMode = CARD_ENTRY_MODE_EMV_CTLS then
      FInRec.Entry := CEV_EMV_CTLS
    else if entryMode = CARD_ENTRY_MODE_FALLBACK then
      FInRec.Entry := CEV_FALLBACK
    else
      FInRec.Entry := entryMode;
  end;

  msgNotice(Format('  Host=%s Entry=%s VoidCode=%s', [FInrec.HostSuffixCode, FInRec.Entry, FInRec.VoidCode]));  // TFS-42272
  FIsContEMVCidDeclined := EMVMgr.IsContEMVCidDecline;  // TFS-26303

  if (FInRec.Entry[1] in [CEV_CHIPCARD, CEV_FALLBACK, CEV_EMV_CTLS]) and
    (not (FInRec.ReqCodeN in EBT_Cash_Set + EBT_FS_Set)) then  //CPCLIENTS-18819 // EMV transaction // TFS-29130 added Fallback to EMV Receipts  // TFS-137696 // TFS-147506
  begin                                                                                                           // TFS-16031 TFS-16037 TFS-16036 // TFS-16310
    if (FInRec.VoidCode = 'V')  then // TFS-27263      roll forward from 828.7 126032  TFS-42272
    begin
      msgDebug('Build_ReceiptForHost - VoidCode');
      Build_Receipt(HostBuf.AddressLines, HostBuf.HostTruncateAcctNo);
    end
    else
    begin
      if IsHostOneOf(FInrec.HostSuffixCode, 'VNT/MPS/BYL/LYN/CHS/ATL/NOV/ACI/RPD/PBL/JET/CBS') then // TFS-14301, TFS-15013, TFS-16648 TFS-28014 added PBL // CPC-5058: JetPay // CPCLIENTS-11513-BAMS
      begin
        msgDebug('Build_ReceiptForHost - MakeEMVRcpt');
        MakeEMVRcpt;
      end
      else
      begin
        msgDebug('Build_ReceiptForHost - BuildTDBReceipt');
        BuildTDBReceipt;
      end;
    end;
  end
  else
  if IsHost('LYN') then
  begin
    Build_ReceiptH(HostBuf.AddressLines,
                    LynkRetrievalData,
                    LynkMerchantID + LynkTermIDDeviceID,
                    '',
                    '',
                    LynkBatchandReceipt,
                    LynkFNSNumberOrOdometer,
                    '',
                    HostBuf.HostTruncateAcctNo);
  end
  else
  if IsHost('FLC') then
  begin
    if TranApproved
      then HostMsg := ''    // only want host message if declined
      else HostMsg := ' ' + FInrec.HostTextMsg;

    if ISFLCECC then
      BuildBuypassECCReceipt(HostBuf.HostTruncateAcctNo)
    else
    begin
      Build_ReceiptH(HostBuf.AddressLines,
                     'TraceID: ' + FInrec.TraceID,
                     '',
                     '',
                     '',
                     HostMsg,
                     '',
                     '',
                     HostBuf.HostTruncateAcctNo, aStore);
    end;
  end
  else
  // CPCLIENTS-5020 Added ATL host for receipt
  if IsBYLHostGroup(FInrec.HostSuffixCode) and (Trim(EMVMgr.AppID) = '') then     // TFS-14778 Add BYL
  begin
    msgDebug('Build_ReceiptForHost - BYLHostGroup, No AppID');
    if (trim(FInrec.EBT_Voucher_Num) <> '')
      then ExtraMsg := ' Voucher# ' + FInrec.EBT_Voucher_Num
      else ExtraMsg := '';

    if TranApproved
      then HostMsg := ''    // only want host message if declined
      else HostMsg := ' ' + FInrec.HostTextMsg;

    SetICforATL; //8710 Set issuer code for ATL based on Af field

    if IsBYLATLECC then   // CPCLIENTS-5020
      BuildBuypassECCReceipt(HostBuf.HostTruncateAcctNo)
    else
    begin
      Build_ReceiptH(HostBuf.AddressLines,
                     '',
                     '',
                     '',
                     '',
                     HostMsg,
                     BYLATLReceiptMerchTerm(FInrec.BYLATLIssuerCode), // CPCLIENTS-5179 To have meaningful name for BYLATLIssuerCode/BYLATLReceiptMerchTerm as it is implemented for ATL too
                     ExtraMsg,
                     HostBuf.HostTruncateAcctNo, aStore);
      if (FInrec.ReqCodeN = wireLessActivationN) then
        AddWMsgToReceipt(wireLessPIN, wireLessTracking, wireLessAccess);
    end;
  end
  else
  if IsHost('ACI') then
    Build_ReceiptH(HostBuf.AddressLines,
                 '',
                 '',
                 '',
                 '',
                 '',
                 fnPosTracking(FInrec.ACIPOSTrackingNumber),
                 fnDOB(FInrec.DOB),
                 HostBuf.HostTruncateAcctNo, aStore)
  else
  if IsHost('TDB') or (HostBuf.Suffix = 'TDB') then  // DOEP-41857
    BuildTDBReceipt
  else
    Build_Receipt(HostBuf.AddressLines, HostBuf.HostTruncateAcctNo);
end;

function TReceipt.GetCid: AnsiString;      // TFS-18673 moved from below
begin
  if (EMVMgr.Gen2EMV9F27Tag <> '' {EMVMgr.cid <> ''}) then    // TFS-15026
  begin
    Result := EMVMgr.Gen2EMV9F27Tag; //  EMVMgr.cid;                                                // TFS-15026
    if ((FInrec.CardProcID = 'MC') or (FInrec.CardProcID = 'MT') or (FInrec.CardProcID = 'AM')) and
        ({IsApprovedOffline or} IsDeclinedOffline) {or IsMACOrChipDecline} then
    begin
        Result := EMVMgr.c33_cid;
        if Result <> '' then
          sm('Using cid value from c33_cid = ' + IntToStr(ord(Result[1])));
        // tc := EMVMgr.c33_tc;
    end;
  end;
end;

function TReceipt.IsBYL_LYN_Fallback: Boolean;                               // TFS-15013    added LYN
begin
  Result := IsHostOneOf(FInrec.HostSuffixCode, 'BYL/LYN/CHS/ATL/NOV/VNT/ACI/RPD/MPS/PBL/JET') // TFS-20215 added MPS // TFS-111392: added PBL // CPC-5058
    and (FInRec.Entry = CEV_FALLBACK);       // TFS-15013   // TFS-16031 TFS-16037 TFS-16036, TFS-16648
end;

function TReceipt.IsCidOfflineDecline(cid: AnsiString): boolean;   // TFS-18673 made it a function in the class
begin
  Result := EMVMgr.IsTransCIDDeclined and (not EMVMgr.WeSentY3) and (not (FInRec.Entry = CEV_FALLBACK));   // TFS-18673
              // TFS-13971    // TFS-18673
  if not Result then                                // TFS-137696 // TFS-147506
    Result := EMVMgr.IsEMVContactlessCIDDeclined(FInrec.ReqCodeN);   // TFS-137696 // TFS-178257
end;

function TReceipt.Is_EMV_VNT_BYL_LYN: boolean;
var
  cid: AnsiString;
begin                                                 // TFS-1430   // TFS-14778 Add BYL // TFS-15013 Add LYN
  cid := GetCid;
  result := IsHostOneOf(FInrec.HostSuffixCode, 'VNT/MPS/BYL/LYN/CHS/ATL/NOV/ACI/RPD/JET') // TFS-16648 // TFS-16031 TFS-16037 TFS-16036 // TFS-16310 // CLC-5058: JetPay
    // (IsHost('VNT') or IsHost('MPS') or IsHost('BYL') or IsHost('LYN') or IsHost('CHS') or IsHost('ATL'))       // TFS-16031 TFS-16037 TFS-16036
        and (FInrec.ReqCodeN in [crPurchN, crReturnN, DbPurchN, DbReturnN]) and (FInRec.Entry[1] = CEV_CHIPCARD) {(EMVMgr.AppID > '')}
        and (TranApproved or IsCidOfflineDecline(cid));
end;

procedure TReceipt.MakeEMVRcpt;                        // TFS-15013
var
  cid: AnsiString;
  RcptType: TRcptType;

begin
  if Not Assigned(FReceiptCommon) then
    FReceiptCommon := TReceiptCommon.Create(FInrec.HostSuffixCode); // CPCLIENTS-15022 - Modified TReceiptCommon.Create method to pass host name which can be used while creating the Receipt line fields.

  cid := GetCid;

  sm('cid value=' + cid);
  sm('IsCidOfflineDecline=' + BoolToStr(IsCidOfflineDecline(cid), True));
  sm('IsDeclinedOffline=' + BoolToStr(IsDeclinedOffline, True));
  sm('EMVMgr.IsContEMVCidDecline=' + BoolToStr(EMVMgr.IsContEMVCidDecline, True));

  // TFS-18673, 18851 - Set EMV receipt data for all credit and debit transactions
  if (not (FInrec.ReqCodeN in Cr_Set + Prop_Cr_Set + Db_Set + Prop_Db_Set)) then
  begin
    sm(Format('EMV receipt data only for credit and debit - not for ReqCodeN[%d]', [FInRec.ReqCodeN]));
    Exit;
  end;

  if (FInRec.VoidCode = 'V') then
    RcptType := rtVoid
  else
  // if ord(EMVMgr.c33_cid[1]) and 64 {b01000000} > 0 then  // This indicates the card approved it offline
  if IsCidOfflineDecline(cid) or EMVMgr.IsContEMVCidDecline then   // Chip declined              // TFS-9634   // TFS-26303
  begin
    if (FInrec.ReqCodeN in All_Return_Trxs)  then
      RcptType := rtChipRefundDeclined
    else
      RcptType := rtChipDeclined;
  end
  else if IsDeclinedOffline then   // offline declined
  begin
    if (FInrec.ReqCodeN in All_Return_Trxs)  then
      RcptType := rtRefundOfflineDeclined
    else
      RcptType := rtOfflineDeclined;
  end
  else // approvals
  begin
    if (FInrec.ReqCodeN in All_Return_Trxs)  then
      RcptType := rtRefund
    else
      RcptType := rtPurchase
  end;
  if Assigned(FDrwrReceipt) then
    FDrwrReceipt.Free;
  FDrwrReceipt := FReceiptCommon.PrintReceipt(MAXLINELEN, ctMerchant, RcptType, FInRec, {EMVMgr,} FEMVC31ErrorCode, FIsContEMVCidDeclined, FRspMMR, PrintTipLineOnReceipt); // CPCLIENTS-1942
  if (RcptType <> rtVoid) then
  begin
    if Assigned(FCustReceipt) then
      FCustReceipt.Free;
    FCustReceipt := FReceiptCommon.PrintReceipt(MAXLINELEN, ctCustomer, RcptType, FInRec, {EMVMgr,} FEMVC31ErrorCode, FIsContEMVCidDeclined, FRspMMR, PrintTipLineOnReceipt); // CPCLIENTS-1942
  end;
end;

{ // XE: Remove WinEPS - not in use
procedure TReceipt.ClearCustReceipt;
begin
  FCustReceipt.Clear;
end;

procedure TReceipt.ClearDrwrReceipt;
begin
  FDrwrReceipt.Clear;
end;
}

procedure TReceipt.SetICforATL;
begin
  if IsHost(ATL) then   //8846 For ATL Af field value of '99' in SE_RECV to be considered instead of HostBuf.ChkCode
  begin
    if FInrec.SwRspCode = ATLRspCode_ChkElectronic then
      FInrec.BYLATLIssuerCode := 'TE'
    else if FInrec.SwRspCode = ATLRspCode_ChkPaper then
      FInrec.BYLATLIssuerCode := 'TK'
    else FInrec.BYLATLIssuerCode := '';
  end;
end;

procedure TReceipt.SetINCPhoneCardVars(aPIN, aMsg: string);
begin
  FPhoneCardPIN := aPIN;
  // FPhoneCardMsg.Clear;                                                                       // CPCLIENTS-12067
  // FPhoneCardMsg.text := ReplaceString(aMsg,LF,CRLF);        // $0A used to separate lines    // CPCLIENTS-12067
  TDCC.FormatLongMsgForPrinting(aMsg, FPhoneCardMsg, MAXLINELEN);                               // CPCLIENTS-12067
end;

procedure TReceipt.SetLynkVars(aBatch: string; aReceiptData: string);
begin
  FLynkBatch := aBatch;
  FLynkReceiptData := aReceiptData;
end;

procedure TReceipt.SetFInRec(Inrec: MdMsgRec); // DOEP-42647
begin
  FInRec := Inrec;
end;

//How it works:#1.store config xml must have HospitalityIncludeTip set to Y (global tip line setting)
//#2. if 1 is true, then check CPP xml (tender specific tip line setting
//TODO: Refactor to improve code clarity. For this we need to Look into pulling ReceiptHospitalityIncludeTip into PrintTipLineOnReceipt.
function TReceipt.PrintTipLineOnReceipt : Boolean; // CPCLIENTS-1942
begin
  if not Assigned(FCTBuf) then
  begin
    MsgDebug('TReceipt.PrintTipLineOnReceipt FCTBuf is nil');
    Result := True; //CPCLIENTS-4094, By default Return to True, if FCTBuf is not yet assigned e.g. RT (Release Tab) scenario.
    Exit;
  end;
  Result := Assigned(FCTBuf) and FCTBuf.ProcessingFlags.TipLineOnReceipt;
  MsgDebug('TReceipt.PrintTipLineOnReceipt result=' + BoolStr(result));
end;

procedure TReceipt.PrintCopyLine(Which_Copy: integer); // CPCLIENTS-5933 Print Customer/Merchant Label
begin
  if printWhichCopy then
  begin
    SetJsonReceiptBlock(Which_Copy, grbCopy);              // CPCLIENTS-5932
    case which_copy of
      Cust_Copy   :  PrintCopy('CUSTOMER COPY');
      Drawer_Copy :  PrintCopy('MERCHANT COPY');
    end;
  end;
end;

procedure TReceipt.PrintAID(Which_Copy: integer);   // CPCLIENTS-2788, 2959 // CPCLIENTS-5932
begin
  MsgDebug('TReceipt.PrintAID AID=' + EMVMgr.AppID);
  if EMVMgr.AppID <> '' then
  begin
    SetJsonReceiptBlock(Which_Copy, grbEMVData);              // CPCLIENTS-5932
    WriteRLine(' ');
    WriteRLine(' AID:' + EMVMgr.AppID);
    WriteRLine(' ');
  end;
  FAIDPrinted := True;
end;

//CPCLIENTS-19133 Print EBT Data such as AppLabel, AID, and Entry Method for Contact/Contactless on receipt
procedure TReceipt.PrintEBTChipData(Which_Copy: integer);
var
  tmpEntry: String;
begin
  tmpEntry := '';
  MsgDebug('TReceipt.PrintEBTChipData AppLabel=' + EMVMgr.AppLabel);
  MsgDebug('TReceipt.PrintEBTChipData AID=' + EMVMgr.AppID);
  MsgDebug('TReceipt.PrintEBTChipData EntryMethod=' + EMVMgr.CardEntryType);
  SetJsonReceiptBlock(Which_Copy, grbEMVData);
  if EMVMgr.AppLabel <> '' then
  begin
    WriteRLine(' Chip Card:       ' + EMVMgr.AppLabel);
  end;
  if EMVMgr.AppID <> '' then
  begin
    WriteRLine(' AID:             ' + EMVMgr.AppID);
  end;
  if EMVMgr.CardEntryType <> '' then
  begin
    if EMVMgr.IsEMVContactRead then
      tmpEntry := 'Chip Read'
    else if EMVMgr.IsEMVContactlessTrans then
      tmpEntry := 'Contactless';
    if tmpEntry <> '' then
      WriteRLine(' Entry Method:    ' + tmpEntry);
  end;
end;

initialization
  ExtendedLog('receiptClass Initialization');
finalization
  ExtendedLog('receiptClass Finalization');

end.


