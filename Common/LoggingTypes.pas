unit LoggingTypes;

interface

uses
  SysUtils, Classes, Generics.Collections, SyncObjs{$IFDEF USE_CODESITE}, CodeSiteLogging{$ENDIF};

const
  DEFAULT_LOG_FILE_NAME = 'DefaultJournal.txt';
  DEBUG = '[DEBUG] ';
  SSL_DEBUG = '[SSLDEBUG] ';
  SENSITIVE = '[*** SENSITIVE - NOT FOR PRODUCTION ***] ';

type
  TLogType    = (ltBasic, ltDebug, ltSSLDebug, ltSensitive);
  TFileChange = (fcRenameCurrentFile, fcStartNewFile, fcRenameKeepingCurrentFile);

TFixedCriticalSection = class(TCriticalSection)
private
  FDummy: array[0..95] of Byte;
end;

TSimpleThreadedQueue<T> = class
strict private
  FQueue: TQueue<T>;
  FCriticalSection: TFixedCriticalSection;
  FSemaphore: TSemaphore;
public
  constructor Create;
  destructor Destroy; override;
  procedure Enqueue(const Item: T);
  function Dequeue(var Item: T; Timeout: LongWord = INFINITE): TWaitResult;
  function QueueSize: integer;
end;

implementation

constructor TSimpleThreadedQueue<T>.Create;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'Create' );{$ENDIF}
  inherited Create;
  FCriticalSection := TFixedCriticalSection.Create;
  FQueue := TQueue<T>.Create;
  FSemaphore := TSemaphore.Create(nil, 0, MaxInt, '');
end;

destructor TSimpleThreadedQueue<T>.Destroy;
begin
  FreeAndNil(FQueue);
  FreeAndNil(FCriticalSection);
  FreeAndNil(FSemaphore);
  inherited Destroy;
end;

procedure TSimpleThreadedQueue<T>.Enqueue(const Item: T);
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'Enqueue' );{$ENDIF}
  FCriticalSection.Enter;
  try
    FQueue.Enqueue(Item);
  finally
    FCriticalSection.Leave;
  end;
  FSemaphore.Release;
end;

function TSimpleThreadedQueue<T>.Dequeue(var Item: T; Timeout: LongWord): TWaitResult;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'Dequeue' );{$ENDIF}
  Result := FSemaphore.WaitFor(Timeout);
  if Result <> wrSignaled then Exit;
  FCriticalSection.Enter;
  try
    Item := FQueue.Dequeue;
  finally
    FCriticalSection.Leave;
  end;
end;

function TSimpleThreadedQueue<T>.QueueSize: Integer;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'QueueSize' );{$ENDIF}
  FCriticalSection.Enter;
  try
    Result := FQueue.Count;
  finally
    FCriticalSection.Leave;
  end;
end;

end.


