// (c) MTXEPS, Inc. 1988-2008
{*************************************************************************}
{                                                                         }
{                         Delphi XML Data Binding                         }
{                                                                         }
{         Generated on: 6/4/2007 9:04:34 PM                               }
{       Generated from: C:\Program Files\MicroTrax\EPS\WinEpsStatus.xml   }
{                                                                         }
{*************************************************************************}
//  JTG: and manually edited several times since the above date

//v8270.0 2010-10-29 JTG added KEK3 tags and attributes, had to manually merge from an autogenerated
//  XML (using the File|New|Other|Data Binding Wizard tool, using the XML from Hahn
//  I don't know why we can't simply auto-gen except that I think some people manually edited this
//  and now we are stuck. Doesn't make sense though, if we had a complete XML to gen from, it should gen the same....

unit WinEpsStatusXML;

interface

uses
  FinalizationLog,
  xmldom, XMLDoc, XMLIntf;

type

{ Forward Decls }

  IXMLWinEpsStatusType = interface;
  IXMLLocalMachineType = interface;
  IXMLWinEpsKEK3Type = interface;
  IXMLWinEpsServiceType = interface;
  IXMLDriveType = interface;
  IXMLIpAddressesType = interface;
  IXMLModulesType = interface;
  IXMLModuleType = interface;
  IXMLHostsType = interface;
  IXMLHostType = interface;
  IXMLPendingTransactionsType = interface;
  IXMLCompleteTransactionsType = interface;
  IXMLRedundancyType = interface;
  IXMLLanesType = interface;
  IXMLLaneStatusType = interface;
  IXMLOpenEpsKEK3Type = interface;
  IXMLPinPadType = interface;
  IXMLConfigFilesType = interface;
  IXMLTermConfigType = interface;
  IXMLCardProcessingProfilesType = interface;

{ IXMLWinEpsStatusType }

  IXMLWinEpsStatusType = interface(IXMLNode)
    ['{E2172788-09C0-4D74-980A-B7ADAAE2276A}']
    { Property Accessors }
    function Get_Version: WideString;
    function Get_LastModified: WideString;
    function Get_LocalMachine: IXMLLocalMachineType;
    function Get_Lanes: IXMLLanesType;
    procedure Set_Version(Value: WideString);
    procedure Set_LastModified(Value: WideString);
    { Methods & Properties }
    property Version: WideString read Get_Version write Set_Version;
    property LastModified: WideString read Get_LastModified write Set_LastModified;
    property LocalMachine: IXMLLocalMachineType read Get_LocalMachine;
    property Lanes: IXMLLanesType read Get_Lanes;
  end;

{ IXMLLocalMachineType }

  IXMLLocalMachineType = interface(IXMLNode)
    ['{2BFE1351-F6ED-40D5-8DD2-3BA6259D7446}']
    { Property Accessors }
    function Get_UpdateTime: WideString;
    function Get_WinEpsKEK3: IXMLWinEpsKEK3Type;
    function Get_WinEpsService: IXMLWinEpsServiceType;
    function Get_Drive: IXMLDriveType;
    function Get_OSVersion: WideString;
    function Get_LastDebitBINUpdate: WideString;
    function Get_LastFSABINUpdate: WideString;
    function Get_IpAddresses: IXMLIpAddressesType;
    function Get_Modules: IXMLModulesType;
    function Get_Hosts: IXMLHostsType;
    function Get_Redundancy: IXMLRedundancyType;
    procedure Set_UpdateTime(Value: WideString);
    procedure Set_OSVersion(Value: WideString);
    procedure Set_LastDebitBINUpdate(Value: WideString);
    procedure Set_LastFSABINUpdate(Value: WideString);
    { Methods & Properties }
    property UpdateTime: WideString read Get_UpdateTime write Set_UpdateTime;
    property WinEpsKEK3: IXMLWinEpsKEK3Type read Get_WinEpsKEK3;
    property WinEpsService: IXMLWinEpsServiceType read Get_WinEpsService;
    property Drive: IXMLDriveType read Get_Drive;
    property OSVersion: WideString read Get_OSVersion write Set_OSVersion;
    property LastDebitBINUpdate: WideString read Get_LastDebitBINUpdate write Set_LastDebitBINUpdate;
    property LastFSABINUpdate: WideString read Get_LastFSABINUpdate write Set_LastFSABINUpdate;
    property IpAddresses: IXMLIpAddressesType read Get_IpAddresses;
    property Modules: IXMLModulesType read Get_Modules;
    property Hosts: IXMLHostsType read Get_Hosts;
    property Redundancy: IXMLRedundancyType read Get_Redundancy;
  end;

{ IXMLWinEpsKEK3Type }

  IXMLWinEpsKEK3Type = interface(IXMLNode)
    ['{1DE18A17-D416-4017-BE9E-50B342D51AD9}']
    { Property Accessors }
    function Get_Timestamp: WideString;
    procedure Set_Timestamp(Value: WideString);
    { Methods & Properties }
    property Timestamp: WideString read Get_Timestamp write Set_Timestamp;
  end;

{ IXMLWinEpsServiceType }

  IXMLWinEpsServiceType = interface(IXMLNode)
    ['{47525945-CB06-4B3F-8180-E2ADAF4009D8}']
    { Property Accessors }
    function Get_Dir: WideString;
    function Get_Status: WideString;
    procedure Set_Dir(Value: WideString);
    procedure Set_Status(Value: WideString);
    { Methods & Properties }
    property Dir: WideString read Get_Dir write Set_Dir;
    property Status: WideString read Get_Status write Set_Status;
  end;

{ IXMLDriveType }

  IXMLDriveType = interface(IXMLNode)
    ['{E5A531E8-F481-4A1C-A510-9D06946059E2}']
    { Property Accessors }
    function Get_Letter: WideString;
    function Get_Size: Integer;
    function Get_FreeSpace: Integer;
    function Get_DriveSize: Integer;
    procedure Set_Letter(Value: WideString);
    procedure Set_Size(Value: Integer);
    procedure Set_FreeSpace(Value: Integer);
    procedure Set_DriveSize(Value: Integer);
    { Methods & Properties }
    property Letter: WideString read Get_Letter write Set_Letter;
    property Size: Integer read Get_Size write Set_Size;
    property FreeSpace: Integer read Get_FreeSpace write Set_FreeSpace;
    property DriveSize: Integer read Get_DriveSize write Set_DriveSize;
  end;

{ IXMLIpAddressesType }

  IXMLIpAddressesType = interface(IXMLNodeCollection)
    ['{0B1C087F-5B23-4E0C-B45A-8AA17182B473}']
    { Property Accessors }
    function Get_IPAddress(Index: Integer): WideString;
    { Methods & Properties }
    function Add(const IPAddress: WideString): IXMLNode;
    function Insert(const Index: Integer; const IPAddress: WideString): IXMLNode;
    property IPAddress[Index: Integer]: WideString read Get_IPAddress; default;
  end;

{ IXMLModulesType }

  IXMLModulesType = interface(IXMLNodeCollection)
    ['{FEB97666-2C69-4B3F-AB7B-511176DC6265}']
    { Property Accessors }
    function Get_Module(Index: Integer): IXMLModuleType;
    { Methods & Properties }
    function Add: IXMLModuleType;
    function Insert(const Index: Integer): IXMLModuleType;
    property Module[Index: Integer]: IXMLModuleType read Get_Module; default;
  end;

{ IXMLModuleType }

  IXMLModuleType = interface(IXMLNode)
    ['{15CA9612-F4DC-4C00-9F86-DC660CA97A23}']
    { Property Accessors }
    function Get_Name: WideString;
    function Get_Version: WideString;
    procedure Set_Name(Value: WideString);
    procedure Set_Version(Value: WideString);
    { Methods & Properties }
    property Name: WideString read Get_Name write Set_Name;
    property Version: WideString read Get_Version write Set_Version;
  end;

{ IXMLHostsType }

  IXMLHostsType = interface(IXMLNodeCollection)
    ['{994700DD-2D1C-4F2B-AC6A-DF739230AE18}']
    { Property Accessors }
    function Get_Host(Index: Integer): IXMLHostType;
    { Methods & Properties }
    function Add: IXMLHostType;
    function Insert(const Index: Integer): IXMLHostType;
    property Host[Index: Integer]: IXMLHostType read Get_Host; default;
  end;

{ IXMLHostType }

  IXMLHostType = interface(IXMLNode)
    ['{CA37CEC0-F794-4C93-B034-A42243AFB802}']
    { Property Accessors }
    function Get_Name: WideString;
    function Get_Status: WideString;
    function Get_PendingTransactions: IXMLPendingTransactionsType;
    function Get_CompleteTransactions: IXMLCompleteTransactionsType;
    procedure Set_Name(Value: WideString);
    procedure Set_Status(Value: WideString);
    { Methods & Properties }
    property Name: WideString read Get_Name write Set_Name;
    property Status: WideString read Get_Status write Set_Status;
    property PendingTransactions: IXMLPendingTransactionsType read Get_PendingTransactions;
    property CompleteTransactions: IXMLCompleteTransactionsType read Get_CompleteTransactions;
  end;

{ IXMLPendingTransactionsType }

  IXMLPendingTransactionsType = interface(IXMLNode)
    ['{2915275F-863F-47B6-A944-3E4544C48431}']
    { Property Accessors }
    function Get_OfflineCount: Integer;
    function Get_OfflineAmount: Integer;
    function Get_TorCount: Integer;
    function Get_TorAmount: Integer;
    function Get_SignatureCount: Integer;
    procedure Set_OfflineCount(Value: Integer);
    procedure Set_OfflineAmount(Value: Integer);
    procedure Set_TorCount(Value: Integer);
    procedure Set_TorAmount(Value: Integer);
    procedure Set_SignatureCount(Value: Integer);
    { Methods & Properties }
    property OfflineCount: Integer read Get_OfflineCount write Set_OfflineCount;
    property OfflineAmount: Integer read Get_OfflineAmount write Set_OfflineAmount;
    property TorCount: Integer read Get_TorCount write Set_TorCount;
    property TorAmount: Integer read Get_TorAmount write Set_TorAmount;
    property SignatureCount: Integer read Get_SignatureCount write Set_SignatureCount;
  end;

{ IXMLCompleteTransactionsType }

  IXMLCompleteTransactionsType = interface(IXMLNode)
    ['{2D5D413D-A4A3-4C1B-9E8F-9080F2BFD575}']
    { Property Accessors }
    function Get_ApprovedCount: Integer;
    function Get_ApprovedAmount: Integer;
    function Get_DeclinedCount: Integer;
    procedure Set_ApprovedCount(Value: Integer);
    procedure Set_ApprovedAmount(Value: Integer);
    procedure Set_DeclinedCount(Value: Integer);
    { Methods & Properties }
    property ApprovedCount: Integer read Get_ApprovedCount write Set_ApprovedCount;
    property ApprovedAmount: Integer read Get_ApprovedAmount write Set_ApprovedAmount;
    property DeclinedCount: Integer read Get_DeclinedCount write Set_DeclinedCount;
  end;

{ IXMLRedundancyType }

  IXMLRedundancyType = interface(IXMLNode)
    ['{E6C8ECBD-C78D-454D-AC51-D792441A06E0}']
    { Property Accessors }
    function Get_ServerType: WideString;
    function Get_Status: WideString;
    procedure Set_ServerType(Value: WideString);
    procedure Set_Status(Value: WideString);
    { Methods & Properties }
    property ServerType: WideString read Get_ServerType write Set_ServerType;
    property Status: WideString read Get_Status write Set_Status;
  end;

{ IXMLLanesType }

  IXMLLanesType = interface(IXMLNodeCollection)
    ['{5DB94E28-293A-4BF8-8215-816CC9C90AA4}']
    { Property Accessors }
    function Get_Lane(Index: Integer): IXMLLaneStatusType;
    { Methods & Properties }
    function Add: IXMLLaneStatusType;
    function Insert(const Index: Integer): IXMLLaneStatusType;
    property Lane[Index: Integer]: IXMLLaneStatusType read Get_Lane; default;
  end;

{ IXMLLaneStatusType }

  IXMLLaneStatusType = interface(IXMLNode)
    ['{D892EDBB-A3F9-4751-9A1A-87237F58E850}']
    { Property Accessors }
    function Get_Number: Integer;
    function Get_LaneType: WideString;
    function Get_UpdateTime: WideString;
    function Get_OpenEpsKEK3: IXMLOpenEpsKEK3Type;
    function Get_Drive: IXMLDriveType;
    function Get_OSVersion: WideString;
    function Get_IpAddresses: IXMLIpAddressesType;
    function Get_PosApplicationVersion: WideString;
    function Get_PendingTransactions: IXMLPendingTransactionsType;
    function Get_PinPad: IXMLPinPadType;
    function Get_Modules: IXMLModulesType;
    function Get_ConfigFiles: IXMLConfigFilesType;
    function Get_BioStatus: WideString;
    procedure Set_Number(Value: Integer);
    procedure Set_LaneType(Value: WideString);
    procedure Set_UpdateTime(Value: WideString);
    procedure Set_OSVersion(Value: WideString);
    procedure Set_PosApplicationVersion(Value: WideString);
    procedure Set_BioStatus(Value: WideString);
    { Methods & Properties }
    property Number: Integer read Get_Number write Set_Number;
    property LaneType: WideString read Get_LaneType write Set_LaneType;
    property UpdateTime: WideString read Get_UpdateTime write Set_UpdateTime;
    property OpenEpsKEK3: IXMLOpenEpsKEK3Type read Get_OpenEpsKEK3;
    property Drive: IXMLDriveType read Get_Drive;
    property OSVersion: WideString read Get_OSVersion write Set_OSVersion;
    property IpAddresses: IXMLIpAddressesType read Get_IpAddresses;
    property PosApplicationVersion: WideString read Get_PosApplicationVersion write Set_PosApplicationVersion;
    property PendingTransactions: IXMLPendingTransactionsType read Get_PendingTransactions;
    property PinPad: IXMLPinPadType read Get_PinPad;
    property Modules: IXMLModulesType read Get_Modules;
    property ConfigFiles: IXMLConfigFilesType read Get_ConfigFiles;
    property BioStatus: WideString read Get_BioStatus write Set_BioStatus;
  end;

{ IXMLOpenEpsKEK3Type }

  IXMLOpenEpsKEK3Type = interface(IXMLNode)
    ['{37570FB7-7FFF-4C39-9896-508F0A3C7C86}']
    { Property Accessors }
    function Get_Timestamp: WideString;
    procedure Set_Timestamp(Value: WideString);
    { Methods & Properties }
    property Timestamp: WideString read Get_Timestamp write Set_Timestamp;
  end;

{ IXMLPinPadType }

  IXMLPinPadType = interface(IXMLNode)
    ['{36A92F9C-9B32-4BE7-80DB-D06B657B25A0}']
    { Property Accessors }
    function Get_TermType: WideString;
    function Get_ApplicationVersion: WideString;
    function Get_DataVersion: WideString;
    function Get_OsVersion: WideString;
    function Get_ModelNumber: WideString;
    function Get_SerialNumber: WideString;
    function Get_AdditionalInfo: WideString;
    procedure Set_TermType(Value: WideString);
    procedure Set_ApplicationVersion(Value: WideString);
    procedure Set_DataVersion(Value: WideString);
    procedure Set_OsVersion(Value: WideString);
    procedure Set_ModelNumber(Value: WideString);
    procedure Set_SerialNumber(Value: WideString);
    procedure Set_AdditionalInfo(Value: WideString);
    { Methods & Properties }
    property TermType: WideString read Get_TermType write Set_TermType;
    property ApplicationVersion: WideString read Get_ApplicationVersion write Set_ApplicationVersion;
    property DataVersion: WideString read Get_DataVersion write Set_DataVersion;
    property OsVersion: WideString read Get_OsVersion write Set_OsVersion;
    property ModelNumber: WideString read Get_ModelNumber write Set_ModelNumber;
    property SerialNumber: WideString read Get_SerialNumber write Set_SerialNumber;
    property AdditionalInfo: WideString read Get_AdditionalInfo write Set_AdditionalInfo;
  end;

{ IXMLConfigFilesType }

  IXMLConfigFilesType = interface(IXMLNode)
    ['{58CFBD57-7264-45CA-A22F-7D39F1490AEB}']
    { Property Accessors }
    function Get_TermConfig: IXMLTermConfigType;
    function Get_CardProcessingProfiles: IXMLCardProcessingProfilesType;
    { Methods & Properties }
    property TermConfig: IXMLTermConfigType read Get_TermConfig;
    property CardProcessingProfiles: IXMLCardProcessingProfilesType read Get_CardProcessingProfiles;
  end;

{ IXMLTermConfigType }

  IXMLTermConfigType = interface(IXMLNode)
    ['{1A6DE943-9347-4245-9405-87A0F3279E66}']
    { Property Accessors }
    function Get_Name: WideString;
    function Get_Version: WideString;
    function Get_LastModified: WideString;
    procedure Set_Name(Value: WideString);
    procedure Set_Version(Value: WideString);
    procedure Set_LastModified(Value: WideString);
    { Methods & Properties }
    property Name: WideString read Get_Name write Set_Name;
    property Version: WideString read Get_Version write Set_Version;
    property LastModified: WideString read Get_LastModified write Set_LastModified;
  end;

{ IXMLCardProcessingProfilesType }

  IXMLCardProcessingProfilesType = interface(IXMLNode)
    ['{C8727AE2-6CB4-479E-AD70-C00A34CBAE90}']
    { Property Accessors }
    function Get_Name: WideString;
    function Get_Version: WideString;
    function Get_LastModified: WideString;
    procedure Set_Name(Value: WideString);
    procedure Set_Version(Value: WideString);
    procedure Set_LastModified(Value: WideString);
    { Methods & Properties }
    property Name: WideString read Get_Name write Set_Name;
    property Version: WideString read Get_Version write Set_Version;
    property LastModified: WideString read Get_LastModified write Set_LastModified;
  end;

{ Forward Decls }

  TXMLWinEpsStatusType = class;
  TXMLLocalMachineType = class;
  TXMLWinEpsKEK3Type = class;
  TXMLWinEpsServiceType = class;
  TXMLDriveType = class;
  TXMLIpAddressesType = class;
  TXMLModulesType = class;
  TXMLModuleType = class;
  TXMLHostsType = class;
  TXMLHostType = class;
  TXMLPendingTransactionsType = class;
  TXMLCompleteTransactionsType = class;
  TXMLRedundancyType = class;
  TXMLLanesType = class;
  TXMLLaneStatusType = class;
  TXMLOpenEpsKEK3Type = class;
  TXMLPinPadType = class;
  TXMLConfigFilesType = class;
  TXMLTermConfigType = class;
  TXMLCardProcessingProfilesType = class;

{ TXMLWinEpsStatusType }

  TXMLWinEpsStatusType = class(TXMLNode, IXMLWinEpsStatusType)
  protected
    { IXMLWinEpsStatusType }
    function Get_Version: WideString;
    function Get_LastModified: WideString;
    function Get_LocalMachine: IXMLLocalMachineType;
    function Get_Lanes: IXMLLanesType;
    procedure Set_Version(Value: WideString);
    procedure Set_LastModified(Value: WideString);
  public
    procedure AfterConstruction; override;
  end;

{ TXMLLocalMachineType }

  TXMLLocalMachineType = class(TXMLNode, IXMLLocalMachineType)
  protected
    { IXMLLocalMachineType }
    function Get_UpdateTime: WideString;
    function Get_WinEpsKEK3: IXMLWinEpsKEK3Type;
    function Get_WinEpsService: IXMLWinEpsServiceType;
    function Get_Drive: IXMLDriveType;
    function Get_OSVersion: WideString;
    function Get_LastDebitBINUpdate: WideString;
    function Get_LastFSABINUpdate: WideString;
    function Get_IpAddresses: IXMLIpAddressesType;
    function Get_Modules: IXMLModulesType;
    function Get_Hosts: IXMLHostsType;
    function Get_Redundancy: IXMLRedundancyType;
    procedure Set_UpdateTime(Value: WideString);
    procedure Set_OSVersion(Value: WideString);
    procedure Set_LastDebitBINUpdate(Value: WideString);
    procedure Set_LastFSABINUpdate(Value: WideString);
  public
    procedure AfterConstruction; override;
  end;

{ TXMLWinEpsKEK3Type }

  TXMLWinEpsKEK3Type = class(TXMLNode, IXMLWinEpsKEK3Type)
  protected
    { IXMLWinEpsKEK3Type }
    function Get_Timestamp: WideString;
    procedure Set_Timestamp(Value: WideString);
  end;

{ TXMLDriveType }

  TXMLDriveType = class(TXMLNode, IXMLDriveType)
  protected
    { IXMLDriveType }
    function Get_Letter: WideString;
    function Get_Size: Integer;
    function Get_FreeSpace: Integer;
    function Get_DriveSize: Integer;
    procedure Set_Letter(Value: WideString);
    procedure Set_Size(Value: Integer);
    procedure Set_FreeSpace(Value: Integer);
    procedure Set_DriveSize(Value: Integer);
  end;

{ TXMLWinEpsServiceType }

  TXMLWinEpsServiceType = class(TXMLNode, IXMLWinEpsServiceType)
  protected
    { IXMLWinEpsServiceType }
    function Get_Dir: WideString;
    function Get_Status: WideString;
    procedure Set_Dir(Value: WideString);
    procedure Set_Status(Value: WideString);
  end;

{ TXMLIpAddressesType }

  TXMLIpAddressesType = class(TXMLNodeCollection, IXMLIpAddressesType)
  protected
    { IXMLIpAddressesType }
    function Get_IPAddress(Index: Integer): WideString;
    function Add(const IPAddress: WideString): IXMLNode;
    function Insert(const Index: Integer; const IPAddress: WideString): IXMLNode;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLModulesType }

  TXMLModulesType = class(TXMLNodeCollection, IXMLModulesType)
  protected
    { IXMLModulesType }
    function Get_Module(Index: Integer): IXMLModuleType;
    function Add: IXMLModuleType;
    function Insert(const Index: Integer): IXMLModuleType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLModuleType }

  TXMLModuleType = class(TXMLNode, IXMLModuleType)
  protected
    { IXMLModuleType }
    function Get_Name: WideString;
    function Get_Version: WideString;
    procedure Set_Name(Value: WideString);
    procedure Set_Version(Value: WideString);
  end;

{ TXMLHostsType }

  TXMLHostsType = class(TXMLNodeCollection, IXMLHostsType)
  protected
    { IXMLHostsType }
    function Get_Host(Index: Integer): IXMLHostType;
    function Add: IXMLHostType;
    function Insert(const Index: Integer): IXMLHostType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLHostType }

  TXMLHostType = class(TXMLNode, IXMLHostType)
  protected
    { IXMLHostType }
    function Get_Name: WideString;
    function Get_Status: WideString;
    function Get_PendingTransactions: IXMLPendingTransactionsType;
    function Get_CompleteTransactions: IXMLCompleteTransactionsType;
    procedure Set_Name(Value: WideString);
    procedure Set_Status(Value: WideString);
  public
    procedure AfterConstruction; override;
  end;

{ TXMLPendingTransactionsType }

  TXMLPendingTransactionsType = class(TXMLNode, IXMLPendingTransactionsType)
  protected
    { IXMLPendingTransactionsType }
    function Get_OfflineCount: Integer;
    function Get_OfflineAmount: Integer;
    function Get_TorCount: Integer;
    function Get_TorAmount: Integer;
    function Get_SignatureCount: Integer;
    procedure Set_OfflineCount(Value: Integer);
    procedure Set_OfflineAmount(Value: Integer);
    procedure Set_TorCount(Value: Integer);
    procedure Set_TorAmount(Value: Integer);
    procedure Set_SignatureCount(Value: Integer);
  end;

{ TXMLCompleteTransactionsType }

  TXMLCompleteTransactionsType = class(TXMLNode, IXMLCompleteTransactionsType)
  protected
    { IXMLCompleteTransactionsType }
    function Get_ApprovedCount: Integer;
    function Get_ApprovedAmount: Integer;
    function Get_DeclinedCount: Integer;
    procedure Set_ApprovedCount(Value: Integer);
    procedure Set_ApprovedAmount(Value: Integer);
    procedure Set_DeclinedCount(Value: Integer);
  end;

{ TXMLRedundancyType }

  TXMLRedundancyType = class(TXMLNode, IXMLRedundancyType)
  protected
    { IXMLRedundancyType }
    function Get_ServerType: WideString;
    function Get_Status: WideString;
    procedure Set_ServerType(Value: WideString);
    procedure Set_Status(Value: WideString);
  end;

{ TXMLLanesType }

  TXMLLanesType = class(TXMLNodeCollection, IXMLLanesType)
  protected
    { IXMLLanesType }
    function Get_Lane(Index: Integer): IXMLLaneStatusType;
    function Add: IXMLLaneStatusType;
    function Insert(const Index: Integer): IXMLLaneStatusType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLLaneType }

  TXMLLaneStatusType = class(TXMLNode, IXMLLaneStatusType)
  protected
    { IXMLLaneType }
    function Get_Number: Integer;
    function Get_LaneType: WideString;
    function Get_UpdateTime: WideString;
    function Get_OpenEpsKEK3: IXMLOpenEpsKEK3Type;
    function Get_Drive: IXMLDriveType;
    function Get_OSVersion: WideString;
    function Get_IpAddresses: IXMLIpAddressesType;
    function Get_PosApplicationVersion: WideString;
    function Get_PendingTransactions: IXMLPendingTransactionsType;
    function Get_PinPad: IXMLPinPadType;
    function Get_Modules: IXMLModulesType;
    function Get_ConfigFiles: IXMLConfigFilesType;
    function Get_BioStatus: WideString;
    procedure Set_Number(Value: Integer);
    procedure Set_LaneType(Value: WideString);
    procedure Set_UpdateTime(Value: WideString);
    procedure Set_OSVersion(Value: WideString);
    procedure Set_PosApplicationVersion(Value: WideString);
    procedure Set_BioStatus(Value: WideString);
  public
    procedure AfterConstruction; override;
  end;

{ TXMLOpenEpsKEK3Type }

  TXMLOpenEpsKEK3Type = class(TXMLNode, IXMLOpenEpsKEK3Type)
  protected
    { IXMLOpenEpsKEK3Type }
    function Get_Timestamp: WideString;
    procedure Set_Timestamp(Value: WideString);
  end;

{ TXMLPinPadType }

  TXMLPinPadType = class(TXMLNode, IXMLPinPadType)
  protected
    { IXMLPinPadType }
    function Get_TermType: WideString;
    function Get_ApplicationVersion: WideString;
    function Get_DataVersion: WideString;
    function Get_OsVersion: WideString;
    function Get_ModelNumber: WideString;
    function Get_SerialNumber: WideString;
    function Get_AdditionalInfo: WideString;
    procedure Set_TermType(Value: WideString);
    procedure Set_ApplicationVersion(Value: WideString);
    procedure Set_DataVersion(Value: WideString);
    procedure Set_OsVersion(Value: WideString);
    procedure Set_ModelNumber(Value: WideString);
    procedure Set_SerialNumber(Value: WideString);
    procedure Set_AdditionalInfo(Value: WideString);
  end;

{ TXMLConfigFilesType }

  TXMLConfigFilesType = class(TXMLNode, IXMLConfigFilesType)
  protected
    { IXMLConfigFilesType }
    function Get_TermConfig: IXMLTermConfigType;
    function Get_CardProcessingProfiles: IXMLCardProcessingProfilesType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLTermConfigType }

  TXMLTermConfigType = class(TXMLNode, IXMLTermConfigType)
  protected
    { IXMLTermConfigType }
    function Get_Name: WideString;
    function Get_Version: WideString;
    function Get_LastModified: WideString;
    procedure Set_Name(Value: WideString);
    procedure Set_Version(Value: WideString);
    procedure Set_LastModified(Value: WideString);
  end;

{ TXMLCardProcessingProfilesType }

  TXMLCardProcessingProfilesType = class(TXMLNode, IXMLCardProcessingProfilesType)
  protected
    { IXMLCardProcessingProfilesType }
    function Get_Name: WideString;
    function Get_Version: WideString;
    function Get_LastModified: WideString;
    procedure Set_Name(Value: WideString);
    procedure Set_Version(Value: WideString);
    procedure Set_LastModified(Value: WideString);
  end;

{ Global Functions }

function GetWinEpsStatus(Doc: IXMLDocument): IXMLWinEpsStatusType;
function LoadWinEpsStatus(const FileName: WideString): IXMLWinEpsStatusType;
function NewWinEpsStatus: IXMLWinEpsStatusType;
function LoadLaneStatus(const xmlstring: WideString): IXMLLaneStatusType;
function GetLaneStatus(Doc: IXMLDocument): IXMLLaneStatusType;
function NewLaneStatus: IXMLLaneStatusType;

implementation

{ Global Functions }

function GetWinEpsStatus(Doc: IXMLDocument): IXMLWinEpsStatusType;
begin
  Result := Doc.GetDocBinding('WinEpsStatus', TXMLWinEpsStatusType) as IXMLWinEpsStatusType;
end;

function LoadWinEpsStatus(const FileName: WideString): IXMLWinEpsStatusType;
begin
  Result := LoadXMLDocument(FileName).GetDocBinding('WinEpsStatus', TXMLWinEpsStatusType) as IXMLWinEpsStatusType;
end;

function NewWinEpsStatus: IXMLWinEpsStatusType;
begin
  Result := NewXMLDocument.GetDocBinding('WinEpsStatus', TXMLWinEpsStatusType) as IXMLWinEpsStatusType;
end;

function LoadLaneStatus(const xmlstring: WideString): IXMLLaneStatusType;
begin
  Result := LoadXMLData(xmlstring).GetDocBinding('Lane', TXMLLaneStatusType) as IXMLLaneStatusType;
end;

function GetLaneStatus(Doc: IXMLDocument): IXMLLaneStatusType;
begin
  Result := Doc.GetDocBinding('LaneStatus', TXMLLaneStatusType) as IXMLLaneStatusType;
end;

function NewLaneStatus: IXMLLaneStatusType;
begin
  Result := NewXMLDocument.GetDocBinding('LaneStatus', TXMLLaneStatusType) as IXMLLaneStatusType;
end;

{ TXMLWinEpsStatusType }

procedure TXMLWinEpsStatusType.AfterConstruction;
begin
  RegisterChildNode('LocalMachine', TXMLLocalMachineType);
  RegisterChildNode('Lanes', TXMLLanesType);
  inherited;
end;

function TXMLWinEpsStatusType.Get_Version: WideString;
begin
  Result := AttributeNodes['Version'].Text;
end;

procedure TXMLWinEpsStatusType.Set_Version(Value: WideString);
begin
  SetAttribute('Version', Value);
end;

function TXMLWinEpsStatusType.Get_LastModified: WideString;
begin
  Result := AttributeNodes['LastModified'].Text;
end;

procedure TXMLWinEpsStatusType.Set_LastModified(Value: WideString);
begin
  SetAttribute('LastModified', Value);
end;

function TXMLWinEpsStatusType.Get_LocalMachine: IXMLLocalMachineType;
begin
  Result := ChildNodes['LocalMachine'] as IXMLLocalMachineType;
end;

function TXMLWinEpsStatusType.Get_Lanes: IXMLLanesType;
begin
  Result := ChildNodes['Lanes'] as IXMLLanesType;
end;

{ TXMLLocalMachineType }

procedure TXMLLocalMachineType.AfterConstruction;
begin
  RegisterChildNode('WinEpsKEK3', TXMLWinEpsKEK3Type);
  RegisterChildNode('Drive', TXMLDriveType);
  RegisterChildNode('WinEpsService', TXMLWinEpsServiceType);
  RegisterChildNode('IpAddresses', TXMLIpAddressesType);
  RegisterChildNode('Modules', TXMLModulesType);
  RegisterChildNode('Hosts', TXMLHostsType);
  RegisterChildNode('Redundancy', TXMLRedundancyType);
  inherited;
end;

function TXMLLocalMachineType.Get_UpdateTime: WideString;
begin
  Result := AttributeNodes['UpdateTime'].Text;
end;

procedure TXMLLocalMachineType.Set_UpdateTime(Value: WideString);
begin
  SetAttribute('UpdateTime', Value);
end;

function TXMLLocalMachineType.Get_WinEpsKEK3: IXMLWinEpsKEK3Type;
begin
  Result := ChildNodes['WinEpsKEK3'] as IXMLWinEpsKEK3Type;
end;

function TXMLLocalMachineType.Get_Drive: IXMLDriveType;
begin
  Result := ChildNodes['Drive'] as IXMLDriveType;
end;

function TXMLLocalMachineType.Get_OSVersion: WideString;
begin
  Result := ChildNodes['OSVersion'].Text;
end;

procedure TXMLLocalMachineType.Set_OSVersion(Value: WideString);
begin
  ChildNodes['OSVersion'].NodeValue := Value;
end;

function TXMLLocalMachineType.Get_LastDebitBINUpdate: WideString;
begin
  Result := ChildNodes['LastDebitBINUpdate'].Text;
end;

function TXMLLocalMachineType.Get_LastFSABINUpdate: WideString;
begin
  Result := ChildNodes['LastFSABINUpdate'].Text;
end;

procedure TXMLLocalMachineType.Set_LastDebitBINUpdate(Value: WideString);
begin
  ChildNodes['LastDebitBINUpdate'].NodeValue := Value;
end;

procedure TXMLLocalMachineType.Set_LastFSABINUpdate(Value: WideString);
begin
  ChildNodes['LastFSABINUpdate'].NodeValue := Value;
end;

function TXMLLocalMachineType.Get_WinEpsService: IXMLWinEpsServiceType;
begin
  Result := ChildNodes['WinEpsService'] as IXMLWinEpsServiceType;
end;

function TXMLLocalMachineType.Get_IpAddresses: IXMLIpAddressesType;
begin
  Result := ChildNodes['IpAddresses'] as IXMLIpAddressesType;
end;

function TXMLLocalMachineType.Get_Modules: IXMLModulesType;
begin
  Result := ChildNodes['Modules'] as IXMLModulesType;
end;

function TXMLLocalMachineType.Get_Hosts: IXMLHostsType;
begin
  Result := ChildNodes['Hosts'] as IXMLHostsType;
end;

function TXMLLocalMachineType.Get_Redundancy: IXMLRedundancyType;
begin
  Result := ChildNodes['Redundancy'] as IXMLRedundancyType;
end;

{ TXMLDriveType }

function TXMLDriveType.Get_Letter: WideString;
begin
  Result := AttributeNodes['Letter'].Text;
end;

procedure TXMLDriveType.Set_Letter(Value: WideString);
begin
  SetAttribute('Letter', Value);
end;

function TXMLDriveType.Get_Size: Integer;
begin
  Result := AttributeNodes['Size'].NodeValue;
end;

procedure TXMLDriveType.Set_Size(Value: Integer);
begin
  SetAttribute('Size', Value);
end;

function TXMLDriveType.Get_FreeSpace: Integer;
begin
  Result := AttributeNodes['FreeSpace'].NodeValue;
end;

procedure TXMLDriveType.Set_FreeSpace(Value: Integer);
begin
  SetAttribute('FreeSpace', Value);
end;

function TXMLDriveType.Get_DriveSize: Integer;
begin
  Result := AttributeNodes['DriveSize'].NodeValue;
end;

procedure TXMLDriveType.Set_DriveSize(Value: Integer);
begin
  SetAttribute('DriveSize', Value);
end;

function TXMLWinEpsKEK3Type.Get_Timestamp: WideString;
begin
  Result := AttributeNodes['Timestamp'].Text;
end;

procedure TXMLWinEpsKEK3Type.Set_Timestamp(Value: WideString);
begin
  SetAttribute('Timestamp', Value);
end;

{ TXMLWinEpsServiceType }

function TXMLWinEpsServiceType.Get_Dir: WideString;
begin
  Result := AttributeNodes['Dir'].Text;
end;

procedure TXMLWinEpsServiceType.Set_Dir(Value: WideString);
begin
  SetAttribute('Dir', Value);
end;

function TXMLWinEpsServiceType.Get_Status: WideString;
begin
  Result := AttributeNodes['Status'].Text;
end;

procedure TXMLWinEpsServiceType.Set_Status(Value: WideString);
begin
  SetAttribute('Status', Value);
end;

{ TXMLIpAddressesType }

procedure TXMLIpAddressesType.AfterConstruction;
begin
  ItemTag := 'IPAddress';
  ItemInterface := IXMLNode;
  inherited;
end;

function TXMLIpAddressesType.Get_IPAddress(Index: Integer): WideString;
begin
  Result := List[Index].Text;
end;

function TXMLIpAddressesType.Add(const IPAddress: WideString): IXMLNode;
begin
  Result := AddItem(-1);
  Result.NodeValue := IPAddress;
end;

function TXMLIpAddressesType.Insert(const Index: Integer; const IPAddress: WideString): IXMLNode;
begin
  Result := AddItem(Index);
  Result.NodeValue := IPAddress;
end;

{ TXMLModulesType }

procedure TXMLModulesType.AfterConstruction;
begin
  RegisterChildNode('Module', TXMLModuleType);
  ItemTag := 'Module';
  ItemInterface := IXMLModuleType;
  inherited;
end;

function TXMLModulesType.Get_Module(Index: Integer): IXMLModuleType;
begin
  Result := List[Index] as IXMLModuleType;
end;

function TXMLModulesType.Add: IXMLModuleType;
begin
  Result := AddItem(-1) as IXMLModuleType;
end;

function TXMLModulesType.Insert(const Index: Integer): IXMLModuleType;
begin
  Result := AddItem(Index) as IXMLModuleType;
end;

{ TXMLModuleType }

function TXMLModuleType.Get_Name: WideString;
begin
  Result := AttributeNodes['Name'].Text;
end;

procedure TXMLModuleType.Set_Name(Value: WideString);
begin
  SetAttribute('Name', Value);
end;

function TXMLModuleType.Get_Version: WideString;
begin
  Result := AttributeNodes['Version'].Text;
end;

procedure TXMLModuleType.Set_Version(Value: WideString);
begin
  SetAttribute('Version', Value);
end;

{ TXMLHostsType }

procedure TXMLHostsType.AfterConstruction;
begin
  RegisterChildNode('Host', TXMLHostType);
  ItemTag := 'Host';
  ItemInterface := IXMLHostType;
  inherited;
end;

function TXMLHostsType.Get_Host(Index: Integer): IXMLHostType;
begin
  Result := List[Index] as IXMLHostType;
end;

function TXMLHostsType.Add: IXMLHostType;
begin
  Result := AddItem(-1) as IXMLHostType;
end;

function TXMLHostsType.Insert(const Index: Integer): IXMLHostType;
begin
  Result := AddItem(Index) as IXMLHostType;
end;

{ TXMLHostType }

procedure TXMLHostType.AfterConstruction;
begin
  RegisterChildNode('PendingTransactions', TXMLPendingTransactionsType);
  RegisterChildNode('CompleteTransactions', TXMLCompleteTransactionsType);
  inherited;
end;

function TXMLHostType.Get_Name: WideString;
begin
  Result := AttributeNodes['Name'].Text;
end;

procedure TXMLHostType.Set_Name(Value: WideString);
begin
  SetAttribute('Name', Value);
end;

function TXMLHostType.Get_Status: WideString;
begin
  Result := AttributeNodes['Status'].Text;
end;

procedure TXMLHostType.Set_Status(Value: WideString);
begin
  SetAttribute('Status', Value);
end;

function TXMLHostType.Get_PendingTransactions: IXMLPendingTransactionsType;
begin
  Result := ChildNodes['PendingTransactions'] as IXMLPendingTransactionsType;
end;

function TXMLHostType.Get_CompleteTransactions: IXMLCompleteTransactionsType;
begin
  Result := ChildNodes['CompleteTransactions'] as IXMLCompleteTransactionsType;
end;

{ TXMLPendingTransactionsType }

function TXMLPendingTransactionsType.Get_OfflineCount: Integer;
begin
  Result := AttributeNodes['OfflineCount'].NodeValue;
end;

procedure TXMLPendingTransactionsType.Set_OfflineCount(Value: Integer);
begin
  SetAttribute('OfflineCount', Value);
end;

function TXMLPendingTransactionsType.Get_OfflineAmount: Integer;
begin
  Result := AttributeNodes['OfflineAmount'].NodeValue;
end;

procedure TXMLPendingTransactionsType.Set_OfflineAmount(Value: Integer);
begin
  SetAttribute('OfflineAmount', Value);
end;

function TXMLPendingTransactionsType.Get_TorCount: Integer;
begin
  Result := AttributeNodes['TorCount'].NodeValue;
end;

function TXMLPendingTransactionsType.Get_SignatureCount: Integer;
begin
  Result := AttributeNodes['SignatureCount'].NodeValue;
end;

procedure TXMLPendingTransactionsType.Set_TorCount(Value: Integer);
begin
  SetAttribute('TorCount', Value);
end;

procedure TXMLPendingTransactionsType.Set_SignatureCount(Value: Integer);
begin
  SetAttribute('SignatureCount', Value);
end;

function TXMLPendingTransactionsType.Get_TorAmount: Integer;
begin
  Result := AttributeNodes['TorAmount'].NodeValue;
end;

procedure TXMLPendingTransactionsType.Set_TorAmount(Value: Integer);
begin
  SetAttribute('TorAmount', Value);
end;

{ TXMLCompleteTransactionsType }

function TXMLCompleteTransactionsType.Get_ApprovedCount: Integer;
begin
  Result := AttributeNodes['ApprovedCount'].NodeValue;
end;

procedure TXMLCompleteTransactionsType.Set_ApprovedCount(Value: Integer);
begin
  SetAttribute('ApprovedCount', Value);
end;

function TXMLCompleteTransactionsType.Get_ApprovedAmount: Integer;
begin
  Result := AttributeNodes['ApprovedAmount'].NodeValue;
end;

procedure TXMLCompleteTransactionsType.Set_ApprovedAmount(Value: Integer);
begin
  SetAttribute('ApprovedAmount', Value);
end;

function TXMLCompleteTransactionsType.Get_DeclinedCount: Integer;
begin
  Result := AttributeNodes['DeclinedCount'].NodeValue;
end;

procedure TXMLCompleteTransactionsType.Set_DeclinedCount(Value: Integer);
begin
  SetAttribute('DeclinedCount', Value);
end;

{ TXMLRedundancyType }

function TXMLRedundancyType.Get_ServerType: WideString;
begin
  Result := AttributeNodes['ServerType'].Text;
end;

procedure TXMLRedundancyType.Set_ServerType(Value: WideString);
begin
  SetAttribute('ServerType', Value);
end;

function TXMLRedundancyType.Get_Status: WideString;
begin
  Result := AttributeNodes['Status'].Text;
end;

procedure TXMLRedundancyType.Set_Status(Value: WideString);
begin
  SetAttribute('Status', Value);
end;

{ TXMLLanesType }

procedure TXMLLanesType.AfterConstruction;
begin
  RegisterChildNode('Lane',TXMLLaneStatusType);
  ItemTag := 'Lane';
  ItemInterface := IXMLLaneStatusType;
  inherited;
end;

function TXMLLanesType.Get_Lane(Index: Integer): IXMLLaneStatusType;
begin
  Result := List[Index] as IXMLLaneStatusType;
end;

function TXMLLanesType.Add: IXMLLaneStatusType;
begin
  Result := AddItem(-1) as IXMLLaneStatusType;
end;

function TXMLLanesType.Insert(const Index: Integer): IXMLLaneStatusType;
begin
  Result := AddItem(Index) as IXMLLaneStatusType;
end;

{ TXMLLaneType }

procedure TXMLLaneStatusType.AfterConstruction;
begin
  RegisterChildNode('OpenEpsKEK3', TXMLOpenEpsKEK3Type);
  RegisterChildNode('Drive', TXMLDriveType);
  RegisterChildNode('IpAddresses', TXMLIpAddressesType);
  RegisterChildNode('PendingTransactions', TXMLPendingTransactionsType);
  RegisterChildNode('PinPad', TXMLPinPadType);
  RegisterChildNode('Modules', TXMLModulesType);
  RegisterChildNode('ConfigFiles', TXMLConfigFilesType);
  inherited;
end;

function TXMLLaneStatusType.Get_Number: Integer;
begin
  Result := AttributeNodes['Number'].NodeValue;
end;

procedure TXMLLaneStatusType.Set_Number(Value: Integer);
begin
  SetAttribute('Number', Value);
end;

function TXMLLaneStatusType.Get_LaneType: WideString;
begin
  Result := AttributeNodes['LaneType'].Text;
end;

procedure TXMLLaneStatusType.Set_LaneType(Value: WideString);
begin
  SetAttribute('LaneType', Value);
end;

function TXMLLaneStatusType.Get_UpdateTime: WideString;
begin
  Result := AttributeNodes['UpdateTime'].Text;
end;

procedure TXMLLaneStatusType.Set_UpdateTime(Value: WideString);
begin
  SetAttribute('UpdateTime', Value);
end;

function TXMLLaneStatusType.Get_OpenEpsKEK3: IXMLOpenEpsKEK3Type;
begin
  Result := ChildNodes['OpenEpsKEK3'] as IXMLOpenEpsKEK3Type;
end;

function TXMLLaneStatusType.Get_Drive: IXMLDriveType;
begin
  Result := ChildNodes['Drive'] as IXMLDriveType;
end;

function TXMLLaneStatusType.Get_OSVersion: WideString;
begin
  Result := ChildNodes['OSVersion'].Text;
end;

procedure TXMLLaneStatusType.Set_OSVersion(Value: WideString);
begin
  ChildNodes['OSVersion'].NodeValue := Value;
end;

function TXMLLaneStatusType.Get_IpAddresses: IXMLIpAddressesType;
begin
  Result := ChildNodes['IpAddresses'] as IXMLIpAddressesType;
end;

function TXMLLaneStatusType.Get_PosApplicationVersion: WideString;
begin
  Result := ChildNodes['PosApplicationVersion'].Text;
end;

procedure TXMLLaneStatusType.Set_PosApplicationVersion(Value: WideString);
begin
  ChildNodes['PosApplicationVersion'].NodeValue := Value;
end;

function TXMLLaneStatusType.Get_PendingTransactions: IXMLPendingTransactionsType;
begin
  Result := ChildNodes['PendingTransactions'] as IXMLPendingTransactionsType;
end;

function TXMLLaneStatusType.Get_PinPad: IXMLPinPadType;
begin
  Result := ChildNodes['PinPad'] as IXMLPinPadType;
end;

function TXMLLaneStatusType.Get_Modules: IXMLModulesType;
begin
  Result := ChildNodes['Modules'] as IXMLModulesType;
end;

function TXMLLaneStatusType.Get_ConfigFiles: IXMLConfigFilesType;
begin
  Result := ChildNodes['ConfigFiles'] as IXMLConfigFilesType;
end;

function TXMLLaneStatusType.Get_BioStatus: WideString;
begin
  Result := ChildNodes['BioStatus'].Text;
end;

procedure TXMLLaneStatusType.Set_BioStatus(Value: WideString);
begin
  ChildNodes['BioStatus'].NodeValue := Value;
end;

{ TXMLOpenEpsKEK3Type }

function TXMLOpenEpsKEK3Type.Get_Timestamp: WideString;
begin
  Result := AttributeNodes['Timestamp'].Text;
end;

procedure TXMLOpenEpsKEK3Type.Set_Timestamp(Value: WideString);
begin
  SetAttribute('Timestamp', Value);
end;

{ TXMLPinPadType }

function TXMLPinPadType.Get_TermType: WideString;
begin
  Result := AttributeNodes['TermType'].Text;
end;

procedure TXMLPinPadType.Set_TermType(Value: WideString);
begin
  SetAttribute('TermType', Value);
end;

function TXMLPinPadType.Get_ApplicationVersion: WideString;
begin
  Result := ChildNodes['ApplicationVersion'].Text;
end;

procedure TXMLPinPadType.Set_ApplicationVersion(Value: WideString);
begin
  ChildNodes['ApplicationVersion'].NodeValue := Value;
end;

function TXMLPinPadType.Get_DataVersion: WideString;
begin
  Result := ChildNodes['DataVersion'].Text;
end;

procedure TXMLPinPadType.Set_DataVersion(Value: WideString);
begin
  ChildNodes['DataVersion'].NodeValue := Value;
end;

function TXMLPinPadType.Get_OsVersion: WideString;
begin
  Result := ChildNodes['OsVersion'].Text;
end;

procedure TXMLPinPadType.Set_OsVersion(Value: WideString);
begin
  ChildNodes['OsVersion'].NodeValue := Value;
end;

function TXMLPinPadType.Get_ModelNumber: WideString;
begin
  Result := ChildNodes['ModelNumber'].Text;
end;

procedure TXMLPinPadType.Set_ModelNumber(Value: WideString);
begin
  ChildNodes['ModelNumber'].NodeValue := Value;
end;

function TXMLPinPadType.Get_SerialNumber: WideString;
begin
  Result := ChildNodes['SerialNumber'].Text;
end;

function TXMLPinPadType.Get_AdditionalInfo: WideString;
begin
  Result := ChildNodes['AdditionalInfo'].Text;
end;

procedure TXMLPinPadType.Set_SerialNumber(Value: WideString);
begin
  ChildNodes['SerialNumber'].NodeValue := Value;
end;

procedure TXMLPinPadType.Set_AdditionalInfo(Value: WideString);
begin
  ChildNodes['AdditionalInfo'].NodeValue := Value;
end;

{ TXMLConfigFilesType }

procedure TXMLConfigFilesType.AfterConstruction;
begin
  RegisterChildNode('TermConfig', TXMLTermConfigType);
  RegisterChildNode('CardProcessingProfiles', TXMLCardProcessingProfilesType);
  inherited;
end;

function TXMLConfigFilesType.Get_TermConfig: IXMLTermConfigType;
begin
  Result := ChildNodes['TermConfig'] as IXMLTermConfigType;
end;

function TXMLConfigFilesType.Get_CardProcessingProfiles: IXMLCardProcessingProfilesType;
begin
  Result := ChildNodes['CardProcessingProfiles'] as IXMLCardProcessingProfilesType;
end;

{ TXMLTermConfigType }

function TXMLTermConfigType.Get_Name: WideString;
begin
  Result := AttributeNodes['Name'].Text;
end;

procedure TXMLTermConfigType.Set_Name(Value: WideString);
begin
  SetAttribute('Name', Value);
end;

function TXMLTermConfigType.Get_Version: WideString;
begin
  Result := AttributeNodes['Version'].Text;
end;

procedure TXMLTermConfigType.Set_Version(Value: WideString);
begin
  SetAttribute('Version', Value);
end;

function TXMLTermConfigType.Get_LastModified: WideString;
begin
  Result := AttributeNodes['LastModified'].Text;
end;

procedure TXMLTermConfigType.Set_LastModified(Value: WideString);
begin
  SetAttribute('LastModified', Value);
end;

{ TXMLCardProcessingProfilesType }

function TXMLCardProcessingProfilesType.Get_Name: WideString;
begin
  Result := AttributeNodes['Name'].Text;
end;

procedure TXMLCardProcessingProfilesType.Set_Name(Value: WideString);
begin
  SetAttribute('Name', Value);
end;

function TXMLCardProcessingProfilesType.Get_Version: WideString;
begin
  Result := AttributeNodes['Version'].Text;
end;

procedure TXMLCardProcessingProfilesType.Set_Version(Value: WideString);
begin
  SetAttribute('Version', Value);
end;

function TXMLCardProcessingProfilesType.Get_LastModified: WideString;
begin
  Result := AttributeNodes['LastModified'].Text;
end;

procedure TXMLCardProcessingProfilesType.Set_LastModified(Value: WideString);
begin
  SetAttribute('LastModified', Value);
end;

initialization
  ExtendedLog('WinEpsStatusXML Initialization');
finalization
  ExtendedLog('WinEpsStatusXML Finalization');

end.

