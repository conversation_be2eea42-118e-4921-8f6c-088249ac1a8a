// (c) MTXEPS, Inc. 1988-2008
unit CashiersXML;

interface

uses
  Sys<PERSON><PERSON><PERSON>,
  xmldom, XMLDoc, XMLIntf;

type

{ Forward Decls }

  IXMLCashiersType = interface;
  IXMLCashierType = interface;

{ IXMLCashiersType }

  IXMLCashiersType = interface(IXMLNodeCollection)
    ['{CB6AEA7C-B47F-41E4-8456-D84CE01D6D04}']
    { Property Accessors }
    function GetVersion: string;
    function GetLastModified: string;
    function GetCashier(Index: Integer): IXMLCashierType;
    procedure SetVersion(Value: string);
    procedure SetLastModified(Value: string);
    { Methods & Properties }
    function Add: IXMLCashierType;
    function Insert(const Index: Integer): IXMLCashierType;
    property Version: string read GetVersion write SetVersion;
    property LastModified: string read GetLastModified write SetLastModified;
    property Cashier[Index: Integer]: IXMLCashierType read GetCashier; default;
  end;

{ IXMLCashierType }

  IXMLCashierType = interface(IXMLNode)
    ['{C91CE021-5AB2-44BF-94C4-A00695FEFD7F}']
    { Property Accessors }
    function GetNumber: Integer;
    function GetName: string;
    procedure SetNumber(Value: Integer);
    procedure SetName(Value: string);
    { Methods & Properties }
    property Number: Integer read GetNumber write SetNumber;
    property Name: string read GetName write SetName;
  end;

{ Forward Decls }

  TXMLCashiersType = class;
  TXMLCashierType = class;

{ TXMLCashiersType }

  TXMLCashiersType = class(TXMLNodeCollection, IXMLCashiersType)
  protected
    { IXMLCashiersType }
    function GetVersion: string;
    function GetLastModified: string;
    function GetCashier(Index: Integer): IXMLCashierType;
    procedure SetVersion(Value: string);
    procedure SetLastModified(Value: string);
    function Add: IXMLCashierType;
    function Insert(const Index: Integer): IXMLCashierType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLCashierType }

  TXMLCashierType = class(TXMLNode, IXMLCashierType)
  protected
    { IXMLCashierType }
    function GetNumber: Integer;
    function GetName: string;
    procedure SetNumber(Value: Integer);
    procedure SetName(Value: string);
  end;

{ Global Functions }

function GetCashiers(Doc: IXMLDocument): IXMLCashiersType;
function LoadCashiers(const FileName: string): IXMLCashiersType;
function NewCashiers: IXMLCashiersType;

implementation

uses
  MTX_Lib,
  MTX_XMLClasses;

{ Global Functions }

function GetCashiers(Doc: IXMLDocument): IXMLCashiersType;
begin
  Result := Doc.GetDocBinding('Cashiers', TXMLCashiersType) as IXMLCashiersType;
end;

function LoadCashiers(const FileName: string): IXMLCashiersType;
begin
  Result := LoadXMLDocument(FileName).GetDocBinding('Cashiers', TXMLCashiersType) as IXMLCashiersType;
end;

function NewCashiers: IXMLCashiersType;
begin
  Result := NewXMLDocument.GetDocBinding('Cashiers', TXMLCashiersType) as IXMLCashiersType;
end;

{ TXMLCashiersType }

procedure TXMLCashiersType.AfterConstruction;
begin
  RegisterChildNode('Cashier', TXMLCashierType);
  ItemTag := 'Cashier';
  ItemInterface := IXMLCashierType;
  inherited;
end;

function TXMLCashiersType.GetVersion: string;
begin
  Result := AttributeNodes['version'].Text;
end;

procedure TXMLCashiersType.SetVersion(Value: string);
begin
  SetAttribute('version', Value);
end;

function TXMLCashiersType.GetLastModified: string;
begin
  Result := AttributeNodes['LastModified'].Text;
end;

procedure TXMLCashiersType.SetLastModified(Value: string);
begin
  SetAttribute('LastModified', Value);
end;

function TXMLCashiersType.GetCashier(Index: Integer): IXMLCashierType;
begin
  Result := List[Index] as IXMLCashierType;
end;

function TXMLCashiersType.Add: IXMLCashierType;
begin
  Result := AddItem(-1) as IXMLCashierType;
end;

function TXMLCashiersType.Insert(const Index: Integer): IXMLCashierType;
begin
  Result := AddItem(Index) as IXMLCashierType;
end;


{ TXMLCashierType }

function TXMLCashierType.GetNumber: Integer;
begin
  Result := AttributeNodes['Number'].NodeValue;
end;

procedure TXMLCashierType.SetNumber(Value: Integer);
begin
  SetAttribute('Number', Value);
end;

function TXMLCashierType.GetName: string;
begin
  Result := AttributeNodes['name'].Text;
end;

procedure TXMLCashierType.SetName(Value: string);
begin
  SetAttribute('name', Value);
end;

end.

