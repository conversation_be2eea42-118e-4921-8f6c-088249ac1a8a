// (c) MTXEPS, Inc. 1988-2008
unit MdMsg;
{
ver 825.3 08-18-09 TSL-B add TrxDecBlackHawkUPC = 240
04-30-09 TSL-A Add PropCrPreAuth, PropCrAuthComp
}

interface

{$A-}

const
  //{$I RPTINFO.INC}

  DoRptToMenu        = 23;
  
  iNone              = 0;
  idebit             = 1;
  icredit            = 2;
  ipldebit           = 3;
  iplcredit          = 4;
  icheck             = 5;
  iebt_fs            = 6;
  iebt_ca            = 7;
  iuser_1            = 8;
  ifleet             = 9;
  iuser_2            = 10;
  iwireless          = 11;
  iach               = 12;
  iConnectPay        = 13;
  ieWic              = 14;
  iBenefitsProgram   = 19;  // CPCLIENTS-9586 Assigned to 19 as ServerEPS is in process for other Tender type 15-18
  iMultiTender       = 20;  // CPCLIENTS-17849 declared new MultiTender
  iMaxTender         = 20;  // CPCLIENTS-9586 Increased to 19 to be effective with Benefits Program

  MaxTerms           = 99;

 { Report #'s }

  StoreSummary       = '1';
  StoreSummaryN      =  1;
  LaneSummary        = '2';
  LaneSummaryN       =  2;
  CashierSummary     = '3';
  CashierSummaryN    =  3;
  VoidRpt            = '4';
  VoidRptN           =  4;
  StandIn            = '5';
  StandInN           =  5;
  Pending            = '6';
  PendingN           =  6;
  Rejected           = '7';
  RejectedN          =  7;
  VoucherClear       = '8';    // DEV-8000
  VoucherClearN      = 8;
  Host               = '9';
  HostN              =  9;
  ApprovedTrxs       = 'A';
  ApprovedTrxsN      =  10;
  Declines           = 'B';
  DeclinesN          = 11;
  ResponseSum        = 'C';
  ResponseSumN       = 12;
  YConversion        = 'D';    { JMR-B }
  YConversionN       = 13;     { JMR-B }
  NConversion        = 'E';    { JMR-B }
  NConversionN       = 14;     { JMR-B }
  InfoMsg            = 'F';    { JMR-G }
  InfoMsgN           = 15;     { JMR-G }
  TroubleMsg         = 'G';    { JMR-G }
  TroubleMsgN        = 16;     { JMR-G }
  PreAuth            = 'H';    { TSL-X }
  PreAuthN           = 17;     { TSL-X }
  Spool              = 'I';    { JMR-B }
  SpoolN             = 18;     { JMR-B }
  Totals             = 'J';    { JMR-B : totals needs to be last, since it only}
  //TotalsN            = 19;     { JMR-B     shows on print rpts }
  TotalsN            = 99;                                                      
  DetailedCashier    = 'K';    { TRI-A }
  DetailedCashierN   = 20;
  DetailedLane       = 'L';    { TRI-A }
  DetailedLaneN      = 21;
  TrainingCashier    = 'M';
  TrainingCashierN   = 22;
  DeclinesOffline    = 'N'; // DEV-11199
  DeclinesOfflineN   = 23; // DEV-11199

  MaxNumGroups       = 11;
  EOD_REPORTS_COUNT  = 21; // DEV-11199: was 19
  EOD_REPORTS: array [0..EOD_REPORTS_COUNT - 1] of char = (StoreSummary, LaneSummary, CashierSummary, VoidRpt, StandIn,
      Pending, Rejected, VoucherClear, Host, ApprovedTrxs, Declines, ResponseSum, YConversion, NConversion,
      InfoMsg, TroubleMsg, PreAuth, DetailedCashier, DetailedLane, TrainingCashier, DeclinesOffline); // DEV-11199: add Declines_Offline

{ FLAGS }

  TraceOn            = 5;
  DiskInUse          = 6;
  AcctFileEmpty      = 9;       // not used
  CashierFileActive  = 10;
  ManagerFileActive  = 11;
  LaneFileActive     = 12;
  Close_In_Progress  = 21;

  Host_01_SessionUp  = 40; { JGS-MH }
  Host_02_SessionUp  = 41;
  Host_03_SessionUp  = 42;
  Host_04_SessionUp  = 43;
  Host_05_SessionUp  = 44;
  Host_06_SessionUp  = 45;
  Host_07_SessionUp  = 46;
  Host_08_SessionUp  = 47;
  Host_09_SessionUp  = 48;
  Host_10_SessionUp  = 49;

  Offline_01_Empty   = 50;
  Offline_02_Empty   = 51;
  Offline_03_Empty   = 52;
  Offline_04_Empty   = 53;
  Offline_05_Empty   = 54;
  Offline_06_Empty   = 55;
  Offline_07_Empty   = 56;
  Offline_08_Empty   = 57;
  Offline_09_Empty   = 58;
  Offline_10_Empty   = 59;

{ Request types for MDMsg }

  NormalTrx          = '0';
  TORLateRsp         = '1';
  TORNoTermRsp       = '2';
  TORTimeout         = '3';
  TrxKeyRequest      = '4';
  OfflineFwd         = '5';
  TermNoResponse     = '6';
  TermRecvdOK        = '7';
  TermPowerOff       = '8';
  TrxFailMTXCk       = '9';
  OfflineReq         = 'A';
  TORTimeoutOfflineFwd = 'B';
  OpenEPSDecline     = 'C';
  TORReq             = 'D';
  ACROfflineReq      = 'E';
  Base24Completion   = 'F';

  TORSet             : set of AnsiChar=[TORLateRsp,TORNoTermRsp,TORTimeout{,TOROfflineFwd},
                                    TORTimeoutOfflineFwd];   { TSL-D }

{ Transaction final disposition codes  (var: TrxFinalDisp in MDmsgRec) }
{ these codes are set in MsgHan                                        }

  DispOnlineApp      = 'A';
  DispOnLineDec      = 'B';
  DispOffApp         = 'C';
  DispOffDec         = 'D';
  DispFwdApp         = 'E';
  DispFwdDec         = 'F';
  DispUnDeliv        = 'G';
  { the next two go together when a void comes in and the original is in the }
  { offine queue with a dispostion of DispOffApp, so neither go to the host  }
  DispNoHostVPurch   = 'H';  { OrigTrxOffApp: orig trx changed from OffApp to this }
  DispNoHostAppVoid  = 'I';  { OrigTrxOffApp: disp of a void if orig in offline }
  { original tran disp is DispFwdDec, no need for the void - don't send to host }
  DispNoHostVoidCan  = 'J';  { OrigTrxOffApp but fwd declined, throw away void  }
  DispFwdHeld        = 'K';
  DispInflight       = 'L';
  DispWaitingLaneRsp = 'M';
  DispNone           = 'N';
  DispVoucherRemoved = 'O'; // JMR DEV-8000 Manually removed from offline queue
//  DispVoucherHeld    = 'P'; // JMR DEV-8000 Auth Code Required, Held
//  DispVoucherDec     = 'Q'; // JMR DEV-8000 Declined by host
  OffReqNotSaved     = 'Z';  { in updtAcctFile used if recv dup OpenEPS offlineReq }

  ApprovedDispSet: set of AnsiChar = [DispOnlineApp,DispOffApp,DispFwdApp,DispFwdHeld{,DispVoucherHeld}];  //JMR DEV-8000
  DeclinedDispSet: set of AnsiChar = [DispOnlineDec,DispOffDec,DispFwdDec,DispUnDeliv,DispNoHostVPurch,
                                  DispNoHostAppVoid,DispNoHostVoidCan,OffReqNotSaved];
  DeclAdvDispSet: set of AnsiChar = [DispOffDec,DispUnDeliv,DispNoHostVPurch,DispNoHostAppVoid,DispNoHostVoidCan];

{ MTX internal request codes }

  HandShakeReq      = '00';
  HandShakeReqN     =   0;
  BenefitPurchase = '01';     // CPCLIENTS-9586 Transaction Type that supports Benefits Program Tender Type
  BenefitPurchaseN = 1;
  BenefitBalanceInquiry = '02';
  BenefitBalanceInquiryN = 2;  
  PropDbPreAuth      = '03';   { TSL-A }
  PropDbPreAuthN     =  3;
  PropDbPreAuthComp  = '04';
  PropDbPreAuthCompN =  4;
  ACHTotals         = '05';
  ACHTotalsN        =   5 ;
  WorkingKeyReq     = '05';
  WorkingKeyReqN    =   5;
  GiftTotReq        = '06';
  GiftTotReqN       =   6;
  SignOffWTot       = '06';
  SignOffWTotN      =   6;
  BenefitReturn = '07';       // CPCLIENTS-9586 Transaction Type that supports Benefits Program Tender Type
  BenefitReturnN = 7;
  BenefitItemQualification = '08';
  BenefitItemQualificationN = 8;
  SettleTotReq      = '09';
  SettleTotReqN     =   9;

  DbPurch           = '10';
  DbPurchN          =  10;
  DbOverride        = '11';
  DbOverrideN       =  11;
  DbVoiceAuth       = '12';
  DbVoiceAuthN      =  12;
  DbReturn          = '13';
  DbReturnN         =  13;
  DbPreAuth         = '14';
  DbPreAuthN        =  14;
  DbPreAuthComp     = '15';
  DbPreAuthCompN    =  15;
  DbBalInq          = '16';
  DbBalInqN         =  16;
  // DOEP-33273
  //DbKeyExchange     = '17';    //not needed use WorkingKeyReqN above. Reused below for CrAdjustment
  //DbKeyExchangeN     = 17;
  ConnectPayPurch   = '18';
  ConnectPayPurchN  =  18;
  ConnectPayReturn  = '19';
  ConnectPayReturnN =  19;

  ConnectPay_Set    : set of Byte = [ConnectPayPurchN, ConnectPayReturnN];

  CrPurch           = '20';
  CrPurchN          =  20;
  CrOverride        = '21';
  CrOverrideN       =  21;
  CrVoiceAuth       = '22';
  CrVoiceAuthN      =  22;
  CrReturn          = '23';
  CrReturnN         =  23;
  CrPreAuth         = '24';
  CrPreAuthN        =  24;
  CrPreAuthComp     = '25';
  CrPreAuthCompN    =  25;
  CrBalInq          = '26';
  CrBalInqN         =  26;
  // DOEP-33267
  CrPaymentOnAcct   = '27';
  CrPaymentOnAcctN  =  27;
  // DOEP-33268
  CrReturnWithValid = '28';
  CrReturnWithValidN=  28;
  CrAdjustment      = '17';
  CrAdjustmentN     =  17;

  { Credit to Debit codes, (var: CrToDbCode in MDmsgRec) }
  CrToDb            = 'D';    { JMR-A : converted from credit to debit}
  CrToDbToCr        = 'C';    { JMR-A : converted from credit to debit, and back to credit}

  Cr_Set            : Set of Byte = [CrPurchN, CrOverrideN, CrVoiceAuthN,
                                     CrPreAuthN,  CrPreAuthCompN,
                                     CrReturnN, CrBalInqN, CrPaymentOnAcctN,
                                     CrReturnWithValidN, CrAdjustmentN]; { JGS-I }
  // DOEP-33268
  DbReturnWithValid = '29';
  DbReturnWithValidN = 29;

  Db_Set            : Set of Byte = [DbPurchN, DbOverrideN, DbVoiceAuthN,
                                     DbPreAuthN,  DbPreAuthCompN,
                                     DbReturnN, DbBalInqN, DbReturnWithValidN];
                                     //DbKeyExchangeN]; { JGS-I }  not needed see above

  PropDbPurch       = '30';
  PropDbPurchN      =  30;
  PropDbOverride    = '31';
  PropDbOverrideN   =  31;
  PropDbVoiceAuth   = '32';
  PropDbVoiceAuthN  =  32;
  PropDbReturn      = '33';
  PropDbReturnN     =  33;
  PropDbPINReset    = '34';
  PropDbPINResetN   =  34;
  PropDbBalInq      = '35';
  PropDbBalInqN     =  35;

  eWicBalInq        = '36';
  eWicBalInqN       =  36;
  eWicForce         = '37';
  eWicForceN        =  37;
  eWicPreAuth       = '38';
  eWicPreAuthN      =  38;
  eWicPreAuthComp   = '39';
  eWicPreAuthCompN  =  39;

  eWic_Set          : set of byte = [eWicBalInqN,eWicForceN,eWicPreAuthN,eWicPreAuthCompN];

  PropCrPurch       = '40';
  PropCrPurchN      =  40;
  PropCrRecharge    = '41';
  PropCrRechargeN   =  41;
  PropCrVoiceAuth   = '42';
  PropCrVoiceAuthN  =  42;
  PropCrReturn      = '43';
  PropCrReturnN     =  43;
  PropCrPreAuth      = '44';   { TSL-A }
  PropCrPreAuthN     =  44;
  PropCrPreAuthComp  = '45';
  PropCrPreAuthCompN =  45;
  PropCrBalInq       = '46';
  PropCrBalInqN      =  46;

  AchPurch          = '47';
  AchPurchN         =  47;
  AchVoiceAuth      = '48';
  AchVoiceAuthN     =  48;
  AchReturn         = '49';
  AchReturnN        =  49;

  Ach_Set           : Set of Byte = [AchPurchN, AchVoiceAuthN, AchReturnN];

  CheckPurch        = '50';
  CheckPurchN       =  50;
  CheckOverride     = '51';
  CheckOverRideN    =  51;
  CheckVoiceAuth    = '52';
  CheckVoiceAuthN   =  52;
  CheckBalInq       = '53';
  CheckBalInqN      =  53;

  Check_Set         : Set of Byte = [CheckPurchN, CheckVoiceAuthN, CheckOverrideN, CheckBalInqN];

  // DOEP-33268
  PropCrReturnWithValid = '54';
  PropCrReturnWithValidN = 54;

  Prop_Cr_Set       : Set of Byte = [PropCrPurchN, PropCrRechargeN, PropCrVoiceAuthN,
                                     PropCrReturnN, PropCrBalInqN, PropCrPreAuthN, PropCrPreAuthCompN,
                                     PropCrReturnWithValidN];

  PropDbReturnWithValid =  '55';
  PropDbReturnWithValidN =  55;

  Prop_Db_Set       : Set of Byte = [PropDbPurchN, PropDbOverrideN, PropDbVoiceAuthN,
                                     PropDbReturnN, PropDbPINResetN, PropDbBalInqN,
                                     PropDbReturnWithValidN, PropDbPreAuthN, PropDbPreAuthCompN]; // CPCLIENTS-12016

  // DOEP-33269
  User_1_RefundActivation =  '56';
  User_1_RefundActivationN =  56;
  // DOEP-33271
  User_1_CashOut =  '57';
  User_1_CashOutN =  57;

  WirelessActivation  = '58';
  WirelessActivationN =  58;
  WirelessDeactivate  = '59';
  WirelessDeactivateN =  59;
  WirelessPreAuth     = '69';      // CPCLIENTS-11904
  WirelessPreAuthN    = 69;

  WirelessSet       : Set of Byte = [WirelessActivationN, WirelessDeactivateN, WirelessPreAuthN];        // CPCLIENTS-11904

  EBT_FS_Purch      = '60';
  EBT_FS_PurchN     =  60;
  EBT_FS_Override   = '61'; { JGS-I }
  EBT_FS_OverrideN  =  61;  { JGS-I }
  EBT_FS_Return     = '62';
  EBT_FS_ReturnN    =  62;
  EBT_FS_Voice      = '63';
  EBT_FS_VoiceN     =  63;
  EBT_FS_BalInq     = '64';
  EBT_FS_BalInqN    =  64;

  // DOEP-33272
  User_1_PreRecharge    = '65';
  User_1_PreRechargeN   =  65;
  // Phone card Pre-Activation is currently not supported but must be defined for ServerEPS decline advice message
  User_2_PreActivation  = '66';
  User_2_PreActivationN =  66;
  User_1_ReActivation   = '67';
  User_1_ReActivationN  =  67;
  User_1_FinalTender    = '68';
  User_1_FinalTenderN   =  68;

  EBT_Cash_Purch        = '70';
  EBT_Cash_PurchN       =  70;
  EBT_Cash_Override     = '71'; { JGS-I }
  EBT_Cash_OverrideN    =  71;  { JGS-I }
  EBT_Cash_Return       = '72';
  EBT_Cash_ReturnN      =  72;
  EBT_Cash_Voice        = '73';
  EBT_Cash_VoiceN       =  73;
  EBT_Cash_PreAuth      = '74';   { TSL-W }
  EBT_Cash_PreAuthN     =  74;
  EBT_Cash_PreAuthComp  = '75';
  EBT_Cash_PreAuthCompN =  75;
  EBT_Cash_BalInq       = '76';
  EBT_Cash_BalInqN      =  76;
  MtPurch               = '77';    //17849 Multitender Purchase
  MtPurchN              =  77;     //17849 Multitender Purchase

  EBT_Cash_Set      : Set of Byte = [EBT_Cash_PurchN, EBT_Cash_OverrideN, EBT_Cash_VoiceN,
                                     EBT_Cash_ReturnN, EBT_Cash_PreAuthN, EBT_Cash_PreAuthCompN,
                                     EBT_Cash_BalInqN];  { JGS-I }

  User_1_Purch          = '79';
  User_1_PurchN         =  79;
  User_1_Deactivate     = '80'; { TSL-03 }
  User_1_DeactivateN    =  80;
  User_1_Override       = '81'; { JGS-I }
  User_1_OverrideN      =  81;  { JGS-I }
  User_1_Return         = '82';
  User_1_ReturnN        =  82;
  User_1_Voice          = '83';
  User_1_VoiceN         =  83;
  User_1_Activation     = '84';
  User_1_ActivationN    =  84;   { TSL-03 }
  User_1_Recharge       = '85';  { TSL-02 }
  User_1_RechargeN      =  85;
  User_1_PreAuth        = '86';   { TSL-03 }
  User_1_PreAuthN       =  86;    { TSL-03 }
  User_1_PreAuthComp    = '87';   { TSL-03 }
  User_1_PreAuthCompN   =  87;    { TSL-03 }
  User_1_PreActivation  = '88'; { TSL-04 }
  User_1_PreActivationN =  88;
  User_1_BalInq         = '89';
  User_1_BalInqN        =  89;

  User_1_Set        : Set of Byte = [User_1_PurchN, User_1_DeactivateN,
                                     User_1_OverrideN, User_1_ReturnN,
                                     User_1_VoiceN, User_1_ActivationN, User_1_RechargeN,
                                     User_1_PreAuthN, User_1_PreAuthCompN,
                                     User_1_PreActivationN,User_1_BalInqN,
                                     User_1_RefundActivationN, User_1_CashOutN,
                                     User_1_ReActivationN,
                                     User_1_PreRechargeN, User_1_FinalTenderN];

  User_2_Purch        = '90';    { Phone card activate }
  User_2_PurchN       =  90;
  User_2_Override     = '91';    { Phone card refresh }
  User_2_OverrideN    =  91;
  User_2_Return       = '92';    { EBT FS voucher return }
  User_2_ReturnN      =  92;
  User_2_Voice        = '93';    { Phone card deactivate }
  User_2_VoiceN       =  93;
  User_2_BalInq       = '94';
  User_2_BalInqN      =  94;

  FleetPurch          = '95';   { actually uses original PIN Change crdPfx table }
  FleetPurchN         =  95;
  FleetReturn         = '96';
  FleetReturnN        =  96;
  FleetPreAuth        = '97';
  FleetPreAuthN       =  97;
  FleetPreAuthComp    = '98';
  FleetPreAuthCompN   =  98;

  BenefitsProgram_Set : set of Byte = [BenefitPurchaseN, BenefitBalanceInquiryN, BenefitReturnN, BenefitItemQualificationN]; // CPCLIENTS-9586 Transaction set for Offline

  PhoneCard_Set     : set of Byte = [user_2_purchN, user_2_OverrideN, user_2_VoiceN];

  Fleet_Set         : Set of Byte = [FleetPurchN, FleetReturnN, FleetPreAuthN,
                                     FleetPreAuthCompN];
  EBT_FS_Set        : Set of Byte = [EBT_FS_PurchN, EBT_FS_OverrideN, EBT_FS_VoiceN,
                                     EBT_FS_ReturnN, EBT_FS_BalInqN, User_2_ReturnN];         { TSL-04 }

  EBT_Set           : Set of Byte = [EBT_Cash_PurchN, EBT_Cash_OverrideN, EBT_Cash_VoiceN,
                                     EBT_Cash_ReturnN, EBT_Cash_PreAuthN, EBT_Cash_PreAuthCompN,
                                     EBT_Cash_BalInqN, EBT_FS_PurchN, EBT_FS_OverrideN, EBT_FS_VoiceN,
                                     EBT_FS_ReturnN, EBT_FS_BalInqN, User_2_ReturnN];

  EBT_Voucher_Set   : Set of Byte = [EBT_FS_VoiceN, EBT_Cash_VoiceN, User_2_ReturnN];

  User_2_Set        : Set of Byte = [User_2_PurchN,  User_2_OverrideN, User_2_VoiceN,         { TSL-04 }
                                     User_2_BalInqN];  { no user_2_return - it is EBT }


  PreAuth_Set       : Set of Byte = [DbPreAuthN, CrPreAuthN, PropCrPreAuthN, User_1_PreAuthN, FleetPreAuthN,
                                     EBT_Cash_PreAuthN, eWicPreAuthN, PropDbPreAuthN, WireLessPreAuthN];  // CPCLIENTS-12016  // CPCLIENTS-11904

  AuthComp_Set      : Set of Byte = [DbPreAuthCompN, CrPreAuthCompN, PropCrPreAuthCompN, User_1_PreAuthCompN,
                                     FleetPreAuthCompN, EBT_Cash_PreAuthCompN, eWicPreAuthCompN, PropDbPreAuthCompN]; // CPCLIENTS-12016

  Activation_Set    : Set of Byte = [User_1_ActivationN, WirelessActivationN, User_2_PurchN];
  Deactivate_Set    : Set of Byte = [User_1_DeactivateN,WirelessDeactivateN, User_2_VoiceN];

  Override_Set      : Set of Byte = [CrOverrideN,DbOverrideN,CheckOverrideN,PropDbOverrideN,
                                     EBT_FS_OverrideN,EBT_Cash_OverrideN,User_1_OverrideN];

  All_Purchase_Trxs : Set of Byte = [CrPurchN,
                                     CrOverrideN, { JGS-I }
                                     CrPreAuthN,
                                     CrPreAuthCompN,
                                     DbPurchN,
                                     DbOverrideN, { JGS-I }
                                     DbPreAuthN,
                                     DbPreAuthCompN,
                                     ConnectPayPurchN,
                                     CheckPurchN,
                                     CheckOverrideN,
                                     PropDbPurchN,
                                     PropDbOverrideN,
                                     eWicPreAuthN,
                                     eWicPreAuthCompN,
                                     PropCrPurchN,
                                     PropCrPreAuthN,
                                     PropCrPreAuthCompN,
                                     PropDbPreAuthN,      // CPCLINETS-12016
                                     PropDbPreAuthCompN,  // CPCLINETS-12016
                                     AchPurchN,
                                     AchVoiceAuthN,
                                     EBT_FS_PurchN,
                                     EBT_FS_OverrideN,
                                     EBT_Cash_PurchN,
                                     EBT_Cash_OverrideN,
                                     EBT_Cash_PreAuthN,
                                     EBT_Cash_PreAuthCompN,
                                     User_1_PurchN,
                                     User_1_OverrideN,
                                     User_1_ActivationN,    { TSL-03 }
                                     User_1_PreActivationN, { TSL-04 }
                                     User_1_DeactivateN,    { TSL_03 }
                                     User_1_PreAuthN,       { TSL-03 }
                                     User_1_PreAuthCompN,   { TSL-03 }
                                     FleetPurchN,
                                     FleetPreAuthN,
                                     FleetPreAuthCompN,
                                     User_2_PurchN,
                                     User_2_OverrideN,
                                     CrPaymentOnAcctN];

  All_Return_Trxs   : Set of Byte = [CrReturnN,
                                     DbReturnN,
                                     ConnectPayReturnN,
                                     PropDbReturnN,
                                     PropCrReturnN,
                                     AchReturnN,
                                     EBT_FS_ReturnN,
                                     EBT_Cash_ReturnN,
                                     User_1_ReturnN,
                                     FleetReturnN,
                                     User_2_ReturnN,
                                     CrReturnWithValidN,
                                     DbReturnWithValidN,
                                     PropCrReturnWithValidN,
                                     PropDbReturnWithValidN,
                                     BenefitReturnN]; // CPCLIENTS-9586 Trasaction Type for Benefits Program for offline

  // DOEP-41403 - All non-debit return transactions
  All_Non_Db_Return_Trxs : set of Byte = [CrReturnN,
                                          ConnectPayReturnN,
                                          PropCrReturnN,
                                          AchReturnN,
                                          EBT_FS_ReturnN,
                                          EBT_Cash_ReturnN,
                                          User_1_ReturnN,
                                          FleetReturnN,
                                          User_2_ReturnN,
                                          CrReturnWithValidN,
                                          PropCrReturnWithValidN];

  All_Voice_Trxs    : Set of Byte = [CrVoiceAuthN,
                                     DbVoiceAuthN,
                                     CheckVoiceAuthN,
                                     PropDbVoiceAuthN,
                                     PropCrVoiceAuthN,
                                     AchVoiceAuthN,
                                     EBT_FS_VoiceN,
                                     EBT_Cash_VoiceN,
                                     eWicForceN,
                                     User_1_VoiceN];

  All_Trxs          : Set of Byte = [CrPurchN,
                                     CrOverrideN,          { JGS-I }
                                     CrPreAuthN,
                                     CrPreAuthCompN,
                                     CrReturnN,
                                     CrVoiceAuthN,
                                     CrPaymentOnAcctN,
                                     CrReturnWithValidN,
                                     CrAdjustmentN,

                                     DbPurchN,
                                     DbOverrideN,          { JGS-I }
                                     DbPreAuthN,
                                     DbPreAuthCompN,
                                     DbReturnN,
                                     DbVoiceAuthN,
                                     DbReturnWithValidN,
                                     //DbKeyExchangeN,

                                     ConnectPayPurchN,
                                     ConnectPayReturnN,

                                     CheckPurchN,
                                     CheckOverrideN,
                                     CheckVoiceAuthN,

                                     PropDbPurchN,
                                     PropDbOverrideN,      { JGS-I }
                                     PropDbReturnN,
                                     PropDbVoiceAuthN,
                                     PropDbPINResetN,
                                     PropDbReturnWithValidN,

                                     eWicForceN,
                                     eWicPreAuthN,
                                     eWicPreAuthCompN,

                                     PropCrPurchN,
                                     PropCrRechargeN,      { JGS-I }
                                     PropCrReturnN,
                                     PropCrVoiceAuthN,
                                     PropCrPreAuthN,
                                     PropCrPreAuthCompN,
                                     PropCrReturnWithValidN,

                                     PropDbPreAuthN,      // CPCLINETS-12016
                                     PropDbPreAuthCompN,  // CPCLINETS-12016

                                     AchPurchN,
                                     AchVoiceAuthN,
                                     AchReturnN,

                                     EBT_FS_PurchN,
                                     EBT_FS_OverrideN,     { JGS-I }
                                     EBT_FS_ReturnN,
                                     EBT_FS_VoiceN,
                                     User_2_ReturnN,

                                     EBT_Cash_PurchN,
                                     EBT_Cash_OverrideN,   { JGS-I }
                                     EBT_Cash_ReturnN,
                                     EBT_Cash_VoiceN,
                                     EBT_Cash_PreAuthN,
                                     EBT_Cash_PreAuthCompN,

                                     User_1_ActivationN,   { TSL-03 }
                                     User_1_PreActivationN,{ TSL-04 }
                                     User_1_DeactivateN,   { TSL-03 }
                                     User_1_PurchN,
                                     User_1_OverrideN,     { JGS-I }
                                     User_1_PreAuthN,      { TSL-03 }
                                     User_1_PreAuthCompN,  { TSL-03 }
                                     User_1_RechargeN,     { TSL-03 }
                                     User_1_ReturnN,
                                     User_1_VoiceN,
                                     User_1_RefundActivationN,
                                     User_1_CashOutN,
                                     User_1_PreRechargeN,

                                     FleetPurchN,
                                     FleetPreAuthN,
                                     FleetPreAuthCompN,
                                     FleetReturnN,

                                     User_2_PurchN,
                                     User_2_OverrideN,     { JGS-I }
                                     User_2_VoiceN,

                                     WorkingKeyReqN];

  Tot_Trx_Set       : Set of Byte = [CrPurchN,
                                     DbPurchN,
                                     ConnectPayPurchN,
                                     CheckPurchN,
                                     PropDbPurchN,
                                     PropCrPurchN,
                                     AchPurchN,
                                     EBT_FS_PurchN,
                                     EBT_Cash_PurchN,
                                     wireLessActivationN,
                                     wireLessDeactivateN,
                                     User_1_DeactivateN,    { TSL-03 }
                                     User_1_ActivationN,
                                     User_1_PreActivationN, { TSL-04 }
                                     User_1_PurchN,
                                     FleetPurchN,
                                     User_2_PurchN,
                                     CrOverrideN,
                                     DbOverrideN,
                                     PropDbOverrideN,
                                     PropCrRechargeN,
                                     EBT_FS_OverrideN,
                                     EBT_Cash_OverrideN,
                                     User_1_OverrideN,
                                     FleetReturnN,
                                     User_2_OverrideN,
                                     CrReturnN,
                                     DbReturnN,
                                     ConnectPayReturnN,
                                     CheckOverrideN,
                                     PropDbReturnN,
                                     PropCrReturnN,
                                     AchReturnN,
                                     EBT_FS_ReturnN,
                                     EBT_Cash_ReturnN,
                                     User_1_RechargeN,
                                     User_1_ReturnN,
                                     FleetPreAuthN,
                                     User_2_ReturnN,
                                     CrVoiceAuthN,
                                     DbVoiceAuthN,
                                     CheckVoiceAuthN,
                                     PropDbVoiceAuthN,
                                     PropCrVoiceAuthN,
                                     AchVoiceAuthN,
                                     EBT_FS_VoiceN,
                                     EBT_Cash_VoiceN,
                                     User_1_VoiceN,
                                     FleetPreAuthCompN,
                                     User_2_VoiceN,
                                     CrPreAuthN,
                                     DbPreAuthN,
                                     PropCrPreAuthN,
                                     User_1_PreAuthN,      { TSL-03 }
                                     EBT_Cash_PreAuthN,
                                     CrPreAuthCompN,
                                     DbPreAuthCompN,
                                     eWicForceN,
                                     eWicPreAuthCompN,
                                     PropCrPreAuthCompN,
                                     EBT_Cash_PreAuthCompN,
                                     User_1_PreAuthCompN,  { TSL-03 }
                                     DbBalInqN,
                                     CrBalInqN,
                                     CheckBalInqN,
                                     PropDbBalInqN,
                                     PropCrBalInqN,
                                     EBT_FS_BalInqN,
                                     EBT_Cash_BalInqN,
                                     User_1_BalInqN,
                                     User_2_BalInqN,
                                     CrPaymentOnAcctN,
                                     CrReturnWithValidN,
                                     DbReturnWithValidN,
                                     //DbKeyExchangeN,
                                     PropCrReturnWithValidN,
                                     PropDbReturnWithValidN,
                                     User_1_RefundActivationN,
                                     User_1_CashOutN,
                                     User_1_PreRechargeN,
                                     WorkingKeyReqN,
                                     PropDbPreAuthN,      // CPCLINETS-12016
                                     PropDbPreAuthCompN  // CPCLINETS-12016
                                     ];

BalInq_Set          : Set of Byte = [DbBalInqN,
                                     CrBalInqN,
                                     PropDbBalInqN,
                                     eWicBalInqN,
                                     PropCrBalInqN,
                                     CheckBalInqN,
                                     EBT_FS_BalInqN,
                                     EBT_Cash_BalInqN,
                                     User_1_BalInqN,
                                     User_2_BalInqN,
                                     BenefitBalanceInquiryN]; // CPCLIENTS-9586 Transaction Type for Benefits Program for Offline

{ MTX response codes }

  TrxAppWCap        = '101';
  TrxAppWCapN       =  101;
  TrxAppSet         : set of byte = [100, TrxAppWCapN];   // 100 is the old Trx-Approved var, no longer used

  TrxUseTermRspCode = '102';
  TrxUseTermRspCodeN=  102;
  TrxCkAppMgrOver   = '103';
  TrxCkAppMgrOverN  =  103 ;

  TrxDecNoAuthNo    = '115';
  TrxDecNoAuthNoN   = 115;                                                      

  KeyAllRcvd        = '120';
  KeyAllRcvdN       =  120;
  KeyOneRcvd        = '121';
  KeyOneRcvdN       =  121;
  KeyTimeout        = '122';
  KeyTimeoutN       =  122;
  KeyOneUnsol       = '123';
  KeyOneUnsolN      =  123;
  KeyAllUnsol       = '124';
  KeyAllUnsolN      =  124;

  TrxDecBadTrack2         = '126'; // DEV-39191 (827.2's DEV-39190), for publix and bad track 2 data
  TrxDecBadTrack2N        =  126;
  TrxDecCustomerCancel    = '127';
  TrxDecCustomerCancelN   =  127;
  TrxDecCVV2Bad           = '128';  // CVV2  Len must be less than 5
  TrxDecCVV2BadN          =  128;
  TrxDecInsuffFunds       = '129'; // created for BYL host which incorrectly sends an approval for zero dollars on lesser
  TrxDecInsuffFundsN      =  129;
  TrxDecInvalidAmount     = '130';
  TrxDecInvalidAmountN    =  130;
  TrxDecNeedPINAndTrack2  = '131';
  TrxDecNeedPINAndTrack2N =  131;
  TrxDecFSAAmountInvalid  = '132';
  TrxDecFSAAmountInvalidN =  132;
  TrxDecPINTriesExceeded  = '133';
  TrxDecPINTriesExceededN = '133';
  TrxDecVoidMismatch      = '134';
  TrxDecVoidMismatchN     =  134;
  TrxDecVoidNotAllowed    = '135';
  TrxDecVoidNotAllowedN   =  135;
  TrxDecCashBackMismatch  = '136';
  TrxDecCashBackMismatchN =  136;
  TrxDecBadEBTVoucher     = '137';
  TrxDecBadEBTVoucherN    =  137;
  TrxDecExpDateManual     = '138';
  TrxDecExpDateManualN    =  138;
  TrxDecBadPfxManual      = '139';    { Publix wanted this }
  TrxDecBadPfxManualN     =  139;
  CashierOn               = '140';    { INTERNAL ONLY CODES }
  CashierOnN              =  140;
  CashierInvalid          = '141';
  CashierInvalidN         =  141;
  ManagerInvalid          = '142';
  ManagerInvalidN         =  142;
  ModemDown               = '143';
  ModemDownN              =  143;
  TrxDecTimeout           = '144';
  TrxDecTimeoutN          =  144;
  TrxDecBadPrefix         = '145';
  TrxDecBadPrefixN        =  145;
  TrxDecTrxNotAlwd        = '146';
  TrxDecTrxNotAlwdN       =  146;
  TrxDecDup               = '147';
  TrxDecDupN              =  147;
  NewKeyNeeded            = '148';
  NewKeyNeededN           =  148;
  TrxDecNoFCT             = '149';   { not valid for XML config }
  TrxDecNoFCTN            =  149;
  TrxDecBadM10            = '150';
  TrxDecBadM10N           =  150;
  TrxDecExpDate           = '151';
  TrxDecExpDateN          =  151;
  TrxDecAmt               = '152';
  TrxDecAmtN              =  152;
  TrxDecCashB             = '153';
  TrxDecCashBN            =  153;
  TrxDecNoManEnt          = '154';
  TrxDecNoManEntN         =  154;
  TrxDecInvalidLane       = '155';      { TSL-B }
  TrxDecInvalidLaneN      =  155;       { TSL-B }
  TrxDecBadMICR           = '156';
  TrxDecBadMICRN          =  156;
  TrxDecNotInActLog       = '157';      { TSL-02 }
  TrxDecNotInActLogN      =  157;
  TrxDecCashBNAllow       = '158';
  TrxDecCashBNAllowN      =  158;
  TrxUnderMin             = '159';      { JMR-I }
  TrxUnderMinN            =  159;

  DeclAdvMTXRspCodeSet : set of byte = [TrxDecNoAuthNoN..TrxUnderMinN];

  TrxDecNewSeq      = '160';      { FROM HOST SYSTEM }
  TrxDecNewSeqN     =  160;
  TrxDecTryLocal    = '161';
  TrxDecTryLocalN   =  161;
  TrxDecGen         = '162';
  TrxDecGenN        =  162;
  TrxDecNoHostDef   = '163';
  TrxDecNoHostDefN  =  163;
(*
  TrxDecCall        = '164';     {Removed per Tom}
  TrxDecCallN       =  164;  *)
  TrxDecNSF         = '165';
  TrxDecNSFN        =  165;
  TrxDecBankUnav    = '166';
  TrxDecBankUnavN   =  166;
  TrxDecRetryOff    = '167';
  TrxDecRetryOffN   =  167;
  TrxDecNoSlot      = '168';
  TrxDecNoSlotN     =  168;
  TrxDecSwitchDown  = '169';
  TrxDecSwitchDownN =  169;
  TrxDecBankTimeout = '170';
  TrxDecBankTimeoutN = 170;
  TrxDecNoPartialAuth = '171';
  TrxDecNoPartialAuthN = 171;

  TrxAppOff         = '200';
  TrxAppOffN        =  200;
  TrxDecOffPref     = '201';
  TrxDecOffPrefN    =  201;
  TrxAppOffEcc      = '202';
  TrxAppOffEccN     =  202;
  { 203 unused }
  TrxDecOffAmt      = '204';
  TrxDecOffAmtN     =  204;
  TrxDecOffCashB    = '205';
  TrxDecOffCashBN   =  205;
  TrxDecOffInvalid  = '209';
  TrxDecOffInvalidN =  209;
  TrxDecOffWaitTO   = '210';   { Waiting offline timeout delay }
  TrxDecOffWaitTON  =  210;
  TrxDecHostNetworkDown  = '211';
  TrxDecHostNetworkDownN = 211;
  TrxDecDbNoOffline = '214';    // note 213 is an approved code in the internal rsp code so we skipped it here
  TrxDecDbNoOfflineN = 214;
  TrxDecOffVoiceAuth  = '215'; // DEV-28404
  TrxDecOffVoiceAuthN =  215;  
  TrxAppOffSet      : Set of Byte = [TrxAppOffN,TrxAppOffEccN];

  { OpenEPS specific codes }
  TrxDecOpenEPSFwdFail  = '221';
  TrxDecOpenEPSFwdFailN =  221;
  TrxDecOpenEPSGoLocal  = '222';
  TrxDecOpenEPSGoLocalN =  222;

  TrxAppSigCap          = '223';
  TrxAppSigCapN         =  223;

  TrxDecPrePaidNoOffline = '229';
  TrxDecPrePaidNoOfflineN = 229;
  TrxDecOffSysFail      = '230';
  TrxDecOffSysFailN     =  230 ;
  TrxDecTranTypeBad     = '231';
  TrxDecTranTypeBadN    =  231;
  TrxDecNeedFleetID     = '232';    { TSL-05 }
  TrxDecNeedFleetIDN    =  232;
  TrxDecNoHostVPurch    = '233';  { OrigTrxOffApp: orig trx changed from OffApp to this }
  TrxDecNoHostVPurchN   =  233;
  TrxDecNoHostAppVoid   = '234';  { OrigTrxOffApp: disp of a void if orig in offline }
  TrxDecNoHostAppVoidN  =  234;
  TrxDecPINDataBad      = '235';    { i.e. bad PIN block data sent from register }
  TrxDecPINDataBadN     =  235;
  TrxDecCheckType       = '236';    { check type from register invalid }
  TrxDecCheckTypeN      =  236;
  TrxDecNeedSecondID    = '237';    { TSL-07 }
  TrxDecNeedSecondIDN   =  237;
  TrxDecNoCashOnly      = '238';
  TrxDecNoCashOnlyN     =  238;
  TrxDecNoHostDefined   = '239';
  TrxDecNoHostDefinedN  =  239;
  TrxDecBlackHawkUPC    = '240';
  TrxDecBlackHawkUPCN   =  240;
  TrxDecNVNotAllowed    = '241';
  TrxDecNVNotAllowedN   =  241;
  TrxDecNoTranComplete  = '242';
  TrxDecNoTranCompleteN =  242;
  TrxDecNoXPIFallBack   = '243';
  TrxDecNoXPIFallBackN  =  243;
  TrxDecNeedZipCode     = '244';
  TrxDecNeedZipCodeN    =  244;
  TrxDecNeedMACBlock    = '245';
  TrxDecNeedMACBlockN   =  245;
  TrxDecNotAccepted     = '246';
  TrxDecNotAcceptedN    =  246;
  TrxDecNoFallback      = '247';      // DOEP-49845
  TrxDecNoFallbackN     =  247;
  TrxDecHostFormatError = '248';
  TrxDecHostFormatErrorN=  248;
  TrxDecHostDCC         = '249';      //11758
  TrxDecHostDCCN        =  249;       //11758
  TrxDecNoCrToDbToCr    = '250';      // TFS-18094
  TrxDecNoCrToDbToCrN   =  250;       // TFS-18094
  TrxDecNoXPIPINVerify  = '251';      // TFS-24293
  TrxDecNoXPIPINVerifyN =  251;       // TFS-24293
  TrxDecPWQRFlagOFF     = '252';  // CPCLIENTS-11227
  TrxDecMinPurchaseCBAmt= '253';  // CPCLIENTS-18763
  TrxDecInvalidBPFollowUpTran  = '254';  // 19342

  TimeOutSet : set of byte = [TrxDecTimeOutN, TrxDecBankUnavN, TrxDecBankTimeoutN];
  LocalSet   : set of byte = [TrxDecTryLocalN, TrxDecRetryOffN, TrxDecNoSlotN];


  LaneMsgDSN_           = 'lanemsg.eft';
  TraceDSN_             = 'trace.eft';
  LaneMsgPrefix         = 'lanemsg.';   { TSL-N }

  SettleVersionL    = 16;   { JMR-F }
  SettleReportL     = 800;  { JMR-H }
  RptInfoL          = 16;   { JMR-H }
  (*          conflict with host.inc file - must be same length
  HostCkAuthTypeL   = 1;
  HostNameL         = 8;
  HostSuffixL       = 3;
  HostTransactionsL = 1;
  *)
  ZipCodeL          = 10;    { JMR-J }

  // DEV-51062, maximum size of eWIC RX data
  MAX_EWIC_RX_LEN = 3000;

//  256 bit AES key lengths
//
//  clear length        encrypted length
//  ------------        ----------------
//  1-15                44
//  16-31               64
//  32-47               88
//  48-63               108
//  64-79               128
//  80-95               152

type
  fleetArray = array[1..500] of char;
  S2 = string[2];   // CPCLIENTS-9586 Changed its length as new MTX internal request code for Benefits Program exceeded 2 digits value


  MDMsgRec = Record
    ReqCode: S2;

    case byte of
      1 :  { Normal trx MdMsgRec }
         (VoidCode                : String[1]; { V or blank            }
          ReqType                 : String[1]; { see Request types for MDMsg }
      {$IFDEF MTXEPSDLL}
          LaneNo                  : string[4]; // DOEP-65125, need to expand lane# beyond 2-digits for Target and future
      {$ELSE}
          LaneNo                  : string[2]; // DOEP-65125, Keep this 2-digit for all but EPS DLL
      {$ENDIF}
          RecNo                   : String[2];
          Cashier                 : String[9];
          SeqNo                   : String[6];
          AcctNo                  : String[64];   { JMR-K : was 20 }  { encrypted field }
          Entry                   : String[1];
          AtallaKey               : String[16];
          PIN                     : String[64];   { JMR-K : was 16 } { encrypted field }
          ExpDate                 : String[44];   { JMR-K : was 4 } { encrypted field }
          AcctInd                 : String[1];
          TrxAmt                  : String[12];   { TSL-02 }
          CashBack                : String[9];
          FeeAmt                  : String[6];
          FeeType                 : String[1];       //12398
          Customer_Name           : String[88];   { JMR-K : was 40 } { encrypted field }
          EBT_Cash_Balance        : String[9];
          EBT_FS_Balance          : String[9];
          Tip {EBT_Case_Num }     : String[6];    { TSL-T } { JMR-A : was 7 }
          CrToDbCode              : AnsiChar;         { JMR-A }     //7934
          StoreID                 : String[16];   { TSL-H }
          TrxFinalDisp            : String[1];
          SentToModemYN           : String[1];
          SwRspCode               : String[3];
          MTXRspCode              : String[3];
          TermRspCode             : String[2];
          AuthCode                : String[8];
          PreAuthTrxFinalDisp     : string[1];          { TSL-03 set in TrxStuf.pas }
          PrimaryOrBackup         : AnsiChar;        //7934
          Supp_Date               : String[6];
          Supp_Time               : String[6];
          Dr_License              : String[40];
          Check                   : String[44];  { TSL-L was 65,reduce for DukptKeySerialNumber }
          DukptKeySerialNumber    : String[64];  { JMR-K : was 20 } { encrypted field }
          St_Code                 : String[2];
          DOB                     : String[6];
          OldSeq                  : String[6];
          SwSeqNo                 : String[10];
          TrxDate                 : String[6];
          TrxTime                 : String[6];
          Track2Data              : String[88];  { JMR-K : was 40 } { encrypted field }
          BankOff                 : String[1];  { * in offline file     }
          CashierName             : String[16]; {   indicates this is a }
          CashPadDisp             : String[16]; {   trx from a prior day}
          PrimeCustDisp           : String[24];
          AltCustDisp             : String[24];
          CardProcID              : String[2];
          CardName                : String[16];
          preAuthAmt              : String[8];   { TSL-W }
          ReturnedACI             : string[1];   { TSL-W }
          CashierSignGrp          : integer;     { JMR-E }
          OldKey                  : String[16];
          ManagerID               : String[9];
          SwRefNo                 : String[10];
          IntoOfflineQueueDate    : String[7];  { YYYYDDD  }
          IntoOfflineQueueTime    : String[6];  { HHMMSS }
          LastOfflineSubmitDate   : String[7];  { YYYYDDD  }
          LastOfflineSubmitTime   : String[6];  { HHMMSS }
          NextOfflineSubmitDate   : String[7];  { YYYYDDD  }
          NextOfflineSubmitTime   : String[6];  { HHMMSS }
          FinalOfflineSubmitDate  : String[7];  { YYYYDDD  }
          FinalOfflineSubmitTime  : String[6];  { HHMMSS }
          SuppOfflineSubmitDate   : String[7];  { YYYYMMDD }
          SuppOfflineSubmitTime   : String[6];  { HHMMSS }
          CurDailyOfflineSubmitCnt: Word;
          MaxDailyOfflineSubmitCnt: Word;
          CurTotalOfflineSubmitCnt: Word;
          MaxTotalOfflineSubmitCnt: Word;
          ReqCodeN                : Byte;
          LaneNoN                 : Byte;
          RecNoN                  : Byte;
          To_Switch_Time          : LongInt;
          Fm_Switch_Time          : LongInt;
          TrxAmtN                 : Int64;      { TSL-02 }
          CashBackN               : LongInt;
          FeeAmtN                 : LongInt;
          EBT_Cash_BalanceN       : LongInt;
          EBT_FS_BalanceN         : LongInt;
          AcctFileRecNoN          : LongInt;
          VoidRecNoN              : LongInt;
          SwRspCodeN              : Word;
          OffLineAuthCodeN        : Byte;
          TrackCkr                : AnsiChar;     //7934
          MTXRspCodeN             : Byte;
          MTX_SeqNo               : String[6];
          Build_Rcpt_Data         : String[3];  { CU Type, Receipt Size, Printer Type }
          Trx_Is_Training         : Boolean;
          Entry_Check             : String[1];  { TSL-C }
          Entry_Track2            : String[1];  { TSL-C }
          Entry_DL                : String[1];  { TSL-C }
          { added in version 810 }              { TSL-W }
          RetrievalRefNo          : String[15];
          Check_Num               : String[8];
          Check_Type              : String[7];
          Check_Field_1           : String[19];
          Check_Field_2           : String[19];
          Check_Field_3           : String[19];
          EBT_Voucher_Num         : String[15];
          PostTransactionNumber   : String[22];
          ReferenceId             : String[40];
          OriginalReferenceId     : String[40];
          unused3                 : String[17]; // was POSechoData
          PreAuthRecNoN           : integer;
          CompletionRecNoN        : integer;
          HostSuffixCode          : string[3];
          MaxOfflineAmt           : string[6];
          PhoneNumber             : string[16]; { TSL-03 }
          HostApproveLowerAmount  : AnsiChar;       { TSL-03 = Y/N from post, =^ if it happened}    //7934
          { Added in 813.0 }
          fleetCardType           : integer;
          fleetOdometer           : string[10];
          fleetVehicleID          : string[20];
          fleetDriverID           : string[20];
          fleetData               : fleetArray; { or RAW MICR or EMV Req tags for MON host since fleet and check/EMV don't go together }
          fleetLen                : integer;
          SecondaryIDType         : string[2];
          CmdSequence             : string[20];
          TaxAmt                  : string[9];
          TaxExempt               : Boolean;//14562
          ACISlotNumberN          : integer;
          UPCCode                 : string[14];
          TORType                 : string[1];   { see ReqType - for making TOR file }
          PONumber:                 string[12];
          ACIPreAuthSeqNo         : string[6];
          primaryIDType           : string[2];
          UUID                    : string[32];
          HostTextMsg             : string[20];
          YYYYMMDD                : string[8]; { TSL-13 }  { was unused zip for string[10] }
          languageID              : byte;      { TSL-13 }  {  "  }
          BarCodeScan             : Boolean;   { JMR-L }
          doNotCancelOffPurchVoid : AnsiChar;  { doesn't check off.Orig_In_Offline_Q for voids - set in host prog (Publix) } //7934
          EChkCapable             : AnsiChar;  { for check auth, =Y if chk conversion allowed }                              //7934
          FuelAmount              : integer;
          CVV2                    : string[44];   { JMR-K : was 9 } { encrypted field }
          LongSwRspCode           : string[10];
          BioMetricsTran          : AnsiChar;  { Y = Biometrics tran, N = not BioMetric Tran }  //7934
          FleetRestrictionCode    : string[4];
          OriginalIsoMsgType      : string[4];
          ZipCode                 : string[44]; { JMR-L } { encrypted field }
          Account2Balance         : integer;    { TSL-13 }  { PIN minute bal for phone card }
          WirelessPIN             : string[20]; { TSL-13 }
          ControlNumber           : string[20]; { TSL-13 }  { for phone cards and anything else }
          RFID                    : string[3];
          AcctNoFirst6            : array[1..6] of char;
          AcctNoLast4             : array[1..4] of char;
          PrintNameOnRcpt         : AnsiChar;                //7934
          BatchNumber             : string[9];
          ReturnCheckFeeNote      : string[100];
          PreAuthAmtOnlinePump    : string[6];
          PreAuthAmtOfflinePump   : string[6];                                  
          UserID                  : string[50]; //JTG
          id                      : integer; { JMR: primary key for table, i.e. act_id, used to insert to "other" DB }
          CustomerAddress:          string[40]; // JTG-WS, subtract 41 from Unused (was 176)
          CustomerCity:             string[40]; // JTG-WS, subtract 41 from Unused (was 135)
          CustomerState:            string[2];  // JTG-WS, subtract 3 from Unused (was 94)
          FsaAmount:                integer;
          RxAmount:                 integer;
          CurrencyCode:             string[3];  // JTG-WS subtract 4 from Unused of 83

          MailOrderType:            string[1];  // JTG subtract 2 from unused of 79
          ECommSecurityInd:         string[2];  //etc
          ECommGoodsInd:            string[1];  //ditto
          POSConditionCode:         string[2];
          FSACard:                  AnsiChar;  { Y = FSA Card, N = not FSA Card }                       //7934
          ChkSigLineECCOnly:        AnsiChar;   // if = Y print sig line on ECC chks, not on others     //7934
          DentalAmount:             integer;
          MedicalAmount:            integer;
          VisionAmount:             integer;
          ProgramID:                string[5];
          PinPadSerialNumber:       string[20];
          EPSLaneType:              AnsiChar;         //7934
          KeyVersion:               string[10];      // ex. 'ver825.1  '
          Key:                      string[128];    // encrypted 64 length hex string
          KeyVerificationString:    string[88];     // encrypted (clear string can be up to 47 chars long)
          LocalOverrideFlag:        boolean;
          ZipCodeEntry:             AnsiChar; // DEV-8449: Y,N     //7934
          OrigReqType:              string[1];
          NOVvoidData:              string[28];
          AuthCodeIfLocal:          string[6];      // TSL Dev-10859
          skipSigOnline:            boolean;
          skipSigOffline:           boolean;
          skipSigAmount:            word;
          IsIbmRegister:            boolean;
          TORSuffixCode:            string[3];    // needed for Stater Bros, LML and LM2
          ReturnCheckFee:           string[6];    // moved from BYL host below, used in LYNK as well, now
          ValidateChecker:          boolean;
          OriginalTrxAmtN:          Int64;        // DEV-14384 TSL
          VoucherClearProcess:      boolean;      // JMR DEV-8000 Hannaford Voucher Clear Process
          PromptForOdometer:        string[1];    // TSL DOEP-49890 FUEL prompt for ODO could be set in FCTBuf for any non fleet preAuth
          IsHostResponse:           boolean;      // TFS-13971
          LookupPrefix:             array[1..8] of AnsiChar;   // CPCLIENTS-16776 was: PAN10digits_Unused: string[9]; // which is 10 bytes
          Unused1:                  byte;
          Unused2:                  byte;
          PanLen:                   byte;
          EncryptedTrack:           boolean;
          LastAuthCode:             string[8];   // DEV-8000 Voucher Clear App: keep last auth code to show user
          DeclinedAdvice:           boolean;
          PromptOnManual:           string[1];
          EBTHIPCurBal:             integer;   // in dollars, no cents
          EBTHIPMtdBal:             integer;   // in dollars, no cents
          EBTHIPAmount:             integer;
          IsSigElectronic:          boolean;

          // DOEP-37634: moved from host specifc area - takes 38 bytes
          TDBRetailerID:            string[24];    //JTG 32385   for ServerEPS TDB host prefix lookups
          TDBFIID:                  string[4];     //JTG 32385
          TDBHostResponse:          string[3];
          TDBPINVerified:           boolean;
          TDBSigNotRequired:        boolean;
          TDBDraftCaptureFlag:      string[1];    //JTG 32385
          CardServiceCode:          string[3];    // TSL used in prefixSearch for TDB host to determine debit tender

          SSN:                      string[9];    //JTG Dev 31708
          PayrollCheckIssueDate:    string[8];    //JTG Dev 31708
          PrintMinimalReceipt:      AnsiChar;         //TSL Dev 42109    //7934
          BlankPreAuthCardInfo    : boolean;
          AVSApprYorN             : string[1];    // TSL Dev 53003
          PaymentInstrumentToken  : string[255];  // CPCLIENTS-11585 - To support value received from Heliopy for 121 TokenTransactions
          NoCVMAmount             :
             {$IF Defined(D2007)}
             integer;
             {$ELSEIF Defined(VER140)}
             Integer;
             {$ELSE}
             Int32;
             {$IFEND}        // MCD TFS-7513
      {$IFDEF MTXEPSDLL}
          UnusedArea              : array[1..14] of char;  // DOEP-65125, made LaneNo longer above, so shortened this
      {$ELSE}
          UnusedArea              : array[1..16] of char;  // DOEP-65125, Keep this the same for all but EPS DLL
      {$ENDIF}

          case byte of
            1:(HostSpecificData        : string[150]); { extra space for hosts }

            2:   { cashier sign off }
              (TGroupCode              : Array[1..MaxNumGroups] of String[2];
               TNetNum                 : Array[1..MaxNumGroups] of Word;
               TNetAmt                 : Array[1..MaxNumGroups] of LongInt;
               TCashDispNum            : Word;
               TCashDispAmt            : LongInt;
               TNumOfGroups            : Integer );

            3:   { Mercury and LML Host }
              (Long_Journal_Msg        : String[38];
               Checker_Messages        : String[40]);

            4:   { Buypass host for ACR }
              (bylUnused1              : String[20];  // So that PassthruMsg doesn't conflict with FTermSerialNum in the IBM variant section
               PassThruMsg             : String[20];
               VelocityData            : String[20];
               bylUnused2              : AnsiChar;          //7934
               IsFLCSVSAuthUsed        : boolean;
               bylFidP                 : string[1];   // could use the new AVSApprYorN above - just change this var everywhere it is used.
               ECCProductCode          : string[06];
               TraceID                 : string[22];
               BillingControlNum       : string[25]; // not supported
               DenialRecordNum         : string[07];
               BYLATLIssuerCode        : string[2]   // CPCLIENTS-5179 To have meaningful name for BYLATLIssuerCode as it is implemented for ATL too
               );

            5:  { Paypoint host }
              (RewardsVoucherNumber    : string[20];
               RewardsKeyCode          : string[20];
               RewardsVoucherIssuer    : string[20];
               DiscountPerUnit         : integer;
               UnitsAllowedForDiscount : integer;
               TotalDiscountGiven      : integer;
               ProductType             : string[20];
               PricePerUnit            : integer;
               TotalUnits              : integer;
               RewardsVoucherRedeemedDate : string[20];
               NKProcessingTAC         : string[10];
               PayRspCode              : string[3]);

            6:  { Global host }                              { TSL-R }
              (GlobalPayBatchNum       : string[16];
               GlobalPayItemNum        : string[16]);

            7:  { Lynk host}
              (LynkRetrievalData       : string[100];
               Extra_EBT_Msg           : String[16];
               EBT_FNS_Number          : string[8];
               LynkACHRefNo            : string[11]);

            8:(New_PIN                 : String[16]);  { mainsail }

            9:(BofHReversalData        : String[23]);  { ces, wells }

           10:(PublixTransmissionNumber: String[2];    { publix host }
               PublixResponseFids      : string[15];
               PublixHostRetrievalNo   : string[15];
               PublixFieldhSeqNo       : string[10];
               PublixDeleteTOR         : AnsiChar;       //7934
               PublixDr_LicFromPOS     : AnsiChar;       //7934
               PublixOldTermRspCode    : string[2];
               PublixVoidSeqNum        : string[6];  { TSL-03 }
               PublixSettleType        : string[1];
               PublixReceivedCheckAcct : boolean;
               PublixhSeqNoForAuthComp : string[10];
               PublixZeroAuthComp      : boolean;
               // DEV-52504, use original purch req date/time in special publix fields for FID 'a' user data in a void trans. Needed to add fields to store that info.
               PublixPurchReqDate      : string[6];
               PublixPurchReqTime      : string[6];
               // DEV-52503, needed to add and use special fields for publix approved amount for eWIC
               PublixApprovedAmtN      : Int64);

           11:  { IBM register - make sure total len is =< bylUnused above}
              (FTermSerialNum          : string[8];
               FTranNum                : string[4];
               FTranCode               : string[2];
               FIBMSeqNo               : string[7]);

           12:  { NOV Host items }
              (NOVreferenceNum         : string[8];
               NOVtraceNum             : string[6];
               NOVtranRefNum           : string[10];
               NOVtranID               : string[15];
               NOVfreeFormData         : string[76];
               NOVSequenceNum          : string[3];
               NOVServiceFeeData       : string[21];
               NOVRspSource            : string[1]);

           13:(MerHostMsg              : string[40];
               MerRecordNo             : string[99]);

           14:(TeleCheckProductCode    : string[40];        // FLC, FL2 hosts for Food Lion
               TeleCheckMicrSeqNum     : string[3];
               TeleCheckAchStatus      : string[1];
               TeleCheckAchAction      : string[1];
               TeleCheckLoyaltyCode    : string[1];
               FL2OldTermRspCode       : string[2];
               FL2HostMsg              : string[30]);

           15:(MONTorType:               string[1];   // I=ICC reversal, M=MAC Reversal
               MONTransmissionNumber:    String[2];
               MONFieldhSeqNo:           string[10];
               MONResponseFids:          string[14];
               MONAddrVerificationCode:  string[1];
               MONPumpLanguageID:        string[1];
               MONRspCodeISO:            string[2];
               MONEMVIso:                string[2];
               MONMACEncryptionKey:      string[16];
               MONMACData:               string[8];
               MONFid9J:                 string[32];
               MONMsgHeader:             string[48];
               MONMACShouldBeBlank:      Boolean); // DEV-60508

           16:(isDCCTrans:               boolean;    // Rapid Connect Host, DCC enabled, TFS 11758
               DCC_Indicator:            integer;    //enumDCCIndicator
               DCC_ExchangeRate:         double;
               DCC_ExchangeRateStr:      string[10];
               DCC_ExchangeRateMarginPercentage: double;
               DCC_CurrencyCode:         string[3];  // 11758
               DCC_CurrencyNumber:       integer;  // 11758
               DCC_CurrencySymbol:       string[1];  // 11758
               DCC_DecimalDigits:        integer;
               DCC_CodeAndAmount:        string[20];// 17758
               DCC_CurrencyName:         string[30]);

           17:(ACIVoiceAuthCode        : string[6];
               ACIPOSTrackingNumber    : string[40]));  { ACI host } { TSL-03 }

      2: { Report }
          (RptVersion                 : string[16];  // i.e. 'RPTv807         '
           RptGroupName               : string[16];  // i.e. 'Credit Cards    '
           RptGroupCode               : string[2];   // i.e. 'MC'
           RptReport                  : string[2]);  // i.e. 'SS' (store summary)

      3: { Settlement } { This makes the settlement file no longer necessary }
          (SettleVersion              : string[SettleVersionL];        { JMR-F }
           HostSuffix                 : string[3];
           SettleReport               : array[1..SettleReportL] of byte;
           HostCkAuthType             : string[1];       { TSL-T }
           HostName                   : string[8];
           HostTransactions           : Array[1..10] of String[1];
           HostMessageOut             : Array[1..500] of byte);

//      4: { Rpt Info }                                                  { JMR-H }
//          (RptInfo                    : string[RptInfoL];    // 'RPTINFO         ';
//           RptInfoRec                 : DSRptInfoRec;
//           MDMSGRecordLength          : string[5]);

//      5: { File Encryption }
//          (KeyVersion                 : string[16];    // 'kver_825.1      '
//           Key                        : string[88];    // encrypted 32 length hex string
//           VerificationString         : string[152]);  // encrypted (clear string can be up to 95 chars long)


                                         { Tot   77   "     Cannot be bigger than New_Pin -> Field_4_CU }
  end; { of MdMsgRec }                   {                  which is 89 bytes                           }
                                         { Then plug in 17 dummy bytes then continue                    }


  { These records are sent from the switch to }
  { The terminal module.  If in State 7, they }
  { are re-blocked and sent to the terminal   }

  PrintRec  =
    record
      TermNum   : Byte;
      PrintRecs : array[1..24{30}] of string[40{32}];   { TSL-07 }
    end;

  KeyRec =               { TSL-S }
    record
      ReqCode         : string[2];
      ReqCodeN        : byte;
      ReqType         : string[1];
      LaneNo          : string[2];
      LaneNoN         : byte;
      SwRefNo         : String[10];
      PIN             : String[16];
      AtallaKey       : String[16];
      MTXRspCode      : String[3];
      MTXRspCodeN     : Byte;
      SentToModemYN   : String[1];
    end;

  FleetRec =                                      { TSL-05 }
    record
      dummy  : char;
    end;

  TeWicRec =
    record
      eWICRxMerchDiscount: string[19];
      LaneNo: string[02]; // failsafe
      PIN: string[64];
      DukptKeySerialNumber: string[64];
      Track2Data: string[88];
      eWicRx: array [1..MAX_EWIC_RX_LEN] of char; // DEV-49897: was AnsiString;
    end;

  (*                                                                           // TFS-00000
  TEMVReceiptData =
    record
      AppID: string[32];    // tag 4F, tag 84 (max len 32)
      AppLabel: string[19]; // tag 50
      cid: string[20];      // tag 9F27
      iad: string[32];      // tag 9F10
      tc: string[16];       // tag 9F26
      tvr: string[10];      // tag 95
      tcc: string[4];       // tag 9F1A
      tcd: string[4];       // tag 5F2A
      aip: string[4];       // tag 82
      atc: string[4];       // tag 9F36
      cvmr: string[6];      // tag 9F34
      ps: string[2];        // tag 5F34
      up: string[8];        // tag 9F37
      iac_df: string[10];  // tag 9F0D
      iac_dn: string[10];  // tag 9F0E
      iac_ol: string[10];  // tag 9F0F
      ContEMV_cid: string[20]; // tag 9F27 from C33
      ContEMV_tc: string[16]; // tag 9F26 from C33
      language: string[1];
      accountType: string[1];
      tsi: string[4];     // tag 9B   // TFS-14778
      arc: string[4];     // tag 8A   // TFS-14778
      PreferredName: string[16]; // tag 9F12 // TFS-14778
      CodeTableIndex: string[2]; // tag 9F11 // TFS-14778
      HostReceiptMessage: string[30];  // DEV-44997 - moved HostReceiptMessage field from MdMsgRec to the TDB host receipt record
    end;
    *)

  THostExtraData =
    record
      MACDataString: string[120];
      EMVRspData: string[255];       // Dev-48931
      EMVIssuerScriptData1: string[255];
      EMVIssuerScriptData2: string[255];
    end;

  THostEMVTags =
    record
      tags1: string[255];
      tags2: string[255];
    end;

var
  DSLaneMsgFile: file of MDMsgRec;
  DSLaneMsgFile0: file of MDMsgRec;
  DSLaneMsgBuf: MdMsgRec;
  DSOffBufReq: MdMsgRec;
  DSOffBufRsp: MdMsgRec;
  DSOffBufTrx: MdMsgRec;
  eWicRec: TeWicRec;
  EWicRecPtr: ^TeWicRec;
  // EMVReceiptData: TEMVReceiptData;     // TFS-00000
  EMVTags: THostEMVTags;
  HostExtraData: THostExtraData;

const
  MDMSG_RECORD_LENGTH = '3071';

implementation

end.
