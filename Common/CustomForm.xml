<Form Name="MTX7"> 
    <Buttons>   
        <Button Number="N1" Caption="ONE" ID="I5" /> 
        <Button Number="N2" Caption="TWO" ID="I6" /> 
        <Button Number="N3" Caption="THREE" ID="I7" /> 
        <Button Number="N4" Caption="FOUR" ID="I8" /> 
        <Button Number="N5" Caption="FIVE" ID="I13" /> 
        <Button Number="N6" Caption="SIX" ID="I15" /> 
        <Button Number="N7" Caption="SEVEN" ID="I16" /> 
    </Buttons>   
    <Labels>   
        <Label Number="N1" Caption="Test Line1" ID="I1" /> 
        <Label Number="N2" Caption="Test Line2" ID="I2" /> 
        <Label Number="N3" Caption="Test Line3" ID="I3" /> 
    </Labels>   
    <EditBoxes> 
      <EditBox Number="N1" ValType="Numeric" DisplayString="MM/DD/YYYY" FormatString="NNCNNCNNNN" Min="M8" Max="M8" ID="I4"/> 
      <EditBox Number="N2" ValType="Numeric" DisplayString="MM/DD/YYYY" FormatString="NNCNNCNNNN" Min="M8" Max="M8" ID="I5"/>  
    </EditBoxes>
    <SignatureBox CaptureSignature="Y" Coordinates="150,250,650,375" SignatureData="NA"/>
    <CardReader EnableCardReader="Y" EnableChipReader="N" EnableRFID="Y" PersonalAccountNumberMasked="A" PanFirst6="A" PanLast4="A" ExpirationDate="A" CardHolderName="A" Track1Data="A" Track2Data="A" TokenData="A"/>
    <PINEntryRequest PINEntry="Y" Text="Please Enter PIN and Press ENTER" RandomNumber="****************"/>
    <EncryptedDataRequest EntryText="Please Enter SSN" ReEntryText="Please Re-Enter SSN" DisplayString="***-**-****" FormatString="NNNCNNCNNNN" Min="A" Max="A" DataType="SSN" DataOptions="A0011" TokenData="A" />
</Form>
   
