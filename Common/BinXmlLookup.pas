unit BinXmlLookup;

interface

uses
  Classes,
  SysUtils,
  StrUtils,
  Math,
  {$IFDEF MTXEPSDLL}
  epstrace,
  {$ENDIF MTXEPSDLL}
  BinXML,
  MTX_Constants;
  //MTX_XMLClasses,
  //MTX_Lib;

function ReloadBinXml(aFileName: string): boolean;
function BinLookup(aBinType: TBinType; aPrefix: string; aPanLength: integer; var isFound: boolean; var networkList: TStrings): boolean;
function BinLookup0Length(aBinType: TBinType; aPrefix: string; var isFound: boolean; var networkList: TStrings): boolean;

implementation

uses
  FinalizationLog;

type
  TPanLength = class(TObject)
  private
  public
    Length: integer;
    RangeList: TStringList;
    constructor Create(aLength: integer);
    destructor Destroy; override;
  end;

  TNetwork = class(TObject)
  private
  public
    Id: string;
    PanLengths: TList;
    constructor Create(aId: string);
    destructor Destroy; override;
  end;

const
  _AlphaNumerics = '123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';

var
  FsaList: TList;
  DebitList: TList;
  PrepaidList: TList;
  DCCList: TList;
  XmlFileName: string;

procedure msgLog(aMsg: string);
begin
  {$IFDEF MTXEPSDLL}
  showTrace(idTCP, aMsg);
  {$ENDIF MTXEPSDLL}
  {$IFDEF FUEL}
  SM(aMsg);
  {$ENDIF FUEL}  
end;

procedure MsgDebug(aMsg: string);
begin
  {$IFDEF DEBUG}
    msgLog('****DEBUG: ' + aMsg);
  {$ENDIF DEBUG}
end;

constructor TPanLength.Create(aLength: integer);
begin
  Length := aLength;
  RangeList := TStringList.Create;
  RangeList.Sorted := true;
end;

destructor TPanLength.Destroy;
begin
  FreeAndNil(RangeList);
  inherited Destroy;
end;

constructor TNetwork.Create(aId: string);
begin
  //msgLog('DEBUG 11758: ++++++++++ TNetwork.Create ++++++++++ ('+aId+')');
  Id := aId;
  PanLengths := TList.Create;
end;

destructor TNetwork.Destroy;
var i: integer;
begin
  //msgLog('DEBUG 11758: ---------- TNetwork.Destroy ---------- ('+Id+')');
  for i := 0 to PanLengths.count-1 do
    TPanLength(PanLengths.Items[i]).Free;
  PanLengths.Clear; // Shmilo - fix memory leak
  FreeAndNil(PanLengths);
  inherited Destroy;
end;

procedure DestroyLists;
var i: integer;
begin
  try
//    msgLog('---------- DestroyLists ----------');
    if Assigned(FsaList) then
    begin
      for i := 0 to FsaList.Count-1 do
        TNetwork(FsaList.Items[i]).Free;
      FsaList.Clear;
      FreeAndNil(FsaList);
    end;

    if Assigned(DebitList) then
    begin
      for i := 0 to DebitList.Count-1 do
        TNetwork(DebitList.Items[i]).Free;
      DebitList.Clear;
      FreeAndNil(DebitList);
    end;

    if Assigned(PrepaidList) then
    begin
      for i := 0 to PrepaidList.Count-1 do
        TNetwork(PrepaidList.Items[i]).Free;
      PrepaidList.Clear;
      FreeAndNil(PrepaidList);
    end;

    if Assigned(DCCList) then
    begin
      for i := 0 to DCCList.Count-1 do
        TNetwork(DCCList.Items[i]).Free;
      DCCList.Clear;
      FreeAndNil(DCCList);
    end;
  except on e: exception do
    msgLog('DestroyLists EXCEPTION - '+e.Message);
  end;
end;

function Rpad(s: string255; c: char; l: byte): string255;
var
  i: byte;
begin
  for i := length(s)+1 to l do s := s + c;
  result := s;
end;

function BinCompare(haystack, needle: string255): integer;
var
  adder: integer;
  fromRange, toRange, ineedle: int64;
  toHaystack: string255;
begin
  result := -1;
  try
    toHaystack := haystack;
    // convert the range to integers "from" and "to"
    if Pos('+', haystack) > 0 then
    begin
      adder := StrToIntDef(Copy(haystack, Pos('+', haystack)+1, length(haystack)),0);
      haystack := Copy(haystack, 1, Pos('+', haystack)-1);
      toHaystack := IntToStr(StrToInt64Def(haystack, 0) + adder);
    end;

    if length(needle) > length(haystack) then
      begin
      haystack := Rpad(haystack, '0', length(needle));
      toHaystack := Rpad(toHaystack, '9', length(needle));
      end
    else
    if length(needle) < length(haystack) then
      begin
      haystack := leftStr(haystack, length(needle));       // truncate haystack to the needle
      toHaystack := leftStr(toHaystack, length(needle));
      end
    else
      needle := Rpad(needle, '0', length(haystack));

    ineedle := StrToInt64Def(needle, 0);
    fromRange := StrToInt64Def(haystack, MAXINT);
    toRange := StrToInt64Def(toHaystack, 0);

    //now compare the ints
    if (ineedle >= fromRange) and (ineedle <= toRange) then
      result := 0
    else if (ineedle < fromRange) then
      result := -1
    else result := 1;
  except
    on e: exception do
      msgLog('Try..Except BinCompare - '+ e.Message);
  end;
end;

// JMR: copied this from http://www.swissdelphicenter.ch/torry/showcode.php?id=1916 and modified accordingly.
function BinarySearch(Strings: TStringList; aPrefix: string): Integer;
var
  First: Integer;
  Last: Integer;
  Pivot: Integer;
  Found: Boolean;
  i: integer;
begin
  Result := -1; //Initializes the Result

  if not Assigned(Strings) then // CPCLIENTS-12406
    Exit;

  try
  First  := 0; //Sets the first item of the range
  Last   := Strings.Count-1; //Sets the last item of the range
  Found  := False; //Initializes the Found flag (Not found yet)

  //If First > Last then the searched item doesn't exist
  //If the item is found the loop will stop
  while (First <= Last) and (not Found) do
  begin
    //Gets the middle of the selected range
    Pivot := (First + Last) div 2;
    //Compares the String in the middle with the searched one
    i := BinCompare(Strings[Pivot], aPrefix);
    if i  = 0 then
    begin
      Found  := True;
      Result := Pivot;
    end
    //If the Item in the middle has a bigger value than
    //the searched item, then select the first half
    else if i = -1 then
      Last := Pivot - 1
    else //select the second half
      First := Pivot + 1;
  end;
  except
    on e: exception do
      msgLog('Try..Except BinarySearch - '+ e.Message);
  end;
end;

function ReloadBinXml(aFileName: string): boolean;
var
  BinXMLObj: IXMLServerEPS_BinFileType;
  aStr: string;

  procedure CreateLists;
  var
    i: integer;
    newNetwork: TNetwork;
    bins: string;

    procedure PopulateBinRanges(bins: string; newNetwork: TNetwork);
    var
      j,k,
      len: integer;
      newPanLength: TPanLength;
      lenFound: boolean;
      bin: string255;
      s: string;
      sl: TStrings;
    begin
      // put these bin ranges into a string list, parsing by commas
      sl := TStringList.Create;
      try
        sl.CommaText := bins;
        for j := 0 to sl.Count - 1 do
        begin
          bin := sl[j];
          len := Pos(Copy(bin, 1, 1), _AlphaNumerics); // get pan length
          bin := Copy(bin, 2, length(bin)); // get rid of pan length

          lenFound := false;
          for k := 0 to newNetwork.PanLengths.Count-1 do // find this length
          begin
            if TPanLength(newNetwork.PanLengths[k]).Length = len then
            begin
              lenFound := true;
              TPanLength(newNetwork.PanLengths[k]).RangeList.Add(bin);
              break;
            end;
          end;
          if not lenFound then // add this length
          begin
            newPanLength := TPanLength.Create(len);
            newNetwork.PanLengths.Add(newPanLength);
            s := bin;                                  //JMR : leaks if a short string is added to a stringlist
            newPanLength.RangeList.Add(s);
          end;
        end;
      finally
        sl.Free;
      end;
    end;

  begin // CreateLists
    try
      DestroyLists;
      //msgLog('DEBUG ++++++++++ CreateLists ++++++++++');
      //msgLog(format('DEBUG 11758: BinXmlLookup - DebitBin Count[%d]',[BinXMLObj.DebitBin.Networks.Count]));
      if DebitList = nil then
        DebitList := TList.Create;
      for i := 0 to BinXMLObj.DebitBin.Networks.Count-1 do
      begin
        newNetwork := TNetwork.Create(BinXMLObj.DebitBin.Networks[i].Id);
        DebitList.Add(newNetwork);
        bins := BinXMLObj.DebitBin.Networks[i].BinRanges;
        PopulateBinRanges(bins, newNetwork);
      end;

      //msgLog(format('DEBUG 11758: BinXmlLookup - FsaBin Count[%d]',[BinXMLObj.FsaBin.Networks.Count]));
      if FsaList = nil then
        FsaList := TList.Create;
      for i := 0 to BinXMLObj.FsaBin.Networks.Count-1 do
      begin
        newNetwork := TNetwork.Create(BinXMLObj.FsaBin.Networks[i].Id);
        FsaList.Add(newNetwork);
        bins := BinXMLObj.FsaBin.Networks[i].BinRanges;
        PopulateBinRanges(bins, newNetwork);
      end;

      //msgLog(format('DEBUG 11758: BinXmlLookup - PrepaidList Count[%d]',[BinXMLObj.PrepaidBin.Networks.Count]));
      if PrepaidList = nil then
        PrepaidList := TList.Create;
      for i := 0 to BinXMLObj.PrepaidBin.Networks.Count-1 do
      begin
        newNetwork := TNetwork.Create(BinXMLObj.PrepaidBin.Networks[i].Id);
        PrepaidList.Add(newNetwork);
        bins := BinXMLObj.PrepaidBin.Networks[i].BinRanges;
        PopulateBinRanges(bins, newNetwork);
      end;

      //msgLog(format('BinXmlLookup - DCCList Count[%d]',[BinXMLObj.DCCBin.Networks.Count]));
      if DCCList = nil then
        DCCList := TList.Create;
      for i := 0 to BinXMLObj.DCCBin.Networks.Count-1 do
      begin
        newNetwork := TNetwork.Create(BinXMLObj.DCCBin.Networks[i].Id);
        DCCList.Add(newNetwork);
        MsgLog('BinXmlLookup: DCCList Add Network '+BinXMLObj.DCCBin.Networks[i].Id);
        bins := BinXMLObj.DCCBin.Networks[i].BinRanges;
        PopulateBinRanges(bins, newNetwork);
        aStr := '';
      end;

    except on e: exception do
      msgLog('CreateLists EXCEPTION - '+e.Message);
    end;
  end; // CreateLists

begin // ReloadBinXml
  if  fileExists(aFileName) and (ExtractFileName(aFileName) <> '')
    then aStr := 'True'
    else aStr := 'False';
  msgLog('BinXMLLookup: ReloadBinXML Filename = [' + aFilename + '] File Exists = ' + aStr);
  result := FileExists(aFilename) and (ExtractFileName(aFileName) <> '');

  if result then
    try
      if Assigned(BinXMLObj) then
        BinXMLObj := nil;
      BinXMLObj := LoadServerEPS_BinFile(aFileName);
      XmlFileName := aFileName;             // set for later, if needed
      CreateLists;
    except
      on e: exception do
      begin
        msgLog('Try..Except ReloadBinXml ' + e.message);
        result := false;
      end;
    end;
end; // ReloadBinXml

function BinLookup0Length(aBinType: TBinType; aPrefix: string; var isFound: boolean; var networkList: TStrings): boolean;
const
  sBinType: array[TBinType] of string[7] = ('Debit','FSA','Prepaid','DCC');
var
  i,j: integer;
  binList: TList;
begin
  result := true;
  isFound := false;
  try
    networkList.Clear;
    binList := nil;

    case aBinType of
      btFsa:
        binList := fsaList;
      btDebit:
        binList := debitList;
      btPrepaid:
        binList := prepaidList;
      btDCC:
        binList := DCCList;
      else
        msgLog(format('case aBinType of got invalid aBinType of %d',[ord(aBinType)]));
    end;

    if binList = nil
      then msgLog('BIN list is STILL NIL');

    if Assigned(binList) then
      for i := 0 to binList.Count-1 do // go thru each network searching for prefix
      begin
        if Assigned(binList[i]) then // CPCLIENTS-12406
        begin
          for j := 0 to TNetwork(binList[i]).PanLengths.Count-1 do // find the correct length
            if Assigned(TNetwork(binList[i]).PanLengths.Items[j]) and // CPCLIENTS-12406
              (TPanLength(TNetwork(binList[i]).PanLengths.Items[j]).Length in [0..19]) then
              if Assigned(TPanLength(TNetwork(binList[i]).PanLengths.Items[j]).RangeList) and // CPCLIENTS-12406
                (BinarySearch(TPanLength(TNetwork(binList[i]).PanLengths.Items[j]).RangeList, aPrefix) >= 0) then
              begin
                if TNetwork(binList[i]).Id <> '' then // custom mods have no network id
                  networkList.Add(TNetwork(binList[i]).Id);
                isFound := true;
                break;
              end;
        end;
      end;
  except
    on e:exception do
    begin
      msgLog('Try..Except: BinLookup0Length ' + e.message);
      result := false;
    end;
  end;
end;

// returns FALSE if problem with lookup.  Check isFound to see if actually found.
function BinLookup(aBinType: TBinType; aPrefix: string; aPanLength: integer; var isFound: boolean; var networkList: TStrings): boolean;
const
  sBinType: array[TBinType] of string[7] = ('Debit','FSA','Prepaid','DCC');
var
  i,j: integer;
  binList: TList;
begin
  result := true;
  isFound := false;
  try
    if not assigned(networkList) then
    begin
      msgLog('network list is NIL so create it');
      networkList := TStringList.Create;
    end;

    networkList.Clear;
    binList := nil;

    case aBinType of
      btFsa:
        binList := fsaList;
      btDebit:
        binList := debitList;
      btPrepaid:
        binList := prepaidList;
      btDCC:
        binList := DCCList;
      else
        msgLog(format('Invalid Parameter aBinType[%d]',[ord(aBinType)]));
    end;

    if binList = nil
      then msgLog('BIN list is STILL NIL');

    if Assigned(binList) then
      for i := 0 to binList.Count-1 do // go thru each network searching for prefix
      begin
        if Assigned(binList[i]) then // CPCLIENTS-12406
        begin
          if not assigned(TNetwork(binList[i]).PanLengths) then
            msgLog('PanLengths list is NIL');

          for j := 0 to TNetwork(binList[i]).PanLengths.Count-1 do // find the correct length
            if Assigned(TNetwork(binList[i]).PanLengths.Items[j]) and // CPCLIENTS-12406
              (TPanLength(TNetwork(binList[i]).PanLengths.Items[j]).Length in [0,aPanLength]) then
            begin
              if Assigned(TPanLength(TNetwork(binList[i]).PanLengths.Items[j]).RangeList) and // CPCLIENTS-12406
                (BinarySearch(TPanLength(TNetwork(binList[i]).PanLengths.Items[j]).RangeList, aPrefix) >= 0) then
              begin
                if TNetwork(binList[i]).Id <> '' then // custom mods have no network id
                  networkList.Add(TNetwork(binList[i]).Id);
                isFound := true;
                MsgLog('BinLookup: found prefix in ' + SBinType[aBinType] + ', network = ' + TNetwork(binList[i]).Id);
                MsgDebug('BinLookup: found prefix [' + aPrefix + '] in ' + SBinType[aBinType] + ', network = ' + TNetwork(binList[i]).Id);
              end;
  //            break;  don't break here, since it could be a wildcard.
            end;
        end;
      end;
  except
    on e:exception do
    begin
      msgLog('Try..Except: BinLookUp ' + e.message);
      result := false;
    end;
  end;
end;


initialization
{$IFDEF GUIJR}
  xLog('BinXmlLookup initialization END');
finalization
  xLog('BinXmlLookup finalization END');
  DestroyLists;
  xLog('BinXmlLookup finalization END');
{$ELSE}
  ExtendedLog('BinXmlLookup initialization');
finalization
  ExtendedLog('BinXmlLookup finalization END', procedure
    begin
    DestroyLists;
    end
    );
{$ENDIF GUIJR}
end.

