// (c) MTXEPS, Inc. 1988-2008
unit MTX_Classes;
(******************************************************************************
 *
 *
 *
 ******************************************************************************)
interface

uses
  Classes,
  SyncObjs,
  MTX_Types,
  EpsTrace;

const
  MTX_DEFAULT_LOCK_NAME              = '';
  MTX_DEFAULT_LOGLEVEL: TMTXLogLevel = llNormal;

  {$IFDEF PCI_LOG_NONCOMPLIANCE}  // include this compiler directive IF you want non-PCI logging
  EnforcePCI = false;
  {$ELSE}
  EnforcePCI = true;   // the normal default condition
  {$ENDIF}

type
(******************************************************************************
 * Base Classes
 ******************************************************************************)
  TMTXObject = class(TInterfacedObject, IMTXObject)
  private
    FLock: TSynchroObject;
    FLogLevel: TMTXLogLevel;
  protected
    function CreateLock(const ALockName: TMTXString = MTX_DEFAULT_LOCK_NAME): TSynchroObject; virtual;
    procedure AcquireLock; virtual;
    procedure ReleaseLock; virtual;
    procedure Log(const AMsg: string); overload;
    procedure Log(const AFmt: string; AParams: array of const); overload;
    function GetLogLevel: TMTXLogLevel;
    procedure SetLogLevel(const ALogLevel: TMTXLogLevel);
    property LogLevel: TMTXLogLevel read FLogLevel write SetLogLevel;
  public
    procedure AfterConstruction; override;
  end;

  TMTXThread = class(TThread, IMTXObject, IMTXThread)
  private
    FRefCount: TMTXInteger;
    FLock: TSynchroObject;
  protected
    // IInterface
    function QueryInterface(const IID: TGUID; out Obj): HResult; stdcall;
    function _AddRef: Integer; stdcall;
    function _Release: Integer; stdcall;
    // IMTXObject
    function CreateLock(const ALockName: TMTXString = MTX_DEFAULT_LOCK_NAME): TSynchroObject; virtual;
    procedure AcquireLock; virtual;
    procedure ReleaseLock; virtual;
    procedure Log(const AMsg: string); overload;
    procedure Log(const AFmt: string; AParams: array of const); overload;
    // IMTXThread
    // procedure Suspend;
    // procedure Resume;
    // procedure Terminate;
    function IsSuspended: Boolean;
    function IsTerminared: Boolean;
  public
    procedure AfterConstruction; override;
  end;

implementation

uses
  FinalizationLog,
  {$IFNDEF LINUX}                                                               
  Windows,
  {$ENDIF LINUX}
  DLLTypes,
  SysUtils;

{ TMTXObject }

procedure TMTXObject.AcquireLock;
begin
  if FLock = nil then FLock:= CreateLock;
  Assert(FLock <> nil);
  FLock.Acquire;
end;                                 

procedure TMTXObject.AfterConstruction;
begin
  inherited;
  FLogLevel:= MTX_DEFAULT_LOGLEVEL;
end;

function TMTXObject.CreateLock(const ALockName: TMTXString): TSynchroObject;
begin
  Result:= TCriticalSection.Create;
end;

procedure TMTXObject.Log(const AMsg: string);
var
  TraceStr: TMTXString;
begin
  if FLogLevel <> llNone then
  begin
    TraceStr:= {$IFNDEF LINUX}IntToHex(GetCurrentThreadId, 8) + ' ' + {$ENDIF}AMsg; 
    ShowTrace(idVT, TraceStr);
  end;
end;

function TMTXObject.GetLogLevel: TMTXLogLevel;
begin
  Result:= FLogLevel;
end;

procedure TMTXObject.Log(const AFmt: string; AParams: array of const);
begin
  if copy(AFmt,1,3) = 'PCI' then   // if log line starts with PCI, then we process it more intelligently
    if EnforcePCI
      then {Log('PCI BLOCK') log nothing}
      else Log(Format(copy(AFmt,5,length(AFmt)),AParams))  // start past 'PCI '
  else
    Log(format(AFmt,AParams));
end;

procedure TMTXObject.ReleaseLock;
begin
  if FLock <> nil then FLock.Release;
end;

procedure TMTXObject.SetLogLevel(const ALogLevel: TMTXLogLevel);
begin
  FLogLevel:= ALogLevel;
end;

{ TMTXThread }

procedure TMTXThread.AcquireLock;
begin
  if FLock = nil then FLock:= CreateLock;
  Assert(FLock <> nil);
  FLock.Acquire;
end;

procedure TMTXThread.AfterConstruction;
begin
  inherited;
  FreeOnTerminate:= True;
end;

function TMTXThread.CreateLock(const ALockName: TMTXString): TSynchroObject;
begin
  Result:= TCriticalSection.Create;
end;

procedure TMTXThread.Log(const AMsg: string);
begin
  ShowTrace(idVT, AMsg);
end;

function TMTXThread.IsSuspended: Boolean;
begin
  Result:= Self.Suspended;
end;

function TMTXThread.IsTerminared: Boolean;
begin
  Result:= Self.Terminated;
end;

procedure TMTXThread.Log(const AFmt: string; AParams: array of const);
begin
  Log(Format(AFmt, AParams));
end;

function TMTXThread.QueryInterface(const IID: TGUID; out Obj): HResult;
begin
  if GetInterface(IID, Obj) then Result:= S_OK else Result:= E_NOINTERFACE;
end;

procedure TMTXThread.ReleaseLock;
begin
  if FLock <> nil then FLock.Release;
end;

function TMTXThread._AddRef: Integer;
begin
  Inc(FRefCount);
  Result:= FRefCount;
end;

function TMTXThread._Release: Integer;
begin
  if FRefCount > 0 then Dec(FRefCount) else Terminate;
  Result:= FRefCount;
end;

initialization
  ExtendedLog('MTX_Classes Initialization');
finalization
  ExtendedLog('MTX_Classes Finalization');

end.
