// (c) MTXEPS, Inc. 1988-2008
unit StoreConfigurationsXML;
(*
Version History
--------------------------------------------------------------------------------
1.0     DEV-4656 Convert EFT files to XML - Phase 1 & 2
2.0     DEV-5885 ConnectPay - GUI, update program, RPT
        ConnectPayEnabled       string /YN
        ConnectPayPrefix        string /10 digit numeric chars
*)


interface

uses
  FinalizationLog,
  Classes, SysUtils, IdGlobal,
  UXMLCommon;

type

{ Forward Decls }

  TXMLStoreConfigurationsType = class;
  TXMLProcessingOptionsType = class;
  TXMLPrintSettingsType = class;
  TXMLFontStylesType = class;
  TXMLReceiptTextsType = class;                                                 { YHJ-515 }
  TXMLReceiptTextType = class;                                                 
  TXMLHostsType = class;
  TXMLHostType = class;
  TXMLDialBackupConfigurationType = class; // YHJ-843

{ TXMLStoreConfigurationsType }
  TXMLStoreConfigurationsType = class
  private
    FVersion: string;
    FLastModified: string;
    FProcessingOptions: TXMLProcessingOptionsType;
    FPrintSettings: TXMLPrintSettingsType;
    FReceiptTexts: TXMLReceiptTextsType;                                        { YHJ-515 }
    FHosts: TXMLHostsType;
    FDialBackupConfiguration: TXMLDialBackupConfigurationType; // YHJ-843
    function GetVersion: string;
    function GetLastModified: string;
    function GetProcessingOptions: TXMLProcessingOptionsType;
    function GetPrintSettings: TXMLPrintSettingsType;
    function GetReceiptTexts: TXMLReceiptTextsType;                             { YHJ-515 }
    function GetHosts: TXMLHostsType;
    procedure SetVersion(Value: string);
    procedure SetLastModified(Value: string);
    procedure CreateNodes(Root: TXMLParserNode); // DEV-12765
  public
    HostXMLHash: string; // DEV-12765
    property Version: string read GetVersion write SetVersion;
    property LastModified: string read GetLastModified write SetLastModified;
    property ProcessingOptions: TXMLProcessingOptionsType read GetProcessingOptions;
    property PrintSettings: TXMLPrintSettingsType read GetPrintSettings;
    property ReceiptTexts: TXMLReceiptTextsType read GetReceiptTexts;           { YHJ-515 }
    property Hosts: TXMLHostsType read GetHosts;
    property DialBackupConfiguration: TXMLDialBackupConfigurationType read FDialBackupConfiguration; // YHJ-843
    constructor Create;
    destructor Destroy; override;
    function LoadFromFile(aFileName: string): boolean;
    function SaveToFile(aFileName: string): boolean;
    function GetHostXMLHash: string; // DEV-12765
  end;

{ TXMLProcessingOptionsType }

  TXMLProcessingOptionsType = class
  private
    FPasswordExpirationDays: Integer;
    FTruncateCardDataAtEOD: string;                                             { YHJ-517 }
    FFtpStoreNumber: string; // YHJ-694
    FEnableTransactionAdjustments : boolean;
    function GetPasswordExpirationDays: Integer;
    procedure SetPasswordExpirationDays(Value: Integer);
  public
    DoNotRetainCustomerName: string;
    property PasswordExpirationDays: Integer read GetPasswordExpirationDays write SetPasswordExpirationDays;
    property TruncateCardDataAtEOD: string read FTruncateCardDataAtEOD write FTruncateCardDataAtEOD; { YHJ-517 }
    property FTPStoreNumber: string read FFTPStoreNumber write FFtpStoreNumber; // YHJ-694
    property EnableTransactionAdjustments : boolean read  FEnableTransactionAdjustments write FEnableTransactionAdjustments;
  end;

{ TXMLPrintSettingsType }

  TXMLPrintSettingsType = class
  private
    FFontName: string;
    FFontSize: string;
    FFontStyles: TXMLFontStylesType;
    FOrientation: string;
    FNumberOfCopies: string;
    { TXMLPrintSettingsType }
    function GetFontName: string;
    function GetFontSize: string;
    function GetFontStyles: TXMLFontStylesType;
    function GetOrientation: string;
    function GetNumberOfCopies: string;
    procedure SetFontName(Value: string);
    procedure SetFontSize(Value: string);
    procedure SetOrientation(Value: string);
    procedure SetNumberOfCopies(Value: string);
  public
    property FontName: string read GetFontName write SetFontName;
    property FontSize: string read GetFontSize write SetFontSize;
    property FontStyles: TXMLFontStylesType read GetFontStyles;
    property Orientation: string read GetOrientation write SetOrientation;
    property NumberOfCopies: string read GetNumberOfCopies write SetNumberOfCopies;
    constructor Create;
    destructor Destroy; override;
  end;

{ TXMLFontStylesType }

  TXMLFontStylesType = class
  private
    FBold: string;
    FItalic: string;
    FUnderline: string;
    FStrikeOut: string;
    { TXMLFontStylesType }
    function GetBold: string;
    function GetItalic: string;
    function GetUnderline: string;
    function GetStrikeOut: string;
    procedure SetBold(Value: string);
    procedure SetItalic(Value: string);
    procedure SetUnderline(Value: string);
    procedure SetStrikeOut(Value: string);
  public
    property Bold: string read GetBold write SetBold;
    property Italic: string read GetItalic write SetItalic;
    property Underline: string read GetUnderline write SetUnderline;
    property StrikeOut: string read GetStrikeOut write SetStrikeOut;
  end;

{ TXMLReceiptTextsType }                                                        { YHJ-515 }

  TXMLReceiptTextsType = class(TCollection)
  private
    {.$HINTS OFF}
    FReceiptText: TXMLReceiptTextType;
    {.$HINTS ON}
    function GetReceiptText(Index: Integer): TXMLReceiptTextType;
  public
    property ReceiptText[Index: Integer]: TXMLReceiptTextType read GetReceiptText; default;
    function Add: TXMLReceiptTextType;
  end;

{ TXMLReceiptTextType }

  TXMLReceiptTextType = class(TCollectionItem)
  private
    FStoreNumber: string;                                                       { YHJ-515 }
    FMerchantLanguage: string;
    FReceiptHeaderLine1: string;
    FReceiptHeaderLine2: string;
    FReceiptHeaderLine3: string;
    FReceiptHeaderLine4: string;
    FReceiptFooterLine1: string;
    FReceiptFooterLine2: string;
    FCheckDepositLegend: string;
    FCheckDepositBankName: string;
    FCheckDepositAccountNumber: string;
    FHospitalityIncludeTip: boolean;
    { TXMLReceiptTextType }
    function GetStoreNumber: string;                                            { YHJ-515 }
    function GetReceiptHeaderLine1: string;
    function GetReceiptHeaderLine2: string;
    function GetReceiptHeaderLine3: string;
    function GetReceiptHeaderLine4: string;
    function GetReceiptFooterLine1: string;
    function GetReceiptFooterLine2: string;
    function GetCheckDepositLegend: string;
    function GetCheckDepositBankName: string;
    function GetCheckDepositAccountNumber: string;
    function GetHospitalityIncludeTip: boolean;
    procedure SetStoreNumber(Value: string);                                    { YHJ-515 }
    procedure SetReceiptHeaderLine1(Value: string);
    procedure SetReceiptHeaderLine2(Value: string);
    procedure SetReceiptHeaderLine3(Value: string);
    procedure SetReceiptHeaderLine4(Value: string);
    procedure SetReceiptFooterLine1(Value: string);
    procedure SetReceiptFooterLine2(Value: string);
    procedure SetCheckDepositLegend(Value: string);
    procedure SetCheckDepositBankName(Value: string);
    procedure SetCheckDepositAccountNumber(Value: string);
    procedure SetHospitalityIncludeTip(Value: boolean);
  public
    property StoreNumber: string read GetStoreNumber write SetStoreNumber;      { YHJ-515 }
    property MerchantLanguage: string read FMerchantLanguage write FMerchantLanguage;      
    property ReceiptHeaderLine1: string read GetReceiptHeaderLine1 write SetReceiptHeaderLine1;
    property ReceiptHeaderLine2: string read GetReceiptHeaderLine2 write SetReceiptHeaderLine2;
    property ReceiptHeaderLine3: string read GetReceiptHeaderLine3 write SetReceiptHeaderLine3;
    property ReceiptHeaderLine4: string read GetReceiptHeaderLine4 write SetReceiptHeaderLine4;
    property ReceiptFooterLine1: string read GetReceiptFooterLine1 write SetReceiptFooterLine1;
    property ReceiptFooterLine2: string read GetReceiptFooterLine2 write SetReceiptFooterLine2;
    property CheckDepositLegend: string read GetCheckDepositLegend write SetCheckDepositLegend;
    property CheckDepositBankName: string read GetCheckDepositBankName write SetCheckDepositBankName;
    property CheckDepositAccountNumber: string read GetCheckDepositAccountNumber write SetCheckDepositAccountNumber;
    property HospitalityIncludeTip: boolean read GetHospitalityIncludeTip write SetHospitalityIncludeTip;
  end;

{ TXMLHostsType }

  TXMLHostsType = class(TCollection)
  private
    {.$HINTS OFF}
    FHost: TXMLHostType;
    {.$HINTS ON}
    { TXMLHostsType }
    function GetHost(Index: Integer): TXMLHostType;
  public
    property Host[Index: Integer]: TXMLHostType read GetHost; default;
    function Add: TXMLHostType;
  end;

{ TXMLOfflineProcessingType }
  TXMLOfflineProcessingType = class // DEV-11373
  public
    UseOtherHost: string;
  end;

  // TXMLDialBackupConfigurationType
  TXMLDialBackupConfigurationType = class // YHJ-843
  public
    IPAddress: string;
    Port: integer;
    IdleTimeout: integer;
    RAS: string;
    constructor Create;
  end;

{ TXMLHostType }

  TXMLHostType = class(TCollectionItem)
  private
    FPrefix: string;
    FSuffix: string;
    //FSeqNo: Integer;
    FRetryCounter: string;
    FOfflineProcessing: string;
    FSimulatedDelay: string;
    FWorkingKey: string;
    FUseEBCDIC: string;
    FMerchantNum: string;
    FPassword: string;
    FChkAuthType: string;
    FChkCode: string;
    FChkDefaultStateCode: string;
    FVerifyDebitPrefixOnline: string;
    FVerifyDebitPrefixOffline: string;
    FLXHostSlotNumbers: string;
    FStoreNumber: string;
    FTerminalID: string;
    FHostManagmentInfo: string;
    FMerchantType: string;
    FHostID: string;
    FMerchantName: string;
    FMerchantAddress: string;
    FMerchantCity: string;
    FMerchantState: string;
    FMerchantZipcode: string;
    FMerchantCountry: string;
    FCurrencyCode: string;
    FNumericStateCode: string;
    FNumericCountryCode: string;
    FGMTOffset: string;
    FSendCashierToHost: string;
    FTraceMsgsToLog: string;
    FResubmitOfflineForwards: string;
    FTerminalType: string;
    FMerchantNumber: string;
    FTerminalNumber: string;
    FPrimaryHostPort: string;
    FPrimaryHostIPAddress: string;
    FBackupHostPort: string;
    FBackupHostIPAddress: string;
    FKeepCheckHistory: string;
    FPrimaryGCHostIP: string;
    FBackupGCHostIP: string;
    FLXHostTimout: string;
    FTransactionAllowed: string;
    FOnlineTimout: string;
    FOfflineForwardDelay: string;
    FOfflineForwardSecondsBetweenForwards: string;
    FTORCycleDelay: string;
    FOfflineForwardCycleDelay: string;
    FSendHealthMessage: string;
    FCertegyPayroll: string;
    FHealthMessageInterval: string;
    FTakeWICChecksLocally: string;
    FLXHostSlotNumAndTimout: string;
    FLXHostOnlineStartingSlotNum: string;
    FPhoneCardMagStripe: string;
    FPartialBINDownload: string;
    FPBTHostMerchantID: string;
    FFTPUserHName: string;
    FFTPIPAddress: string;
    FFTPPassword: string;
    FFTPFileName: string;
    FAddressLines: string;
    FProcessingMode: string;
    FSendSwipedDLInfo: string;
    FPrimaryLocalPort: string;
    FBackupLocalPort: string;
    FLocalIPforTerminalConnect: string;
    FFTPInterval: string;
    FSettlementReport: string;
    FForceTenderOffline: string;
    FReceiptFileTransferType: string;
    FReceiptHostPrimaryIPAddress: string;
    FReceiptHostPrimaryFTPPort: string;
    FReceiptHostBackupIPAddress: string;
    FReceiptHostBackupFTPPort: string;
    FReceiptHostUserID: string;
    FReceiptHostPassword: string;
    FReceiptHostFileName: string;
    FReceiptHostFTPPath: string;
    FTruncateAcctNo: string;
    FSendRawMICR: string;
    FHostSuspendCt: string;
    FHostFatalCt: string;
    FHostResumeCt: string;
    FHostOnTOxxxx: string;
    FHost_Comm_NRZI: string;
    FHost_Main_Session: string;
    FHost_TOR_Session: string;
    FHost_Offl_Session: string;
    FHostPort: string;
    FHostBits: string;
    FHostParity: string;
    FHostStop: string;
    FHostIRQ: string;
    FHostMgtInfoData: string;
    FHostTORLate: string;
    FHostTORTimeout: string;
    FHostTORNoT2: string;
    FHostRetryPhone1: string;
    FHostRetryPhone2: string;
    FHostModemInit: string;
    FHostInitMain: string;
    FTORInitSelfName: string;
    FOfflineInitSelfName: string;
    FHostConfigNum: string;
    FHostSecondScrName: string;
    FFTP_BINFileFromHost: string;
    FHostDivision: string;
    FHostPort2: string;
    FHostBits2: string;
    FHostParity2: string;
    FHostStop2: string;
    FHostIRQ2: string;
    FHostPort3: string;
    FHostBits3: string;
    FHostParity3: string;
    FHostStop3: string;
    FHostIRQ3: string;
    FHostSetForDayLight: string;
    FHostTruncateAcctNo: string;
    FHostPlatformID: string;
    FHostClearingCode: string;
    FHostMerchBIN: string;
    FHostMerchICA: string;
    FHostIPLocalPort3: string;
    FHostLastBINFtpDate: string;
    FHostProgramId: string;
    FHostFTPDir: string;
    FHostERCUse: string;
    FFreqShopReqOfflineDebit: string;                                           { YHJ-503 }
    FUseSSL: string;                                                            { YHJ-526 }
    FConnectPayEnabled: string;                                                 { YHJ-508 }
    FConnectPayPrefix: string;                                                  { YHJ-508 }
    FHealthStatusProbationInterval: string;
    FHealthStatusNotOkInterval: string;
    FProxyEnabled: string;
    FProxyAddress: string;
    FDynamicCurrConv: string; // TFS-9730

    { TXMLHostType }
    function GetPrefix_: string;
    function GetSuffix: string;
    //function GetSeqNo: Integer;
    function GetRetryCounter: string;
    function GetOfflineProcessing: string;
    function GetSimulatedDelay: string;
    function GetWorkingKey: string;
    function GetUseEBCDIC: string;
    function GetMerchantNum: string;
    function GetPassword: string;
    function GetChkAuthType: string;
    function GetChkCode: string;
    function GetChkDefaultStateCode: string;
    function GetVerifyDebitPrefixOnline: string;
    function GetVerifyDebitPrefixOffline: string;
    function GetLXHostSlotNumbers: string;
    function GetStoreNumber: string;
    function GetTerminalID: string;
    function GetHostManagmentInfo: string;
    function GetMerchantType: string;
    function GetHostID: string;
    function GetMerchantName: string;
    function GetMerchantAddress: string;
    function GetMerchantCity: string;
    function GetMerchantState: string;
    function GetMerchantZipcode: string;
    function GetMerchantCountry: string;
    function GetCurrencyCode: string;
    function GetNumericStateCode: string;
    function GetNumericCountryCode: string;
    function GetGMTOffset: string;
    function GetSendCashierToHost: string;
    function GetTraceMsgsToLog: string;
    function GetResubmitOfflineForwards: string;
    function GetTerminalType: string;
    function GetMerchantNumber: string;
    function GetTerminalNumber: string;
    function GetPrimaryHostPort: string;
    function GetPrimaryHostIPAddress: string;
    function GetBackupHostPort: string;
    function GetBackupHostIPAddress: string;
    function GetKeepCheckHistory: string;
    function GetPrimaryGCHostIP: string;
    function GetBackupGCHostIP: string;
    function GetLXHostTimout: string;
    function GetTransactionAllowed: string;
    function GetOnlineTimout: string;
    function GetOfflineForwardDelay: string;
    function GetOfflineForwardSecondsBetweenForwards: string;
    function GetTORCycleDelay: string;
    function GetOfflineForwardCycleDelay: string;
    function GetSendHealthMessage: string;
    function GetHealthMessageInterval: string;
    function GetTakeWICChecksLocally: string;
    function GetLXHostSlotNumAndTimout: string;
    function GetLXHostOnlineStartingSlotNum: string;
    function GetPhoneCardMagStripe: string;
    function GetPartialBINDownload: string;
    function GetPBTHostMerchantID: string;
    function GetFTPUserHName: string;
    function GetFTPIPAddress: string;
    function GetFTPPassword: string;
    function GetFTPFileName: string;
    function GetAddressLines: string;
    function GetProcessingMode: string;
    function GetSendSwipedDLInfo: string;
    function GetPrimaryLocalPort: string;
    function GetBackupLocalPort: string;
    function GetLocalIPforTerminalConnect: string;
    function GetFTPInterval: string;
    function GetSettlementReport: string;
    function GetForceTenderOffline: string;
    function GetReceiptFileTransferType: string;
    function GetReceiptHostPrimaryIPAddress: string;
    function GetReceiptHostPrimaryFTPPort: string;
    function GetReceiptHostBackupIPAddress: string;
    function GetReceiptHostBackupFTPPort: string;
    function GetReceiptHostUserID: string;
    function GetReceiptHostPassword: string;
    function GetReceiptHostFileName: string;
    function GetReceiptHostFTPPath: string;
    function GetTruncateAcctNo: string;
    function GetSendRawMICR: string;
    function GetHostSuspendCt: string;
    function GetHostFatalCt: string;
    function GetHostResumeCt: string;
    function GetHostOnTOxxxx: string;
    function GetHost_Comm_NRZI: string;
    function GetHost_Main_Session: string;
    function GetHost_TOR_Session: string;
    function GetHost_Offl_Session: string;
    function GetHostPort: string;
    function GetHostBits: string;
    function GetHostParity: string;
    function GetHostStop: string;
    function GetHostIRQ: string;
    function GetHostMgtInfoData: string;
    function GetHostTORLate: string;
    function GetHostTORTimeout: string;
    function GetHostTORNoT2: string;
    function GetHostRetryPhone1: string;
    function GetHostRetryPhone2: string;
    function GetHostModemInit: string;
    function GetHostInitMain: string;
    function GetTORInitSelfName: string;
    function GetOfflineInitSelfName: string;
    function GetHostConfigNum: string;
    function GetHostSecondScrName: string;
    function GetFTP_BINFileFromHost: string;
    function GetHostDivision: string;
    function GetHostPort2: string;
    function GetHostBits2: string;
    function GetHostParity2: string;
    function GetHostStop2: string;
    function GetHostIRQ2: string;
    function GetHostPort3: string;
    function GetHostBits3: string;
    function GetHostParity3: string;
    function GetHostStop3: string;
    function GetHostIRQ3: string;
    function GetHostSetForDayLight: string;
    function GetHostTruncateAcctNo: string;
    function GetHostPlatformID: string;
    function GetHostClearingCode: string;
    function GetHostMerchBIN: string;
    function GetHostMerchICA: string;
    function GetHostIPLocalPort3: string;
    function GetHostLastBINFtpDate: string;
    function GetHostProgramId: string;
    function GetHostFTPDir: string;
    function GetHostERCUse: string;
    function GetFreqShopReqOfflineDebit: string;                                { YHJ-503 }
    function GetDynamicCurrConv: string; // TFS-9730
    procedure SetPrefix(Value: string);
    procedure SetSuffix(Value: string);
    //procedure SetSeqNo(Value: Integer);
    procedure SetRetryCounter(Value: string);
    procedure SetOfflineProcessing(Value: string);
    procedure SetSimulatedDelay(Value: string);
    procedure SetWorkingKey(Value: string);
    procedure SetUseEBCDIC(Value: string);
    procedure SetMerchantNum(Value: string);
    procedure SetPassword(Value: string);
    procedure SetChkAuthType(Value: string);
    procedure SetChkCode(Value: string);
    procedure SetChkDefaultStateCode(Value: string);
    procedure SetVerifyDebitPrefixOnline(Value: string);
    procedure SetVerifyDebitPrefixOffline(Value: string);
    procedure SetLXHostSlotNumbers(Value: string);
    procedure SetStoreNumber(Value: string);
    procedure SetTerminalID(Value: string);
    procedure SetHostManagmentInfo(Value: string);
    procedure SetMerchantType(Value: string);
    procedure SetHostID(Value: string);
    procedure SetMerchantName(Value: string);
    procedure SetMerchantAddress(Value: string);
    procedure SetMerchantCity(Value: string);
    procedure SetMerchantState(Value: string);
    procedure SetMerchantZipcode(Value: string);
    procedure SetMerchantCountry(Value: string);
    procedure SetCurrencyCode(Value: string);
    procedure SetNumericStateCode(Value: string);
    procedure SetNumericCountryCode(Value: string);
    procedure SetGMTOffset(Value: string);
    procedure SetSendCashierToHost(Value: string);
    procedure SetTraceMsgsToLog(Value: string);
    procedure SetResubmitOfflineForwards(Value: string);
    procedure SetTerminalType(Value: string);
    procedure SetMerchantNumber(Value: string);
    procedure SetTerminalNumber(Value: string);
    procedure SetPrimaryHostPort(Value: string);
    procedure SetPrimaryHostIPAddress(Value: string);
    procedure SetBackupHostPort(Value: string);
    procedure SetBackupHostIPAddress(Value: string);
    procedure SetKeepCheckHistory(Value: string);
    procedure SetPrimaryGCHostIP(Value: string);
    procedure SetBackupGCHostIP(Value: string);
    procedure SetLXHostTimout(Value: string);
    procedure SetTransactionAllowed(Value: string);
    procedure SetOnlineTimout(Value: string);
    procedure SetOfflineForwardDelay(Value: string);
    procedure SetOfflineForwardSecondsBetweenForwards(Value: string);
    procedure SetTORCycleDelay(Value: string);
    procedure SetOfflineForwardCycleDelay(Value: string);
    procedure SetSendHealthMessage(Value: string);
    procedure SetHealthMessageInterval(Value: string);
    procedure SetTakeWICChecksLocally(Value: string);
    procedure SetLXHostSlotNumAndTimout(Value: string);
    procedure SetLXHostOnlineStartingSlotNum(Value: string);
    procedure SetPhoneCardMagStripe(Value: string);
    procedure SetPartialBINDownload(Value: string);
    procedure SetPBTHostMerchantID(Value: string);
    procedure SetFTPUserHName(Value: string);
    procedure SetFTPIPAddress(Value: string);
    procedure SetFTPPassword(Value: string);
    procedure SetFTPFileName(Value: string);
    procedure SetAddressLines(Value: string);
    procedure SetProcessingMode(Value: string);
    procedure SetSendSwipedDLInfo(Value: string);
    procedure SetPrimaryLocalPort(Value: string);
    procedure SetBackupLocalPort(Value: string);
    procedure SetLocalIPforTerminalConnect(Value: string);
    procedure SetFTPInterval(Value: string);
    procedure SetSettlementReport(Value: string);
    procedure SetForceTenderOffline(Value: string);
    procedure SetReceiptFileTransferType(Value: string);
    procedure SetReceiptHostPrimaryIPAddress(Value: string);
    procedure SetReceiptHostPrimaryFTPPort(Value: string);
    procedure SetReceiptHostBackupIPAddress(Value: string);
    procedure SetReceiptHostBackupFTPPort(Value: string);
    procedure SetReceiptHostUserID(Value: string);
    procedure SetReceiptHostPassword(Value: string);
    procedure SetReceiptHostFileName(Value: string);
    procedure SetReceiptHostFTPPath(Value: string);
    procedure SetTruncateAcctNo(Value: string);
    procedure SetSendRawMICR(Value: string);
    procedure SetHostSuspendCt(Value: string);
    procedure SetHostFatalCt(Value: string);
    procedure SetHostResumeCt(Value: string);
    procedure SetHostOnTOxxxx(Value: string);
    procedure SetHost_Comm_NRZI(Value: string);
    procedure SetHost_Main_Session(Value: string);
    procedure SetHost_TOR_Session(Value: string);
    procedure SetHost_Offl_Session(Value: string);
    procedure SetHostPort(Value: string);
    procedure SetHostBits(Value: string);
    procedure SetHostParity(Value: string);
    procedure SetHostStop(Value: string);
    procedure SetHostIRQ(Value: string);
    procedure SetHostMgtInfoData(Value: string);
    procedure SetHostTORLate(Value: string);
    procedure SetHostTORTimeout(Value: string);
    procedure SetHostTORNoT2(Value: string);
    procedure SetHostRetryPhone1(Value: string);
    procedure SetHostRetryPhone2(Value: string);
    procedure SetHostModemInit(Value: string);
    procedure SetHostInitMain(Value: string);
    procedure SetTORInitSelfName(Value: string);
    procedure SetOfflineInitSelfName(Value: string);
    procedure SetHostConfigNum(Value: string);
    procedure SetHostSecondScrName(Value: string);
    procedure SetFTP_BINFileFromHost(Value: string);
    procedure SetHostDivision(Value: string);
    procedure SetHostPort2(Value: string);
    procedure SetHostBits2(Value: string);
    procedure SetHostParity2(Value: string);
    procedure SetHostStop2(Value: string);
    procedure SetHostIRQ2(Value: string);
    procedure SetHostPort3(Value: string);
    procedure SetHostBits3(Value: string);
    procedure SetHostParity3(Value: string);
    procedure SetHostStop3(Value: string);
    procedure SetHostIRQ3(Value: string);
    procedure SetHostSetForDayLight(Value: string);
    procedure SetHostTruncateAcctNo(Value: string);
    procedure SetHostPlatformID(Value: string);
    procedure SetHostClearingCode(Value: string);
    procedure SetHostMerchBIN(Value: string);
    procedure SetHostMerchICA(Value: string);
    procedure SetHostIPLocalPort3(Value: string);
    procedure SetHostLastBINFtpDate(Value: string);
    procedure SetHostProgramId(Value: string);
    procedure SetHostFTPDir(Value: string);
    procedure SetHostERCUse(Value: string);
    procedure SetFreqShopReqOfflineDebit(value: string); // YHJ-503
    procedure SetDynamicCurrConv(const Value: string); // TFS-9730
  public
    Use3DES: string; // DEV-7678
    ReceivingInstituionId: string; // DEV-10155
    ForwardAllChecksOnline: string;
    ForwardOtherChecks: string; // DEV-11323 <
    ForwardWICChecks: string;
    MerchantPhoneNumber: string; // >
    UseOtherHostForOfflineProcessing: boolean; // DEV-12505 <
    NumOfDaysToKeepOffline: string;
    NetworkRoutingValue: string; // DEV-12382
    FluCareCardCode: string;
    FluCareUPC: string;
    FloodStartTime: string;
    FloodEndTime: string;
    FloodOfflineForwardSecondsBetweenForwards: string; // DEV-12505 >
    OfflineForwardSecondsBetweenResubmits: string; //  DEV-12942
    ForceOfflineByCardCode: TStringList; // DEV-18409
    CurrencyAbbreviation: string; 
    //DoNotForwardGiftCardForce: string; // DEV-18499 

    property Prefix: string read GetPrefix_ write SetPrefix;
    property Suffix: string read GetSuffix write SetSuffix;
    //property SeqNo: Integer read GetSeqNo write SetSeqNo;
    property RetryCounter: string read GetRetryCounter write SetRetryCounter;
    property OfflineProcessing: string read GetOfflineProcessing write SetOfflineProcessing; 
    property SimulatedDelay: string read GetSimulatedDelay write SetSimulatedDelay;
    property WorkingKey: string read GetWorkingKey write SetWorkingKey;
    property UseEBCDIC: string read GetUseEBCDIC write SetUseEBCDIC;
    property MerchantNum: string read GetMerchantNum write SetMerchantNum;
    property Password: string read GetPassword write SetPassword;
    property ChkAuthType: string read GetChkAuthType write SetChkAuthType;
    property ChkCode: string read GetChkCode write SetChkCode;
    property ChkDefaultStateCode: string read GetChkDefaultStateCode write SetChkDefaultStateCode;
    property VerifyDebitPrefixOnline: string read GetVerifyDebitPrefixOnline write SetVerifyDebitPrefixOnline;
    property VerifyDebitPrefixOffline: string read GetVerifyDebitPrefixOffline write SetVerifyDebitPrefixOffline;
    property LXHostSlotNumbers: string read GetLXHostSlotNumbers write SetLXHostSlotNumbers;
    property StoreNumber: string read GetStoreNumber write SetStoreNumber;
    property TerminalID: string read GetTerminalID write SetTerminalID;
    property HostManagmentInfo: string read GetHostManagmentInfo write SetHostManagmentInfo;
    property MerchantType: string read GetMerchantType write SetMerchantType;
    property HostID: string read GetHostID write SetHostID;
    property MerchantName: string read GetMerchantName write SetMerchantName;
    property MerchantAddress: string read GetMerchantAddress write SetMerchantAddress;
    property MerchantCity: string read GetMerchantCity write SetMerchantCity;
    property MerchantState: string read GetMerchantState write SetMerchantState;
    property MerchantZipcode: string read GetMerchantZipcode write SetMerchantZipcode;
    property MerchantCountry: string read GetMerchantCountry write SetMerchantCountry;
    property CurrencyCode: string read GetCurrencyCode write SetCurrencyCode;
    property NumericStateCode: string read GetNumericStateCode write SetNumericStateCode;
    property NumericCountryCode: string read GetNumericCountryCode write SetNumericCountryCode;
    property GMTOffset: string read GetGMTOffset write SetGMTOffset;
    property SendCashierToHost: string read GetSendCashierToHost write SetSendCashierToHost;
    property TraceMsgsToLog: string read GetTraceMsgsToLog write SetTraceMsgsToLog;
    property ResubmitOfflineForwards: string read GetResubmitOfflineForwards write SetResubmitOfflineForwards;
    property TerminalType: string read GetTerminalType write SetTerminalType;
    property MerchantNumber: string read GetMerchantNumber write SetMerchantNumber;
    property TerminalNumber: string read GetTerminalNumber write SetTerminalNumber;
    property PrimaryHostPort: string read GetPrimaryHostPort write SetPrimaryHostPort;
    property PrimaryHostIPAddress: string read GetPrimaryHostIPAddress write SetPrimaryHostIPAddress;
    property BackupHostPort: string read GetBackupHostPort write SetBackupHostPort;
    property BackupHostIPAddress: string read GetBackupHostIPAddress write SetBackupHostIPAddress;
    property KeepCheckHistory: string read GetKeepCheckHistory write SetKeepCheckHistory;
    property PrimaryGCHostIP: string read GetPrimaryGCHostIP write SetPrimaryGCHostIP;
    property BackupGCHostIP: string read GetBackupGCHostIP write SetBackupGCHostIP;
    property LXHostTimout: string read GetLXHostTimout write SetLXHostTimout;
    property TransactionAllowed: string read GetTransactionAllowed write SetTransactionAllowed;
    property OnlineTimout: string read GetOnlineTimout write SetOnlineTimout;
    property OfflineForwardDelay: string read GetOfflineForwardDelay write SetOfflineForwardDelay;
    property OfflineForwardSecondsBetweenForwards: string read GetOfflineForwardSecondsBetweenForwards write SetOfflineForwardSecondsBetweenForwards;
    property TORCycleDelay: string read GetTORCycleDelay write SetTORCycleDelay;
    property OfflineForwardCycleDelay: string read GetOfflineForwardCycleDelay write SetOfflineForwardCycleDelay;
    property SendHealthMessage: string read GetSendHealthMessage write SetSendHealthMessage;
    property CertegyPayroll: string read FCertegyPayroll write FCertegyPayroll;
    property HealthMessageInterval: string read GetHealthMessageInterval write SetHealthMessageInterval;
    property TakeWICChecksLocally: string read GetTakeWICChecksLocally write SetTakeWICChecksLocally;
    property LXHostSlotNumAndTimout: string read GetLXHostSlotNumAndTimout write SetLXHostSlotNumAndTimout;
    property LXHostOnlineStartingSlotNum: string read GetLXHostOnlineStartingSlotNum write SetLXHostOnlineStartingSlotNum;
    property PhoneCardMagStripe: string read GetPhoneCardMagStripe write SetPhoneCardMagStripe;
    property PartialBINDownload: string read GetPartialBINDownload write SetPartialBINDownload;
    property PBTHostMerchantID: string read GetPBTHostMerchantID write SetPBTHostMerchantID;
    property FTPUserHName: string read GetFTPUserHName write SetFTPUserHName;
    property FTPIPAddress: string read GetFTPIPAddress write SetFTPIPAddress;
    property FTPPassword: string read GetFTPPassword write SetFTPPassword;
    property FTPFileName: string read GetFTPFileName write SetFTPFileName;
    property AddressLines: string read GetAddressLines write SetAddressLines;
    property ProcessingMode: string read GetProcessingMode write SetProcessingMode;
    property SendSwipedDLInfo: string read GetSendSwipedDLInfo write SetSendSwipedDLInfo;
    property PrimaryLocalPort: string read GetPrimaryLocalPort write SetPrimaryLocalPort;
    property BackupLocalPort: string read GetBackupLocalPort write SetBackupLocalPort;
    property LocalIPforTerminalConnect: string read GetLocalIPforTerminalConnect write SetLocalIPforTerminalConnect;
    property FTPInterval: string read GetFTPInterval write SetFTPInterval;
    property SettlementReport: string read GetSettlementReport write SetSettlementReport;
    property ForceTenderOffline: string read GetForceTenderOffline write SetForceTenderOffline;
    property ReceiptFileTransferType: string read GetReceiptFileTransferType write SetReceiptFileTransferType;
    property ReceiptHostPrimaryIPAddress: string read GetReceiptHostPrimaryIPAddress write SetReceiptHostPrimaryIPAddress;
    property ReceiptHostPrimaryFTPPort: string read GetReceiptHostPrimaryFTPPort write SetReceiptHostPrimaryFTPPort;
    property ReceiptHostBackupIPAddress: string read GetReceiptHostBackupIPAddress write SetReceiptHostBackupIPAddress;
    property ReceiptHostBackupFTPPort: string read GetReceiptHostBackupFTPPort write SetReceiptHostBackupFTPPort;
    property ReceiptHostUserID: string read GetReceiptHostUserID write SetReceiptHostUserID;
    property ReceiptHostPassword: string read GetReceiptHostPassword write SetReceiptHostPassword;
    property ReceiptHostFileName: string read GetReceiptHostFileName write SetReceiptHostFileName;
    property ReceiptHostFTPPath: string read GetReceiptHostFTPPath write SetReceiptHostFTPPath;
    property TruncateAcctNo: string read GetTruncateAcctNo write SetTruncateAcctNo;
    property SendRawMICR: string read GetSendRawMICR write SetSendRawMICR;
    property HostSuspendCt: string read GetHostSuspendCt write SetHostSuspendCt;
    property HostFatalCt: string read GetHostFatalCt write SetHostFatalCt;
    property HostResumeCt: string read GetHostResumeCt write SetHostResumeCt;
    property HostOnTOxxxx: string read GetHostOnTOxxxx write SetHostOnTOxxxx;
    property Host_Comm_NRZI: string read GetHost_Comm_NRZI write SetHost_Comm_NRZI;
    property Host_Main_Session: string read GetHost_Main_Session write SetHost_Main_Session;
    property Host_TOR_Session: string read GetHost_TOR_Session write SetHost_TOR_Session;
    property Host_Offl_Session: string read GetHost_Offl_Session write SetHost_Offl_Session;
    property HostPort: string read GetHostPort write SetHostPort;
    property HostBits: string read GetHostBits write SetHostBits;
    property HostParity: string read GetHostParity write SetHostParity;
    property HostStop: string read GetHostStop write SetHostStop;
    property HostIRQ: string read GetHostIRQ write SetHostIRQ;
    property HostMgtInfoData: string read GetHostMgtInfoData write SetHostMgtInfoData;
    property HostTORLate: string read GetHostTORLate write SetHostTORLate;
    property HostTORTimeout: string read GetHostTORTimeout write SetHostTORTimeout;
    property HostTORNoT2: string read GetHostTORNoT2 write SetHostTORNoT2;
    property HostRetryPhone1: string read GetHostRetryPhone1 write SetHostRetryPhone1;
    property HostRetryPhone2: string read GetHostRetryPhone2 write SetHostRetryPhone2;
    property HostModemInit: string read GetHostModemInit write SetHostModemInit;
    property HostInitMain: string read GetHostInitMain write SetHostInitMain;
    property TORInitSelfName: string read GetTORInitSelfName write SetTORInitSelfName;
    property OfflineInitSelfName: string read GetOfflineInitSelfName write SetOfflineInitSelfName;
    property HostConfigNum: string read GetHostConfigNum write SetHostConfigNum;
    property HostSecondScrName: string read GetHostSecondScrName write SetHostSecondScrName;
    property FTP_BINFileFromHost: string read GetFTP_BINFileFromHost write SetFTP_BINFileFromHost;
    property HostDivision: string read GetHostDivision write SetHostDivision;
    property HostPort2: string read GetHostPort2 write SetHostPort2;
    property HostBits2: string read GetHostBits2 write SetHostBits2;
    property HostParity2: string read GetHostParity2 write SetHostParity2;
    property HostStop2: string read GetHostStop2 write SetHostStop2;
    property HostIRQ2: string read GetHostIRQ2 write SetHostIRQ2;
    property HostPort3: string read GetHostPort3 write SetHostPort3;
    property HostBits3: string read GetHostBits3 write SetHostBits3;
    property HostParity3: string read GetHostParity3 write SetHostParity3;
    property HostStop3: string read GetHostStop3 write SetHostStop3;
    property HostIRQ3: string read GetHostIRQ3 write SetHostIRQ3;
    property HostSetForDayLight: string read GetHostSetForDayLight write SetHostSetForDayLight;
    property HostTruncateAcctNo: string read GetHostTruncateAcctNo write SetHostTruncateAcctNo;
    property HostPlatformID: string read GetHostPlatformID write SetHostPlatformID;
    property HostClearingCode: string read GetHostClearingCode write SetHostClearingCode;
    property HostMerchBIN: string read GetHostMerchBIN write SetHostMerchBIN;
    property HostMerchICA: string read GetHostMerchICA write SetHostMerchICA;
    property HostIPLocalPort3: string read GetHostIPLocalPort3 write SetHostIPLocalPort3;
    property HostLastBINFtpDate: string read GetHostLastBINFtpDate write SetHostLastBINFtpDate;
    property HostProgramId: string read GetHostProgramId write SetHostProgramId;
    property HostFTPDir: string read GetHostFTPDir write SetHostFTPDir;
    property HostERCUse: string read GetHostERCUse write SetHostERCUse;
    property FreqShopReqOfflineDebit: string read GetFreqShopReqOfflineDebit write SetFreqShopReqOfflineDebit; // YHJ-503 
    property UseSSL: string read FUseSSL write FUseSSL; // YHJ-526 
    property ConnectPayEnabled: string read FConnectPayEnabled write FConnectPayEnabled; // YHJ-508
    property ConnectPayPrefix: string read FConnectPayPrefix write FConnectPayPrefix; // YHJ-508
    property HealthStatusProbationInterval: string read FHealthStatusProbationInterval write FHealthStatusProbationInterval;
    property HealthStatusNotOkInterval: string read FHealthStatusNotOkInterval write FHealthStatusNotOkInterval;
    property ProxyEnabled: string read FProxyEnabled write FProxyEnabled;
    property ProxyAddress: string read FProxyAddress write FProxyAddress;
    property DynamicCurrConv: string read GetDynamicCurrConv write SetDynamicCurrConv; // TFS-9730

    constructor Create(AOwner: TCollection); override;
    destructor Destroy; override;
  end;

{ Global Functions }

//function GetStoreConfigurations(Doc: TXMLDocument): TXMLStoreConfigurationsType;
function LoadStoreConfigurations(FileName: string): TXMLStoreConfigurationsType;
function NewStoreConfigurations: TXMLStoreConfigurationsType;

implementation

uses
  GeneralUtilities,    // for SplitStr
  MTX_Constants,
  MTX_Lib,
  MTX_Utils,
  MTXEncryptionUtils,
  MTX_XMLClasses;

const
    _StoreConfigurations = 'StoreConfigurations'; 
{ TXMLStoreConfigurationsType }
    _Version            = 'Version';
    _LastModified       = 'LastModified';
    _ProcessingOptions  = 'ProcessingOptions';
    _PrintSettings      = 'PrintSettings';
    _ReceiptTexts       = 'ReceiptTexts';                                       { YHJ-515 }
    _Hosts              = 'Hosts';
{ TXMLProcessingOptionsType }
    _PasswordExpirationDays = 'PasswordExpirationDays';
    _TruncateCardDataAtEOD = 'TruncateCardDataAtEOD';                           { YHJ-517 }
    _FtpStoreNumber = 'FtpStoreNumber'; // YHJ-694
    _DoNotRetainCustomerName = 'DoNotRetainCustomerName';
    _EnableTransactionAdjustments = 'EnableTransactionAdjustments';
/// TXMLDialBackupConfigurationType // YHJ-843
    _DialBackupConfiguration = 'DialBackupConfiguration';
    _DBCIPAddress = 'DBCIPAddress';
    _DBCPort = 'DBCPort';
    _DBCIdleTimeout = 'DBCIdleTimeout';
    _DBCRAS = 'DBCRAS';
{ TXMLPrintSettingsType }
    _FontName           = 'FontName';
    _FontSize           = 'FontSize';
    _FontStyles         = 'FontStyles';
    _Orientation        = 'Orientation';
    _NumberOfCopies = 'NumberOfCopies';
{ TXMLFontStylesType }
    _Bold = 'Bold';
    _Italic = 'Italic';
    _Underline = 'Underline';
    _StrikeOut = 'StrikeOut';
{ TXMLReceiptTextType }
    _ReceiptText        = 'ReceiptText';                                        { YHJ-515 }
    _ReceiptHeaderLine1 = 'ReceiptHeaderLine1';
    _ReceiptHeaderLine2 = 'ReceiptHeaderLine2';
    _ReceiptHeaderLine3 = 'ReceiptHeaderLine3';
    _ReceiptHeaderLine4 = 'ReceiptHeaderLine4';
    _ReceiptFooterLine1 = 'ReceiptFooterLine1';
    _ReceiptFooterLine2 = 'ReceiptFooterLine2';
    _CheckDepositLegend = 'CheckDepositLegend';
    _CheckDepositBankName = 'CheckDepositBankName';
    _CheckDepositAccountNumber = 'CheckDepositAccountNumber';
    _HospitalityIncludeTip = 'HospitalityIncludeTip';
{ TXMLHostType }
    _Host = 'Host';
    _Prefix = 'Prefix';
    _Name = 'Name';
    _Suffix = 'Suffix';
    //_SeqNo = 'SeqNo';
    _RetryCounter = 'RetryCounter';
    _OfflineProcessing = 'OfflineProcessing';
    _UseOtherHost = 'UseOtherHost'; // DEV-12505
    _NumOfDaysToKeepOffline = 'NumOfDaysToKeepOffline';
    _FloodStartTime = 'FloodStartTime';
    _FloodEndTime  = 'FloodEndTime';
    _FloodOfflineForwardSecondsBetweenForwards = 'FloodOfflineForwardSecondsBetweenForwards'; // DEV-12505 >
    _SimulatedDelay = 'SimulatedDelay';
    _WorkingKey = 'WorkingKey';
    _UseEBCDIC = 'UseEBCDIC';
    _MerchantNum = 'MerchantNum';
    _Password = 'Password';
    _ChkAuthType = 'ChkAuthType';
    _ChkCode = 'ChkCode';
    _ChkDefaultStateCode = 'ChkDefaultStateCode';
    _VerifyDebitPrefixOnline = 'VerifyDebitPrefixOnline';
    _VerifyDebitPrefixOffline = 'VerifyDebitPrefixOffline';
    _LXHostSlotNumbers = 'LXHostSlotNumbers';
    _StoreNumber = 'StoreNumber';
    _MerchantLanguage = 'MerchantLanguage';
    _TerminalID = 'TerminalID';
    _HostManagmentInfo = 'HostManagmentInfo';
    _MerchantType = 'MerchantType';
    _HostID = 'HostID';
    _MerchantName = 'MerchantName';
    _MerchantAddress = 'MerchantAddress';
    _MerchantCity = 'MerchantCity';
    _MerchantState = 'MerchantState';
    _MerchantZipcode = 'MerchantZipcode';
    _MerchantCountry = 'MerchantCountry';
    _CurrencyCode = 'CurrencyCode';
    _CurrencyAbbreviation = 'CurrencyAbbreviation';    
    _NumericStateCode = 'NumericStateCode';
    _NumericCountryCode = 'NumericCountryCode';
    _GMTOffset = 'GMTOffset';
    _SendCashierToHost = 'SendCashierToHost';
    _TraceMsgsToLog = 'TraceMsgsToLog';
    _ResubmitOfflineForwards = 'ResubmitOfflineForwards';
    _TerminalType = 'TerminalType';
    _MerchantNumber = 'MerchantNumber';
    _TerminalNumber = 'TerminalNumber';
    _PrimaryHostPort = 'PrimaryHostPort';
    _PrimaryHostIPAddress = 'PrimaryHostIPAddress';
    _BackupHostPort = 'BackupHostPort';
    _BackupHostIPAddress = 'BackupHostIPAddress';
    _KeepCheckHistory = 'KeepCheckHistory';
    _PrimaryGCHostIP = 'PrimaryGCHostIP';
    _BackupGCHostIP = 'BackupGCHostIP';
    _LXHostTimout = 'LXHostTimout';
    _TransactionAllowed = 'TransactionAllowed';
    _OnlineTimout = 'OnlineTimout';
    _OfflineForwardDelay = 'OfflineForwardDelay';
    _OfflineForwardSecondsBetweenForwards = 'OfflineForwardSecondsBetweenForwards';
    _TORCycleDelay = 'TORCycleDelay';
    _OfflineForwardCycleDelay = 'OfflineForwardCycleDelay';
    _OfflineForwardSecondsBetweenResubmits = 'OfflineForwardSecondsBetweenResubmits'; // DEV-12942
    _ForceOfflineByCardCode = 'ForceOfflineByCardCode'; // DEV-18409
    //_DoNotForwardGiftCardForce = 'DoNotForwardGiftCardForce'; // DEV-18499
    _SendHealthMessage = 'SendHealthMessage';
    _CertegyPayroll = 'CertegyPayroll';
    _HealthMessageInterval = 'HealthMessageInterval';
    _TakeWICChecksLocally = 'TakeWICChecksLocally';
    _LXHostSlotNumAndTimout = 'LXHostSlotNumAndTimout';
    _LXHostOnlineStartingSlotNum = 'LXHostOnlineStartingSlotNum';
    _PhoneCardMagStripe = 'PhoneCardMagStripe';
    _PartialBINDownload = 'PartialBINDownload';
    _PBTHostMerchantID = 'PBTHostMerchantID';
    _FTPUserHName = 'FTPUserHName';
    _FTPIPAddress = 'FTPIPAddress';
    _FTPPassword = 'FTPPassword';
    _FTPFileName = 'FTPFileName';
    _AddressLines = 'AddressLines';
    _ProcessingMode = 'ProcessingMode';
    _SendSwipedDLInfo = 'SendSwipedDLInfo';
    _PrimaryLocalPort = 'PrimaryLocalPort';
    _BackupLocalPort = 'BackupLocalPort';
    _LocalIPforTerminalConnect = 'LocalIPforTerminalConnect';
    _FTPInterval = 'FTPInterval';
    _SettlementReport = 'SettlementReport';
    _ForceTenderOffline = 'ForceTenderOffline';
    _ReceiptFileTransferType = 'ReceiptFileTransferType';
    _ReceiptHostPrimaryIPAddress = 'ReceiptHostPrimaryIPAddress';
    _ReceiptHostPrimaryFTPPort = 'ReceiptHostPrimaryFTPPort';
    _ReceiptHostBackupIPAddress = 'ReceiptHostBackupIPAddress';
    _ReceiptHostBackupFTPPort = 'ReceiptHostBackupFTPPort';
    _ReceiptHostUserID = 'ReceiptHostUserID';
    _ReceiptHostPassword = 'ReceiptHostPassword';
    _ReceiptHostFileName = 'ReceiptHostFileName';
    _ReceiptHostFTPPath = 'ReceiptHostFTPPath';
    _TruncateAcctNo = 'TruncateAcctNo';
    _SendRawMICR = 'SendRawMICR';
    _HostSuspendCt = 'HostSuspendCt';
    _HostFatalCt = 'HostFatalCt';
    _HostResumeCt = 'HostResumeCt';
    _HostOnTOxxxx = 'HostOnTOxxxx';
    _Host_Comm_NRZI = 'Host_Comm_NRZI';
    _Host_Main_Session = 'Host_Main_Session';
    _Host_TOR_Session = 'Host_TOR_Session';
    _Host_Offl_Session = 'Host_Offl_Session';
    _HostPort = 'HostPort';
    _HostBits = 'HostBits';
    _HostParity = 'HostParity';
    _HostStop = 'HostStop';
    _HostIRQ = 'HostIRQ';
    _HostMgtInfoData = 'HostMgtInfoData';
    _HostTORLate = 'HostTORLate';
    _HostTORTimeout = 'HostTORTimeout';
    _HostTORNoT2 = 'HostTORNoT2';
    _HostRetryPhone1 = 'HostRetryPhone1';
    _HostRetryPhone2 = 'HostRetryPhone2';
    _HostModemInit = 'HostModemInit';
    _HostInitMain = 'HostInitMain';
    _TORInitSelfName = 'TORInitSelfName';
    _OfflineInitSelfName = 'OfflineInitSelfName';
    _HostConfigNum = 'HostConfigNum';
    _HostSecondScrName = 'HostSecondScrName';
    _FTP_BINFileFromHost = 'FTP_BINFileFromHost';
    _HostDivision = 'HostDivision';
    _HostPort2 = 'HostPort2';
    _HostBits2 = 'HostBits2';
    _HostParity2 = 'HostParity2';
    _HostStop2 = 'HostStop2';
    _HostIRQ2 = 'HostIRQ2';
    _HostPort3 = 'HostPort3';
    _HostBits3 = 'HostBits3';
    _HostParity3 = 'HostParity3';
    _HostStop3 = 'HostStop3';
    _HostIRQ3 = 'HostIRQ3';
    _HostSetForDayLight = 'HostSetForDayLight';
    _HostTruncateAcctNo = 'HostTruncateAcctNo';
    _HostPlatformID = 'HostPlatformID';
    _HostClearingCode = 'HostClearingCode';
    _HostMerchBIN = 'HostMerchBIN';
    _HostMerchICA = 'HostMerchICA';
    _HostIPLocalPort3 = 'HostIPLocalPort3';
    _HostLastBINFtpDate = 'HostLastBINFtpDate';
    _HostProgramId = 'HostProgramId';
    _HostFTPDir = 'HostFTPDir';
    _HostERCUse = 'HostERCUse';
    _FreqShopReqOfflineDebit = 'FreqShopReqOfflineDebit';                       { YHJ-503 }
    _UseSSL = 'UseSSL';                                                         { YHJ-526 }
    _ConnectPayEnabled    = 'ConnectPayEnabled';                                { YHJ-508 }
    _ConnectPayPrefix     = 'ConnectPayPrefix';                                 { YHJ-508 }
    _HealthStatusProbationInterval = 'HealthStatusProbationInterval';
    _HealthStatusNotOkInterval     = 'HealthStatusNotOkInterval';
    _ProxyEnabled = 'ProxyEnabled';
    _ProxyAddress = 'ProxyAddress';
    _Use3DES = 'Use3DES'; // DEV-7678
    _ReceivingInstituionId = 'ReceivingInstituionId'; // DEV-10155
    _ForwardAllChecksOnline = 'ForwardAllChecksOnline';
    _ForwardOtherChecks = 'ForwardOtherChecks'; // DEV-11323 <
    _ForwardWICChecks = 'ForwardWICChecks';
    _MerchantPhoneNumber = 'MerchantPhoneNumber'; // DEV-11323 >
    _NetworkRoutingValue = 'NetworkRoutingValue'; // DEV-12382
    _FluCareCardCode = 'FluCareCardCode';
    _FluCareUPC = 'FluCareUPC';
    _DynamicCurrencyConversion = 'DynamicCurrencyConversion'; // TFS-9730

{ Global Functions }
{
function GetStoreConfigurations(Doc: TXMLDocument): TXMLStoreConfigurationsType;
begin
  Result := Doc.GetDocBinding('StoreConfigurations', TXMLStoreConfigurationsType) as TXMLStoreConfigurationsType;
end;
}
function LoadStoreConfigurations(FileName: string): TXMLStoreConfigurationsType;
begin
  try
    //SM('StoreConfigurationsXML.LoadStoreConfigurations');
    result := TXMLStoreConfigurationsType.Create;
    if Assigned(result) then
      if not result.LoadFromFile(FileName) then
        begin
        SM('FAILED LoadStoreConfigurations LoadFromFile(' + FileName + ')');
        FreeAndNil(result);
        end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: StoreConfigurationsXML.LoadStoreConfigurations: ' + FileName + ' - ' + e.message);
  end;
end;

function NewStoreConfigurations: TXMLStoreConfigurationsType;
begin
  result := TXMLStoreConfigurationsType.Create;
end;

{ TXMLStoreConfigurationsType }

constructor TXMLStoreConfigurationsType.Create;
begin
  inherited;
  FProcessingOptions := TXMLProcessingOptionsType.Create;
  FPrintSettings := TXMLPrintSettingsType.Create;
  FReceiptTexts := TXMLReceiptTextsType.Create(TXMLReceiptTextType);            { YHJ-515 }
  FHosts := TXMLHostsType.Create(TXMLHostType);
  FDialBackupConfiguration := TXMLDialBackupConfigurationType.Create; // YHJ-843
end;

destructor TXMLStoreConfigurationsType.Destroy;
begin
  if Assigned(FProcessingOptions) then FreeAndNil(FProcessingOptions);
  if Assigned(FPrintSettings) then FreeAndNil(FPrintSettings);
  if Assigned(FReceiptTexts) then FreeAndNil(FReceiptTexts);                    { YHJ-515 }
  if Assigned(FHosts) then FreeAndNil(FHosts);
  if Assigned(FDialBackupConfiguration) then FreeAndNil(FDialBackupConfiguration); // YHJ-843
  inherited;
end;

function TXMLStoreConfigurationsType.LoadFromFile(aFileName: string): boolean;
var
  aXMLObj: TXMLConfiguration;
  N1, N2, N3: TXMLParserNode;
  AHost: TXMLHostType;
  i,j,k: integer;
  tmpReceiptText: TXMLReceiptTextType;                                          { YHJ-515 }
begin
  //SM('TXMLStoreConfigurationsType.LoadFromFile');
  result := false;
  try
    if not FileExists(aFileName) then Exit;
    aXMLObj := TXMLConfiguration.Create;
    try
      if NOT aXMLObj.FXMLParser.LoadFromFile(aFileName) then                                 // 828.5
      begin                                                                                  // 828.5
        SM('TXMLStoreConfigurationsType.LoadFromFile: Failed to load ' + aFileName);
        Exit;                                                                                // 828.5
      end;                                                                                   // 828.5
      aXMLObj.FXMLParser.StartScan;                                                          // 828.5
      aXMLObj.ScanElement(nil);

      if not SameText(aXMLObj.Root.Name, _StoreConfigurations) then Exit;
//      for i := 0 to Hosts.Count-1 do
//        Hosts.Host[i].Free;
      Version := aXMLObj.Root.Attr.Values[_Version];
      LastModified := aXMLObj.Root.Attr.Values[_LastModified];
      for i := 0 to aXMLObj.Root.Children.Count - 1 do
      begin
        N1 := aXMLObj.Root.Children[i];
{ ProcessingOptions }
        if SameText(N1.Name, _ProcessingOptions) then
        begin
          for j := 0 to N1.Children.Count -1 do
          begin
            N2 := N1.Children[j];
            if SameText(N2.Name, _PasswordExpirationDays) then
              ProcessingOptions.PasswordExpirationDays := StrToIntDef(N2.Text, 0);
            if SameText(N2.Name, _TruncateCardDataAtEOD) then                   { YHJ-517 }
              ProcessingOptions.TruncateCardDataAtEOD := N2.Text;               { YHJ-517 }
            if SameText(N2.Name, _FtpStoreNumber) then
              ProcessingOptions.FtpStoreNumber := N2.Text; // YHJ-694
            if SameText(N2.Name, _DoNotRetainCustomerName) then
              ProcessingOptions.DoNotRetainCustomerName := N2.Text;
            if SameText(N2.Name, _EnableTransactionAdjustments) then
              ProcessingOptions.EnableTransactionAdjustments := N2.Text = 'Y';            
          end;
        // DialBackupConfiguration
        {$IFDEF GUIJR}
        end else if SameText(N1.Name, _DialBackupConfiguration) then // YHJ-843
        begin
          for j := 0 to N1.Children.Count -1 do
          begin
            N2 := N1.Children[j];
            if SameText(N2.Name, _DBCIPAddress) then DialBackupConfiguration.IPAddress := N2.Text
            else if SameText(N2.Name, _DBCPort) then DialBackupConfiguration.Port := StrToIntDef(N2.Text, DialBackupDefaultPort)
            else if SameText(N2.Name, _DBCIdleTimeout) then DialBackupConfiguration.IdleTimeout := StrToIntDef(N2.Text, DialBackupDefaultIdleTimeout)
            else if SameText(N2.Name, _DBCRAS) then DialBackupConfiguration.RAS := N2.Text;
          end;
        {$ENDIF}
{ PrintSettings }
        end else if SameText(N1.Name, _PrintSettings) then
        begin
          for j := 0 to N1.Children.Count -1 do
          begin
            N2 := N1.Children[j];
            if SameText(N2.Name, _FontName) then PrintSettings.FontName := N2.Text
            else if SameText(N2.Name, _FontSize) then PrintSettings.FontSize := N2.Text
            else if SameText(N2.Name, _FontStyles) then
            begin
              for K := 0 to N2.Attr.Count-1 do
              begin
                if (Pos(_Bold,N2.Attr[k]) > 0) then PrintSettings.FontStyles.Bold := YN(N2.Attr[k]=_Bold+'=Y')
                else if (Pos(_Italic,N2.Attr[k]) > 0) then PrintSettings.FontStyles.Italic := YN(N2.Attr[k]=_Italic+'=Y')
                else if (Pos(_Underline,N2.Attr[k]) > 0) then PrintSettings.FontStyles.Underline := YN(N2.Attr[k]=_Underline+'=Y')
                else if (Pos(_StrikeOut,N2.Attr[k]) > 0) then PrintSettings.FontStyles.StrikeOut := YN(N2.Attr[k]=_StrikeOut+'=Y');
              end;
            end
            else if SameText(N2.Name, _Orientation) then PrintSettings.Orientation := N2.Text
            else if SameText(N2.Name, _NumberOfCopies) then PrintSettings.NumberOfCopies := N2.Text;
          end;
{ ReceiptText }
        end else if SameText(N1.Name, _ReceiptTexts) then
        begin
          for j := 0 to N1.Children.Count -1 do
          begin
            N2 := N1.Children[j];
            if SameText(N2.Name, _ReceiptText) then                             { YHJ-515 }
            begin
              tmpReceiptText := ReceiptTexts.Add;
              tmpReceiptText.StoreNumber := N2.Attr.Values[_StoreNumber];
              if Trim(tmpReceiptText.StoreNumber) = '' then
                tmpReceiptText.StoreNumber := IntToStr(DEFAULT_STORE_NUMBER);
              tmpReceiptText.MerchantLanguage := N2.Attr.Values[_MerchantLanguage];
              for k := 0 to N2.Children.Count -1 do
              begin
                N3 := N2.Children[k];
                if SameText(N3.Name, _ReceiptHeaderLine1) then tmpReceiptText.ReceiptHeaderLine1 := N3.Text
                else if SameText(N3.Name, _ReceiptHeaderLine2) then tmpReceiptText.ReceiptHeaderLine2 := N3.Text
                else if SameText(N3.Name, _ReceiptHeaderLine3) then tmpReceiptText.ReceiptHeaderLine3 := N3.Text
                else if SameText(N3.Name, _ReceiptHeaderLine4) then tmpReceiptText.ReceiptHeaderLine4 := N3.Text
                else if SameText(N3.Name, _ReceiptFooterLine1) then tmpReceiptText.ReceiptFooterLine1 := N3.Text
                else if SameText(N3.Name, _ReceiptFooterLine2) then tmpReceiptText.ReceiptFooterLine2 := N3.Text
                else if SameText(N3.Name, _CheckDepositLegend) then tmpReceiptText.CheckDepositLegend := N3.Text
                else if SameText(N3.Name, _CheckDepositBankName) then tmpReceiptText.CheckDepositBankName := N3.Text
                else if SameText(N3.Name, _CheckDepositAccountNumber) then tmpReceiptText.CheckDepositAccountNumber := N3.Text
                else if SameText(N3.Name, _HospitalityIncludeTip) then tmpReceiptText.HospitalityIncludeTip := AnsiCompareText(N3.Text,'y') = 0;
              end;
            end;
          end;
{ Hosts }
        end else if SameText(N1.Name, _Hosts) then
        begin
          HostXMLHash := GetCryptoHash(N1.GetXMLStr); // DEV-12765
          for j := 0 to N1.Children.Count -1 do
          begin
            N2 := N1.Children[j];
            if SameText(N2.Name, _Host) then
            begin
              //AHost := TXMLHostType.Create(TXMLHostsType);
              AHost := Hosts.Add;
              try
                AHost.Prefix := N2.Attr.Values[_Prefix];
                //AHost.Name := N2.Attr.Values[_Name];
                AHost.Suffix := N2.Attr.Values[_Suffix];
                for k := 0 to N2.Children.Count -1 do
                begin
                  N3 := N2.Children[k];
                  if SameText(N3.Name, _RetryCounter) then AHost.RetryCounter := N3.Text
                  else if SameText(N3.Name, _OfflineProcessing) then
                  begin
                    AHost.OfflineProcessing := N3.Text;
                    AHost.UseOtherHostForOfflineProcessing := SameText(N3.Attr.Values[_UseOtherHost], 'Y'); // DEV-12505
                  end
                  else if SameText(N3.Name, _NumOfDaysToKeepOffline) then AHost.NumOfDaysToKeepOffline := iif(MTX_Lib.IsNumeric(N3.Text), N3.Text, '0') // DEV-12505 <
                  else if SameText(N3.Name, _FloodStartTime) then AHost.FloodStartTime := iif(IsValidMMDD(N3.Text), N3.Text, '0000')
                  else if SameText(N3.Name, _FloodEndTime) then AHost.FloodEndTime := iif(IsValidMMDD(N3.Text), N3.Text, '0000')
                  else if SameText(N3.Name, _FloodOfflineForwardSecondsBetweenForwards) then
                    AHost.FloodOfflineForwardSecondsBetweenForwards := iif(MTX_Lib.IsNumeric(N3.Text), N3.Text, '0') // DEV-12505 >
                  else if SameText(N3.Name, _SimulatedDelay) then AHost.SimulatedDelay := N3.Text
                  else if SameText(N3.Name, _WorkingKey) then AHost.WorkingKey := N3.Text
                  else if SameText(N3.Name, _UseEBCDIC) then AHost.UseEBCDIC := N3.Text
                  else if SameText(N3.Name, _MerchantNum) then AHost.MerchantNum := N3.Text
                  else if SameText(N3.Name, _Password) then AHost.Password := N3.Text
                  else if SameText(N3.Name, _ChkAuthType) then AHost.ChkAuthType := N3.Text
                  else if SameText(N3.Name, _ChkCode) then AHost.ChkCode := N3.Text
                  else if SameText(N3.Name, _ChkDefaultStateCode) then AHost.ChkDefaultStateCode := N3.Text
                  else if SameText(N3.Name, _VerifyDebitPrefixOnline) then AHost.VerifyDebitPrefixOnline := N3.Text
                  else if SameText(N3.Name, _VerifyDebitPrefixOffline) then AHost.VerifyDebitPrefixOffline := N3.Text
                  else if SameText(N3.Name, _LXHostSlotNumbers) then AHost.LXHostSlotNumbers := N3.Text
                  else if SameText(N3.Name, _StoreNumber) then AHost.StoreNumber := N3.Text
                  else if SameText(N3.Name, _TerminalID) then AHost.TerminalID := N3.Text
                  else if SameText(N3.Name, _HostManagmentInfo) then AHost.HostManagmentInfo := N3.Text
                  else if SameText(N3.Name, _MerchantType) then AHost.MerchantType := N3.Text
                  else if SameText(N3.Name, _HostID) then AHost.HostID := N3.Text
                  else if SameText(N3.Name, _MerchantName) then AHost.MerchantName := N3.Text
                  else if SameText(N3.Name, _MerchantAddress) then AHost.MerchantAddress := N3.Text
                  else if SameText(N3.Name, _MerchantCity) then AHost.MerchantCity := N3.Text
                  else if SameText(N3.Name, _MerchantState) then AHost.MerchantState := N3.Text
                  else if SameText(N3.Name, _MerchantZipcode) then AHost.MerchantZipcode := N3.Text
                  else if SameText(N3.Name, _MerchantCountry) then AHost.MerchantCountry := N3.Text
                  else if SameText(N3.Name, _CurrencyCode) then AHost.CurrencyCode := N3.Text
                  else if SameText(N3.Name, _CurrencyAbbreviation) then AHost.CurrencyAbbreviation := N3.Text
                  else if SameText(N3.Name, _NumericStateCode) then AHost.NumericStateCode := N3.Text
                  else if SameText(N3.Name, _NumericCountryCode) then AHost.NumericCountryCode := N3.Text
                  else if SameText(N3.Name, _GMTOffset) then AHost.GMTOffset := N3.Text
                  else if SameText(N3.Name, _SendCashierToHost) then AHost.SendCashierToHost := N3.Text
                  else if SameText(N3.Name, _TraceMsgsToLog) then AHost.TraceMsgsToLog := N3.Text
                  else if SameText(N3.Name, _ResubmitOfflineForwards) then AHost.ResubmitOfflineForwards := N3.Text
                  else if SameText(N3.Name, _TerminalType) then AHost.TerminalType := N3.Text
                  else if SameText(N3.Name, _MerchantNumber) then AHost.MerchantNumber := N3.Text
                  else if SameText(N3.Name, _TerminalNumber) then AHost.TerminalNumber := N3.Text
                  else if SameText(N3.Name, _PrimaryHostPort) then AHost.PrimaryHostPort := N3.Text
                  else if SameText(N3.Name, _PrimaryHostIPAddress) then AHost.PrimaryHostIPAddress := N3.Text
                  else if SameText(N3.Name, _BackupHostPort) then AHost.BackupHostPort := N3.Text
                  else if SameText(N3.Name, _BackupHostIPAddress) then AHost.BackupHostIPAddress := N3.Text
                  else if SameText(N3.Name, _KeepCheckHistory) then AHost.KeepCheckHistory := N3.Text
                  else if SameText(N3.Name, _PrimaryGCHostIP) then AHost.PrimaryGCHostIP := N3.Text
                  else if SameText(N3.Name, _BackupGCHostIP) then AHost.BackupGCHostIP := N3.Text
                  else if SameText(N3.Name, _LXHostTimout) then AHost.LXHostTimout := N3.Text
                  else if SameText(N3.Name, _TransactionAllowed) then AHost.TransactionAllowed := N3.Text
                  else if SameText(N3.Name, _OnlineTimout) then AHost.OnlineTimout := N3.Text
                  else if SameText(N3.Name, _OfflineForwardDelay) then AHost.OfflineForwardDelay := N3.Text
                  else if SameText(N3.Name, _OfflineForwardSecondsBetweenForwards) then AHost.OfflineForwardSecondsBetweenForwards := N3.Text
                  else if SameText(N3.Name, _OfflineForwardSecondsBetweenResubmits) then AHost.OfflineForwardSecondsBetweenResubmits := N3.Text // DEV-12942
                  else if SameText(N3.Name, _ForceOfflineByCardCode) then SplitStr(N3.Text, ',', AHost.ForceOfflineByCardCode, true) // DEV-18409
                  //else if SameText(N3.Name, _DoNotForwardGiftCardForce) then AHost.DoNotForwardGiftCardForce := N3.Text // DEV-18499
                  else if SameText(N3.Name, _TORCycleDelay) then AHost.TORCycleDelay := N3.Text
                  else if SameText(N3.Name, _OfflineForwardCycleDelay) then AHost.OfflineForwardCycleDelay := N3.Text
                  else if SameText(N3.Name, _SendHealthMessage) then AHost.SendHealthMessage := N3.Text
                  else if SameText(N3.Name, _CertegyPayroll) then AHost.CertegyPayroll := N3.Text
                  else if SameText(N3.Name, _HealthMessageInterval) then AHost.HealthMessageInterval := N3.Text
                  else if SameText(N3.Name, _TakeWICChecksLocally) then AHost.TakeWICChecksLocally := N3.Text
                  else if SameText(N3.Name, _LXHostSlotNumAndTimout) then AHost.LXHostSlotNumAndTimout := N3.Text
                  else if SameText(N3.Name, _LXHostOnlineStartingSlotNum) then AHost.LXHostOnlineStartingSlotNum := N3.Text
                  else if SameText(N3.Name, _PhoneCardMagStripe) then AHost.PhoneCardMagStripe := N3.Text
                  else if SameText(N3.Name, _PartialBINDownload) then AHost.PartialBINDownload := N3.Text
                  else if SameText(N3.Name, _PBTHostMerchantID) then AHost.PBTHostMerchantID := N3.Text
                  else if SameText(N3.Name, _FTPUserHName) then AHost.FTPUserHName := N3.Text
                  else if SameText(N3.Name, _FTPIPAddress) then AHost.FTPIPAddress := N3.Text
                  else if SameText(N3.Name, _FTPPassword) then AHost.FTPPassword := N3.Text
                  else if SameText(N3.Name, _FTPFileName) then AHost.FTPFileName := N3.Text
                  else if SameText(N3.Name, _AddressLines) then AHost.AddressLines := N3.Text
                  else if SameText(N3.Name, _ProcessingMode) then AHost.ProcessingMode := N3.Text
                  else if SameText(N3.Name, _SendSwipedDLInfo) then AHost.SendSwipedDLInfo := N3.Text
                  else if SameText(N3.Name, _PrimaryLocalPort) then AHost.PrimaryLocalPort := N3.Text
                  else if SameText(N3.Name, _BackupLocalPort) then AHost.BackupLocalPort := N3.Text
                  else if SameText(N3.Name, _LocalIPforTerminalConnect) then AHost.LocalIPforTerminalConnect := N3.Text
                  else if SameText(N3.Name, _FTPInterval) then AHost.FTPInterval := N3.Text
                  else if SameText(N3.Name, _SettlementReport) then AHost.SettlementReport := N3.Text
                  else if SameText(N3.Name, _ForceTenderOffline) then AHost.ForceTenderOffline := N3.Text
                  else if SameText(N3.Name, _ReceiptFileTransferType) then AHost.ReceiptFileTransferType := N3.Text
                  else if SameText(N3.Name, _ReceiptHostPrimaryIPAddress) then AHost.ReceiptHostPrimaryIPAddress := N3.Text
                  else if SameText(N3.Name, _ReceiptHostPrimaryFTPPort) then AHost.ReceiptHostPrimaryFTPPort := N3.Text
                  else if SameText(N3.Name, _ReceiptHostBackupIPAddress) then AHost.ReceiptHostBackupIPAddress := N3.Text
                  else if SameText(N3.Name, _ReceiptHostBackupFTPPort) then AHost.ReceiptHostBackupFTPPort := N3.Text
                  else if SameText(N3.Name, _ReceiptHostUserID) then AHost.ReceiptHostUserID := N3.Text
                  else if SameText(N3.Name, _ReceiptHostPassword) then AHost.ReceiptHostPassword := N3.Text
                  else if SameText(N3.Name, _ReceiptHostFileName) then AHost.ReceiptHostFileName := N3.Text
                  else if SameText(N3.Name, _ReceiptHostFTPPath) then AHost.ReceiptHostFTPPath := N3.Text
                  else if SameText(N3.Name, _TruncateAcctNo) then AHost.TruncateAcctNo := N3.Text
                  else if SameText(N3.Name, _SendRawMICR) then AHost.SendRawMICR := N3.Text
                  else if SameText(N3.Name, _HostSuspendCt) then AHost.HostSuspendCt := N3.Text
                  else if SameText(N3.Name, _HostFatalCt) then AHost.HostFatalCt := N3.Text
                  else if SameText(N3.Name, _HostResumeCt) then AHost.HostResumeCt := N3.Text
                  else if SameText(N3.Name, _HostOnTOxxxx) then AHost.HostOnTOxxxx := N3.Text
                  else if SameText(N3.Name, _Host_Comm_NRZI) then AHost.Host_Comm_NRZI := N3.Text
                  else if SameText(N3.Name, _Host_Main_Session) then AHost.Host_Main_Session := N3.Text
                  else if SameText(N3.Name, _Host_TOR_Session) then AHost.Host_TOR_Session := N3.Text
                  else if SameText(N3.Name, _Host_Offl_Session) then AHost.Host_Offl_Session := N3.Text
                  else if SameText(N3.Name, _HostPort) then AHost.HostPort := N3.Text
                  else if SameText(N3.Name, _HostBits) then AHost.HostBits := N3.Text
                  else if SameText(N3.Name, _HostParity) then AHost.HostParity := N3.Text
                  else if SameText(N3.Name, _HostStop) then AHost.HostStop := N3.Text
                  else if SameText(N3.Name, _HostIRQ) then AHost.HostIRQ := N3.Text
                  else if SameText(N3.Name, _HostMgtInfoData) then AHost.HostMgtInfoData := N3.Text
                  else if SameText(N3.Name, _HostTORLate) then AHost.HostTORLate := N3.Text
                  else if SameText(N3.Name, _HostTORTimeout) then AHost.HostTORTimeout := N3.Text
                  else if SameText(N3.Name, _HostTORNoT2) then AHost.HostTORNoT2 := N3.Text
                  else if SameText(N3.Name, _HostRetryPhone1) then AHost.HostRetryPhone1 := N3.Text
                  else if SameText(N3.Name, _HostRetryPhone2) then AHost.HostRetryPhone2 := N3.Text
                  else if SameText(N3.Name, _HostModemInit) then AHost.HostModemInit := N3.Text
                  else if SameText(N3.Name, _HostInitMain) then AHost.HostInitMain := N3.Text
                  else if SameText(N3.Name, _TORInitSelfName) then AHost.TORInitSelfName := N3.Text
                  else if SameText(N3.Name, _OfflineInitSelfName) then AHost.OfflineInitSelfName := N3.Text
                  else if SameText(N3.Name, _HostConfigNum) then AHost.HostConfigNum := N3.Text
                  else if SameText(N3.Name, _HostSecondScrName) then AHost.HostSecondScrName := N3.Text
                  else if SameText(N3.Name, _FTP_BINFileFromHost) then AHost.FTP_BINFileFromHost := N3.Text
                  else if SameText(N3.Name, _HostDivision) then AHost.HostDivision := N3.Text
                  else if SameText(N3.Name, _HostPort2) then AHost.HostPort2 := N3.Text
                  else if SameText(N3.Name, _HostBits2) then AHost.HostBits2 := N3.Text
                  else if SameText(N3.Name, _HostParity2) then AHost.HostParity2 := N3.Text
                  else if SameText(N3.Name, _HostStop2) then AHost.HostStop2 := N3.Text
                  else if SameText(N3.Name, _HostIRQ2) then AHost.HostIRQ2 := N3.Text
                  else if SameText(N3.Name, _HostPort3) then AHost.HostPort3 := N3.Text
                  else if SameText(N3.Name, _HostBits3) then AHost.HostBits3 := N3.Text
                  else if SameText(N3.Name, _HostParity3) then AHost.HostParity3 := N3.Text
                  else if SameText(N3.Name, _HostStop3) then AHost.HostStop3 := N3.Text
                  else if SameText(N3.Name, _HostIRQ3) then AHost.HostIRQ3 := N3.Text
                  else if SameText(N3.Name, _HostSetForDayLight) then AHost.HostSetForDayLight := N3.Text
                  else if SameText(N3.Name, _HostTruncateAcctNo) then AHost.HostTruncateAcctNo := N3.Text
                  else if SameText(N3.Name, _HostPlatformID) then AHost.HostPlatformID := N3.Text
                  else if SameText(N3.Name, _HostClearingCode) then AHost.HostClearingCode := N3.Text
                  else if SameText(N3.Name, _HostMerchBIN) then AHost.HostMerchBIN := N3.Text
                  else if SameText(N3.Name, _HostMerchICA) then AHost.HostMerchICA := N3.Text
                  else if SameText(N3.Name, _HostIPLocalPort3) then AHost.HostIPLocalPort3 := N3.Text
                  else if SameText(N3.Name, _HostLastBINFtpDate) then AHost.HostLastBINFtpDate := N3.Text
                  else if SameText(N3.Name, _HostProgramId) then AHost.HostProgramId := N3.Text
                  else if SameText(N3.Name, _HostFTPDir) then AHost.HostFTPDir := N3.Text
                  else if SameText(N3.Name, _HostERCUse) then AHost.HostERCUse := N3.Text
                  else if SameText(N3.Name, _FreqShopReqOfflineDebit) then AHost.FreqShopReqOfflineDebit := N3.Text { YHJ-503 }
                  else if SameText(N3.Name, _UseSSL) then AHost.UseSSL := N3.Text { YHJ-526 }
                  else if SameText(N3.Name, _ConnectPayEnabled) then AHost.ConnectPayEnabled := N3.Text { YHJ-508 }
                  else if SameText(N3.Name, _ConnectPayPrefix) then AHost.ConnectPayPrefix := N3.Text { YHJ-508 }
                  else if SameText(N3.Name, _HealthStatusProbationInterval) then AHost.HealthStatusProbationInterval := N3.Text
                  else if SameText(N3.Name, _HealthStatusNotOkInterval) then AHost.HealthStatusNotOkInterval := N3.Text
                  else if SameText(N3.Name, _ProxyEnabled) then AHost.ProxyEnabled := N3.Text
                  else if SameText(N3.Name, _ProxyAddress) then AHost.ProxyAddress := N3.Text
                  else if SameText(N3.Name, _Use3DES) then AHost.Use3DES := N3.Text // DEV-7678
                  else if SameText(N3.Name, _ReceivingInstituionId) then AHost.ReceivingInstituionId := N3.Text // DEV-10155
                  else if SameText(N3.Name, _ForwardAllChecksOnline) then AHost.ForwardAllChecksOnline := N3.Text
                  else if SameText(N3.Name, _ForwardOtherChecks) then AHost.ForwardOtherChecks := N3.Text // DEV-11323 <
                  else if SameText(N3.Name, _ForwardWICChecks) then AHost.ForwardWICChecks := N3.Text
                  else if SameText(N3.Name, _MerchantPhoneNumber) then AHost.MerchantPhoneNumber := N3.Text // DEV-11323 >
                  else if SameText(N3.Name, _NetworkRoutingValue) then AHost.NetworkRoutingValue := N3.Text // DEV-12382
                  else if SameText(N3.Name, _FluCareCardCode) then AHost.FluCareCardCode := N3.Text
                  else if SameText(N3.Name, _FluCareUPC) then AHost.FluCareUPC := N3.Text // DEV-12382
                  else if SameText(N3.Name, _DynamicCurrencyConversion) then AHost.DynamicCurrConv := N3.Text; // TFS-9730
                end;
                // default value
                if Trim(AHost.NetworkRoutingValue) = '' then
                  AHost.NetworkRoutingValue := '00';
                if Trim(AHost.OfflineForwardSecondsBetweenResubmits) = '' then // DEV-12942
                  AHost.OfflineForwardSecondsBetweenResubmits := '00600';
                if Trim(AHost.FluCareCardCode) = '' then
                  AHost.FluCareCardCode := 'FC';
              finally
//                AHost.Free;
              end;
            end;
          end;
        end; // end of if SameText(N1.Name, _Hosts) then
      end; // end of for i := 0 to XMLObj.Root.Children.Count - 1 do
    finally
      FreeAndNil(aXMLObj);
    end;
    result := true;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLStoreConfigurationsType.LoadFromFile: ' + aFileName + ' - ' + e.message);
  end;
end;

function TXMLStoreConfigurationsType.GetHostXMLHash: string; // DEV-12765
var
  Root, N1: TXMLParserNode;
  i: integer;
begin
  result := '';
  try
    Root := TXMLParserNode.Create(nil);
    try
      CreateNodes(Root);
      for i := 0 to Root.Children.Count - 1 do
      begin
        N1 := Root.Children[i];
        if SameText(N1.Name, _Hosts) then
        begin
          result := GetCryptoHash(N1.GetXMLStr);
          exit;
        end;
      end;
    finally
      FreeAndNil(Root);
    end;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLStoreConfigurationsType.GetHostXMLHash - ' + e.message);
  end;
end;

function TXMLStoreConfigurationsType.SaveToFile(aFileName: string): boolean;
var
  Root: TXMLParserNode;
begin
  result := false;
  try
    Root := TXMLParserNode.Create(nil);
    try
      CreateNodes(Root); // DEV-12765
      Root.SaveToFile(aFileName);
    finally
      FreeAndNil(Root);
    end;
    result := true;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TXMLStoreConfigurationsType.SaveToFile: ' + aFileName + ' - ' + e.message);
  end;
end;

procedure TXMLStoreConfigurationsType.CreateNodes(Root: TXMLParserNode); // DEV-12765
var
  N1, N2, N3: TXMLParserNode;
  i: Integer;
begin
  Root.Name := _StoreConfigurations;
  Root.Attr.Values[_Version] := Version;
  Root.Attr.Values[_LastModified] := FormatDateTime(FORMAT_LASTMODIFIED, Now);
  { ProcessingOptions }
  N1 := Root.AddChild(_ProcessingOptions);
  N1.AddChild(_PasswordExpirationDays).Text := IntToStr(ProcessingOptions.PasswordExpirationDays);
  N1.AddChild(_TruncateCardDataAtEOD).Text := ProcessingOptions.TruncateCardDataAtEOD; { YHJ-517 }
  N1.AddChild(_FtpStoreNumber).Text := ProcessingOptions.FtpStoreNumber; // YHJ-694
  N1.AddChild(_DoNotRetainCustomerName).Text := ProcessingOptions.DoNotRetainCustomerName; 
  { PrintSettings }
  N1 := Root.AddChild(_PrintSettings);
  N1.AddChild(_FontName).Text := PrintSettings.FontName;
  N1.AddChild(_FontSize).Text := PrintSettings.FontSize;
  N2 := N1.AddChild(_FontStyles);
  N2.Attr.Values[_Bold] := YN(PrintSettings.FontStyles.Bold='Y');
  N2.Attr.Values[_Italic] := YN(PrintSettings.FontStyles.Italic='Y');
  N2.Attr.Values[_Underline] := YN(PrintSettings.FontStyles.Underline='Y');
  N2.Attr.Values[_StrikeOut] := YN(PrintSettings.FontStyles.StrikeOut='Y');
  N1.AddChild(_Orientation).Text := PrintSettings.Orientation;
  N1.AddChild(_NumberOfCopies).Text := PrintSettings.NumberOfCopies;
  /// DialBackupConfiguration
  {$IFDEF GUIJR}
  N1 := Root.AddChild(_DialBackupConfiguration); // YHJ-843 <
  N1.AddChild(_DBCIPAddress).Text := DialBackupConfiguration.IPAddress;
  N1.AddChild(_DBCPort).Text := IntToStr(DialBackupConfiguration.Port);
  N1.AddChild(_DBCIdleTimeout).Text := IntToStr(DialBackupConfiguration.IdleTimeout);
  N1.AddChild(_DBCRAS).Text := DialBackupConfiguration.RAS; // YHJ-843 >
  {$ENDIF}
  { ReceiptText }
  N1 := Root.AddChild(_ReceiptTexts);                                       { YHJ-515 }
  for i := 0 to ReceiptTexts.Count -1 do
  begin
    N2 := N1.AddChild(_ReceiptText);
    N2.Attr.Values[_StoreNumber] := ReceiptTexts.ReceiptText[i].StoreNumber;
    N2.Attr.Values[_MerchantLanguage] := ReceiptTexts.ReceiptText[i].MerchantLanguage;
    N2.AddChild(_ReceiptHeaderLine1).Text := ReceiptTexts.ReceiptText[i].ReceiptHeaderLine1;
    N2.AddChild(_ReceiptHeaderLine2).Text := ReceiptTexts.ReceiptText[i].ReceiptHeaderLine2;
    N2.AddChild(_ReceiptHeaderLine3).Text := ReceiptTexts.ReceiptText[i].ReceiptHeaderLine3;
    N2.AddChild(_ReceiptHeaderLine4).Text := ReceiptTexts.ReceiptText[i].ReceiptHeaderLine4;
    N2.AddChild(_ReceiptFooterLine1).Text := ReceiptTexts.ReceiptText[i].ReceiptFooterLine1;
    N2.AddChild(_ReceiptFooterLine2).Text := ReceiptTexts.ReceiptText[i].ReceiptFooterLine2;
    N2.AddChild(_CheckDepositLegend).Text := ReceiptTexts.ReceiptText[i].CheckDepositLegend;
    N2.AddChild(_CheckDepositBankName).Text := ReceiptTexts.ReceiptText[i].CheckDepositBankName;
    N2.AddChild(_CheckDepositAccountNumber).Text := ReceiptTexts.ReceiptText[i].CheckDepositAccountNumber;
    N2.AddChild(_HospitalityIncludeTip).Text := YN(ReceiptTexts.ReceiptText[i].HospitalityIncludeTip);
  end;
  { Hosts }
  N1 := Root.AddChild(_Hosts);
  for i := 0 to Hosts.Count -1 do
  begin
    N2 := N1.AddChild(_Host);
    N2.Attr.Values[_Prefix] := Hosts.Host[i].Prefix;
    //N2.Attr.Values[_Name] := Hosts.Host[i].Name;
    N2.Attr.Values[_Suffix] := Hosts.Host[i].Suffix;
    N2.AddChild(_RetryCounter).Text := Hosts.Host[i].RetryCounter;
    //N2.AddChild(_OfflineProcessing).Text := Hosts.Host[i].OfflineProcessing; // DEV-11373
    N3 := N2.AddChild(_OfflineProcessing); // DEV-12505 <
    N3.Text := Hosts.Host[i].OfflineProcessing;
    N3.Attr.Values[_UseOtherHost] := YN(Hosts.Host[i].UseOtherHostForOfflineProcessing); 

    N2.AddChild(_NumOfDaysToKeepOffline).Text := iif(StrToIntDef(Hosts.Host[i].NumOfDaysToKeepOffline, -1)=-1, '14', Hosts.Host[i].NumOfDaysToKeepOffline);
    N2.AddChild(_FloodStartTime).Text := iif(IsValidMMDD(Hosts.Host[i].FloodStartTime), Hosts.Host[i].FloodStartTime, '0000');
    N2.AddChild(_FloodEndTime).Text := iif(IsValidMMDD(Hosts.Host[i].FloodEndTime), Hosts.Host[i].FloodEndTime, '0000');
    N2.AddChild(_FloodOfflineForwardSecondsBetweenForwards).Text := Hosts.Host[i].FloodOfflineForwardSecondsBetweenForwards; // DEV-12505 >

    N2.AddChild(_SimulatedDelay).Text := Hosts.Host[i].SimulatedDelay;
    N2.AddChild(_WorkingKey).Text := Hosts.Host[i].WorkingKey;
    N2.AddChild(_UseEBCDIC).Text := Hosts.Host[i].UseEBCDIC;
    N2.AddChild(_MerchantNum).Text := Hosts.Host[i].MerchantNum;
    N2.AddChild(_Password).Text := Hosts.Host[i].Password;
    N2.AddChild(_ChkAuthType).Text := Hosts.Host[i].ChkAuthType;
    N2.AddChild(_ChkCode).Text := Hosts.Host[i].ChkCode;
    N2.AddChild(_ChkDefaultStateCode).Text := Hosts.Host[i].ChkDefaultStateCode;
    N2.AddChild(_VerifyDebitPrefixOnline).Text := Hosts.Host[i].VerifyDebitPrefixOnline;
    N2.AddChild(_VerifyDebitPrefixOffline).Text := Hosts.Host[i].VerifyDebitPrefixOffline;
    N2.AddChild(_LXHostSlotNumbers).Text := Hosts.Host[i].LXHostSlotNumbers;
    N2.AddChild(_StoreNumber).Text := Hosts.Host[i].StoreNumber;
    N2.AddChild(_TerminalID).Text := Hosts.Host[i].TerminalID;
    N2.AddChild(_HostManagmentInfo).Text := Hosts.Host[i].HostManagmentInfo;
    N2.AddChild(_MerchantType).Text := Hosts.Host[i].MerchantType;
    N2.AddChild(_HostID).Text := Hosts.Host[i].HostID;
    N2.AddChild(_MerchantName).Text := Hosts.Host[i].MerchantName;
    N2.AddChild(_MerchantAddress).Text := Hosts.Host[i].MerchantAddress;
    N2.AddChild(_MerchantCity).Text := Hosts.Host[i].MerchantCity;
    N2.AddChild(_MerchantState).Text := Hosts.Host[i].MerchantState;
    N2.AddChild(_MerchantZipcode).Text := Hosts.Host[i].MerchantZipcode;
    N2.AddChild(_MerchantCountry).Text := Hosts.Host[i].MerchantCountry;
    N2.AddChild(_CurrencyCode).Text := Hosts.Host[i].CurrencyCode;
    N2.AddChild(_CurrencyAbbreviation).Text := Hosts.Host[i].CurrencyAbbreviation;
    N2.AddChild(_NumericStateCode).Text := Hosts.Host[i].NumericStateCode;
    N2.AddChild(_NumericCountryCode).Text := Hosts.Host[i].NumericCountryCode;
    N2.AddChild(_GMTOffset).Text := Hosts.Host[i].GMTOffset;
    N2.AddChild(_SendCashierToHost).Text := Hosts.Host[i].SendCashierToHost;
    N2.AddChild(_TraceMsgsToLog).Text := Hosts.Host[i].TraceMsgsToLog;
    N2.AddChild(_ResubmitOfflineForwards).Text := Hosts.Host[i].ResubmitOfflineForwards;
    N2.AddChild(_TerminalType).Text := Hosts.Host[i].TerminalType;
    N2.AddChild(_MerchantNumber).Text := Hosts.Host[i].MerchantNumber;
    N2.AddChild(_TerminalNumber).Text := Hosts.Host[i].TerminalNumber;
    N2.AddChild(_PrimaryHostPort).Text := Hosts.Host[i].PrimaryHostPort;
    N2.AddChild(_PrimaryHostIPAddress).Text := Hosts.Host[i].PrimaryHostIPAddress;
    N2.AddChild(_BackupHostPort).Text := Hosts.Host[i].BackupHostPort;
    N2.AddChild(_BackupHostIPAddress).Text := Hosts.Host[i].BackupHostIPAddress;
    N2.AddChild(_KeepCheckHistory).Text := Hosts.Host[i].KeepCheckHistory;
    N2.AddChild(_PrimaryGCHostIP).Text := Hosts.Host[i].PrimaryGCHostIP;
    N2.AddChild(_BackupGCHostIP).Text := Hosts.Host[i].BackupGCHostIP;
    N2.AddChild(_LXHostTimout).Text := Hosts.Host[i].LXHostTimout;
    N2.AddChild(_TransactionAllowed).Text := Hosts.Host[i].TransactionAllowed;
    N2.AddChild(_OnlineTimout).Text := Hosts.Host[i].OnlineTimout;
    N2.AddChild(_OfflineForwardDelay).Text := Hosts.Host[i].OfflineForwardDelay;
    N2.AddChild(_OfflineForwardSecondsBetweenForwards).Text := Hosts.Host[i].OfflineForwardSecondsBetweenForwards;
    N2.AddChild(_TORCycleDelay).Text := Hosts.Host[i].TORCycleDelay;
    N2.AddChild(_OfflineForwardCycleDelay).Text := Hosts.Host[i].OfflineForwardCycleDelay;
    N2.AddChild(_SendHealthMessage).Text := Hosts.Host[i].SendHealthMessage;
    N2.AddChild(_CertegyPayroll).Text := Hosts.Host[i].CertegyPayroll;
    N2.AddChild(_HealthMessageInterval).Text := Hosts.Host[i].HealthMessageInterval;
    N2.AddChild(_TakeWICChecksLocally).Text := Hosts.Host[i].TakeWICChecksLocally;
    N2.AddChild(_LXHostSlotNumAndTimout).Text := Hosts.Host[i].LXHostSlotNumAndTimout;
    N2.AddChild(_LXHostOnlineStartingSlotNum).Text := Hosts.Host[i].LXHostOnlineStartingSlotNum;
    N2.AddChild(_PhoneCardMagStripe).Text := Hosts.Host[i].PhoneCardMagStripe;
    N2.AddChild(_PartialBINDownload).Text := Hosts.Host[i].PartialBINDownload;
    N2.AddChild(_PBTHostMerchantID).Text := Hosts.Host[i].PBTHostMerchantID;
    N2.AddChild(_FTPUserHName).Text := Hosts.Host[i].FTPUserHName;
    N2.AddChild(_FTPIPAddress).Text := Hosts.Host[i].FTPIPAddress;
    N2.AddChild(_FTPPassword).Text := Hosts.Host[i].FTPPassword;
    N2.AddChild(_FTPFileName).Text := Hosts.Host[i].FTPFileName;
    N2.AddChild(_AddressLines).Text := Hosts.Host[i].AddressLines;
    N2.AddChild(_ProcessingMode).Text := Hosts.Host[i].ProcessingMode;
    N2.AddChild(_SendSwipedDLInfo).Text := Hosts.Host[i].SendSwipedDLInfo;
    N2.AddChild(_PrimaryLocalPort).Text := Hosts.Host[i].PrimaryLocalPort;
    N2.AddChild(_BackupLocalPort).Text := Hosts.Host[i].BackupLocalPort;
    N2.AddChild(_LocalIPforTerminalConnect).Text := Hosts.Host[i].LocalIPforTerminalConnect;
    N2.AddChild(_FTPInterval).Text := Hosts.Host[i].FTPInterval;
    N2.AddChild(_SettlementReport).Text := Hosts.Host[i].SettlementReport;
    N2.AddChild(_ForceTenderOffline).Text := Hosts.Host[i].ForceTenderOffline;
    N2.AddChild(_ReceiptFileTransferType).Text := Hosts.Host[i].ReceiptFileTransferType;
    N2.AddChild(_ReceiptHostPrimaryIPAddress).Text := Hosts.Host[i].ReceiptHostPrimaryIPAddress;
    N2.AddChild(_ReceiptHostPrimaryFTPPort).Text := Hosts.Host[i].ReceiptHostPrimaryFTPPort;
    N2.AddChild(_ReceiptHostBackupIPAddress).Text := Hosts.Host[i].ReceiptHostBackupIPAddress;
    N2.AddChild(_ReceiptHostBackupFTPPort).Text := Hosts.Host[i].ReceiptHostBackupFTPPort;
    N2.AddChild(_ReceiptHostUserID).Text := Hosts.Host[i].ReceiptHostUserID;
    N2.AddChild(_ReceiptHostPassword).Text := Hosts.Host[i].ReceiptHostPassword;
    N2.AddChild(_ReceiptHostFileName).Text := Hosts.Host[i].ReceiptHostFileName;
    N2.AddChild(_ReceiptHostFTPPath).Text := Hosts.Host[i].ReceiptHostFTPPath;
    N2.AddChild(_TruncateAcctNo).Text := Hosts.Host[i].TruncateAcctNo;
    N2.AddChild(_SendRawMICR).Text := Hosts.Host[i].SendRawMICR;
    N2.AddChild(_HostSuspendCt).Text := Hosts.Host[i].HostSuspendCt;
    N2.AddChild(_HostFatalCt).Text := Hosts.Host[i].HostFatalCt;
    N2.AddChild(_HostResumeCt).Text := Hosts.Host[i].HostResumeCt;
    N2.AddChild(_HostOnTOxxxx).Text := Hosts.Host[i].HostOnTOxxxx;
    N2.AddChild(_Host_Comm_NRZI).Text := Hosts.Host[i].Host_Comm_NRZI;
    N2.AddChild(_Host_Main_Session).Text := Hosts.Host[i].Host_Main_Session;
    N2.AddChild(_Host_TOR_Session).Text := Hosts.Host[i].Host_TOR_Session;
    N2.AddChild(_Host_Offl_Session).Text := Hosts.Host[i].Host_Offl_Session;
    N2.AddChild(_HostPort).Text := Hosts.Host[i].HostPort;
    N2.AddChild(_HostBits).Text := Hosts.Host[i].HostBits;
    N2.AddChild(_HostParity).Text := Hosts.Host[i].HostParity;
    N2.AddChild(_HostStop).Text := Hosts.Host[i].HostStop;
    N2.AddChild(_HostIRQ).Text := Hosts.Host[i].HostIRQ;
    N2.AddChild(_HostMgtInfoData).Text := Hosts.Host[i].HostMgtInfoData;
    N2.AddChild(_HostTORLate).Text := Hosts.Host[i].HostTORLate;
    N2.AddChild(_HostTORTimeout).Text := Hosts.Host[i].HostTORTimeout;
    N2.AddChild(_HostTORNoT2).Text := Hosts.Host[i].HostTORNoT2;
    N2.AddChild(_HostRetryPhone1).Text := Hosts.Host[i].HostRetryPhone1;
    N2.AddChild(_HostRetryPhone2).Text := Hosts.Host[i].HostRetryPhone2;
    N2.AddChild(_HostModemInit).Text := Hosts.Host[i].HostModemInit;
    N2.AddChild(_HostInitMain).Text := Hosts.Host[i].HostInitMain;
    N2.AddChild(_TORInitSelfName).Text := Hosts.Host[i].TORInitSelfName;
    N2.AddChild(_OfflineInitSelfName).Text := Hosts.Host[i].OfflineInitSelfName;
    N2.AddChild(_HostConfigNum).Text := Hosts.Host[i].HostConfigNum;
    N2.AddChild(_HostSecondScrName).Text := Hosts.Host[i].HostSecondScrName;
    N2.AddChild(_FTP_BINFileFromHost).Text := Hosts.Host[i].FTP_BINFileFromHost;
    N2.AddChild(_HostDivision).Text := Hosts.Host[i].HostDivision;
    N2.AddChild(_HostPort2).Text := Hosts.Host[i].HostPort2;
    N2.AddChild(_HostBits2).Text := Hosts.Host[i].HostBits2;
    N2.AddChild(_HostParity2).Text := Hosts.Host[i].HostParity2;
    N2.AddChild(_HostStop2).Text := Hosts.Host[i].HostStop2;
    N2.AddChild(_HostIRQ2).Text := Hosts.Host[i].HostIRQ2;
    N2.AddChild(_HostPort3).Text := Hosts.Host[i].HostPort3;
    N2.AddChild(_HostBits3).Text := Hosts.Host[i].HostBits3;
    N2.AddChild(_HostParity3).Text := Hosts.Host[i].HostParity3;
    N2.AddChild(_HostStop3).Text := Hosts.Host[i].HostStop3;
    N2.AddChild(_HostIRQ3).Text := Hosts.Host[i].HostIRQ3;
    N2.AddChild(_HostSetForDayLight).Text := Hosts.Host[i].HostSetForDayLight;
    N2.AddChild(_HostTruncateAcctNo).Text := Hosts.Host[i].HostTruncateAcctNo;
    N2.AddChild(_HostPlatformID).Text := Hosts.Host[i].HostPlatformID;
    N2.AddChild(_HostClearingCode).Text := Hosts.Host[i].HostClearingCode;
    N2.AddChild(_HostMerchBIN).Text := Hosts.Host[i].HostMerchBIN;
    N2.AddChild(_HostMerchICA).Text := Hosts.Host[i].HostMerchICA;
    N2.AddChild(_HostIPLocalPort3).Text := Hosts.Host[i].HostIPLocalPort3;
    N2.AddChild(_HostLastBINFtpDate).Text := Hosts.Host[i].HostLastBINFtpDate;
    N2.AddChild(_HostProgramId).Text := Hosts.Host[i].HostProgramId;
    N2.AddChild(_HostFTPDir).Text := Hosts.Host[i].HostFTPDir;
    N2.AddChild(_HostERCUse).Text := Hosts.Host[i].HostERCUse;
    N2.AddChild(_FreqShopReqOfflineDebit).Text := Hosts.Host[i].FreqShopReqOfflineDebit; { YHJ-503 }
    N2.AddChild(_UseSSL).Text := Hosts.Host[i].UseSSL;
    N2.AddChild(_ConnectPayEnabled).Text   := YN(Hosts.Host[i].ConnectPayEnabled='Y'); { YHJ-508 }
    N2.AddChild(_ConnectPayPrefix).Text    := Hosts.Host[i].ConnectPayPrefix; { YHJ-508 }
    N2.AddChild(_HealthStatusProbationInterval).Text := Hosts.Host[i].HealthStatusProbationInterval;
    N2.AddChild(_HealthStatusNotOkInterval).Text     := Hosts.Host[i].HealthStatusNotOkInterval;
    N2.AddChild(_ProxyEnabled).Text := Hosts.Host[i].ProxyEnabled;
    N2.AddChild(_ProxyAddress).Text := Hosts.Host[i].ProxyAddress;
    N2.AddChild(_Use3DES).Text := YN(SameText(Hosts.Host[i].Use3DES, 'Y')); // DEV-7678
    N2.AddChild(_ReceivingInstituionId).Text := Hosts.Host[i].ReceivingInstituionId; // DEV-10155
    N2.AddChild(_ForwardAllChecksOnline).Text := YN(SameText(Hosts.Host[i].ForwardAllChecksOnline, 'Y'));
    N2.AddChild(_ForwardOtherChecks).Text := YN(SameText(Hosts.Host[i].ForwardOtherChecks, 'Y')); // DEV-11323 <
    N2.AddChild(_ForwardWICChecks).Text := YN(SameText(Hosts.Host[i].ForwardWICChecks, 'Y'));
    N2.AddChild(_MerchantPhoneNumber).Text := Hosts.Host[i].MerchantPhoneNumber; // DEV-11323 >
    N2.AddChild(_NetworkRoutingValue).Text := Hosts.Host[i].NetworkRoutingValue; // DEV-12382
    N2.AddChild(_OfflineForwardSecondsBetweenResubmits).Text := Hosts.Host[i].OfflineForwardSecondsBetweenResubmits; // DEV-12942
    N2.AddChild(_ForceOfflineByCardCode).Text := Hosts.Host[i].ForceOfflineByCardCode.CommaText; // DEV-18409
    //N2.AddChild(_DoNotForwardGiftCardForce).Text := YN(SameText(Hosts.Host[i].DoNotForwardGiftCardForce, 'Y')); // DEV-18499
    N2.AddChild(_FluCareCardCode).Text := Hosts.Host[i].FluCareCardCode;
    N2.AddChild(_FluCareUPC).Text := Hosts.Host[i].FluCareUPC;
    N2.AddChild(_DynamicCurrencyConversion).Text := Hosts.Host[i].DynamicCurrConv; // TFS-9730
  end; // end of for
end;

function TXMLStoreConfigurationsType.GetVersion: string;
begin
  Result := FVersion;
end;

procedure TXMLStoreConfigurationsType.SetVersion(Value: string);
begin
  if Value = FVersion then Exit;
  FVersion := Value;
end;

function TXMLStoreConfigurationsType.GetLastModified: string;
begin
  Result := FLastModified;
end;

procedure TXMLStoreConfigurationsType.SetLastModified(Value: string);
begin
  if Value = FLastModified then Exit;
  FLastModified := Value;
end;

function TXMLStoreConfigurationsType.GetProcessingOptions: TXMLProcessingOptionsType;
begin
  Result := FProcessingOptions;
end;

function TXMLStoreConfigurationsType.GetPrintSettings: TXMLPrintSettingsType;
begin
  Result := FPrintSettings;
end;

function TXMLStoreConfigurationsType.GetReceiptTexts: TXMLReceiptTextsType;     { YHJ-515 }
begin
  Result := FReceiptTexts;
end;

function TXMLStoreConfigurationsType.GetHosts: TXMLHostsType;
begin
  Result := FHosts;
end;

{ TXMLDialBackupConfigurationType }

constructor TXMLDialBackupConfigurationType.Create; // YHJ-843
begin
  inherited;
  Port := DialBackupDefaultPort;
  IdleTimeout := DialBackupDefaultIdleTimeout;
end;

{ TXMLProcessingOptionsType }

function TXMLProcessingOptionsType.GetPasswordExpirationDays: Integer;
begin
  Result := FPasswordExpirationDays;
end;

procedure TXMLProcessingOptionsType.SetPasswordExpirationDays(Value: Integer);
begin
  if Value = FPasswordExpirationDays then Exit;
  FPasswordExpirationDays := Value;
end;

{ TXMLPrintSettingsType }

constructor TXMLPrintSettingsType.Create;
begin
  inherited;
  FFontStyles := TXMLFontStylesType.Create;
end;

destructor TXMLPrintSettingsType.Destroy;
begin
  FreeAndNil(FFontStyles);
  inherited;
end;

function TXMLPrintSettingsType.GetFontName: string;
begin
  Result := FFontName;
end;

procedure TXMLPrintSettingsType.SetFontName(Value: string);
begin
  if Value = FFontName then Exit;
  FFontName := Value;
end;

function TXMLPrintSettingsType.GetFontSize: string;
begin
  Result := FFontSize;
end;

procedure TXMLPrintSettingsType.SetFontSize(Value: string);
begin
  if Value = FFontSize then Exit;
  FFontSize := Value;
end;

function TXMLPrintSettingsType.GetFontStyles: TXMLFontStylesType;
begin
  Result := FFontStyles;
end;

function TXMLPrintSettingsType.GetOrientation: string;
begin
  Result := FOrientation;
end;

procedure TXMLPrintSettingsType.SetOrientation(Value: string);
begin
  if Value = FOrientation then Exit;
  FOrientation := Value;
end;

function TXMLPrintSettingsType.GetNumberOfCopies: string;
begin
  Result := FNumberOfCopies;
end;

procedure TXMLPrintSettingsType.SetNumberOfCopies(Value: string);
begin
  FNumberOfCopies := Value;
end;

{ TXMLFontStylesType }

function TXMLFontStylesType.GetBold: string;
begin
  Result := FBold;
end;

procedure TXMLFontStylesType.SetBold(Value: string);
begin
  if Value = FBold then Exit;
  FBold := Value;
end;

function TXMLFontStylesType.GetItalic: string;
begin
  Result := FItalic;
end;

procedure TXMLFontStylesType.SetItalic(Value: string);
begin
  if Value = FItalic then Exit;
  FItalic := Value;
end;

function TXMLFontStylesType.GetUnderline: string;
begin
  Result := FUnderline;
end;

procedure TXMLFontStylesType.SetUnderline(Value: string);
begin
  if Value = FUnderline then Exit;
  FUnderline := Value;
end;

function TXMLFontStylesType.GetStrikeOut: string;
begin
  Result := FStrikeOut;
end;

procedure TXMLFontStylesType.SetStrikeOut(Value: string);
begin
  if Value = FStrikeOut then Exit;
  FStrikeOut := Value;
end;

{ TXMLReceiptTextsType }                                                        { YHJ-515 }

function TXMLReceiptTextsType.GetReceiptText(Index: Integer): TXMLReceiptTextType;
begin
  result := inherited Items[Index] as TXMLReceiptTextType;
end;

function TXMLReceiptTextsType.Add: TXMLReceiptTextType;
begin
  FReceiptText := TXMLReceiptTextType.Create(Self);
  result := FReceiptText;
end;

{ TXMLReceiptTextType }

function TXMLReceiptTextType.GetStoreNumber: string;                            { YHJ-515 }
begin
  Result := FStoreNumber;
end;

function TXMLReceiptTextType.GetReceiptHeaderLine1: string;
begin
  Result := FReceiptHeaderLine1;
end;

procedure TXMLReceiptTextType.SetStoreNumber(Value: string);                    { YHJ-515 }
begin
  if Value = FStoreNumber then Exit;
  FStoreNumber := Value;
end;

procedure TXMLReceiptTextType.SetReceiptHeaderLine1(Value: string);
begin
  if Value = FReceiptHeaderLine1 then Exit;
  FReceiptHeaderLine1 := Value;
end;

function TXMLReceiptTextType.GetReceiptHeaderLine2: string;
begin
  Result := FReceiptHeaderLine2;
end;

procedure TXMLReceiptTextType.SetReceiptHeaderLine2(Value: string);
begin
  if Value = FReceiptHeaderLine2 then Exit;
  FReceiptHeaderLine2 := Value;
end;

function TXMLReceiptTextType.GetReceiptHeaderLine3: string;
begin
  Result := FReceiptHeaderLine3;
end;

procedure TXMLReceiptTextType.SetReceiptHeaderLine3(Value: string);
begin
  if Value = FReceiptHeaderLine3 then Exit;
  FReceiptHeaderLine3 := Value;
end;

function TXMLReceiptTextType.GetReceiptHeaderLine4: string;
begin
  Result := FReceiptHeaderLine4;
end;

procedure TXMLReceiptTextType.SetReceiptHeaderLine4(Value: string);
begin
  if Value = FReceiptHeaderLine4 then Exit;
  FReceiptHeaderLine4 := Value;
end;

function TXMLReceiptTextType.GetReceiptFooterLine1: string;
begin
  Result := FReceiptFooterLine1;
end;

procedure TXMLReceiptTextType.SetReceiptFooterLine1(Value: string);
begin
  if Value = FReceiptFooterLine1 then Exit;
  FReceiptFooterLine1 := Value;
end;

function TXMLReceiptTextType.GetReceiptFooterLine2: string;
begin
  Result := FReceiptFooterLine2;
end;

procedure TXMLReceiptTextType.SetReceiptFooterLine2(Value: string);
begin
  if Value = FReceiptFooterLine2 then Exit;
  FReceiptFooterLine2 := Value;
end;

function TXMLReceiptTextType.GetCheckDepositLegend: string;
begin
  Result := FCheckDepositLegend;
end;

procedure TXMLReceiptTextType.SetCheckDepositLegend(Value: string);
begin
  if Value = FCheckDepositLegend then Exit;
  FCheckDepositLegend := Value;
end;

function TXMLReceiptTextType.GetCheckDepositBankName: string;
begin
  Result := FCheckDepositBankName;
end;

procedure TXMLReceiptTextType.SetCheckDepositBankName(Value: string);
begin
  if Value = FCheckDepositBankName then Exit;
  FCheckDepositBankName := Value;
end;

function TXMLReceiptTextType.GetCheckDepositAccountNumber: string;
begin
  Result := FCheckDepositAccountNumber;
end;

procedure TXMLReceiptTextType.SetCheckDepositAccountNumber(Value: string);
begin
  if Value = FCheckDepositAccountNumber then Exit;
  FCheckDepositAccountNumber := Value;
end;

function TXMLReceiptTextType.GetHospitalityIncludeTip: Boolean;
begin
  result := FHospitalityIncludeTip;
end;

procedure TXMLReceiptTextType.SetHospitalityIncludeTip(value: boolean);
begin
  FHospitalityIncludeTip := value;
end;
{ TXMLHostsType }

function TXMLHostsType.GetHost(Index: Integer): TXMLHostType;
begin
  result := inherited Items[Index] as TXMLHostType;
end;

function TXMLHostsType.Add: TXMLHostType;
begin
  FHost := TXMLHostType.Create(Self);
  Result := FHost;
end;

{ TXMLHostType }

function TXMLHostType.GetPrefix_: string;
begin
  Result := FPrefix;
end;

procedure TXMLHostType.SetPrefix(Value: string);
begin
  if Value = FPrefix then Exit;
  FPrefix := Value;
end;

function TXMLHostType.GetSuffix: string;
begin
  Result := FSuffix;
end;

procedure TXMLHostType.SetSuffix(Value: string);
begin
  if Value = FSuffix then Exit;
  FSuffix := Value;
end;
{
function TXMLHostType.GetSeqNo: Integer;
begin
  Result := FSeqNo;
end;

procedure TXMLHostType.SetSeqNo(Value: Integer);
begin
  if Value = FSeqNo then Exit;
  FSeqNo := Value;
end;
}
function TXMLHostType.GetRetryCounter: string;
begin
  Result := FRetryCounter;
end;

procedure TXMLHostType.SetRetryCounter(Value: string);
begin
  if Value = FRetryCounter then Exit;
  FRetryCounter := Value;
end;

function TXMLHostType.GetOfflineProcessing: string;
begin
  Result := FOfflineProcessing;
end;

procedure TXMLHostType.SetOfflineProcessing(Value: string);
begin
  if Value = FOfflineProcessing then Exit;
  FOfflineProcessing := Value;
end;

function TXMLHostType.GetSimulatedDelay: string;
begin
  Result := FSimulatedDelay;
end;

procedure TXMLHostType.SetSimulatedDelay(Value: string);
begin
  if Value = FSimulatedDelay then Exit;
  FSimulatedDelay := Value;
end;

function TXMLHostType.GetWorkingKey: string;
begin
  Result := FWorkingKey;
end;

procedure TXMLHostType.SetWorkingKey(Value: string);
begin
  if Value = FWorkingKey then Exit;
  FWorkingKey := Value;
end;

function TXMLHostType.GetUseEBCDIC: string;
begin
  Result := FUseEBCDIC;
end;

procedure TXMLHostType.SetUseEBCDIC(Value: string);
begin
  if Value = FUseEBCDIC then Exit;
  FUseEBCDIC := Value;
end;

function TXMLHostType.GetMerchantNum: string;
begin
  Result := FMerchantNum;
end;

procedure TXMLHostType.SetMerchantNum(Value: string);
begin
  if Value = FMerchantNum then Exit;
  FMerchantNum := Value;
end;

function TXMLHostType.GetPassword: string;
begin
  Result := FPassword;
end;

procedure TXMLHostType.SetPassword(Value: string);
begin
  if Value = FPassword then Exit;
  FPassword := Value;
end;

function TXMLHostType.GetChkAuthType: string;
begin
  Result := FChkAuthType;
end;

procedure TXMLHostType.SetChkAuthType(Value: string);
begin
  if Value = FChkAuthType then Exit;
  FChkAuthType := Value;
end;

function TXMLHostType.GetChkCode: string;
begin
  Result := FChkCode;
end;

procedure TXMLHostType.SetChkCode(Value: string);
begin
  if Value = FChkCode then Exit;
  FChkCode := Value;
end;

function TXMLHostType.GetChkDefaultStateCode: string;
begin
  Result := FChkDefaultStateCode;
end;

procedure TXMLHostType.SetChkDefaultStateCode(Value: string);
begin
  if Value = FChkDefaultStateCode then Exit;
  FChkDefaultStateCode := Value;
end;

function TXMLHostType.GetVerifyDebitPrefixOnline: string;
begin
  Result := FVerifyDebitPrefixOnline;
end;

procedure TXMLHostType.SetVerifyDebitPrefixOnline(Value: string);
begin
  if Value = FVerifyDebitPrefixOnline then Exit;
  FVerifyDebitPrefixOnline := Value;
end;

function TXMLHostType.GetVerifyDebitPrefixOffline: string;
begin
  Result := FVerifyDebitPrefixOffline;
end;

procedure TXMLHostType.SetVerifyDebitPrefixOffline(Value: string);
begin
  if Value = FVerifyDebitPrefixOffline then Exit;
  FVerifyDebitPrefixOffline := Value;
end;

function TXMLHostType.GetLXHostSlotNumbers: string;
begin
  Result := FLXHostSlotNumbers;
end;

procedure TXMLHostType.SetLXHostSlotNumbers(Value: string);
begin
  if Value = FLXHostSlotNumbers then Exit;
  FLXHostSlotNumbers := Value;
end;

function TXMLHostType.GetStoreNumber: string;
begin
  Result := FStoreNumber;
end;

procedure TXMLHostType.SetStoreNumber(Value: string);
begin
  if Value = FStoreNumber then Exit;
  FStoreNumber := Value;
end;

function TXMLHostType.GetTerminalID: string;
begin
  Result := FTerminalID;
end;

procedure TXMLHostType.SetTerminalID(Value: string);
begin
  if Value = FTerminalID then Exit;
  FTerminalID := Value;
end;

function TXMLHostType.GetHostManagmentInfo: string;
begin
  Result := FHostManagmentInfo;
end;

procedure TXMLHostType.SetHostManagmentInfo(Value: string);
begin
  if Value = FHostManagmentInfo then Exit;
  FHostManagmentInfo := Value;
end;

function TXMLHostType.GetMerchantType: string;
begin
  Result := FMerchantType;
end;

procedure TXMLHostType.SetMerchantType(Value: string);
begin
  if Value = FMerchantType then Exit;
  FMerchantType := Value;
end;

function TXMLHostType.GetHostID: string;
begin
  Result := FHostID;
end;

procedure TXMLHostType.SetHostID(Value: string);
begin
  if Value = FHostID then Exit;
  FHostID := Value;
end;

function TXMLHostType.GetMerchantName: string;
begin
  Result := FMerchantName;
end;

procedure TXMLHostType.SetMerchantName(Value: string);
begin
  if Value = FMerchantName then Exit;
  FMerchantName := Value;
end;

function TXMLHostType.GetMerchantAddress: string;
begin
  Result := FMerchantAddress;
end;

procedure TXMLHostType.SetMerchantAddress(Value: string);
begin
  if Value = FMerchantAddress then Exit;
  FMerchantAddress := Value;
end;

function TXMLHostType.GetMerchantCity: string;
begin
  Result := FMerchantCity;
end;

procedure TXMLHostType.SetMerchantCity(Value: string);
begin
  if Value = FMerchantCity then Exit;
  FMerchantCity := Value;
end;

function TXMLHostType.GetMerchantState: string;
begin
  Result := FMerchantState;
end;

procedure TXMLHostType.SetMerchantState(Value: string);
begin
  if Value = FMerchantState then Exit;
  FMerchantState := Value;
end;

function TXMLHostType.GetMerchantZipcode: string;
begin
  Result := FMerchantZipcode;
end;

procedure TXMLHostType.SetMerchantZipcode(Value: string);
begin
  if Value = FMerchantZipcode then Exit;
  FMerchantZipcode := Value;
end;

function TXMLHostType.GetMerchantCountry: string;
begin
  Result := FMerchantCountry;
end;

procedure TXMLHostType.SetMerchantCountry(Value: string);
begin
  if Value = FMerchantCountry then Exit;
  FMerchantCountry := Value;
end;

function TXMLHostType.GetCurrencyCode: string;
begin
  Result := FCurrencyCode;
end;

procedure TXMLHostType.SetCurrencyCode(Value: string);
begin
  if Value = FCurrencyCode then Exit;
  FCurrencyCode := Value;
end;

function TXMLHostType.GetNumericStateCode: string;
begin
  Result := FNumericStateCode;
end;

procedure TXMLHostType.SetNumericStateCode(Value: string);
begin
  if Value = FNumericStateCode then Exit;
  FNumericStateCode := Value;
end;

function TXMLHostType.GetNumericCountryCode: string;
begin
  Result := FNumericCountryCode;
end;

procedure TXMLHostType.SetNumericCountryCode(Value: string);
begin
  if Value = FNumericCountryCode then Exit;
  FNumericCountryCode := Value;
end;

function TXMLHostType.GetGMTOffset: string;
begin
  Result := FGMTOffset;
end;

procedure TXMLHostType.SetGMTOffset(Value: string);
begin
  if Value = FGMTOffset then Exit;
  FGMTOffset := Value;
end;

function TXMLHostType.GetSendCashierToHost: string;
begin
  Result := FSendCashierToHost;
end;

procedure TXMLHostType.SetSendCashierToHost(Value: string);
begin
  if Value = FSendCashierToHost then Exit;
  FSendCashierToHost := Value;
end;

function TXMLHostType.GetTraceMsgsToLog: string;
begin
  Result := FTraceMsgsToLog;
end;

procedure TXMLHostType.SetTraceMsgsToLog(Value: string);
begin
  if Value = FTraceMsgsToLog then Exit;
  FTraceMsgsToLog := Value;
end;

function TXMLHostType.GetResubmitOfflineForwards: string;
begin
  Result := FResubmitOfflineForwards;
end;

procedure TXMLHostType.SetResubmitOfflineForwards(Value: string);
begin
  if Value = FResubmitOfflineForwards then Exit;
  FResubmitOfflineForwards := Value;
end;

function TXMLHostType.GetTerminalType: string;
begin
  Result := FTerminalType;
end;

procedure TXMLHostType.SetTerminalType(Value: string);
begin
  if Value = FTerminalType then Exit;
  FTerminalType := Value;
end;

function TXMLHostType.GetMerchantNumber: string;
begin
  Result := FMerchantNumber;
end;

procedure TXMLHostType.SetMerchantNumber(Value: string);
begin
  if Value = FMerchantNumber then Exit;
  FMerchantNumber := Value;
end;

function TXMLHostType.GetTerminalNumber: string;
begin
  Result := FTerminalNumber;
end;

procedure TXMLHostType.SetTerminalNumber(Value: string);
begin
  if Value = FTerminalNumber then Exit;
  FTerminalNumber := Value;
end;

function TXMLHostType.GetPrimaryHostPort: string;
begin
  Result := FPrimaryHostPort;
end;

procedure TXMLHostType.SetPrimaryHostPort(Value: string);
begin
  if Value = FPrimaryHostPort then Exit;
  FPrimaryHostPort := Value;
end;

function TXMLHostType.GetPrimaryHostIPAddress: string;
begin
  Result := FPrimaryHostIPAddress;
end;

procedure TXMLHostType.SetPrimaryHostIPAddress(Value: string);
begin
  if Value = FPrimaryHostIPAddress then Exit;
  FPrimaryHostIPAddress := Value;
end;

function TXMLHostType.GetBackupHostPort: string;
begin
  Result := FBackupHostPort;
end;

procedure TXMLHostType.SetBackupHostPort(Value: string);
begin
  if Value = FBackupHostPort then Exit;
  FBackupHostPort := Value;
end;

function TXMLHostType.GetBackupHostIPAddress: string;
begin
  Result := FBackupHostIPAddress;
end;

procedure TXMLHostType.SetBackupHostIPAddress(Value: string);
begin
  if Value = FBackupHostIPAddress then Exit;
  FBackupHostIPAddress := Value;
end;

function TXMLHostType.GetKeepCheckHistory: string;
begin
  Result := FKeepCheckHistory;
end;

procedure TXMLHostType.SetKeepCheckHistory(Value: string);
begin
  if Value = FKeepCheckHistory then Exit;
  FKeepCheckHistory := Value;
end;

function TXMLHostType.GetPrimaryGCHostIP: string;
begin
  Result := FPrimaryGCHostIP;
end;

procedure TXMLHostType.SetPrimaryGCHostIP(Value: string);
begin
  if Value = FPrimaryGCHostIP then Exit;
  FPrimaryGCHostIP := Value;
end;

function TXMLHostType.GetBackupGCHostIP: string;
begin
  Result := FBackupGCHostIP;
end;

procedure TXMLHostType.SetBackupGCHostIP(Value: string);
begin
  if Value = FBackupGCHostIP then Exit;
  FBackupGCHostIP := Value;
end;

function TXMLHostType.GetLXHostTimout: string;
begin
  Result := FLXHostTimout;
end;

procedure TXMLHostType.SetLXHostTimout(Value: string);
begin
  if Value = FLXHostTimout then Exit;
  FLXHostTimout := Value;
end;

function TXMLHostType.GetTransactionAllowed: string;
begin
  Result := FTransactionAllowed;
end;

procedure TXMLHostType.SetTransactionAllowed(Value: string);
begin
  if Value = FTransactionAllowed then Exit;
  FTransactionAllowed := Value;
end;

function TXMLHostType.GetOnlineTimout: string;
begin
  Result := FOnlineTimout;
end;

procedure TXMLHostType.SetOnlineTimout(Value: string);
begin
  if Value = FOnlineTimout then Exit;
  FOnlineTimout := Value;
end;

function TXMLHostType.GetOfflineForwardDelay: string;
begin
  Result := FOfflineForwardDelay;
end;

procedure TXMLHostType.SetOfflineForwardDelay(Value: string);
begin
  if Value = FOfflineForwardDelay then Exit;
  FOfflineForwardDelay := Value;
end;

function TXMLHostType.GetOfflineForwardSecondsBetweenForwards: string;
begin
  Result := FOfflineForwardSecondsBetweenForwards;
end;

procedure TXMLHostType.SetOfflineForwardSecondsBetweenForwards(Value: string);
begin
  if Value = FOfflineForwardSecondsBetweenForwards then Exit;
  FOfflineForwardSecondsBetweenForwards := Value;
end;

function TXMLHostType.GetTORCycleDelay: string;
begin
  Result := FTORCycleDelay;
end;

procedure TXMLHostType.SetTORCycleDelay(Value: string);
begin
  if Value = FTORCycleDelay then Exit;
  FTORCycleDelay := Value;
end;

function TXMLHostType.GetOfflineForwardCycleDelay: string;
begin
  Result := FOfflineForwardCycleDelay;
end;

procedure TXMLHostType.SetOfflineForwardCycleDelay(Value: string);
begin
  if Value = FOfflineForwardCycleDelay then Exit;
  FOfflineForwardCycleDelay := Value;
end;

function TXMLHostType.GetSendHealthMessage: string;
begin
  Result := FSendHealthMessage;
end;

procedure TXMLHostType.SetSendHealthMessage(Value: string);
begin
  if Value = FSendHealthMessage then Exit;
  FSendHealthMessage := Value;
end;

function TXMLHostType.GetHealthMessageInterval: string;
begin
  Result := FHealthMessageInterval;
end;

procedure TXMLHostType.SetHealthMessageInterval(Value: string);
begin
  if Value = FHealthMessageInterval then Exit;
  FHealthMessageInterval := Value;
end;

function TXMLHostType.GetTakeWICChecksLocally: string;
begin
  Result := FTakeWICChecksLocally;
end;

procedure TXMLHostType.SetTakeWICChecksLocally(Value: string);
begin
  if Value = FTakeWICChecksLocally then Exit;
  FTakeWICChecksLocally := Value;
end;

function TXMLHostType.GetLXHostSlotNumAndTimout: string;
begin
  Result := FLXHostSlotNumAndTimout;
end;

procedure TXMLHostType.SetLXHostSlotNumAndTimout(Value: string);
begin
  if Value = FLXHostSlotNumAndTimout then Exit;
  FLXHostSlotNumAndTimout := Value;
end;

function TXMLHostType.GetLXHostOnlineStartingSlotNum: string;
begin
  Result := FLXHostOnlineStartingSlotNum;
end;

procedure TXMLHostType.SetLXHostOnlineStartingSlotNum(Value: string);
begin
  if Value = FLXHostOnlineStartingSlotNum then Exit;
  FLXHostOnlineStartingSlotNum := Value;
end;

function TXMLHostType.GetPhoneCardMagStripe: string;
begin
  Result := FPhoneCardMagStripe;
end;

procedure TXMLHostType.SetPhoneCardMagStripe(Value: string);
begin
  if Value = FPhoneCardMagStripe then Exit;
  FPhoneCardMagStripe := Value;
end;

function TXMLHostType.GetPartialBINDownload: string;
begin
  Result := FPartialBINDownload;
end;

procedure TXMLHostType.SetPartialBINDownload(Value: string);
begin
  if Value = FPartialBINDownload then Exit;
  FPartialBINDownload := Value;
end;

function TXMLHostType.GetPBTHostMerchantID: string;
begin
  Result := FPBTHostMerchantID;
end;

procedure TXMLHostType.SetPBTHostMerchantID(Value: string);
begin
  if Value = FPBTHostMerchantID then Exit;
  FPBTHostMerchantID := Value;
end;

function TXMLHostType.GetFTPUserHName: string;
begin
  Result := FFTPUserHName;
end;

procedure TXMLHostType.SetFTPUserHName(Value: string);
begin
  if Value = FFTPUserHName then Exit;
  FFTPUserHName := Value;
end;

function TXMLHostType.GetFTPIPAddress: string;
begin
  Result := FFTPIPAddress;
end;

procedure TXMLHostType.SetFTPIPAddress(Value: string);
begin
  if Value = FFTPIPAddress then Exit;
  FFTPIPAddress := Value;
end;

function TXMLHostType.GetFTPPassword: string;
begin
  Result := FFTPPassword;
end;

procedure TXMLHostType.SetFTPPassword(Value: string);
begin
  if Value = FFTPPassword then Exit;
  FFTPPassword := Value;
end;

function TXMLHostType.GetFTPFileName: string;
begin
  Result := FFTPFileName;
end;

procedure TXMLHostType.SetFTPFileName(Value: string);
begin
  if Value = FFTPFileName then Exit;
  FFTPFileName := Value;
end;

function TXMLHostType.GetAddressLines: string;
begin
  Result := FAddressLines;
end;

procedure TXMLHostType.SetAddressLines(Value: string);
begin
  if Value = FAddressLines then Exit;
  FAddressLines := Value;
end;

function TXMLHostType.GetProcessingMode: string;
begin
  Result := FProcessingMode;
end;

procedure TXMLHostType.SetProcessingMode(Value: string);
begin
  if Value = FProcessingMode then Exit;
  FProcessingMode := Value;
end;

function TXMLHostType.GetSendSwipedDLInfo: string;
begin
  Result := FSendSwipedDLInfo;
end;

procedure TXMLHostType.SetSendSwipedDLInfo(Value: string);
begin
  if Value = FSendSwipedDLInfo then Exit;
  FSendSwipedDLInfo := Value;
end;

function TXMLHostType.GetPrimaryLocalPort: string;
begin
  Result := FPrimaryLocalPort;
end;

procedure TXMLHostType.SetPrimaryLocalPort(Value: string);
begin
  if Value = FPrimaryLocalPort then Exit;
  FPrimaryLocalPort := Value;
end;

function TXMLHostType.GetBackupLocalPort: string;
begin
  Result := FBackupLocalPort;
end;

procedure TXMLHostType.SetBackupLocalPort(Value: string);
begin
  if Value = FBackupLocalPort then Exit;
  FBackupLocalPort := Value;
end;

function TXMLHostType.GetLocalIPforTerminalConnect: string;
begin
  Result := FLocalIPforTerminalConnect;
end;

procedure TXMLHostType.SetLocalIPforTerminalConnect(Value: string);
begin
  if Value = FLocalIPforTerminalConnect then Exit;
  FLocalIPforTerminalConnect := Value;
end;

function TXMLHostType.GetFTPInterval: string;
begin
  Result := FFTPInterval;
end;

procedure TXMLHostType.SetFTPInterval(Value: string);
begin
  if Value = FFTPInterval then Exit;
  FFTPInterval := Value;
end;

function TXMLHostType.GetSettlementReport: string;
begin
  Result := FSettlementReport;
end;

procedure TXMLHostType.SetSettlementReport(Value: string);
begin
  if Value = FSettlementReport then Exit;
  FSettlementReport := Value;
end;

function TXMLHostType.GetForceTenderOffline: string;
begin
  Result := FForceTenderOffline;
end;

procedure TXMLHostType.SetForceTenderOffline(Value: string);
begin
  if Value = FForceTenderOffline then Exit;
  FForceTenderOffline := Value;
end;

function TXMLHostType.GetReceiptFileTransferType: string;
begin
  Result := FReceiptFileTransferType;
end;

procedure TXMLHostType.SetReceiptFileTransferType(Value: string);
begin
  if Value = FReceiptFileTransferType then Exit;
  FReceiptFileTransferType := Value;
end;

function TXMLHostType.GetReceiptHostPrimaryIPAddress: string;
begin
  Result := FReceiptHostPrimaryIPAddress;
end;

procedure TXMLHostType.SetReceiptHostPrimaryIPAddress(Value: string);
begin
  if Value = FReceiptHostPrimaryIPAddress then Exit;
  FReceiptHostPrimaryIPAddress := Value;
end;

function TXMLHostType.GetReceiptHostPrimaryFTPPort: string;
begin
  Result := FReceiptHostPrimaryFTPPort;
end;

procedure TXMLHostType.SetReceiptHostPrimaryFTPPort(Value: string);
begin
  if Value = FReceiptHostPrimaryFTPPort then Exit;
  FReceiptHostPrimaryFTPPort := Value;
end;

function TXMLHostType.GetReceiptHostBackupIPAddress: string;
begin
  Result := FReceiptHostBackupIPAddress;
end;

procedure TXMLHostType.SetReceiptHostBackupIPAddress(Value: string);
begin
  if Value = FReceiptHostBackupIPAddress then Exit;
  FReceiptHostBackupIPAddress := Value;
end;

function TXMLHostType.GetReceiptHostBackupFTPPort: string;
begin
  Result := FReceiptHostBackupFTPPort;
end;

procedure TXMLHostType.SetReceiptHostBackupFTPPort(Value: string);
begin
  if Value = FReceiptHostBackupFTPPort then Exit;
  FReceiptHostBackupFTPPort := Value;
end;

function TXMLHostType.GetReceiptHostUserID: string;
begin
  Result := FReceiptHostUserID;
end;

procedure TXMLHostType.SetReceiptHostUserID(Value: string);
begin
  if Value = FReceiptHostUserID then Exit;
  FReceiptHostUserID := Value;
end;

function TXMLHostType.GetReceiptHostPassword: string;
begin
  Result := FReceiptHostPassword;
end;

procedure TXMLHostType.SetReceiptHostPassword(Value: string);
begin
  if Value = FReceiptHostPassword then Exit;
  FReceiptHostPassword := Value;
end;

function TXMLHostType.GetReceiptHostFileName: string;
begin
  Result := FReceiptHostFileName;
end;

procedure TXMLHostType.SetReceiptHostFileName(Value: string);
begin
  if Value = FReceiptHostFileName then Exit;
  FReceiptHostFileName := Value;
end;

function TXMLHostType.GetReceiptHostFTPPath: string;
begin
  Result := FReceiptHostFTPPath;
end;

procedure TXMLHostType.SetReceiptHostFTPPath(Value: string);
begin
  if Value = FReceiptHostFTPPath then Exit;
  FReceiptHostFTPPath := Value;
end;

function TXMLHostType.GetTruncateAcctNo: string;
begin
  Result := FTruncateAcctNo;
end;

procedure TXMLHostType.SetTruncateAcctNo(Value: string);
begin
  if Value = FTruncateAcctNo then Exit;
  FTruncateAcctNo := Value;
end;

function TXMLHostType.GetSendRawMICR: string;
begin
  Result := FSendRawMICR;
end;

procedure TXMLHostType.SetSendRawMICR(Value: string);
begin
  if Value = FSendRawMICR then Exit;
  FSendRawMICR := Value;
end;

function TXMLHostType.GetHostSuspendCt: string;
begin
  Result := FHostSuspendCt;
end;

procedure TXMLHostType.SetHostSuspendCt(Value: string);
begin
  if Value = FHostSuspendCt then Exit;
  FHostSuspendCt := Value;
end;

function TXMLHostType.GetHostFatalCt: string;
begin
  Result := FHostFatalCt;
end;

procedure TXMLHostType.SetHostFatalCt(Value: string);
begin
  if Value = FHostFatalCt then Exit;
  FHostFatalCt := Value;
end;

function TXMLHostType.GetHostResumeCt: string;
begin
  Result := FHostResumeCt;
end;

procedure TXMLHostType.SetHostResumeCt(Value: string);
begin
  if Value = FHostResumeCt then Exit;
  FHostResumeCt := Value;
end;

function TXMLHostType.GetHostOnTOxxxx: string;
begin
  Result := FHostOnTOxxxx;
end;

procedure TXMLHostType.SetHostOnTOxxxx(Value: string);
begin
  if Value = FHostOnTOxxxx then Exit;
  FHostOnTOxxxx := Value;
end;

function TXMLHostType.GetHost_Comm_NRZI: string;
begin
  Result := FHost_Comm_NRZI;
end;

procedure TXMLHostType.SetHost_Comm_NRZI(Value: string);
begin
  if Value = FHost_Comm_NRZI then Exit;
  FHost_Comm_NRZI := Value;
end;

function TXMLHostType.GetHost_Main_Session: string;
begin
  Result := FHost_Main_Session;
end;

procedure TXMLHostType.SetHost_Main_Session(Value: string);
begin
  if Value = FHost_Main_Session then Exit;
  FHost_Main_Session := Value;
end;

function TXMLHostType.GetHost_TOR_Session: string;
begin
  Result := FHost_TOR_Session;
end;

procedure TXMLHostType.SetHost_TOR_Session(Value: string);
begin
  if Value = FHost_TOR_Session then Exit;
  FHost_TOR_Session := Value;
end;

function TXMLHostType.GetHost_Offl_Session: string;
begin
  Result := FHost_Offl_Session;
end;

procedure TXMLHostType.SetHost_Offl_Session(Value: string);
begin
  if Value = FHost_Offl_Session then Exit;
  FHost_Offl_Session := Value;
end;

function TXMLHostType.GetHostPort: string;
begin
  Result := FHostPort;
end;

procedure TXMLHostType.SetHostPort(Value: string);
begin
  if Value = FHostPort then Exit;
  FHostPort := Value;
end;

function TXMLHostType.GetHostBits: string;
begin
  Result := FHostBits;
end;

procedure TXMLHostType.SetHostBits(Value: string);
begin
  if Value = FHostBits then Exit;
  FHostBits := Value;
end;

function TXMLHostType.GetHostParity: string;
begin
  Result := FHostParity;
end;

procedure TXMLHostType.SetHostParity(Value: string);
begin
  if Value = FHostParity then Exit;
  FHostParity := Value;
end;

function TXMLHostType.GetHostStop: string;
begin
  Result := FHostStop;
end;

procedure TXMLHostType.SetHostStop(Value: string);
begin
  if Value = FHostStop then Exit;
  FHostStop := Value;
end;

function TXMLHostType.GetHostIRQ: string;
begin
  Result := FHostIRQ;
end;

procedure TXMLHostType.SetHostIRQ(Value: string);
begin
  if Value = FHostIRQ then Exit;
  FHostIRQ := Value;
end;

function TXMLHostType.GetHostMgtInfoData: string;
begin
  Result := FHostMgtInfoData;
end;

procedure TXMLHostType.SetHostMgtInfoData(Value: string);
begin
  if Value = FHostMgtInfoData then Exit;
  FHostMgtInfoData := Value;
end;

function TXMLHostType.GetHostTORLate: string;
begin
  Result := FHostTORLate;
end;

procedure TXMLHostType.SetHostTORLate(Value: string);
begin
  if Value = FHostTORLate then Exit;
  FHostTORLate := Value;
end;

function TXMLHostType.GetHostTORTimeout: string;
begin
  Result := FHostTORTimeout;
end;

procedure TXMLHostType.SetHostTORTimeout(Value: string);
begin
  if Value = FHostTORTimeout then Exit;
  FHostTORTimeout := Value;
end;

function TXMLHostType.GetHostTORNoT2: string;
begin
  Result := FHostTORNoT2;
end;

procedure TXMLHostType.SetHostTORNoT2(Value: string);
begin
  if Value = FHostTORNoT2 then Exit;
  FHostTORNoT2 := Value;
end;

function TXMLHostType.GetHostRetryPhone1: string;
begin
  Result := FHostRetryPhone1;
end;

procedure TXMLHostType.SetHostRetryPhone1(Value: string);
begin
  if Value = FHostRetryPhone1 then Exit;
  FHostRetryPhone1 := Value;
end;

function TXMLHostType.GetHostRetryPhone2: string;
begin
  Result := FHostRetryPhone2;
end;

procedure TXMLHostType.SetHostRetryPhone2(Value: string);
begin
  if Value = FHostRetryPhone2 then Exit;
  FHostRetryPhone2 := Value;
end;

function TXMLHostType.GetHostModemInit: string;
begin
  Result := FHostModemInit;
end;

procedure TXMLHostType.SetHostModemInit(Value: string);
begin
  if Value = FHostModemInit then Exit;
  FHostModemInit := Value;
end;

function TXMLHostType.GetHostInitMain: string;
begin
  Result := FHostInitMain;
end;

procedure TXMLHostType.SetHostInitMain(Value: string);
begin
  if Value = FHostInitMain then Exit;
  FHostInitMain := Value;
end;

function TXMLHostType.GetTORInitSelfName: string;
begin
  Result := FTORInitSelfName;
end;

procedure TXMLHostType.SetTORInitSelfName(Value: string);
begin
  if Value = FTORInitSelfName then Exit;
  FTORInitSelfName := Value;
end;

function TXMLHostType.GetOfflineInitSelfName: string;
begin
  Result := FOfflineInitSelfName;
end;

procedure TXMLHostType.SetOfflineInitSelfName(Value: string);
begin
  if Value = FOfflineInitSelfName then Exit;
  FOfflineInitSelfName := Value;
end;

function TXMLHostType.GetHostConfigNum: string;
begin
  Result := FHostConfigNum;
end;

procedure TXMLHostType.SetHostConfigNum(Value: string);
begin
  if Value = FHostConfigNum then Exit;
  FHostConfigNum := Value;
end;

function TXMLHostType.GetHostSecondScrName: string;
begin
  Result := FHostSecondScrName;
end;

procedure TXMLHostType.SetHostSecondScrName(Value: string);
begin
  if Value = FHostSecondScrName then Exit;
  FHostSecondScrName := Value;
end;

function TXMLHostType.GetFTP_BINFileFromHost: string;
begin
  Result := FFTP_BINFileFromHost;
end;

procedure TXMLHostType.SetFTP_BINFileFromHost(Value: string);
begin
  if Value = FFTP_BINFileFromHost then Exit;
  FFTP_BINFileFromHost := Value;
end;

function TXMLHostType.GetHostDivision: string;
begin
  Result := FHostDivision;
end;

procedure TXMLHostType.SetHostDivision(Value: string);
begin
  if Value = FHostDivision then Exit;
  FHostDivision := Value;
end;

function TXMLHostType.GetHostPort2: string;
begin
  Result := FHostPort2;
end;

procedure TXMLHostType.SetHostPort2(Value: string);
begin
  if Value = FHostPort2 then Exit;
  FHostPort2 := Value;
end;

function TXMLHostType.GetHostBits2: string;
begin
  Result := FHostBits2;
end;

procedure TXMLHostType.SetHostBits2(Value: string);
begin
  if Value = FHostBits2 then Exit;
  FHostBits2 := Value;
end;

function TXMLHostType.GetHostParity2: string;
begin
  Result := FHostParity2;
end;

procedure TXMLHostType.SetHostParity2(Value: string);
begin
  if Value = FHostParity2 then Exit;
  FHostParity2 := Value;
end;

function TXMLHostType.GetHostStop2: string;
begin
  Result := FHostStop2;
end;

procedure TXMLHostType.SetHostStop2(Value: string);
begin
  if Value = FHostStop2 then Exit;
  FHostStop2 := Value;
end;

function TXMLHostType.GetHostIRQ2: string;
begin
  Result := FHostIRQ2;
end;

procedure TXMLHostType.SetHostIRQ2(Value: string);
begin
  if Value = FHostIRQ2 then Exit;
  FHostIRQ2 := Value;
end;

function TXMLHostType.GetHostPort3: string;
begin
  Result := FHostPort3;
end;

procedure TXMLHostType.SetHostPort3(Value: string);
begin
  if Value = FHostPort3 then Exit;
  FHostPort3 := Value;
end;

function TXMLHostType.GetHostBits3: string;
begin
  Result := FHostBits3;
end;

procedure TXMLHostType.SetHostBits3(Value: string);
begin
  if Value = FHostBits3 then Exit;
  FHostBits3 := Value;
end;

function TXMLHostType.GetHostParity3: string;
begin
  Result := FHostParity3;
end;

procedure TXMLHostType.SetHostParity3(Value: string);
begin
  if Value = FHostParity3 then Exit;
  FHostParity3 := Value;
end;

function TXMLHostType.GetHostStop3: string;
begin
  Result := FHostStop3;
end;

procedure TXMLHostType.SetHostStop3(Value: string);
begin
  if Value = FHostStop3 then Exit;
  FHostStop3 := Value;
end;

function TXMLHostType.GetHostIRQ3: string;
begin
  Result := FHostIRQ3;
end;

procedure TXMLHostType.SetHostIRQ3(Value: string);
begin
  if Value = FHostIRQ3 then Exit;
  FHostIRQ3 := Value;
end;

function TXMLHostType.GetHostSetForDayLight: string;
begin
  Result := FHostSetForDayLight;
end;

procedure TXMLHostType.SetHostSetForDayLight(Value: string);
begin
  if Value = FHostSetForDayLight then Exit;
  FHostSetForDayLight := Value;
end;

function TXMLHostType.GetHostTruncateAcctNo: string;
begin
  Result := FHostTruncateAcctNo;
end;

procedure TXMLHostType.SetHostTruncateAcctNo(Value: string);
begin
  if Value = FHostTruncateAcctNo then Exit;
  FHostTruncateAcctNo := Value;
end;

function TXMLHostType.GetHostPlatformID: string;
begin
  Result := FHostPlatformID;
end;

procedure TXMLHostType.SetHostPlatformID(Value: string);
begin
  if Value = FHostPlatformID then Exit;
  FHostPlatformID := Value;
end;

function TXMLHostType.GetHostClearingCode: string;
begin
  Result := FHostClearingCode;
end;

procedure TXMLHostType.SetHostClearingCode(Value: string);
begin
  if Value = FHostClearingCode then Exit;
  FHostClearingCode := Value;
end;

function TXMLHostType.GetHostMerchBIN: string;
begin
  Result := FHostMerchBIN;
end;

procedure TXMLHostType.SetHostMerchBIN(Value: string);
begin
  if Value = FHostMerchBIN then Exit;
  FHostMerchBIN := Value;
end;

function TXMLHostType.GetHostMerchICA: string;
begin
  Result := FHostMerchICA;
end;

procedure TXMLHostType.SetHostMerchICA(Value: string);
begin
  if Value = FHostMerchICA then Exit;
  FHostMerchICA := Value;
end;

function TXMLHostType.GetHostIPLocalPort3: string;
begin
  Result := FHostIPLocalPort3;
end;

procedure TXMLHostType.SetHostIPLocalPort3(Value: string);
begin
  if Value = FHostIPLocalPort3 then Exit;
  FHostIPLocalPort3 := Value;
end;

function TXMLHostType.GetHostLastBINFtpDate: string;
begin
  Result := FHostLastBINFtpDate;
end;

procedure TXMLHostType.SetHostLastBINFtpDate(Value: string);
begin
  if Value = FHostLastBINFtpDate then Exit;
  FHostLastBINFtpDate := Value;
end;

function TXMLHostType.GetHostProgramId: string;
begin
  Result := FHostProgramId;
end;

procedure TXMLHostType.SetHostProgramId(Value: string);
begin
  if Value = FHostProgramId then Exit;
  FHostProgramId := Value;
end;

function TXMLHostType.GetHostFTPDir: string;
begin
  Result := FHostFTPDir;
end;

procedure TXMLHostType.SetHostFTPDir(Value: string);
begin
  if Value = FHostFTPDir then Exit;
  FHostFTPDir := Value;
end;

function TXMLHostType.GetHostERCUse: string;
begin
  Result := FHostERCUse;
end;

procedure TXMLHostType.SetHostERCUse(Value: string);
begin
  if Value = FHostERCUse then Exit;
  FHostERCUse := Value;
end;

function TXMLHostType.GetFreqShopReqOfflineDebit: string;                       { YHJ-503 }
begin
  Result := FFreqShopReqOfflineDebit;
end;

procedure TXMLHostType.SetFreqShopReqOfflineDebit(Value: string);               { YHJ-503 }
begin
  if Value = FFreqShopReqOfflineDebit then Exit;
  FFreqShopReqOfflineDebit := Value;
end;

constructor TXMLHostType.Create(AOwner: TCollection); // DEV-18409
begin
  inherited Create(AOwner);
  ForceOfflineByCardCode := TStringList.Create;
end;

destructor TXMLHostType.Destroy; // DEV-18409
begin
  FreeAndNil(ForceOfflineByCardCode);
  inherited;
end;

function TXMLHostType.GetDynamicCurrConv: string; // TFS-9730
begin
  Result := FDynamicCurrConv;
end;

procedure TXMLHostType.SetDynamicCurrConv(const Value: string); // TFS-9730
begin
  FDynamicCurrConv := Value;
end;

initialization
  ExtendedLog('StoreConfigurationsXML Initialization');
finalization
  ExtendedLog('StoreConfigurationsXML Finalization');

end.

