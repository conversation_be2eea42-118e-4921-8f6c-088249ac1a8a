// (c) MTXEPS, Inc. 1988-2008
// used by OpenEPS service
unit LogHelper;

interface

procedure Log(s: string);
procedure SetLogFileName(s: string);

implementation

uses
  SysUtils,SyncObjs, ServerEPSConstants;

var
  LockLog: TCriticalSection;
  fileName: string; //filename

const
  _Filename = 'MTX_default_log.txt';

procedure Log(s: string);
var
  f,f2: textfile;
begin
  {$IFDEF LOGGING}
  LockLog.Acquire;
  try
    try
      assignfile(f,fileName);
      try
        if FileExists(fileName)
          then append(f)
          else rewrite(f);
        if S = ''
          then writeln(f)
          else writeln(f,FormatDateTime('mm/dd/yyyy hh:nn:ss.zzz ',Now),S);
      finally
        closefile(f);
      end;
    except on e: exception do
      begin
      assignfile(f2,'z Log is Dead.txt');
      rewrite(f2);
      writeln(f2,'Error for file >' + fileName + '< ' + e.Message);
      closefile(f2);
      end;
    end;
  finally
    LockLog.Release;
  end;
  {$ENDIF}
end;

procedure SetLogFileName(s: string);
begin
  fileName := '\' + s; // place in root folder
end;

initialization
  LockLog := TCriticalSection.Create;
  fileName := _Filename;

finalization
  FreeAndNil(LockLog);

end.
