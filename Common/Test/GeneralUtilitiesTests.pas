unit GeneralUtilitiesTests;

interface

uses
  DUnitX.TestFramework, GeneralUtilities;

type

  [TestFixture]
  TGeneralUtilitiesFixture = class(TObject)
  public
    [Test]
    [TestCase('Equal', '1111 = 2222   =   3333,=,1111=2222=3333')]
    [TestCase('Plus', '1111 + ABCD   +   3333,+,1111+ABCD+3333')]
    // [TestCase('Plus with Null', '1111 + ABCD '#0'  +   3333,+,1111+ABCD+3333')] // Null is not allowed in AnsiStrings
    procedure CleanTest(const AOriginal, AToken, AExpected: AnsiString);
    [Test]
    [TestCase('Equal then Plus', '1111 + 2222 +  =   3333+,=+,1111+2222+=3333+')]
    procedure CleanWithMultiplePassesTest(const AOriginal, ATokens, AExpected: AnsiString);
    [Test]
    [TestCase('FALSE', 'FALSE')]
    [TestCase('false', 'false')]
    [TestCase('false with spaces', '  false   ')]
    [TestCase('Uppercase F', 'F')]
    [TestCase('Lowercase f', 'f')]
    [TestCase('The zero character', '0')]
    [TestCase('Random number characters', '33')]
    [TestCase('Blank', '')]
    [TestCase('Random word', 'BILL')]
    procedure IsTrueStringOnFalseStringsTest(const AString: AnsiString);
    [Test]
    [TestCase('TRUE', 'TRUE')]
    [TestCase('true', 'true')]
    [TestCase('true with spaces', '  true   ')]
    [TestCase('Uppercase T', 'T')]
    [TestCase('Lowercase t', 't')]
    [TestCase('The one character', '1')]
    procedure IsTrueStringOnTrueStringsTest(const AString: AnsiString);
    [Test]
    [TestCase('Empty version', '')]
    [TestCase('Blank version', '               ')]
    [TestCase('One octet', '828000')]
    [TestCase('Incorrect number of periods', '828.0.1117')]
    [TestCase('Non-numeric characters', 'ABC.0.1.117')]
    [TestCase('Non-numeric symbols', '#$%^&*')]
    [TestCase('Extra on the beginning', 'a1.1.1.1')]
    [TestCase('Extra on the end', '1.1.1.1a')]
    [TestCase('Version lookup fails on linux', 'file')]
    procedure IsValidVersionWithInvalidValuesTest(const AVersionString: String);
    [Test]
    [TestCase('Standard', '828.0.1.117')]
    [TestCase('Really Big', '99999.99999.99999.99999')]
    procedure IsValidVersionWithValidValuesTest(const AVersionString: String);
    [Test]
    procedure StrToCharArrayTest;
  end;

implementation

uses
  System.AnsiStrings, MTX_Constants;

procedure TGeneralUtilitiesFixture.CleanTest(const AOriginal, AToken, AExpected: AnsiString);
var
  CleanedOriginal: AnsiString;
begin
  CleanedOriginal := Clean(AOriginal, AToken);
  Assert.AreEqual(AExpected, CleanedOriginal);
end;

procedure TGeneralUtilitiesFixture.CleanWithMultiplePassesTest(const AOriginal, ATokens, AExpected: AnsiString);
var
  CleanedOriginal: AnsiString;
  Token: AnsiChar;
begin
  CleanedOriginal := AOriginal;
  for Token in ATokens do
  begin
    CleanedOriginal := Clean(CleanedOriginal, Token);
  end;
  Assert.AreEqual(AExpected, CleanedOriginal);
end;

procedure TGeneralUtilitiesFixture.IsTrueStringOnFalseStringsTest(const AString: AnsiString);
var
  BooleanResult: boolean;
begin
  BooleanResult := IsTrueString(AString);
  Assert.IsFalse(BooleanResult);
end;

procedure TGeneralUtilitiesFixture.IsTrueStringOnTrueStringsTest(const AString: AnsiString);
var
  BooleanResult: boolean;
begin
  BooleanResult := IsTrueString(AString);
  Assert.IsTrue(BooleanResult);
end;

procedure TGeneralUtilitiesFixture.IsValidVersionWithInvalidValuesTest(const AVersionString: String);
var
  VersionResult: boolean;
  ErrMsg: string;
begin
  VersionResult := IsValidVersion(AVersionString, ErrMsg);
  Assert.IsFalse(VersionResult);
  Assert.IsNotEmpty(ErrMsg);
end;

procedure TGeneralUtilitiesFixture.IsValidVersionWithValidValuesTest(const AVersionString: String);
var
  VersionResult: boolean;
  ErrMsg: string;
begin
  VersionResult := IsValidVersion(AVersionString, ErrMsg);
  Assert.IsTrue(VersionResult);
  Assert.IsEmpty(ErrMsg);
end;

procedure TGeneralUtilitiesFixture.StrToCharArrayTest;
var
  Char5: array [0 .. 5] of AnsiChar;
begin
  StrToCharArray('1234', Char5, 4);
  Assert.AreEqual(Char5[0], AnsiChar('1'), 'char[3] of 1234');
  Assert.AreEqual(Char5[3], AnsiChar('4'), 'char[3] of 1234');
  Assert.AreEqual(Char5[4], AnsiChar(#0), 'char[4] of 1234');
  Assert.AreEqual(Char5[5], AnsiChar(#0), 'char[5] of 1234');

  StrToCharArray('1234', Char5, high(Char5));
  Assert.AreEqual(Char5[0], AnsiChar('1'), 'char[3] of 1234');
  Assert.AreEqual(Char5[3], AnsiChar('4'), 'char[3] of 1234');
  Assert.AreEqual(Char5[4], AnsiChar(#0), 'char[4] of 1234');
  Assert.AreEqual(Char5[5], AnsiChar(#0), 'char[5] of 1234');
end;

initialization

TDUnitX.RegisterTestFixture(TGeneralUtilitiesFixture);

end.
