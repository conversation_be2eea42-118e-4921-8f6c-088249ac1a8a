﻿{-----------------------------------------------------------------------------
 Unit Name: uKEK3
 Author   : HL
 Purpose  : Randomly automatically create key with datetime-randomHex32bytes format
 History  : Created on 10/15/2010 by HL
            10/16/2010 - Used MTXUtils to access registry
            11/3/2010 - Added remove temp files and fixed logic for determine Virtualization on
-----------------------------------------------------------------------------}

unit uKEK3;

interface

{$UNDEF WINEPS}
  {$IFDEF ENGINE}
    {$DEFINE WINEPS}
  {$ENDIF}
  {$IFDEF RSSRV}
    {$DEFINE WINEPS}
  {$ENDIF}

  {$IFDEF TEST}
    {$DEFINE WINEPS}
  {$ENDIF}

{$UNDEF KEK3TOOL_OR_TEST}    // this effectively creates an 'or' conditional define
{$IFDEF KEK3TOOL}
  {$DEFINE KEK3TOOL_OR_TEST}
{$ENDIF KEK3TOOL}
{$IFDEF TEST}
  {$DEFINE KEK3TOOL_OR_TEST}
{$ENDIF ENGINE}



uses
  {$IFDEF MSWINDOWS}
  Windows,
  Dialogs,
  Registry,
  ActiveX,
  {$ENDIF}
  TypInfo, SysUtils, StrUtils, DateUtils, Classes,
  {$IFNDEF WOLF}
  LbCipher,
  {$ENDIF}
  MTX_Types,
  MTX_Constants,
  ComputerInfo;

type
  TUpdateKEKThread = class(TThread)
    CurrPath: string;
    OldKEK3: string;
    NewKEK3: string;
  protected
    procedure Execute; override;
  end;

const
  APP_PATH            = 'Software\MTXEPS\';
  UAC_PATH            = 'SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System';
  VIR_REG_PATH        = 'C:\Windows\System32\reg.exe flags HKLM\software\';
  COMPANY             = 'MTXEPS';
  SET_VIR_OFF         = ' set dont_virtualize /s';
  SET_VIR_ON          = ' set DONT_SILENT_FAIL /s';
  VIR_QUERY           = 'query';
  OS_64_KEY           = 'Wow6432Node';
  VIR_BATFILE         = 'MTXEPSvirtualization.bat';
  VIR_TEXTFILE        = 'MTXEPSvirtualization.txt';

  VIR_KEY             = 'REG_KEY_DONT_VIRTUALIZE';
  VIR_ON              = 'CLEAR';

  KEK3_KEY_NAME       = 'KEK3';
  UAC_VALUE           = 'EnableLUA';
  WINEPS              = 'WinEPS';
  OPENEPS             = 'OpenEPS';
  MAX_KEK3_AGE        = 365; // days
  DASH                = '-';
//  SECONDS_A_DAY       = 86400;
  TIMESTAMP_LEN       = 14;
  KEK_KEY_LEN         = 64;
  ENCRYPTION_KEY      = 'Generate_New_Key';
  _EngineNeedsNewKEK3 = 1;
  ON_FLAG             = 1;
  OFF_FLAG            = 0;
  BACKUP_ZIP_FILENAME = 'backupeft.zip';
  OPENEPS_DEFAULT_DIR = '\Program Files\MicroTrax\OpenEPS\';
  OPENEPSNET          = 'OpenEPSNet';
var
  InUpdateKEK3: boolean;
  UpdateKEKThread: TUpdateKEKThread;
  VirtualizationAlreadyChecked: boolean = {$IFDEF MSWINDOWS} false {$ELSE} true {$ENDIF};

{$IFDEF TEST}
  FailInUpdateEftFiles: boolean;
  FailInUpdateEftFileName: string;
function KEKDateTimeStrToDateTime(const sDate: string; var DT: TDateTime): boolean;  //32649 32650
function GetFormattedDateTime(const sDate: string): string; //return mm/dd/yy hh:mm:ss format
{$ENDIF}

function GetKEK3(var sKey: AnsiString; sPath: AnsiString = ''): boolean;
function TimeToMakeNewKEK3(sServerKekTime: string = ''; sServerTime: string = ''): Boolean;
function GetKEK3Timestamp: string;
function SetVirtualization(const iFlag: integer = 0): boolean;

function GetAppPath(const sApp: String = ''): string;
function Is64BitOS: Boolean;
//function IsUACOn: boolean; //check if UAC is on // XE: Remove WiinEPS - not for OpenEPS
function VirtualizationOn: boolean; // XE: Remove WiinEPS - not for OpenEPS
//function SetUAC(const iFlag: integer): boolean;// XE: Remove WiinEPS - not for OpenEPS

{$IFNDEF D2007}
function GetKEK3withTimeStamp(sPath: string = ''): string;
function SetKEK3(var sKey: AnsiString; sPath: AnsiString = '' {$IFDEF KEK3TOOL_OR_TEST}; const sFullKek: AnsiString = ''{$ENDIF}): boolean;
function GetRandomKey: string;
function UpdateKEK3(AppType: TMTXAppType=atUnKnown): boolean; // DEV-18500
function UpdateEftFilesKEK(AppType: TMTXAppType; Path, Patterns: string; NewKEK3: string=''; OldKEK3: string=''; ProcessAll: boolean=false): boolean; // DEV-18500
function UpdateAEftFileKEK(AppType: TMTXAppType; AFileName: string; NewKEK3: string=''; OldKEK3: string=''): boolean; // DEV-18500
function GenerateKekIfBlank: boolean;
{$IFDEF MSWINDOWS}
//function SetKEK3Full(var sKey: string; const sFullKek: string; var ErrorStr: string): boolean; // XE: Remove WiinEPS - not for OpenEPS
function SetNewEncryptionKeyFlag(i: integer = 0): boolean;
function NeedNewEncryptionKey(const bSkipVirtualization: boolean=FALSE): boolean; //bSkipVirtualization: KEK3tool use this function
//function CheckKEK3: boolean; // DOEP-47616: moved from EngineU
{$ENDIF MSWINDOWS}
{$ENDIF D2007}                                                                          //and doesn't set virtualization
function SetLastWorkingKEK3(aKEK3: AnsiString): boolean; // DEV-28953

implementation

uses
  FinalizationLog,
  ServerEPSConstants, // for SEPS_CONF_FILENAME
  GeneralUtilities,  // for SplitStr
  {$IFDEF D2007}
  uAppServerSoapDM;
  {$ELSE}
  MTX_Utils,
  MTX_Lib,
  Compression,
  MTXEncryptionUtils,
  MTXEncryptionPrivate,
  RecFile,
    {$IFDEF MTXEPSDLL}
    OEFileAccess,
    epsTrace, // DOEP-22713
    {$ENDIF MTXEPSDLL}
  DllTypes, // DOEP-27569
  TrxLog;
  {$ENDIF}

type
  UpdateKEK3Step = (
    stCheckFileSize = 1,
    stGetHash,
    stCloseEftFile,
    stDeleteTmpFileIfExists,
    stRenameEftToTmp,
    stCreateNewEftFile,
    stOpenTmpFile,
    stCopyRecord,
    stValidateFileSize,
    stValidateFileData,
    stCloseTmpFile,
    stDeleteTmpFile
  );

const
  UpdateKEK3StepDesc: array [UpdateKEK3Step] of string = (
    'Check file size',
    'Get hash',
    'Close eft file',
    'Delete temp file if exists',
    'Rename eft to temp file',
    'Create new eft file',
    'Open tmp file',
    'Copy record',
    'Validate file size',
    'Validate file data',
    'Close temp file',
    'Delete temp file');

var
  LastWorkingKEK3: AnsiString = ''; // DEV-28953

function GetLastWorkingKEK3: AnsiString; forward; // DEV-28953

{$IFDEF D2007}
procedure SM(S: string);
begin
  uAppServerSoapDM.Log(S);
end;
{$ENDIF}

{$IFDEF MTXEPSDLL} // DOEP-22713: TimeToMakeNewKEK3 was called from IsoFormat
procedure SM(S: string);
begin
  showTrace(idTCP, S);
end;
{$ENDIF}

procedure DeleteTempFiles;
var
  sCurrDir: string;
  sBatFile,
  sTextFile: string;
begin
  try
    sCurrDir := IncludeTrailingPathDelimiter(DefaultDir);
    sBatFile := sCurrDir+VIR_BATFILE;
    sTextFile := sCurrDir+VIR_TEXTFILE;
    if FileExists(sBatFile) then
      DeleteFile(sBatFile);
    if FileExists(sTextFile) then
      DeleteFile(sTextFile);
  except
    on E: Exception do
      SM('Try..Except DeleteTempFiles - ' + E.Message);
  end;
end;

function ByteToHex(ByteVal: Byte): String;
begin
  result := IntToHex(ByteVal, 2);
End;

function Is64BitOS: Boolean;
{$IFDEF MSWINDOWS}
type
  TIsWow64Process = function(Handle:THandle; var IsWow64 : BOOL) : BOOL; stdcall;
var
  hKernel32 : Integer;
  IsWow64Process : TIsWow64Process;
  IsWow64 : BOOL;
{$ENDIF}
begin
  {$IFDEF MSWINDOWS}
  // we can check if the operating system is 64-bit by checking whether
  // we are running under Wow64 (we are 32-bit code). We must check if this 
  // function is implemented before we call it, because some older versions 
  // of kernel32.dll (eg. Windows 2000) don't know about it. 
  // see http://msdn.microsoft.com/en-us/library/ms684139%28VS.85%29.aspx 
  Result := False; 
  hKernel32 := LoadLibrary('kernel32.dll'); 
  if (hKernel32 = 0) then RaiseLastOSError; 
  @IsWow64Process := GetProcAddress(hkernel32, 'IsWow64Process'); 
  if Assigned(IsWow64Process) then begin 
    IsWow64 := False; 
    if (IsWow64Process(GetCurrentProcess, IsWow64)) then begin 
      Result := IsWow64; 
    end 
    else RaiseLastOSError; 
  end; 
  FreeLibrary(hKernel32);
  {$ENDIF}
end;

(* // XE: Remove WiinEPS - not for OpenEPS
function SetUAC(const iFlag: integer): boolean;
begin
  {$IFDEF MSWINDOWS}
  Result := FALSE;
  try
    Result := SaveIntKeyToRegistry(HKEY_LOCAL_MACHINE, UAC_PATH,UAC_VALUE, iFlag) = REGISTRY_WRITE_SUCCESS;
  except
    on E: Exception do
      SM('Try..Except SetUACOff - ' + E.Message);
  end;
  {$ELSE}
  result := true;
  {$ENDIF}
end;
*)

(* // XE: Remove WiinEPS - not for OpenEPS
function IsUACOn: boolean; //check if UAC is on
begin
  Result := FALSE;
  {$IFDEF MSWINDOWS}
  try
    Result := GetIntKeyFromRegistry(HKEY_LOCAL_MACHINE, UAC_PATH, UAC_VALUE) = 1;
  except
    on E: Exception do
      SM('Try..Except IsUACOff - ' + E.Message);
  end;
  {$ENDIF}
end;
*)

function CreateBatFile: string;
var
  BatFile         : TextFile;
  sBatFile        : string;
  sCurrDir        : string;
  sVir_Query_Path : string;
begin
  Result := '';
  try
    sCurrDir := IncludeTrailingPathDelimiter(DefaultDir);
    sBatFile := sCurrDir+VIR_BATFILE;
    SM('Virtualization CreateBatFile: sBatFile = ' + sBatFile);
    if Is64BitOS then
      sVir_Query_Path := VIR_REG_PATH+OS_64_KEY+'\'+ COMPANY+' '+VIR_QUERY
    else
      sVir_Query_Path := VIR_REG_PATH+ COMPANY+' '+ VIR_QUERY;
    sVir_Query_Path := sVir_Query_Path + ' > ' + '"' + sCurrDir + VIR_TEXTFILE + '"';
    SM('Virtualization CreateBatFile: sVir_Query_Path = ' + sVir_Query_Path);
    AssignFile(BatFile, sBatFile);
    try
      Rewrite(BatFile); //create it
      Writeln(BatFile,sVir_Query_Path); //write to bat file
      Result := sBatFile;
    finally
      CloseFile(BatFile);
    end;
  Except
    on E: Exception do
      SM('Try..Except CreateBatFile - ' + E.Message);
  end;
  SM('Virtualization CreateBatFile: result = ' + result);
end;

function VirtualizationOn: boolean;
var
  sFile    : string;
  sCurrDir : string;
  aList    : TStringList;
  i        : integer;
begin
  Result := TRUE; //Default
  {$IFDEF MSWINDOWS}
  sCurrDir := IncludeTrailingPathDelimiter(DefaultDir);
  SM('Virtualization VirtualizationOn: sCurrDir = ' + sCurrDir);
  sFile  := CreateBatFile;
  try
    if Length(sFile) > 0 then//return the path
    begin
      if WinExecAndWait32(Pchar(sFile), 0) > -1 then //sucessfully created txt file
      begin
        aList := TStringList.Create;
        try
          if FileExists(sCurrDir + VIR_TEXTFILE) then
          begin
            aList.LoadFromFile(sCurrDir + VIR_TEXTFILE);
            for i := 0 to Pred(aList.Count) do
              if pos(VIR_KEY, aList.Strings[i]) > 0 then
              begin
                Result := RightStr(aList.Strings[i], 5)= VIR_ON; //if = 'CLEAR' then on, else if off
                Break; //done.
              end;
          end
          else
            SM('VirtualizationOn could not find file: ' + sCurrDir + VIR_TEXTFILE);
        finally
          aList.Free;
        end;
      end;
    end;
  finally
    DeleteTempFiles;
  end;
  {$ENDIF}
end;

function SetVirtualization(const iFlag: integer = OFF_FLAG): boolean;
{$IFDEF MSWINDOWS}
var
  sVir_Reg_Path: string;

  function IsVistaOrLater: boolean; //check OS to see if it's Vista or later
  var
    aComputer: TComputerInfo;
  begin
    aComputer := TComputerInfo.Create;
    try
      Result := (aComputer.OS >= '6.0');
    finally
      aComputer.Free;
    end;
  end;
{$ENDIF}  
begin
  Result := TRUE;
  if VirtualizationAlreadyChecked then
    exit;
  {$IFDEF MSWINDOWS}
  //first check if UAC is on
  if IsVistaOrLater then
  begin
    try
      if iFlag = ON_FLAG then //set it on - TEST ONLY
      begin
        if NOT VirtualizationOn then //virtualization is off, set it on
        begin
          SM('SetVirtualization: Virtualization is off. Begin to set it on');
          sVir_Reg_Path := '';
          if Is64BitOS then
            sVir_Reg_Path := VIR_REG_PATH+OS_64_KEY+'\'+ COMPANY+SET_VIR_ON
          else
            sVir_Reg_Path := VIR_REG_PATH+ COMPANY+SET_VIR_ON;

          if WinExecAndWait32(Pchar(sVir_Reg_Path), 0) > -1 then //successful
          begin
            //after setting it, check to see if it's good
            if VirtualizationOn then
            begin
              Result := TRUE;
              SM('SetVirtualization: Virtualization is on')
            end
            else
              Result := FALSE;
              SM('SetVirtualization: Failed to set virtualization on');
            begin
            end;
          end;
        end;
      end
      else
      begin //set it off
        if VirtualizationOn then //virtualization is on, set it off
        begin
          SM('SetVirtualization: Virtualization is on. Begin to set it off');
          sVir_Reg_Path := '';
          if Is64BitOS then
            sVir_Reg_Path := VIR_REG_PATH+OS_64_KEY+'\'+ COMPANY+SET_VIR_OFF
          else
            sVir_Reg_Path := VIR_REG_PATH+ COMPANY+SET_VIR_OFF;

          if WinExecAndWait32(Pchar(sVir_Reg_Path), 0) > -1 then //successful
          begin //check and see if it's good
            if NOT VirtualizationOn then
            begin
              Result := TRUE;
              SM('SetVirtualization: Virtualization is off');
            end
            else
            begin
              Result := FALSE;
              SM('SetVirtualization: Failed to set virtualization off');
            end;
          end
          else
            SM('SetVirtualization:  WinExecAndWait32('+sVir_Reg_Path+') FAILED');
        end
        else
          ;//SM('SetVirtualization: Virtualization is already off-No need to set');
      end;
      VirtualizationAlreadyChecked := result; // don't check it again if it is ok.
      SM('SetVirtualization: VirtualizationAlreadyChecked='+BoolToStr(VirtualizationAlreadyChecked, true));
    except
      on E: Exception do
        SM('Try..Except SetVirtualization - ' + E.Message);
    end;
  end
  else
  begin
    VirtualizationAlreadyChecked := true;
    SM('SetVirtualization: Set VirtualizationAlreadyChecked=true for non-Vista (older) system');
  end;
  {$ENDIF}
end;

//get AppPath based on IFDEF syntax
//Default is WinEPS - mtxepsdll in openeps
function GetAppPath(const sApp: String = ''): string;
{$IFDEF MTXEPSDLL}
var
  startPos: Integer;
  lane : string;
{$ENDIF}
begin
  Result := APP_PATH + WINEPS;
  try
    if Length(sApp) = 0 then //it's likely get passed from KEK3TOOL
    begin
      {$IFDEF MTXEPSDLL}
      Result := APP_PATH + OPENEPS;
      // DOEP-48319 - OpenEPS.net - Need to create seperate KEK3 entries in the registry for each instance of OpenEPS
      if NOT SameText(DefaultDir, OPENEPS_DEFAULT_DIR) then
      begin
        startPos := pos(OPENEPS, DefaultDir) + Length(OPENEPS);
        lane := Copy(DefaultDir, startPos, Length(DefaultDir) - startPos);
        Result := Format('%s%s\%s',[APP_PATH, OPENEPSNET, lane]);
      end;
      {$ENDIF}
    end
    else //kek3 tool
      Result := APP_PATH + sApp;
  except
    on E: Exception do
      SM('Try..Except GetAppPath - ' + E.Message);
  end;
end;

//return random key w/o date
{$IFNDEF D2007}
function GetRandomKey: string;
var
  aKey        : TMtxKey256;
  i           : integer;
  sRandomKey  : string;
begin
  Result := '';
  try
    GenerateRandomKey(aKey, ENCRYPTION_KEY_LENGTH); //get random key
    for i := 0 to Pred(Length(aKey)) do
      sRandomKey := sRandomKey+ByteToHex(aKey[i]);
    Result := sRandomKey;
  except
    on E: Exception do
      SM('Try..Except GetRandomKey - ' + E.Message);
  end;
end;

// write a random kek3 (with date) to the registry and return the kek3 (without date). If error, return false.
// This is used by engine, openeps, and installer.
// Pass sDate('YYYYMMDDHHNNSS' format if you need to pass customer date other than now. This is for unit test
function SetKEK3(var sKey: AnsiString; sPath: AnsiString = '' {$IFDEF KEK3TOOL_OR_TEST}; const sFullKek: AnsiString = ''{$ENDIF}): boolean;
var
  sNow        : string;
  //aKey        : TKey256;
  iDashPos    : integer;
  sKEK3Value  : AnsiString;
  sRandomKey  : AnsiString;
  sAppPath    : string;

  sAltFilePath: string;
  IsSaved: boolean;

  {$IFDEF KEK3TOOL}
  sTempKey    : AnsiString;
  sDate       : AnsiString;
  {$ENDIF}
begin
  Result := FALSE;
  IsSaved := FALSE;
  try
{$IFDEF KEK3TOOL_OR_TEST}
    if Length(sFullKek) = 0 then //get random kek key
{$ENDIF}
    begin
      sNow := FormatDateTime('YYYYMMDDHHNNSS', now);
      if (Length(sKey) = KEK_KEY_LEN) and IsHexDigit(sKey) then
        sRandomKey := sKey
      else
      begin
        sRandomKey := GetRandomKey;
      end;

      sKEK3Value := Format('%s-%s',[sNow, sRandomKey]);

      if SameText(MTX_Lib.Reg_Lookup(DefaultDir + OpenEPSIni, USE_ALT_REGISTRY, false), 'Y') then //TFS-166590: don't use registry on thin client implementations
      begin
        //msgDebug('SetKEK3 USE_ALT_REGISTRY');
        sAltFilePath := MTX_Lib.Reg_Lookup(DefaultDir + OpenEPSIni, cALTFILEPATH, true); // DOEP-32312 
        if (sAltFilePath <> '') then // if AltFilePath used
        begin
          //msgDebug('SetKEK3 cALTFILEPATH');
          if (StrToIntDef(SignOnSet.LaneNumber,0) > 0) then // after signon
          begin
            sAltFilePath := IncludeTrailingPathDelimiter(sAltFilePath) + 'Lane' + FormatFloat('00',StrToIntDef(SignOnSet.LaneNumber,0));
            if NOT DirectoryExists(sAltFilePath) then // DOEP-54059
            begin
              SM('SetSec: AltPath not exists - ' + sAltFilePath);
              if ForceDirectories(sAltFilePath)
                then SM('SetSec: AltPath created - ' + sAltFilePath)
                else SM('SetSec: Failed to create AltPath - ' + sAltFilePath);
            end;
            if DirectoryExists(sAltFilePath) then
            begin
              sAltFilePath :=  IncludeTrailingPathDelimiter(sAltFilePath);
              IsSaved := MTX_Lib.Reg_Save(KEK3_KEY_NAME, sKEK3Value, sAltFilePath + LANE_CONF_FILENAME, false)
            end;
          end
          else // before signon
            Exit;
        end;
      end
      else
      begin // use the Windows registry
        if (not VirtualizationAlreadyChecked) and (NOT SetVirtualization) then
        begin
          SM('Set Security: Failed to set virtualization off');
          Exit;
        end;
        if sPath <> '' then
          sAppPath := GetAppPath(sPath)
        else
          sAppPath := GetAppPath;
        IsSaved := SaveStrKeyToRegistry(HKEY_LOCAL_MACHINE, sAppPath, KEK3_KEY_NAME, sKEK3Value) = REGISTRY_WRITE_SUCCESS;
        SM('Set Security SaveStrKeyToRegistry = ' + BoolToStr(IsSaved, true));
      end;
      if IsSaved then
      begin
        //sKey:= sRandomKey;
        MTXEncryptionUtils.MakeKeyEncryptionKey; // Update KEK
        // don't assume key was written to registry unless we can read it back.
        sKey := 'whatever';
        //msgDebug('SetKEK3 before GetKek3');
        GetKek3(sKey, sPath);
        // GetKek3 returns date & dash for kek3 tool.
        iDashPos := Pos('-', sKey);
        if iDashPos > 0 then//dash available
          sKey := RightStr(sKey, Length(sKey)-iDashPos);
        result := SameText(sKey, sRandomKey);
        //SM('PCI: Writing '+sKEK3Value+' to registry');
      end;
    end
{$IFDEF KEK3TOOL_OR_TEST}
    else //full kek is passing over
    begin
      {$IFDEF KEK3TOOL}
        sKey := sFullKek;
        iDashPos := Pos(DASH, sKey);
        if iDashPos > 0 then//dash available
        begin
          sTempKey := RightStr(sKey, Length(sKey)-iDashPos);
          sDate := LeftStr(sKey, 14);
          result := (Length(sTempKey) = KEK_KEY_LEN) and
                    IsHexDigit(sTempKey) and
                    IsDigit(sDate);
        end;
        if result then // don't write a bad kek to the registry
      {$ENDIF}
      Result := SaveStrKeyToRegistry(HKEY_LOCAL_MACHINE, GetAppPath(sPath), KEK3_KEY_NAME, sFullKek) = REGISTRY_WRITE_SUCCESS;
      if Result then
      begin
        GetKEK3(sKey, sPath) //return sKey
      end
      else
        sKey := '';
    end;
  {$ENDIF}
  except
    on E: Exception do
      SM('Try..Except Set Security - ' + E.Message);
  end;
end;

function GetKEK3withTimeStamp(sPath: string = ''): string;
var
  sAppPath: string;
  sAltFilePath: string;
begin
  result := '';
  try
    //msgDebug('GetKEK3withTimeStamp');
    if SameText(MTX_Lib.Reg_Lookup(DefaultDir + OpenEPSIni, USE_ALT_REGISTRY, false), 'Y') then //TFS-166590: don't use registry on thin client implementations
    begin
      //SM('Get Security+TimeStamp USE_ALT_REGISTRY');
      sAltFilePath := MTX_Lib.Reg_Lookup(DefaultDir + OpenEPSIni, cALTFILEPATH, true); // DOEP-32312
      if (sAltFilePath <> '') then // AltFilePath used
      begin
        //SM('Get Security+TimeStamp cALTFILEPATH');
        if (StrToIntDef(SignOnSet.LaneNumber,0) > 0) then // after signon
        begin
          sAltFilePath := IncludeTrailingPathDelimiter(sAltFilePath) + 'Lane' + FormatFloat('00',StrToIntDef(SignOnSet.LaneNumber,0));
          if NOT DirectoryExists(sAltFilePath) then // DOEP-54059
          begin
            SM('GetSec: AltPath not exists - ' + sAltFilePath);
            if ForceDirectories(sAltFilePath)
              then SM('GetSec: AltPath created - ' + sAltFilePath)
              else SM('GetSec: Failed to create AltPath - ' + sAltFilePath);
          end;
          if DirectoryExists(sAltFilePath) then
          begin
            sAltFilePath :=  IncludeTrailingPathDelimiter(sAltFilePath);
            result := MTX_Lib.Reg_Lookup(sAltFilePath + LANE_CONF_FILENAME, KEK3_KEY_NAME, false); // seps.conf
            SM('Sec from alt file path is used');
          end
        end
        else // before signon
          result := ''
      end;
    end
    else
    begin // use Windows registry
      //msgDebug('GetKEK3withTimeStamp NOT USE_ALT_REGISTRY');
      if (not VirtualizationAlreadyChecked) and (NOT SetVirtualization) then
      begin
        SM('ERROR: Get Security+TimeStamp: Failed to set virtualization off');
        Exit;
      end;

      //result := GetStrKeyFromRegistry(HKEY_LOCAL_MACHINE,GetAppPath,KEK3_KEY_NAME)
      sAppPath := GetAppPath(sPath);
      //SM('Registry path '+sAppPath);
      result := GetStrKeyFromRegistry(HKEY_LOCAL_MACHINE, sAppPath, KEK3_KEY_NAME);
      //msgDebug('GetKEK3withTimeStamp GetStrKeyFromRegistry ' + result);
    end;
  except on e: Exception do
    result := 'ERROR: Get Security+TimeStamp EXCEPTION: ' + E.Message;
  end;
end;

//JTG - Like SetKEK3, but specifically uses the 'sFullKek' param so that RS and WinEPS/OpenEPS can exchange full KEK info
// this required a new function because it's a very big deal if this function fails, and so would be useful to know
// why, so therefore we needed to pass back specific error information (since RS doesn't use SM, simple logging didn't suffice)
{$IFDEF MSWINDOWS}

{ // XE: Remove WiinEPS - not for OpenEPS
function ValidFullKekFormat(const sFullKek: string; var ErrorStr: string): boolean;
const
  TITLE = 'Security - Validate Key: ';
var
  iDashPos: integer;
  sJustKey,sDate: string;
begin
  iDashPos := pos(DASH, sFullKek);
  result := iDashPos > 0;            // always have a return value, one way or the other
  if result then
    begin
    sJustKey := RightStr(sFullKek,length(sFullKek)-iDashPos);
    sDate := LeftStr(sFullKek,14);
    result := length(sJustKey) = KEK_KEY_LEN;
    if result then
      begin
      result := IsHexDigit(sJustKey) and IsDigit(sDate);
      if not result  then
        ErrorStr := format('%s BAD FORMAT in INPUT key or date',[TITLE]);
      end
    else
      ErrorStr := format('%s INPUT bad data length = %d',[TITLE,length(sJustKey)]);
    end
  else
    ErrorStr := TITLE + 'NO DASH in INPUT string';
end;
}

{ // XE: Remove WiinEPS - not for OpenEPS
function SetKEK3Full(var sKey: string; const sFullKek: string; var ErrorStr: string): boolean;
const
  TITLE = 'Security:';
var
  RegWriteResult: integer;
begin
  result := false;
  try
    ErrorStr := '';
    sKey := '';
    if VirtualizationAlreadyChecked and SetVirtualization then
      begin
      result := ValidFullKekFormat(sFullKek,ErrorStr);
      if result then     // only write a reasonably well-formatted kek to the registry
        begin
        RegWriteResult := SaveStrKeyToRegistry(HKEY_LOCAL_MACHINE,GetAppPath,KEK3_KEY_NAME,sFullKek);
        result := RegWriteResult = REGISTRY_WRITE_SUCCESS;
        if result then
          begin
          sKey := GetKEK3withTimeStamp;   //return sKey
          result := SameText(sKey,sFullKek);   // better test of success than the other SetKEK3 function
          if result
            then ErrorStr := 'OK'
            else ErrorStr := TITLE + ' in reading what we just wrote, the two do not match';
            //ErrorStr := format('%sthe key we just wrote (%s) it does not match the key we just read back (%S)',[TITLE,sFullKek,sKey]);
          end
        else
          ErrorStr := format('%s SaveStrKeyToRegistry FAILED (result %d) with HKLM/%s/%s',[TITLE,RegWriteResult,GetAppPath,KEK3_KEY_NAME]);
        end;
      end
    else
      begin
      result := false;
      ErrorStr := TITLE + ' Failed to set virtualization off';
      end;
  except on e: exception do
    ErrorStr := TITLE + ' EXCEPTION: ' + E.Message;
  end;
end;
}
{$ENDIF MSWINDOWS}

{$ENDIF}

// get the KEK3 (without the date) from the registry. If key not present or other error, return false;
// This is used by engine, openeps, and installer.
function GetKEK3(var sKey: AnsiString; sPath: AnsiString = ''): boolean;
var
  iDashPos      : integer;
  //sAppPath      : string;
  sDate         : AnsiString;
  sTempKey      : AnsiString;
  {$IFDEF LINUX}
  //sAltFilePath  : string;
  {$ENDIF}
begin
  Result := FALSE;  //nothing happened
  sKey := '';
  //SM('Begin to get key');
  try
    if NOT SameText(MTX_Lib.Reg_Lookup(DefaultDir + OpenEPSIni, USE_ALT_REGISTRY, false), 'Y') then //TFS-166590: don't use registry on thin client implementations
    begin
      //msgDebug('GetKEK3 NOT USE_ALT_REGISTRY');
      if (not VirtualizationAlreadyChecked) and (NOT SetVirtualization) then
        begin
          SM('Get Security: Failed to set virtualization off');
          Exit;
        end;
    end;

    sKey := GetKEK3withTimeStamp(sPath);
    if Length(sKey) > 0 then
    begin
      //SM('Begin to check for valid key');
      if sPath = '' then
      begin  //Return key w/o date otherwise
        //SM('Return key with no date');
        iDashPos := Pos(DASH, sKey);
        if iDashPos > 0 then//dash available
        begin
          sKey := RightStr(sKey, Length(sKey)-iDashPos);
          if Length(sKey) = KEK_KEY_LEN then
          begin
            Result := IsHexDigit(sKey);
            if NOT Result then
              sKey := '';
          end;
        end;
      end
      else //full path for KEK3 tool
      begin
        ///SM('Return full key and date for KEK3 tool');
        iDashPos := Pos(DASH, sKey);
        if iDashPos > 0 then//dash available
        begin
          sTempKey := RightStr(sKey, Length(sKey)-iDashPos);
          sDate    := LeftStr(sKey, 14);
          if Length(sTempKey) = KEK_KEY_LEN then
          begin
            Result := IsHexDigit(sTempKey) and IsDigit(sDate);
//            skey:= sDate+'-'+sTempKey;
            if NOT Result then
              sKey := '';
          end;
        end;
      end;
    end;
  except
    on E: Exception do
      SM('Try..Except Get Security - ' + E.Message);
  end;
end;

{$IFDEF TEST}
//this function is deprecated.. is here only to allow unit test to show how this fails when "locale" is non USA or Date format non-default
function GetFormattedDateTime(const sDate: string): string; //return mm/dd/yy hh:mm:ss format
var
  Year, Month, Day, Hour, Min, Sec: string;  //These are for datetime from myKek
begin
  Year  := LeftStr(sDate,4);
  Month := copy(sDate,5,2);
  Day   := copy(sDate,7,2);
  Hour  := copy(sDate,9,2);
  Min   := copy(sDate,11,2);
  Sec   := copy(sDate,13,2);
  Result := Month+'/'+Day+'/'+Year+' '+Hour+':'+Min+':'+Sec;
end;
{$ENDIF}

//JTG 32649 32650: instead of using above GetFormattedDateTime function to "format" the KEK datetime string into a string to pass into
// the system function 'TryStrToDateTime' (which ASSUMES the localized DateTime formats and will therefore interpret that
// fixed string YYYY/MM/DD HH:MM:SS incorrectly!!), just convert the string to a TDateTime ourselves, directly,
// and be done with it.  Should have been done like this to begin with. 
function KEKDateTimeStrToDateTime(const sDate: string; var DT: TDateTime): boolean;
var
  Year,Month,Day,Hour,Minute,Sec,Ms: word;
begin
  Year  := StrToIntDef(LeftStr(sDate,4),0); //each default value will force a FALSE result to this function
  Month := StrToIntDef(copy(sDate,5,2),0);  //.. because we do NOT want a TRUE result should any of these StrToInt's fail
  Day   := StrToIntDef(copy(sDate,7,2),0);
  Hour  := StrToIntDef(copy(sDate,9,2),99);
  Minute := StrToIntDef(copy(sDate,11,2),99);
  Sec := StrToIntDef(copy(sDate,13,2),99);
  Ms := 0;
  result := TryEncodeDateTime(Year,Month,Day,Hour,Minute,Sec,Ms,DT);
end;

function GetFormattedSec(Sec: Integer): string;
var
  D, H, M, S: string;
  ZD, ZH, ZM, ZS: Integer;
begin
  ZD := 0;
  ZH := Sec div 3600;
  ZM := Sec div 60 - ZH * 60;
  ZS := Sec - (ZH * 3600 + ZM * 60);
  if ZH > 24 then
  begin
    ZD := ZH div 24;
    ZH := ZH mod 24;
  end;
  D := IntToStr(ZD);
  H := FormatFloat('00',ZH);
  M := FormatFloat('00',ZM);
  S := FormatFloat('00',ZS);

  Result := '';
  if ZD > 0 then
    Result := D + ' day(s) ';
  Result := Result + H + ':' + M + ':' + S;
end;

// If mykek is older than serverkek or mykek is older than MAX_KEK3_AGE, return true.
// Get mykek from the registry. If serverkektime is blank, just compare it to MAX_KEK3_AGE.
// This is used by engine (w/o sServerKekTime) & openeps (w/ sServerKekTime) to determine if it's time to update their KEK3.
// serverTime is used to determine time difference between lane & server (used by openeps).
function TimeToMakeNewKEK3(sServerKekTime: string = ''; sServerTime: string = ''): Boolean;
var
  sMyKekTime: string;
  dMyKekTime,
  dServerKekTime,
  dLocalTime,  
  dServerTime: TDateTime;
  timeDiff: integer; // seconds
begin
  Result := FALSE;
  try
    if not VirtualizationAlreadyChecked then
      if NOT SetVirtualization then
      begin
        SM('TimeToMakeNewSec: Failed to set virtualization off');
        Exit;
      end;
    sMyKekTime := GetKEK3Timestamp;
    if sMyKekTime = '' then
    begin
      result := true;
      exit;
    end;
    //sMyKekTime := GetFormattedDateTime(sMyKekTime);
    //if TryStrToDateTime(sMyKekTime, dMyKekTime) then //Good datetime
    if KEKDateTimeStrToDateTime(sMyKekTime,dMyKekTime) then
    begin
      dLocalTime := Now;
      timeDiff := SecondsBetween(dLocalTime, dMyKekTime);
      result := timeDiff >= MAX_KEK3_AGE * SecsPerDay;
      if result then
      begin
        SM(Format('LK: %s, LD: %s, Age: %s',
            [DateTimeToStr(dMyKekTime), DateTimeToStr(dLocalTime), GetFormattedSec(timeDiff)]));
        exit;
      end;
      if Length(sServerKekTime) > 0 then
      begin //check if myKek is older than serverKek
        //sServerKekTime := GetFormattedDateTime(sServerKekTime);
        //if TryStrToDateTime(sServerKekTime,  dServerKekTime) then
        if KEKDateTimeStrToDateTime(sServerKekTime,dServerKekTime) then
        begin // determine time difference between lane & server
          if sServerTime <> '' then
          begin
            //sServerTime := GetFormattedDateTime(sServerTime);
            //if TryStrToDateTime(sServerTime, dServerTime) then
            if KEKDateTimeStrToDateTime(sServerTime,dServerTime) then
            begin
              dLocalTime := Now;
              timeDiff := SecondsBetween(dLocalTime, dServerTime) * CompareDateTime(dLocalTime, dServerTime);
              dMyKekTime := IncSecond(dMyKekTime, (timeDiff * -1));
            end
            else
            begin
              SM('Failed to convert server time >'+sServerTime+'< to dateTime');
              exit;
            end;
          end;
          Result := dMyKekTime < dServerKekTime;

          SM(Format('SD: %s, LD: %s, Offset: %s, SK: %s, LK: %s, Result: %s',
              [DateTimeToStr(dServerTime), DateTimeToStr(dLocalTime), GetFormattedSec(timeDiff),
               DateTimeToStr(dServerKekTime), DateTimeToStr(dMyKekTime), BoolToStr(Result, true)]));
        end
        else
          SM('Failed to convert server sec time >'+sServerKekTime+'< to dateTime');
      end;
    end
    else
      SM('Failed to convert local sec time >'+sMyKekTime+'< to dateTime');
  except
    on E: Exception do
      SM('Try..Except TimeToMakeNewSec - ' + E.Message);
  end;
end;

// return the kek3 timestamp (everything before the dash)
// This is used by the engine for the 810 msg.
function GetKEK3Timestamp: string;
var
  sKey: string;
  iDashPos: integer;
  sResult: string;
  dTime: TDateTime;
begin
  Result := '';  //nothing happened
  try
    if NOT SameText(MTX_Lib.Reg_Lookup(DefaultDir + OpenEPSIni, USE_ALT_REGISTRY, false), 'Y') then //TFS-166590: don't use registry on thin client implementations
    begin
      if (not VirtualizationAlreadyChecked) and (NOT SetVirtualization) then
      begin
        SM('GetKeyTimestamp - Failed to set virtualization off');
        Exit;
      end;
    end;
    sKey := GetKEK3withTimeStamp;
    if Length(sKey) > 0 then
    begin
      iDashPos := Pos(DASH, sKey);
      if iDashPos > 0 then//dash available
      begin
        sResult := LeftStr(sKey, iDashPos-1);

        //check to see if it's a valid datetime
        if Length(sResult) = TIMESTAMP_LEN then //14 chars only
        begin
          if IsDigit(sResult) then //check if a valid integer
          begin
            // make sure this timestamp is not in the future
            //tempStr := GetFormattedDateTime(sResult);
            //if TryStrToDateTime(tempStr, dTime) then
            if KEKDateTimeStrToDateTime(sResult,dTime) then
            begin
              if CompareDateTime(now, dTime) >= 0 then
                Result := sResult
              else
                SM('GetKeyTimestamp - Invalid time stamp: future' );
            end
            else
              SM('GetKeyTimestamp - Invalid time stamp: not a valid datetime' );
          end
        end
        else
          SM('GetKeyTimestamp - Invalid length of time stamp' );
      end
      else
        SM('GetKeyTimestamp - No separator found in key' );
    end
    else
      SM('GetKeyTimestamp - No key' );
  except
    on E: Exception do
      SM('Try..Except GetKeyTimestamp - ' + E.Message);
  end;
end;

{$IFNDEF D2007}

/// AppType: TRecFile / TOERecFile
/// NewKEK3: use NewKEK3 to encrypt KEK, if blank, use KEK3 in registry to encrypt KEK
/// OldKEK3: use OldKEK3 to decrypt KEK, if blank, use KEK3 in registry to decrypt KEK
function UpdateAEftFileKEK(AppType: TMTXAppType; AFileName: string; NewKEK3: string=''; OldKEK3: string=''): boolean; // DEV-18500
var
  tmpRecFile: TRecFile;
  {$IFDEF MTXEPSDLL}
  tmpOERecFile: TOERecFile;
  {$ENDIF MTXEPSDLL}
  UpdateResult: integer;
begin
  result := false;
  try
    if not FileExists(AFileName) then
    begin
      SM('UpdateAEftFileKey - file doesn''t exist - ' + AFileName);
      result := true;
      Exit;
    end;
    if AppType = atWinEPS then
    begin
      tmpRecFile := TRecFile.Create(AFilename);
      try
        UpdateResult := tmpRecFile.UpdateRecordKEK3(NewKEK3, OldKEK3); // this includes data validation
        result := UpdateResult = UPDATE_KEK3_RESULT_SUCCESS;
      finally
        tmpRecFile.Destroy;
      end;
    end
    else if AppType = atOpenEPS then
    begin
      {$IFDEF MTXEPSDLL}
      tmpOERecFile := TOERecFile.Create(AFilename);
      try
        UpdateResult := tmpOERecFile.UpdateRecordKEK3(NewKEK3); 
        result := UpdateResult = UPDATE_KEK3_RESULT_SUCCESS;
      finally
        tmpOERecFile.Destroy;
      end;
      {$ENDIF MTXEPSDLL}
    end;

  except
    on E: Exception do
      SM('Try..Except MTX_Utils.UpdateAEftFileKey - ' + E.Message);
  end;
end;

function UpdateEftFilesKEK(AppType: TMTXAppType; Path, Patterns: string; NewKEK3: string=''; OldKEK3: string=''; ProcessAll: boolean=false): boolean; // DEV-18500
var
  sr: TSearchRec;
  FullPath: string;
  tmpList: TStringList;
  i: integer;
{$IFDEF TEST}
  cnt: integer;
{$ENDIF}  
begin
  result := false;
  try
    //cnt := 1;
    FullPath :=  IncludeTrailingPathDelimiter(ExtractFileDir(Path));
    tmpList := TStringList.Create;
    try
      //tmpList.CommaText := Patterns;
      SplitStr(Patterns, ',', tmpList);
      for i := 0 to tmpList.Count -1 do
      begin
        if FindFirst(FullPath + tmpList[i], faAnyFile, sr) = 0 then
        begin
          repeat
            result := UpdateAEftFileKEK(AppType, FullPath + sr.Name, NewKEK3, OldKEK3);
            //cnt := cnt + 1;
{$IFDEF TEST}
if (cnt = 2) and FailInUpdateEftFiles then
begin
  FailInUpdateEftFileName := FullPath + sr.Name;
  if DeleteFile(FullPath + sr.Name) then
    SM('UpdateEftFilesKey [TEST ROLLBACK] deleted ' + FullPath + sr.Name)
  else
    SM('UpdateEftFilesKey [TEST ROLLBACK] failed to delete ' + FullPath + sr.Name);
  result := false;
end;
{$ENDIF}
            if NOT result then
            begin
              SM('failed to update security in ' + sr.Name); // FullPath +
              if NOT ProcessAll then // DEV-28982  
                Exit;
            end;
          until (FindNext(sr) <> 0);
          FindClose(sr);
        end;
      end;
    finally
      FreeAndNil(tmpList);
    end;
    result := true;
  except
    on E: Exception do
      SM('Try..Except MTX_Utils.UpdateEftFilesKey - ' + E.Message);
  end;
end;

{$IFDEF MSWINDOWS}
function UnzipArchiveFiles(fileName, suffix: string): boolean;
var
  sr: TSearchRec;
  CurrPath, ArchivePath, ArchivePath2, zip: string;
begin
  result := false;
  try
    //OurPath := ExtractFilePath(Application.ExeName);
    CurrPath :=  IncludeTrailingPathDelimiter(DefaultDir);
    ArchivePath := CurrPath + 'Archive\';
    ArchivePath2 := CurrPath + 'Archive';
    try
      if DirectoryExists(ArchivePath) then
      begin
        //chdir(OurPath + ARCHIVE);
        //if (not FileExists('..\unzip.exe')) then
        if (not FileExists(CurrPath + 'unzip.exe')) then
        begin
          SM('ERROR in UnzipArchiveFiles - ' + CurrPath + 'unzip.exe not found.');
          Exit;
        end;
        //if SysUtils.FindFirst('*.zip', faAnyFile, sr) = 0 then
        if SysUtils.FindFirst(ArchivePath + '*.zip', faAnyFile, sr) = 0 then
        begin
          repeat
            //zip := '..\unzip.exe -j -o -C ' + sr.name + ' ' + fileName + '*' + suffix;
            zip := Format('"%sunzip.exe" -j -o -C "%s" %s*%s -d "%s"', [CurrPath, ArchivePath + sr.name, fileName, suffix, ArchivePath2]);
            ExecuteAndWait(zip, 30);
            //SM('UnzipArchiveFiles Unzipped ' + ArchivePath + fileName + '*' + suffix + ' from ' + ArchivePath + sr.name);
            //SM('UnzipArchiveFiles Unzipped cmd=' + zip);
          until SysUtils.FindNext(sr) <> 0;
          SysUtils.FindClose(sr);
        end;
      end;
    finally
      //ChDir(OurPath);
    end;
    result := true;
  except
    on e : Exception do
      SM('Try..Except UnzipArchiveFiles : ' + e.Message);
  end;
end;
{$ENDIF}

{$IFDEF MSWINDOWS}
function ZipArchiveFiles(fileName, suffix: string): boolean;
var
  sr: TSearchRec;
  fdate, zip: string;
  ArcDate: string;
  ArchivePath: string;
  CurrPath: string;

  function Get_FileDate(fname, fprefix: string): string;
  var
    p, d: Integer;
  begin
    p := Pos(UpperCase(fprefix), UpperCase(fname));
    if p = 1 then
    begin
      d := Pos('.', fname);
      if d > 0 then
        Result := Copy(fname, p + Length(fprefix), d - p - Length(fprefix));
    end;
  end;

  function NormalizeDate(const filedate: string): string;
  var
    p, ArcNum: Integer;
    ArcNumS: string;
  begin
    Result := filedate;
    p := Pos('_', filedate);
    if p > 0 then
    begin
      ArcNumS := Copy(filedate, p + 1, Length(filedate));
      ArcNum := StrToIntDef(ArcNumS, -1);
      if ArcNum > 0 then
      begin
        Result := Copy(filedate, 1, p) + IntToStr(ArcNum);
      end;
    end;
  end;

begin
  result := false;
  try
    try
      CurrPath := IncludeTrailingPathDelimiter(DefaultDir);
      ArchivePath := CurrPath + 'Archive\';
      if SysUtils.FindFirst(ArchivePath + fileName + '????????*.*', faAnyFile, sr) = 0 then // ArchivePath linked to SysNames.Inc
      begin
        repeat
          fdate := Get_FileDate(sr.Name, filename);
          Arcdate := NormalizeDate(fdate);
          //zip := 'zip.exe -j -m "' + ArchivePath + 'Archive' + Arcdate + '.zip" "' + ArchivePath + '*' + fdate + suffix +'"';
          zip := Format('"%szip.exe" -j -m "%sArchive%s.zip" "%s*%s"', [CurrPath, ArchivePath, Arcdate, ArchivePath, fdate + suffix]);
          ExecuteAndWait(zip, 30);
          //SM('ZipArchiveFiles Zipped "' + ArchivePath + '*' + fdate + suffix + '" into "' + ArchivePath + 'Archive' + Arcdate + '.zip"');
        until SysUtils.FindNext(sr) <> 0;
        SysUtils.FindClose(sr);
      end
      else
        SM('ZipArchiveFiles - Failed to find file(s): ' + ArchivePath + fileName + '????????*.*');
    finally
      ;
    end;
    result := true;
  except
    on e : Exception do
      SM('Try..Except ZipArchiveFiles -  ' + e.Message);
  end;
end;
{$ENDIF}

function UpdateKEK3_Rollback(CurrDir: string): boolean;
begin
  //result := MTX_Utils.ExtractFromZip(CurrDir + BACKUP_ZIP_FILENAME, '*.*');
  // result := ExtractFromZip(CurrDir + BACKUP_ZIP_FILENAME, '*.*');                         // 828.5
  result := TMTXZip.ExtractFromZip(CurrDir + BACKUP_ZIP_FILENAME, '*.*');
  if result then
  begin
    SM('Rolled back successfully.');
    if NOT MTX_Utils.SecureDeleteFile(CurrDir + BACKUP_ZIP_FILENAME) then
      SM('UpdateSec_Rollback - failed to delete backup file - ' + CurrDir + BACKUP_ZIP_FILENAME);
  end
  else
    SM('[CRITICAL ERROR] failed to rollback');
end;

function UpdateKEK3(AppType: TMTXAppType=atUnKnown): boolean; // DEV-18500
var
  NewKEK3: AnsiString;
  OldKEK3: AnsiString;
  CurrDir: string;
  tmpInt, cp: integer;
begin
  InUpdateKEK3 := true;
  try
    result := false;
    cp := 1;
    try
      SM('------------------------------------------------- Update Security <');
      NewKEK3 := GetRandomKey;
      OldKEK3 := GetLastWorkingKEK3;
      if OldKEK3 = '' then
        GetKEK3(OldKEK3);
      SM('Old security >'+MTXEncryptionUtils.EncryptKEK3(OldKEK3)+'<');
      {$IFDEF SECURITY_DEBUG}
        msgSecurity('NewKEK3: ' + PrintableStr(NewKEK3, rtHex));
        msgSecurity('OldKEK3: ' + PrintableStr(OldKEK3, rtHex));
      {$ENDIF}
      cp := 2;
      if Length(NewKEK3) <> KEK_KEY_LEN then
      begin
        SM('Invalid length of security'); // + NewKEK3;
        Exit;
      end;

      if AppType = atUnknown then
      begin
        SM('Setting AppType...');
        {$IFDEF MTXEPSDLL}
        AppType := atOpenEPS;
        {$ELSE}
        AppType := atWinEPS;
        {$ENDIF}
      end;
      SM('AppType=' + IntToStr(Ord(AppType)));
      cp := 3;

      CurrDir := IncludeTrailingPathDelimiter(DefaultDir);
      {$IFDEF MTXEPSDLL}
      if AppType = atOpenEPS then // DOEP-27569 <
      begin
        if (DllTypes.AltFilePath = '') then
          SM('AltFilePath not in use')
        else
        begin
          if DirectoryExists(DllTypes.AltFilePath) then
          begin
            CurrDir := IncludeTrailingPathDelimiter(DllTypes.AltFilePath);
            SM('AltFilePath exists - ' + DllTypes.AltFilePath);
          end // DOEP-27569 >
          else
          begin
            SM('AltFilePath not exist - ' + DllTypes.AltFilePath);
            Exit;
          end;
          {$IFDEF MSWINDOWS}
          if NOT MTX_Utils.IsDirectoryWritable(DllTypes.AltFilePath) then
          begin
            SM('AltFilePath is not writable - ' + DllTypes.AltFilePath);
            Exit;
          end;
          {$ENDIF} 
        end;
      end;
      {$ENDIF}      

      if FileExists(CurrDir + BACKUP_ZIP_FILENAME) then //DEV-22783 <
      begin
        SM(CurrDir + BACKUP_ZIP_FILENAME + ' exists - previous security update was failed. Attempting rollback files...');
        UpdateKEK3_Rollback(CurrDir);
      end; // DEV-22783 >

      if AppType = atOpenEPS then
      begin
        cp := 10;
        //tmpInt := MTX_Utils.AddToZip(CurrDir + BACKUP_ZIP_FILENAME, CurrDir + 'tor????.eft,' + CurrDir + 'off????.eft');
        // tmpInt := AddToZip(CurrDir + BACKUP_ZIP_FILENAME, CurrDir + 'tor????.eft,' + CurrDir + 'off????.eft');     // 828.5
        tmpInt := TMTXZip.AddToZip(CurrDir + BACKUP_ZIP_FILENAME, CurrDir + 'tor????.eft,' + CurrDir + 'off????.eft');
        if tmpInt <> 0 then
        begin
          SM(Format('failed to zip eft files into %s (AddToZip cp=%d)', [CurrDir + BACKUP_ZIP_FILENAME, tmpInt]));
          Exit;
        end;
        cp := 11;
        if NOT UpdateEftFilesKEK(AppType, CurrDir, 'tor????.eft,off????.eft', NewKEK3, OldKEK3) then
        begin
          UpdateKEK3_Rollback(CurrDir);
          Exit;
        end;
        cp := 12;
        //MTX_Lib.DeleteFiles(CurrDir + 'lanemsg.*');
        MTX_Lib.DeleteFiles(IncludeTrailingPathDelimiter(DefaultDir) + 'lanemsg.*'); // DOEP-27569
        //SM(CurrDir + 'lanemsg.* was deleted');
        cp := 13;
      end
      else if AppType = atWinEPS then
      begin
        {$IFDEF MSWINDOWS}
        cp := 20;
        //if MTX_Utils.AddToZip(CurrDir + BACKUP_ZIP_FILENAME, CurrDir + 'actlog*.eft,'+CurrDir + 'tor.???,'+CurrDir + 'ofline??.???') <> 0 then
        // DEV-39939 - Zip actlog01.eft in EPS folder
        // if AddToZip(CurrDir + BACKUP_ZIP_FILENAME, CurrDir + 'actlog01.eft,'+CurrDir + 'tor.???,'+CurrDir + 'ofline??.???,' + CurrDir + 'preauth.eft') <> 0 // 828.5
        //then
        if TMTXZip.AddToZip(CurrDir + BACKUP_ZIP_FILENAME, CurrDir + 'actlog01.eft,'+CurrDir + 'tor.???,'+CurrDir + 'ofline??.???,' + CurrDir + 'preauth.eft')
            <> 0 then
        begin
          SM('failed to zip eft files');
          Exit;
        end;
        cp := 21;
        // DEV-39939 - Update actlog01.eft in EPS folder
        if NOT UpdateEftFilesKEK(AppType, CurrDir, 'actlog01.eft,tor.???,ofline??.???,preauth.eft', NewKEK3) then
        begin
          UpdateKEK3_Rollback(CurrDir);
          Exit;
        end;
        cp := 22;
        UpdateKEKThread := TUpdateKEKThread.Create(True);
        UpdateKEKThread.CurrPath := CurrDir;
        UpdateKEKThread.OldKEK3 := OldKEK3;
        UpdateKEKThread.NewKEK3 := NewKEK3;
        UpdateKEKThread.FreeOnTerminate := true;
        //UpdateKEKThread.Resume;
        SM('Update archive thread initiated');
        {$ENDIF}
        cp := 25;
      end;
      cp := 30;
      if NOT SetKEK3(NewKEK3) then
      begin
        SM('failed to set new security');
        //RollbackUpdateKEK3;
        Exit;
      end;
      cp := 31;
      if NOT MTX_Utils.SecureDeleteFile(CurrDir + BACKUP_ZIP_FILENAME) then
        SM('failed to delete backup file - ' + CurrDir + BACKUP_ZIP_FILENAME);
      cp := 32;
      result := true;
      SM('New security >'+MTXEncryptionUtils.EncryptKEK3(NewKEK3)+'<');
    except
      on E: Exception do
        SM(Format('Try..Except Update security (cp=%d) %s', [cp, e.Message]));
    end;
  finally
    InUpdateKEK3 := false;
    SM('------------------------------------------------- Update Security >');
  end;
end;

{$IFDEF MSWINDOWS}
//set new encryption key flag for WinEPS engine.
function SetNewEncryptionKeyFlag(i: integer = 0): boolean;
begin
  Result := false;
  try
    //SM('Begin SetNewEncryptionKeyFlag '+IntToStr(i));
    if not VirtualizationAlreadyChecked then
      if NOT SetVirtualization then
      begin
        SM('SetNewEncryptionSecFlag - Failed to set virtualization off');
        Exit;
      end;
    //SM('GetAppPath '+GetAppPath);
    Result := SaveIntKeyToRegistry(HKEY_LOCAL_MACHINE, GetAppPath, ENCRYPTION_KEY, i) = REGISTRY_WRITE_SUCCESS; //successful
  except
    on E: Exception do
      SM('Try..Except SetNewEncryptionSecFlag -  ' + e.Message);
  end;
end;
{$ENDIF}

{$IFDEF MSWINDOWS}
// used by WinEps engine.
function NeedNewEncryptionKey(const bSkipVirtualization: boolean=FALSE): boolean;
begin
  Result := false;
  try
    if NOT bSkipVirtualization then
    begin
      if not VirtualizationAlreadyChecked then
        if NOT SetVirtualization then
        begin
          SM('NeedNewEncryptionSec - Failed to set virtualization off');
          Exit;
        end;
    end;
    Result := (GetIntKeyFromRegistry(HKEY_LOCAL_MACHINE, GetAppPath, ENCRYPTION_KEY) = _EngineNeedsNewKEK3);
  except
    on E: Exception do
      SM('Try..Except NeedNewEncryptionSec -  ' + e.Message);
  end;
end;
{$ENDIF MSWINDOWS}

function GenerateKekIfBlank: boolean;
var KEK: AnsiString;
begin
  result := false;
  GetKEK3(KEK);
  if SameText(KEK, '') or SameText(KEK, 'key') then
  begin
    SM(Format('need to set sec >%s<', [KEK]));
    KEK := GetRandomKey;
    if NOT SetKEK3(KEK) then
    begin
      SM('failed to set sec');
      Exit;
    end
    else
      SM('New Security >'+MTXEncryptionUtils.EncryptKEK3(KEK)+'<');
  end
  else
    SM('sec was set already - ' + MTXEncryptionUtils.EncryptKEK3(KEK));
  result := true;
end;

{$ENDIF D2007}

procedure TUpdateKEKThread.Execute;
begin
  {$IFNDEF FILESERVER}
  {$IFDEF MSWINDOWS}
  try
    SM('------------------------------- TUpdateSecThread.Execute <');
    {$IFDEF MSWINDOWS} CoInitialize(nil); {$ENDIF}
    try
      SM('TUpdateSecThread: Unzip archive files');
      if NOT UnzipArchiveFiles('actlog', 'eft') then
      begin
        SM('TUpdateSecThread: failed to unzip archive files');
        Exit;
      end;

      //cp := 23;
      SM('TUpdateSecThread: Update eft files');
      if NOT UpdateEftFilesKEK(atWinEPS, CurrPath + 'Archive\', 'actlog*.eft', NewKEK3, OldKEK3, true) then
      begin
        SM('TUpdateSecThread: failed to update security in archived files');
        //UpdateKEK3_Rollback(CurrPath + 'Archive\'); // DEV-28982: no need to rollback using backup.zip for archive
        //Exit; // DEV-28982
      end;
      //cp := 24;
      SM('TUpdateSecThread: Archive eft files');
      if NOT ZipArchiveFiles('actlog', '.eft') then
      begin
        SM('TUpdateSecThread: failed to zip archive files');
        Exit;
      end;
    finally
      {$IFDEF MSWINDOWS} CoUninitialize; {$ENDIF}
      DeleteFiles(CurrPath + 'Archive\*.eft', true);
    end;
    SM('------------------------------- TUpdateSecThread.Execute >');
  except
    on e: Exception do
      SM('TUpdateSecThread.Execute exception.  ' + e.message);
  end;
  {$ENDIF}
  {$ENDIF}
end;

function GetLastWorkingKEK3: AnsiString;
begin
  result := '';
  if LastWorkingKEK3 <> '' then
  begin
    result := MTXEncryptionPrivate.DecryptKEK3(LastWorkingKEK3);
    msgSecurity('GetLastWorkingKEK3('+result+')');
  end;
end;

function SetLastWorkingKEK3(aKEK3: AnsiString): boolean;
begin
  result := false;
  if trim(aKEK3) <> '' then
  begin
    LastWorkingKEK3 := MTXEncryptionPrivate.EncryptKEK3(aKEK3);
    result := LastWorkingKEK3 <> '';
    msgSecurity('SetLastWorkingKEK3('+aKEK3+')');
  end;
end;


{$IFDEF MSWINDOWS}
(* // XE: Remove WiinEPS - not for OpenEPS
function CheckKEK3: boolean;
const
  KekErrMsg = 'CRITICAL ERROR: Failed to Set Security. Shutting down Engine. MTX Encryption Error � Please contact Support.';
var
  kek3: string;
  tmpKEKChanged: boolean;
begin
  result := false;
  try
    kek3 := '';
    //tmpKEKChanged := false; // [Hint] EngineU.pas(1475): Value assigned to 'tmpKEKChanged' never used 
    if not VirtualizationAlreadyChecked then
      if not SetVirtualization then
      begin
        SM('CRITICAL ERROR: Check Security: SetVirtualization Failed. Shutting down Engine. � Please contact Support.');
        SM(KekErrMsg);
        MenuMsgIfNotRPT(KekErrMsg);       //47616
{$IFDEF ENGINE}
        ShutDownEngine(false);
{$ENDIF}
        //EngineSrvU.WinEPS.ServiceStopShutdown;
        exit;       // <<< EXIT HERE  <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      end;
    if not GetKEK3(kek3) then // invalid (or blank) KEK3
    begin
      if GetLastWorkingKEK3 = '' then // could be EOD with an invalid KEK3
//        tmpKEKChanged := true
//      else
      begin
        if not SetKEK3(kek3) then
        begin
          SM('Check Security: unable to Get or Set Security');
          SM(KekErrMsg);
          MenuMsgIfNotRPT(KekErrMsg);      //47616
{$IFDEF ENGINE}
          ShutDownEngine(false);
{$ENDIF}
          //EngineSrvU.WinEPS.ServiceStopShutdown;
          exit;       // <<< EXIT HERE  <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
        end;
        SM('New Security >'+MTXEncryptionUtils.EncryptKEK3(kek3)+'<');
  //      SM('DEBUG-KEK: KEK3 >'+kek3+'<');
        MTXEncryptionUtils.MakeKeyEncryptionKey(kek3);
      end;
    end;

    tmpKEKChanged := KEKHasChanged;
//    if tmpKEKChanged then
    begin
      if NeedNewEncryptionKey or TimeToMakeNewKEK3 or tmpKEKChanged then
      begin
        SM('sec has changed = '+booltostr(tmpKEKChanged,true));
        SM('Old ServerTimestamp = ' + GetKEK3Timestamp + ' - Security update needed.');
        SM('Current Security >'+MTXEncryptionUtils.EncryptKEK3(kek3)+'<');
//        SM('DEBUG-KEK: KEK3 >'+kek3+'<');
        if UpdateKEK3 then // this updates the files and if successful gets a new kek3
        begin
          if GetKEK3(kek3) then
          begin
            //SM('New Security >'+MTXEncryptionUtils.EncryptKEK3(kek3)+'<');
//            SM('DEBUG-KEK: KEK3 >'+kek3+'<');
            MTXEncryptionUtils.MakeKeyEncryptionKey(kek3);
            SM('Security updated successful. Reseting security flag');
            SetNewEncryptionKeyFlag;
          end
          else
          begin
            SM('WARNING: Check Security: Get Security failed after Update Security');
            SM(KekErrMsg);
            MenuMsgIfNotRPT(KekErrMsg);       //47616
{$IFDEF ENGINE}
            ShutDownEngine(false);
{$ENDIF}
            exit;    // <<< EXIT HERE  <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
          end;
          if Assigned(uKEK3.UpdateKEKThread) then
          begin
              // don't set oldKEK3 here, it's too late. LastWorkingKEK3 is now the new kek3.
//            if tmpKEKChanged then // DEV-28953
//            begin
//              uKEK3.UpdateKEKThread.OldKEK3 := MTXEncryption.GetLastWorkingKEK3; // DEV-28953
//              SM('DEBUG-KEK: CheckKEK3 - OldKEK3 = ' + uKEK3.UpdateKEKThread.OldKEK3);
//              SM('Check Security: Pass old sec to thread');
//            end;
            SM('Check Security: Resume Thread');
            uKEK3.UpdateKEKThread.Resume;
          end;
          //MTXEncryption.SetLastWorkingKEK3(kek3); // DEV-28953
          SM('Check Security: Update Old Sec');
        end
        else
        begin
          //todo - update might fail on insignificant file, so we really want to shutdown engine in that case?
          SM('Check Security: unable to Update Security');
          SM(KekErrMsg);
          MenuMsgIfNotRPT(KekErrMsg);
{$IFDEF ENGINE}
          ShutDownEngine(false);
{$ENDIF}
          //EngineSrvU.WinEPS.ServiceStopShutdown;
          exit;    // <<< EXIT HERE  <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
        end
      end
      else
      begin
        SM('Current security >'+MTXEncryptionUtils.EncryptKEK3(kek3)+'<');
//        SM('DEBUG-KEK: KEK3 >'+kek3+'<');
      end;
    end;
    result := true;
    
    //result := false;
    //SM('FOURCE STOP');
  except
    on E: Exception do
      SM('Try..Except: EngineU.CheckSecurity ' + E.Message);
  end;
end; // CheckKEK3
*)
{$ENDIF MSWINDOWS}

initialization
  ExtendedLog('uKEK3 Initialization END');
finalization
  ExtendedLog('uKEK3 Finalization END');

end.

