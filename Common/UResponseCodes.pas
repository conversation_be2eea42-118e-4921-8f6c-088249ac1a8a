// (c) MTXEPS, Inc. 1988-2008
unit UResponseCodes;
{
v823.0 (patch) 03-12-08 JMR-A Load global XMLResponseCodes only once instead of with every trx.
}

interface

uses
  Classes, SysUtils, LibXmlParser, UWinEPSConfiguration, UXMLCommon,

  {$IFDEF MTXEPSDLL}
  DllTypes,
  {$ENDIF}

  MTX_Constants, MTX_XMLClasses, MTX_lib;

const
  SResponseCodes = 'ResponseCodes';
  SResponseCode = 'ResponseCode';
  SDescription = 'Description';
  SCardCode = 'CardCode';
  SCardType = 'CardType';
  STerminalAction = 'TerminalAction';
  SCashierLines5x40 = 'CashierLines5x40';
  SCashierLines1x16 = 'CashierLines1x16';
  SCardTypeReadOnly = 'CardTypeReadOnly';
  STerminalActionReadOnly = 'TerminalActionReadOnly';

type
  TResponseCodes = class;

  TResponseCode = class(TObject)
  private
    FResponseCodes: TResponseCodes;
  public
    ResponseCode: string;
    Description: string;
    CardCode: string;
    CardType: string;
    CardTypeReadOnly: Boolean;
    TerminalAction: string;
    TerminalActionReadOnly: Boolean;
    CustomerLines: TCustomerLineList;
    CashierLines5x40: TLineCollection5;
    CashierLines1x16: TLineCollection5;
    constructor Create(AResponseCodes: TResponseCodes);
    destructor Destroy; override;
  end;

  TResponseCodes = class(TXMLConfiguration)
  private
    FList: TList;
    function GetItem(Index: Integer): TResponseCode;
    function GetCount: Integer;
    function LoadFromXMLEx(AFileName: string): boolean;
  public
    Version: string;                                                            { YHJ-499 < }
    LastModified: string;
    fileName: string;                                                           { YHJ-499 > }
    HasDefaultRespCode: Boolean;                                                { YHJ-114 }
    OldLastModified: string; // DEV-12765
    XMLHash: string; // DEV-12765
    constructor Create;
    destructor Destroy; override;
    procedure Clear;
    procedure Add(AResponseCode: TResponseCode);
    procedure Remove(AResponseCode: TResponseCode);
    {$IFDEF GUIJR}
    procedure SaveToXML(AFileName: string); // XE: Remove WinEPS - not for OpenEPS
    {$ENDIF}
    function LoadFromXML(aFileName: string): boolean;
    procedure Sort;
    function LocateByResponseCodeAndCardCode(AResponseCode, ACardCode: string): TResponseCode;
    function ValidateVersion: boolean;                                          { YHJ-499 }
    property Items[Index: Integer]: TResponseCode read GetItem;
    property Count: Integer read GetCount;
    //function GetXMLHash(aLastModified: string): string; // DEV-12765 // XE: Remove WinEPS - not for OpenEPS
    {$IFDEF GUIJR}
    procedure CreateNodes(RootNode: TXMLParserNode; aLastModified: string); // DEV-12765 // XE: Remove WinEPS - not for OpenEPS
    {$ENDIF}
  end;

  TResponseCodesList = class
  private
    FActive: Boolean; 
    Items: TStringList;
    procedure ClearItems;
    function PopulateResponseCodesList(fileLocation: string): Boolean;    
  public
    constructor Create(fileLocation: string);
    destructor Destroy; override;
    function FindResponseCodes(hostName: string3): TResponseCodes;
    property Active: Boolean read FActive;
  end;

function ExtractCardCode(Line: string): string;

var
  XMLResponseCodesList: TResponseCodesList = nil; //JMR-A : list of responseCodes objects.

implementation

uses
  FinalizationLog,
  MTXEncryptionUtils,
  MTX_Utils;

function ExtractCardCode(Line: string): string;
var
  p: Integer;
begin
  p := Pos(' ', Line);
  if p > 0 then
    Result := Copy(Line, 1, p - 1)
  else
    Result := Line;  
end;

function CompareResponseCodes(Item1, Item2: Pointer): Integer;
begin
  Result := CompareStr(TResponseCode(Item1).ResponseCode, TResponseCode(Item2).ResponseCode);
  if Result = 0 then Result := CompareStr(TResponseCode(Item1).CardCode, TResponseCode(Item2).CardCode);
end;

{ TResponseCodes }

procedure TResponseCodes.Add(AResponseCode: TResponseCode);
begin
  FList.Add(AResponseCode);
end;

procedure TResponseCodes.Clear;
begin
  try
    while FList.Count > 0 do
      TResponseCode(FList.Last).Free;
  except
    on e: exception do
    begin
      SM('Try..Except TResponseCodes.Clear FList.Count=' + inttostr(FList.Count) + ' - ' + e.message);
      raise;
    end;
  end;
end;

constructor TResponseCodes.Create;
begin
  inherited;
  FList := TList.Create;
end;

destructor TResponseCodes.Destroy;
begin
  try
    Clear;
    FreeAndNil(FList);
    inherited;
  except
    on e: exception do
    begin
      SM('Try..Except TResponseCodes.Destroy - ' + e.message);
      raise;
    end;
  end;
end;

function TResponseCodes.GetCount: Integer;
begin
  Result := FList.Count;
end;

function TResponseCodes.GetItem(Index: Integer): TResponseCode;
begin
  Result := FList[Index];
end;

function TResponseCodes.LoadFromXML(aFileName: string): boolean;
var ErrCount: integer;
begin
  ErrCount := 0;
  repeat
    result := LoadFromXMLEx(aFileName);
    if not result then                                                          
    begin
      Inc(ErrCount);
      SM(Format('TResponseCodes.LoadFromXML failed (%d)', [ErrCount]));
      Delay(RETRY_FILE_LOCK_MS); // 300 ms by default
    end;
  until result or (ErrCount >= RETRY_FILE_LOCK_COUNT); // 3 times by default
  if result and (ErrCount > 0) then
    SM(Format('TResponseCodes.LoadFromXML successful after %d tries - %s', [ErrCount, aFileName]));
  if NOT result then
    SM(Format('TResponseCodes.LoadFromXML failed after %d tries - %s', [ErrCount, aFileName]));
end;

// DOEP-56189 - FuelEPS: Do Not allow processing on XML load failure
// DOEP-58668 - If an XML file is corrupted mark everything down.
function TResponseCodes.LoadFromXMLEx(AFileName: string): boolean;
var
  Node, Node2: TXMLParserNode;
  cp, i, j: Integer;
  isParseXMLFailed : Boolean;
begin
  result := false;
  isParseXMLFailed := False;
  cp := 0;
  try
    Clear;
    if NOT FXMLParser.LoadFromFile(AFileName) then                               // 828.5
    begin                                                                        // 828.5
      SM('TResponseCodes.LoadFromXMLEx - failed to load ' + AFileName);
      Exit;                                                                      // 828.5
    end;                                                                         // 828.5
    FXMLParser.StartScan;                                                        // 828.5
    ScanElement(nil);
    fileName := AFileName;                                                        { YHJ-499 }
    cp := 1;
    HasDefaultRespCode := False;                                                  { YHJ-114 }
    if Root.Name = SResponseCodes then
    begin
      cp := 2;
      Version := Root.Attr.Values['Version'];                                     { YHJ-499 < }
      if Version = '' then Version := DEFAULT_XML_VERSION;
      LastModified := Root.Attr.Values['LastModified'];                           { YHJ-499 > }
      OldLastModified := LastModified; // DEV-12765
      XMLHash := GetCryptoHash(Root.GetXMLStr); // DEV-12765
      for i := 0 to Root.Children.Count - 1 do
      begin
        Node := Root.Children[i];
        if Node.Name = SResponseCode then
        begin
          with TResponseCode.Create(Self) do
          begin
            ResponseCode := Node.Attr.Values[SResponseCode];
            if ResponseCode = '' then
            begin
              isParseXMLFailed := True;
              Break;
            end
            else
            if ResponseCode = DEFAULT_RESPCODE then
              HasDefaultRespCode := True;
            Description := Node.Attr.Values[SDescription];
            CardCode := Node.Attr.Values[SCardCode];
            CardType := Node.Attr.Values[SCardType];
            CardTypeReadOnly := StrToBoolean(Node.Attr.Values[SCardTypeReadOnly]);
            TerminalAction := Node.Attr.Values[STerminalAction];
            TerminalActionReadOnly := StrToBoolean(Node.Attr.Values[STerminalActionReadOnly]);
            for j := 0 to Node.Children.Count - 1 do
            begin
              Node2 := Node.Children[j];
              if Node2.Name = SCustomerLines then
                GetCustomerLines(Node2, CustomerLines)
              else
              if Node2.Name = SCashierLines5x40 then
                GetLineCollection5(Node2, CashierLines5x40)
              else
              if Node2.Name = SCashierLines1x16 then
                GetLineCollection5(Node2, CashierLines1x16);
            end;
          end;
        end
        else
        begin
          isParseXMLFailed := True;
          Break;
        end;
      end;
    end
    else
      isParseXMLFailed := True;
    cp := 3;
    ValidateVersion;                                                              { YHJ-499 }
    cp := 5;
    Sort;                                                                         { YHJ-114 > }
    cp := 6;
    FreeAndNil(Root);
    cp := 7;
    if (not isParseXMLFailed) and HasDefaultRespCode then
      result := true;
  except on e: exception do
    SM(format('TResponseCodes.LoadFromXML FAILED (cp=%d) - Filename(%s) EXCEPTION MSG - %s',[cp, AFileName,e.message]));
  end;
end;

function TResponseCodes.LocateByResponseCodeAndCardCode(AResponseCode,
  ACardCode: string): TResponseCode;
var
  i: Integer;
begin
  Result := nil;
  for i := 0 to Count - 1 do
  begin
    //sm(format('****DEBUG: LocateByResponseCodeAndCardCode, RspList item# %d, ResponseCodeInList/LookingFor %s/%s,' +
    //          ' CardCodeInList/LookingFor %s/%s',[i,Items[i].ResponseCode,aResponseCode,Items[i].CardCode,ACardCode]));

    if (Items[i].ResponseCode = AResponseCode) and (UpperCase(Items[i].CardCode) = UpperCase(ACardCode)) then
    begin
      Result := Items[i];
      break;
    end;
  end;
end;

function TResponseCodes.ValidateVersion: boolean;                               { YHJ-499 }
var
  ver: Double;
  sVer: string;
begin
  // TODO: need to convert XMLObj to double
  ver := GetValidXMLVersion(xfResponseCode);
  sVer := ConvertToDecimalSeparator(Version);     // 33046
  result := StrToFloatDef(sVer, 0) = ver;
  if not result then
    SM(Format('%s%s Version (%s) is %s valid (should be %.1f)',
        [iif(not result, '***WARNING ',''), ExtractFileName(fileName), sVer, iif(not result, 'not', ''), ver]));
end;

procedure TResponseCodes.Remove(AResponseCode: TResponseCode);
begin
  FList.Remove(AResponseCode);
end;

{ // XE: Remove WinEPS - not for OpenEPS
function TResponseCodes.GetXMLHash(aLastModified: string): string; // DEV-12765
var
  RootNode: TXMLParserNode;
begin
  RootNode := TXMLParserNode.Create(nil);
  try
    CreateNodes(RootNode, aLastModified);
    result := GetMD5Hash(RootNode.GetXMLStr);
  finally
    RootNode.Free;
  end;
end;
}

//{ // XE: Remove WinEPS - not for OpenEPS
{$IFDEF GUIJR}
procedure TResponseCodes.CreateNodes(RootNode: TXMLParserNode; aLastModified: string); // DEV-12765
var
  Node, Child: TXMLParserNode;
  i: Integer;
  ResponseCode: TResponseCode;
begin
  RootNode.Name := SResponseCodes;
  RootNode.Attr.Values['Version'] := FormatFloat(FORMAT_XML_VERSION, GetValidXMLVersion(xfResponseCode)); // YHJ-499
  if aLastModified = '' then
    RootNode.Attr.Values['LastModified'] := FormatDateTime(FORMAT_LASTMODIFIED, Now) // YHJ-499
  else
    RootNode.Attr.Values['LastModified'] := aLastModified;;

  for i := 0 to Count - 1 do
  begin
    ResponseCode := Items[i];
    Node := RootNode.AddChild(SResponseCode);
    Node.Attr.Values[SResponseCode] := ResponseCode.ResponseCode;
    Node.Attr.Values[SDescription] := ResponseCode.Description;
    Node.Attr.Values[SCardCode] := ResponseCode.CardCode;
    Node.Attr.Values[SCardTypeReadOnly] := BooleanStr[ResponseCode.CardTypeReadOnly];
    Node.Attr.Values[STerminalAction] := ResponseCode.TerminalAction;
    Node.Attr.Values[STerminalActionReadOnly] := BooleanStr[ResponseCode.TerminalActionReadOnly];
    Child := Node.AddChild(SCustomerLines);
    AddCustomerLines(Child, ResponseCode.CustomerLines);
    Child := Node.AddChild(SCashierLines5x40);
    AddLineCollection5(Child, ResponseCode.CashierLines5x40);
    Child := Node.AddChild(SCashierLines1x16);
    AddLineCollection5(Child, ResponseCode.CashierLines1x16);
  end;
end;
{$ENDIF}

//{ // XE: Remove WinEPS - not for OpenEPS
{$IFDEF GUIJR}
procedure TResponseCodes.SaveToXML(AFileName: string);
var
  RootNode: TXMLParserNode;
begin
  RootNode := TXMLParserNode.Create(nil);
  try
    CreateNodes(RootNode, '');
    RootNode.SaveToFile(AFileName);
  finally
    RootNode.Free;
  end;
end;
{$ENDIF}

procedure TResponseCodes.Sort;
var
  I: Integer;
  R: TResponseCode;                                                             
begin
  FList.Sort(CompareResponseCodes);
  for I := 0 to FList.Count - 1 do
  begin
    R := FList.Items[I];
    if R.ResponseCode = DEFAULT_RESPCODE then
      FList.Move(I, 0);
  end;
end;

{ TResponseCode }

constructor TResponseCode.Create(AResponseCodes: TResponseCodes);
begin
  inherited Create;
  FResponseCodes := AResponseCodes;
  FResponseCodes.Add(Self);
  CustomerLines := TCustomerLineList.Create;
  CashierLines5x40 := TLineCollection5.Create;
  CashierLines1x16 := TLineCollection5.Create;
end;

destructor TResponseCode.Destroy;
begin
  CustomerLines.Clear; // Shmilo - fixed memory leak
  FreeAndNil(CustomerLines);
  FreeAndNil(CashierLines5x40);
  FreeAndNil(CashierLines1x16);
  FResponseCodes.Remove(Self);
  inherited;
end;

{ ---------------------------------------------------------------------------- }

constructor TResponseCodesList.Create(fileLocation: string);
begin
  FActive := false;
  Items := TStringList.Create;
  Items.CaseSensitive := false;
  Items.Duplicates := dupIgnore;
  if not Assigned(XMLStoreConfigurations) then
    LoadStoreConfigurationsXML(StoreConfigurationsXML_);
  FActive := PopulateResponseCodesList(fileLocation);
end;

destructor TResponseCodesList.Destroy;
begin
  try
    ClearItems;
    Items.Free;
  except
    on e: exception do
    begin
      SM('Try..Except TResponseCodesList.Destroy - ' + e.message);
      raise;
    end;
  end;
end;

procedure TResponseCodesList.ClearItems;
begin
  try
    // must free objects explicitly
    while Items.Count > 0 do
    begin
      if Assigned(Items.Objects[0]) then
        Items.Objects[0].Free;
      Items.Delete(0);
    end;
  except
    on e: exception do
    begin
      SM('Try..Except TResponseCodesList.ClearItems - ' + e.message);
      raise;
    end;
  end;
end;

function TResponseCodesList.PopulateResponseCodesList(fileLocation: string): Boolean;
{$IFNDEF MTXEPSDLL}
  var i: integer;
{$ENDIF}

  function InsertIntoList(hostName: string3): boolean;
  var
    rc: TResponseCodes;
    RespCodeFileName: string;
  begin
    result := false; // false only if LoadFromXML fails
    if Items.IndexOf(hostName) = -1 then
    begin
      RespCodeFileName := fileLocation + LowerCase(RESPONSECODES_FILENAME_PREFIX + hostName + '.xml');
      if FileExists(RespCodeFileName) then
      begin
        SM('Adding ' + hostName + ' to ResponseCodesList. ['+ RespCodeFileName +']');
        rc := TResponseCodes.Create;
        result := rc.LoadFromXML(RespCodeFileName);
        if result
          then Items.AddObject(hostName, rc)
          else SM('Faied to add ' + hostName + ' to ResponseCodesList. ['+ RespCodeFileName +']');
      end
      else
        SM('PopulateResponseCodesList ERROR: File Not Found: '+RespCodeFileName);
    end;
  end;

begin
  result := false;
  SM('PopulateResponseCodesList fileLocation=' + fileLocation);
  try
    ClearItems;

    // add MTX (internal) response codes to list.
    if NOT InsertIntoList('MTX') then
      Exit;

    {$IFDEF FUEL}
    if NOT InsertIntoList('EPS') then
      Exit;
    {$ELSE}
      {$IFDEF MTXEPSDLL}
        //if usingServerEPSHost then        //69739
        //begin
          if NOT InsertIntoList('EPS') then
            Exit;
  //      HMapSuffix := 'EPS';
        //end;
      {$ELSE}
        // add host response codes to list (only adds hosts that are being used).
        for i := 0 to XMLStoreConfigurations.Hosts.Count -1 do
          if NOT InsertIntoList(XMLStoreConfigurations.Hosts.Host[i].Suffix) then
            Exit;
      {$ENDIF MTXEPSDLL}
    {$ENDIF FUEL}
    SM('PopulateResponseCodesList END');
    result := true;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TResponseCodesList.PopulateResponseCodesList: ' + e.message);
  end;
end;

function TResponseCodesList.FindResponseCodes(hostName: string3): TResponseCodes;
var i, cp: integer;
begin
  result := nil;
  cp := 1;
  try
//    if hostName = 'EPS' then hostName := 'MTX';
    if NOT Assigned(Items) then
    begin
      SM('TResponseCodesList.FindResponseCodes - Item not defined');
      Exit;
    end;
    cp := 2;
    i := Items.IndexOf(hostName);
    cp := 3;
    if i >= 0 then
    begin
      cp := 4;
      if Assigned(Items.Objects[i]) and (Items.Objects[i] is TResponseCodes)
        then result := TResponseCodes(Items.Objects[i])
        else SM('TResponseCodesList.FindResponseCodes - TResponseCodes object was not created for ' + hostName);
      cp := 5;
    end
    else
      SM('ERROR: FindResponseCodes for host >' + hostName + '< not found!');
    cp := 6;
  except
    on e: exception do
      SM(Format('****TRY..EXCEPT: TResponseCodesList.FindResponseCodes (cp=%d): %s', [cp, e.message]));
  end;
end;

initialization
{$IFDEF GUIJR}
  xLog('UResponseCodes initialization END');

finalization
  xLog('UResponseCodes finalization BEGIN');
  if Assigned(XMLResponseCodesList) then
    FreeAndNil(XMLResponseCodesList);
  xLog('UResponseCodes finalization END');
{$ELSE}
  ExtendedLog('UResponseCodes Initialization');

finalization
  ExtendedLog('UResponseCodes Finalization', procedure
    begin
      if Assigned(XMLResponseCodesList) then
        FreeAndNil(XMLResponseCodesList);
    end
    );
{$ENDIF GUIJR}
end.

