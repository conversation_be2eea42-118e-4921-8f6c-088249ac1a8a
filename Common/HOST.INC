{
v817.1 12-06-05 YHJ-48 DEV-495 send our signatures to ACI
v815.3 06-22-05 JMR-D Add HostFtpDir (used by REC host custom ftp option)
v815.3 04-25-05 JMR-C Add HostProgramId
v815.0 10-26-04 TSL-N Add two tenders so expand var HostTransactions from 10 to
                      12, reduce HostTransactions from 7 to 5
v814.0 05-11-04 TSL-M Add HostTenderOffline boolean array (Publix)
v811.1 08-18-03 JMR-B Add Settlement report option.
v810 03-02-03 TSL-L Add HostLastBINFtpDate
v810 01-27-03 TSL-K Change HostSendACHTotals to HostPartialBINDll
v807 09-13-01 JMR-A Add ERCLocalPort.  TSL move HostERCLocalPort
v807 05-31-01 TSL-J Take out DUKPT, now in process.inc
v807 05-11-01 TSL-I Change HostUnused to HostDivision add hostFieldNames
 ** 11-30-00 TSL-H Add HostUseDukpt
 ** 09-18-00 TSL-G Add HostIPLocalPort1,2,3
 ------------------Service pack 9 -----------------------------
 ** 06-08-00 TSL-F Add Check type codes (1 digit codes )
 ** 03-30-00 TSL-E Add HostSigCapMode
 ** 10-19-99 TSL-D Add stuff for CES FSP platform
 ** 06-20-99 TSL-C Add HostWicLocal HostFtpbin for Paypoint/Albertsons
 ** 10-07-98 TSL-B Add HostSendHealthMsg, HostHealthMsgInt
 ** 09-11-98 JGS   Add Host Truncate AcctNo array for receipts
 ** 08-20-98 JGS   change length of offline wait intervals to 5
 ** 04-27-98 JGS   Rename Host Local IP 1-3 as unused
 ** 04-02-98 JGS   Add some new variables
 ** 03-30-98 TSL-A Add some new variables
}
{ Member: HOST.INC                                                     }
{ Maps  : HOST.EFT                                                     }
{$A-}
           HostNameL          = 8;
           HostPollCharL      = 2;
           HostPollIntvlL     = 2;
           HostSuspendCtL     = 2;
           HostFatalCtL       = 2;
           HostResumeCtL      = 2;
           HostOnTOL          = 2;
           HostLocalL         = 1;
           HostOffTOL         = 2;
           Host_SSCP_IDL      = 16;
           Host_Is_EBCDICL    = 1;
           Host_Comm_NRZIL    = 1;
           Host_Main_SessionL = 2;
           Host_TOR_SessionL  = 2;
           Host_Offl_SessionL = 2;
           HostMerchantIDL    = 16;
           HostPasswordL      = 8;
           HostCkAuthTypeL    = 1;
           HostCkCCodeL       = 2;
           HostDefaultStateL  = 2;
           HostOnDbCkL        = 1;
           HostOffDbCkL       = 1;
           HostPortL          = 1;
           HostBaudL          = 3;
           HostBitsL          = 1;
           HostParityL        = 1;
           HostStopL          = 1;
           HostIRQL           = 1;
           HostStoreTermIDL   = 6;    { psuedo term }
           HostMsgSecCodeL    = 8;    { bit 96 }
           HostMgtInfoDataL   = 18;   { bit ?? }
           HostSendMgtInfoL   = 1;    { if Y put MgtInfoData on front of msg }
           HostMerchTypeL     = 4;    { = '5411' VISA code for Grocery store }
           HostInstIDCodeL    = 11;
           HostMerchNameL     = 25;
           HostMerchAddressL  = 23;
           HostMerchCityL     = 13;
           HostMerchStateL    = 2;
           HostZipCodeL       = 9;
           HostCountryL       = 2;
           HostCurrencyCodeL  = 3;    { = '840' for  USA }
           HostStateCodeL     = 2;
           HostCountyCodeL    = 3;    { '000' default }
           HostOffsetHrsGMTL  = 3;    { Hrs to add to local time to get GMT }
           HostClerkLaneL     = 1;    { Clerk or Lane accounting            }
           HostTORLateL       = 1;
           HostTORTimeoutL    = 1;
           HostTORNoT2L       = 1;
           Host_Trace_DataL   = 1;
           HostOffResubmitL   = 1;
           HostTerminalTypeL  = 2;

           HostMerchantNumL   = 6;
           HostTerminalNumL   = 3;

           HostRetryPhone1L   = 1;
           HostPhone1L        = 20;  { or TCP/IP port 1}
           HostLogon1L        = 40;  { or TCP/IP addr/name 1}

           HostRetryPhone2L   = 1;
           HostPhone2L        = 20;  { or TCP/IP port 2}
           HostLogon2L        = 40;  { or TCP/IP addr/name 2}

           HostRetryPhone3L   = 1;
           HostPhone3L        = 20;  { or TCP/IP port 3}
           HostLogon3L        = 40;  { or TCP/IP addr/name 3}

           HostModemInitL     = 40;
           HostHoldTimeL      = 5;

           HostInitSelfL      = 8;

           { JGS-MH }
           HostConfigNumL     = 2;
           HostSecondScrNameL = 3{7};
           HostTransactionsL  = 1;
           HostTimeoutL       = 2;
           HostOffFwdDelayL   = 5;
           HostOffPaceDelayL  = 5;
           HostTORWaitIntL    = 5;
           HostTrxWaitIntL    = 5;
           HostSuffixL        = 3;
           { JGS-MH }

           HostWicLocalL      = 1;      { TSL-C }
           HostFtpBinL        = 1;      { TSL-C }
           HostDivisionL      = 3;      { TSL-I }

           { JGS-A Start }
           HostSetForDayLightL= 1;
           HostSaveCheckInfoL = 1;
           HostPartialBINDllL = 1;      { TSL-K }
           HostACHStateCodesL = 49;
           { JGS-A End   }

           HostTruncateAcctNoL= 1;

           HostSendHealthMsgL = 1;      { TSL-B }
           HostHealthMsgIntL  = 4;      { TSL-B }

           HostFTPUserNameL   = 20;   // TSL-E
           HostFTPIpAddrL     = 20;   // TSL-E
           HostFTPPasswordL   = 20;   // TSL-E
           HostFTPFileNameL   = 12;   // TSL-E
           HostFtpDirL        = 100;  // JMR-D
           HostPONumberUsedL  = 1;    // TSL-D
           HostPlatformIDL    = 1;    // TSL-D
           HostClearingCodeL  = 3;    // TSL-D
           HostMerchBINL      = 6;    // TSL-D
           HostMerchICAL      = 6;    // TSL-D
           HostSigCapModeL    = 1;    // TSL-E
           HostPersonalCkL    = 1;    // TSL-F
           HostPayrollCkL     = 1;    // TSL-F
           HostIPLocalPortL   = 20;   // TSL-G
           HostERCLocalPortL  = 5;    // JMR-A
           HostFTPBINDaysL    = 3;    // TSL-L
           { HostLastBINFtpDate = TDateTime; see below } // TSL-L
           HostSettleRptL     = 1;                                 { JMR-B }
           HostProgramIdL     = 3;    // JMR-C
           { YHJ-48 begin }
           HostERCUseL          = 1; // Y/N
           HostERCTransferTypeL = 1; // F: Ftp, D: Don't send file
           HostERCFTPIpAddrL    = 20;
           HostERCFTPPortL      = 5;
           HostERCFTPBackupIpAddrL = 20;
           HostERCFTPBackupPortL   = 5;           
           HostERCFTPUserNameL  = 20;
           HostERCFTPPasswordL  = 20;
           HostERCFTPFileNameL  = 12;
           HostERCFTPDirL       = 100;
           { YHJ-48 end }

           HostDSN_           = 'host.eft';

           hCR = #10;
           hostFieldNames : PChar =
             'HostName=8'             + hCR +    {0}
             'HostPollChar=2'         + hCR +
             'HostPollIntvl=2'        + hCR +
             'HostSuspendCt=2'        + hCR +
             'HostFatalCt=2'          + hCR +
             'HostResumeCt=2'         + hCR +
             'HostOnTOxxxx=2'         + hCR +
             'HostLocal=1'            + hCR +
             'HostOffTO=2'            + hCR +
             'Host_SSCP_ID=16'        + hCR +
             'Host_Is_EBCDIC=1'       + hCR +    {10}
             'Host_Comm_NRZI=1'       + hCR +
             'Host_Main_Session=2'    + hCR +
             'Host_TOR_Session=2'     + hCR +
             'Host_Offl_Session=2'    + hCR +
             'HostMerchantID=16'      + hCR +
             'HostPassword=8'         + hCR +
             'HostCkAuthType=1'       + hCR +
             'HostCkCCode=2'          + hCR +
             'HostDefaultState=2'     + hCR +
             'HostOnDbCk=1'           + hCR +    {20}
             'HostOffDbCk=1'          + hCR +
             'HostPort=1'             + hCR +
             'HostBaud=3'             + hCR +
             'HostBits=1'             + hCR +
             'HostParity=1'           + hCR +
             'HostStop=1'             + hCR +
             'HostIRQ=1'              + hCR +
             'HostStoreTermID=6'      + hCR +
             'HostMsgSecCode=8'       + hCR +
             'HostMgtInfoData=18'     + hCR +    {30}
             'HostSendMgtInfo=1'      + hCR +
             'HostMerchType=4'        + hCR +
             'HostInstIDCode=11'      + hCR +
             'HostMerchName=25'       + hCR +
             'HostMerchAddress=23'    + hCR +
             'HostMerchCity=13'       + hCR +
             'HostMerchState=2'       + hCR +
             'HostZipCode=9'          + hCR +
             'HostCountry=2'          + hCR +
             'HostCurrencyCode=3'     + hCR +    {40}
             'HostStateCode=2'        + hCR +
             'HostCountyCode=3'       + hCR +
             'HostOffsetHrsGMT=3'     + hCR +
             'HostClerkLane=1'        + hCR +
             'HostTORLate=1'          + hCR +
             'HostTORTimeout=1'       + hCR +
             'HostTORNoT2=1'          + hCR +
             'Host_Trace_Data=1'      + hCR +
             'HostOffResubmit=1'      + hCR +
             'HostTerminalType=2'     + hCR +    {50}
             'HostMerchantNum=6'      + hCR +
             'HostTerminalNum=3'      + hCR +
             'HostRetryPhone1=1'      + hCR +
             'HostPhone1=20'          + hCR +
             'HostLogon1=40'          + hCR +
             'HostRetryPhone2=1'      + hCR +
             'HostPhone2=20'          + hCR +
             'HostLogon2=40'          + hCR +
             'HostRetryPhone3=1'      + hCR +
             'HostPhone3=20'          + hCR +    {60}
             'HostLogon3=40'          + hCR +
             'HostModemInit=40'       + hCR +
             'HostHoldTime=5'         + hCR +
             'HostInitMain=8'         + hCR +
             'HostInitTOR=8'          + hCR +
             'HostInitOffline=8'      + hCR +
             'HostConfigNum=2'        + hCR +
             'HostSecondScrName=3'    + hCR +
             'HostTimeoutDB=2'        + hCR +
             'HostTimeoutCR=2'        + hCR +    {70}
             'HostTimeoutPD=2'        + hCR +
             'HostTimeoutPC=2'        + hCR +
             'HostTimeoutCK=2'        + hCR +
             'HostTimeoutEF=2'        + hCR +
             'HostTimeoutEC=2'        + hCR +
             'HostTimeoutU1=2'        + hCR +
             'HostTimeoutPN=2'        + hCR +
             'HostTimeoutU2=2'        + hCR +
             'HostOffFwdDelay=5'      + hCR +
             'HostOffPaceDelay=5'     + hCR +    {80}
             'HostTORWaitInt=5'       + hCR +
             'HostTrxWaitInt=5'       + hCR +
             'HostSuffix=3'           + hCR +
             'HostSendHealthMsg=1'    + hCR +
             'HostHealthMsgInt=4'     + hCR +
             'HostWicLocal=1'         + hCR +
             'HostFtpBin=1'           + hCR +
             'HostDivision=3'         + hCR +
             'HostPort2=1'            + hCR +
             'HostBaud2=3'            + hCR +    {90}
             'HostBits2=1'            + hCR +
             'HostParity2=1'          + hCR +
             'HostStop2=1'            + hCR +
             'HostIRQ2=1'             + hCR +
             'HostPort3=1'            + hCR +
             'HostBaud3=3'            + hCR +
             'HostBits3=1'            + hCR +
             'HostParity3=1'          + hCR +
             'HostStop3=1'            + hCR +
             'HostIRQ3=1'             + hCR +    {100}
             'HostSetForDayLight=1'   + hCR +
             'HostSaveCheckInfo=1'    + hCR +
             'HostPartialBINDll=1'    + hCR +
             'HostACHStateCodes=49'   + hCR +
             'HostTruncateAcctNoDB=1' + hCR +
             'HostTruncateAcctNoCR=1' + hCR +
             'HostTruncateAcctNoPD=1' + hCR +
             'HostTruncateAcctNoPC=1' + hCR +
             'HostTruncateAcctNoCK=1' + hCR +
             'HostTruncateAcctNoEF=1' + hCR +    {110}
             'HostTruncateAcctNoEC=1' + hCR +
             'HostTruncateAcctNoU1=1' + hCR +
             'HostTruncateAcctNoPN=1' + hCR +
             'HostTruncateAcctNoU2=1' + hCR +
             'HostFTPUserName=20'     + hCR +
             'HostFTPIpAddr=20'       + hCR +
             'HostFTPPassword=20'     + hCR +
             'HostFTPFileName=12'     + hCR +
             'HostPONumberUsed=1'     + hCR +
             'HostPlatformID=1'       + hCR +    {120}
             'HostClearingCode=3'     + hCR +
             'HostMerchBIN=6'         + hCR +
             'HostMerchICA=6'         + hCR +
             'HostSigCapMode=1'       + hCR +
             'HostPersonalCk=1'       + hCR +
             'HostPayrollCk=1'        + hCR +
             'HostIPLocalPort1=20'    + hCR +
             'HostIPLocalPort2=20'    + hCR +
             'HostIPLocalPort3=20'    + hCR +
             'HostERCLocalPort=5'     + hCR +    {130}{ JMR-A }
             'HostFTPBINDays=1'       + hCR +
             'HostSettleRpt=1'        + hCR +          { JMR-B }
             'HostDBOffline=1'        + hCR +
             'HostCROffline=1'        + hCR +
             'HostPDOffline=1'        + hCR +
             'HostPCOffline=1'        + hCR +
             'HostCKOffline=1'        + hCR +
             'HostEFOffline=1'        + hCR +
             'HostECOffline=1'        + hCR +
             'HostGCOffline=1'        + hCR +    {140}
             'HostPNOffline=1'        + hCR +
             'HostU2Offline=1'        + hCR +
             'HostProgramId=3'        + hCR +
             'HostFTPDir=100'         + hCR +
             'HostTransactionsDB=1'   + hCR +
             'HostTransactionsCR=1'   + hCR +
             'HostTransactionsPD=1'   + hCR +
             'HostTransactionsPC=1'   + hCR +
             'HostTransactionsCK=1'   + hCR +
             'HostTransactionsEF=1'   + hCR +    {150}
             'HostTransactionsEC=1'   + hCR +
             'HostTransactionsGC=1'   + hCR +
             'HostTransactionsFL=1'   + hCR +
             'HostTransactionsPH=1'   + hCR +
             'HostTransactionsWI=1'   + hCR +
             'HostTransactionsAC=1'   + hCR +
             { YHJ-48 begin }
             'HostERCUse=1'           + hCR +
             'HostERCTransferType=1'  + hCR +
             'HostERCFTPIpAddr=20'    + hCR +
             'HostERCFTPPort=5'       + hCR +
             'HostERCFTPBackupIpAddr=20' + hCR +
             'HostERCFTPBackupPort=5' + hCR +             
             'HostERCFTPUserName=20'  + hCR +
             'HostERCFTPPassword=20'  + hCR +
             'HostERCFTPFileName=12'  + hCR +
             'HostERCFTPDir=100'      + hCR; 
             { YHJ-48 end }

Type
           DSHostRec = Record
           {01}  HostName          : String[HostNameL];
           {02}  HostPollChar      : String[HostPollCharL];
           {03}  HostPollIntvl     : String[HostPollIntvlL];
           {04}  HostSuspendCt     : String[HostSuspendCtL];
           {05}  HostFatalCt       : String[HostFatalCtL];
           {06}  HostResumeCt      : String[HostResumeCtL];
           {07}  HostOnTOxxxx      : String[HostOnTOL];
           {08}  HostLocal         : String[HostLocalL];
           {09}  HostOffTO         : String[HostOffTOL];
           {10}  Host_SSCP_ID      : String[Host_SSCP_IDL];
           {11}  Host_Is_EBCDIC    : String[Host_Is_EBCDICL];
           {12}  Host_Comm_NRZI    : String[Host_Comm_NRZIL];
           {13}  Host_Main_Session : String[Host_Main_SessionL];
           {14}  Host_TOR_Session  : String[Host_TOR_SessionL];
           {15}  Host_Offl_Session : String[Host_Offl_SessionL];
           {16}  HostMerchantID    : String[HostMerchantIDL];
           {17}  HostPassword      : String[HostPasswordL];
           {18}  HostCkAuthType    : String[HostCkAuthTypeL];
           {19}  HostCkCCode       : String[HostCkCCodeL];
           {20}  HostDefaultState  : String[HostDefaultStateL];
           {21}  HostOnDbCk        : String[HostOnDbCkL];
           {22}  HostOffDbCk       : String[HostOffDbCkL];
           {23}  HostPort          : String[HostPortL];
           {24}  HostBaud          : String[HostBaudL];
           {25}  HostBits          : String[HostBitsL];
           {26}  HostParity        : String[HostParityL];
           {27}  HostStop          : String[HostStopL];
           {28}  HostIRQ           : String[HostIRQL];
           {29}  HostStoreTermID   : String[HostStoreTermIDL];
           {30}  HostMsgSecCode    : String[HostMsgSecCodeL];
           {31}  HostMgtInfoData   : String[HostMgtInfoDataL];
           {32}  HostSendMgtInfo   : String[HostSendMgtInfoL];
           {33}  HostMerchType     : String[HostMerchTypeL];
           {34}  HostInstIDCode    : String[HostInstIDCodeL];
           {35}  HostMerchName     : String[HostMerchNameL];
           {36}  HostMerchAddress  : String[HostMerchAddressL];
           {37}  HostMerchCity     : String[HostMerchCityL];
           {38}  HostMerchState    : String[HostMerchStateL];
           {39}  HostZipCode       : String[HostZipCodeL];
           {40}  HostCountry       : String[HostCountryL];
           {41}  HostCurrencyCode  : String[HostCurrencyCodeL];
           {42}  HostStateCode     : String[HostStateCodeL];
           {43}  HostCountyCode    : String[HostCountyCodeL];
           {44}  HostOffsetHrsGMT  : String[HostOffsetHrsGMTL];
           {45}  HostClerkLane     : String[HostClerkLaneL];
           {46}  HostTORLate       : String[HostTORLateL];
           {47}  HostTORTimeout    : String[HostTORTimeoutL];
           {48}  HostTORNoT2       : String[HostTORNoT2L];
           {49}  Host_Trace_Data   : String[Host_Trace_DataL];
           {50}  HostOffResubmit   : String[HostOffResubmitL];
           {51}  HostTerminalType  : String[HostTerminalTypeL];
           {52}  HostMerchantNum   : String[HostMerchantNumL];
           {53}  HostTerminalNum   : String[HostTerminalNumL];
           {54}  HostRetryPhone1   : String[HostRetryPhone1L];
           {55}  HostPhone1        : String[HostPhone1L];
           {56}  HostLogon1        : String[HostLogon1L];
           {57}  HostRetryPhone2   : String[HostRetryPhone2L];
           {58}  HostPhone2        : String[HostPhone2L];
           {59}  HostLogon2        : String[HostLogon2L];
           {60}  HostRetryPhone3   : String[HostRetryPhone3L];
           {61}  HostPhone3        : String[HostPhone3L];
           {62}  HostLogon3        : String[HostLogon3L];
           {63}  HostModemInit     : String[HostModemInitL];
           {64}  HostHoldTime      : String[HostHoldTimeL];
           {65}  HostInitMain      : String[HostInitSelfL];
           {66}  HostInitTOR       : String[HostInitSelfL];
           {67}  HostInitOffline   : String[HostInitSelfL];

           { JGS-MH }
           {68}  HostConfigNum     : String[HostConfigNumL];
           {69}  HostSecondScrName : String[HostSecondScrNameL];
        {70-79}  HostTransactions  : Array[1..12] of String[HostTransactionsL];
        {80-89}  HostTimeout       : Array[1..10] of String[HostTimeoutL];
           {90}  HostOffFwdDelay   : String[HostOffFwdDelayL];
           {91}  HostOffPaceDelay  : String[HostOffPaceDelayL];
           {92}  HostTORWaitInt    : String[HostTORWaitIntL];
           {93}  HostTrxWaitInt    : String[HostTrxWaitIntL];
           {94}  HostSuffix        : String[HostSuffixL];
           { JGS-MH }

           { TSL-B start }
           {95}  HostSendHealthMsg : String[HostSendHealthMsgL];  { TSL-B }
           {96}  HostHealthMsgInt  : String[HostHealthMsgIntL]; { TSL-B }
           {97}  HostWicLocal      : String[HostWicLocalL]; { TSL-C }
           {98}  HostFtpBin        : String[HostFtpBinL];   { TSL-C }
           {99}  HostDivision      : String[HostDivisionL]; { TSL-I }
           { TSL-B end }

           { JGS-A start }
          {100}  HostPort2         : String[HostPortL];
          {101}  HostBaud2         : String[HostBaudL];
          {102}  HostBits2         : String[HostBitsL];
          {103}  HostParity2       : String[HostParityL];
          {104}  HostStop2         : String[HostStopL];
          {105}  HostIRQ2          : String[HostIRQL];
          {106}  HostPort3         : String[HostPortL];
          {107}  HostBaud3         : String[HostBaudL];
          {108}  HostBits3         : String[HostBitsL];
          {109}  HostParity3       : String[HostParityL];
          {110}  HostStop3         : String[HostStopL];
          {111}  HostIRQ3          : String[HostIRQL];

          {112}  HostSetForDayLight: String[HostSetForDayLightL];
          {113}  HostSaveCheckInfo : String[HostSaveCheckInfoL];
          {114}  HostPartialBINDll : String[HostPartialBINDllL];
          {115}  HostACHStateCodes : String[HostACHStateCodesL];

       Case   Byte of
          1 :  (  // leave this alone, it sets aside 1024 bytes of unused area
                  // add any new fields to the variant below
                 HostUnusedArea    : Array[1..1024] of Char;
               );

          2 :  ( HostTruncateAcctNo: Array[1..10] of String[HostTruncateAcctNoL];
                 HostFTPUserName   : String[HostFTPUserNameL];   // TSL-E
                 HostFTPIpAddr     : String[HostFTPIpAddrL];     // TSL-E
                 HostFTPPassword   : String[HostFTPPasswordL];   // TSL-E
                 HostFTPFileName   : String[HostFTPFileNameL];   // TSL-E
                 HostPONumberUsed  : String[HostPONumberUsedL];  // TSL-D
                 HostPlatformID    : String[HostPlatformIDL];    // TSL-D
                 HostClearingCode  : String[HostClearingCodeL];  // TSL-D
                 HostMerchBIN      : String[HostMerchBINL];      // TSL-D
                 HostMerchICA      : String[HostMerchICAL];      // TSL-D
                 HostSigCapMode    : String[HostSigCapModeL];    // TSL-E
                 HostPersonalCk    : String[HostPersonalCkL];    // TSL-F
                 HostPayrollCk     : String[HostPayrollCkL];     // TSL-F
                 HostIPLocalPort1  : String[HostIPLocalPortL];   // TSL-G
                 HostIPLocalPort2  : String[HostIPLocalPortL];   // TSL-G
                 HostIPLocalPort3  : String[HostIPLocalPortL];   // TSL-G
                 HostERCLocalPort  : String[HostERCLocalPortL];  // JMR-A
                 HostFTPBINDays    : String[HostFTPBINDaysL];    // TSL-H
                 HostLastBINFtpDate: TdateTime;                  // TSL-H
                 HostSettleRpt     : String[HostSettleRptL];     // JMR-B
                 HostTenderOffline : array[1..10] of boolean;    // TSL-I
                 {1=db,2=cr,3=pd,4=pc,5=ck,6=EF,7=EC,8=GC,9=Fl,10=U2 - see mtx_lib.tenderIndex}
                 HostUnused1       : string[10];                 // JMR-C : just in case the above array ever needs to be expanded
                 HostProgramId     : String[HostProgramIdL];     // JMR-C
                 HostFTPDir        : string[HostFtpDirL];        // JMR-D
                 { YHJ-48 begin }
                 HostERCUse          : string[HostERCUseL];
                 HostERCTransferType : string[HostERCTransferTypeL];
                 HostERCFTPIpAddr    : string[HostERCFTPIpAddrL];
                 HostERCFTPPort      : string[HostERCFTPPortL];
                 HostERCFTPBackupIpAddr : string[HostERCFTPBackupIpAddrL];
                 HostERCFTPBackupPort   : string[HostERCFTPBackupPortL];
                 HostERCFTPUserName  : string[HostERCFTPUserNameL];
                 HostERCFTPPassword  : string[HostERCFTPPasswordL];
                 HostERCFTPFileName  : string[HostERCFTPFileNameL]; // for future use
                 HostERCFTPDir       : string[HostERCFTPDirL];
                 { YHJ-48 end }
               );
           { JGS-A end }


       End;
Var
           DSHostFile   : File Of DSHostRec;
           DSHostBuf    : DSHostRec;
           { // YHJ-417B moved to XMLTables.pas to remove host.inc
           DLLTruncAcct : Array[1..10] of String[HostTruncateAcctNoL];
           DLLHostPO    : String[HostPONumberUsedL];
           }
Const
