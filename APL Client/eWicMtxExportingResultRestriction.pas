
{***********************************************************************************************}
{                                                                                               }
{                                       XML Data Binding                                        }
{                                                                                               }
{         Generated on: 1/31/2011 12:27:05 PM                                                   }
{       Generated from: Z:\827.0\APL Client\XML Samples\eWicMtxExportingResultRestriction.xml   }
{   Settings stored in: Z:\827.0\APL Client\XML Samples\eWicMtxExportingResultRestriction.xdb   }
{                                                                                               }
{***********************************************************************************************}

unit eWicMtxExportingResultRestriction;

interface

uses xmldom, XMLDoc, XMLIntf;

type

{ Forward Decls }

  IXMLMtxExportingResultType = interface;
  IXMLRestrictionType = interface;

{ IXMLMtxExportingResultType }

  IXMLMtxExportingResultType = interface(IXMLNode)
    ['{BA63BEA0-4D00-4BC9-BBCC-911C48C741A6}']
    { Property Accessors }
    function Get_Result: WideString;
    function Get_Date: WideString;
    function Get_Time: WideString;
    function Get_Restriction: IXMLRestrictionType;
    procedure Set_Result(Value: WideString);
    procedure Set_Date(Value: WideString);
    procedure Set_Time(Value: WideString);
    { Methods & Properties }
    property Result: WideString read Get_Result write Set_Result;
    property Date: WideString read Get_Date write Set_Date;
    property Time: WideString read Get_Time write Set_Time;
    property Restriction: IXMLRestrictionType read Get_Restriction;
  end;

{ IXMLRestrictionType }

  IXMLRestrictionType = interface(IXMLNode)
    ['{3BF4F2B5-3DAC-4338-83FC-91416D605C2D}']
    { Property Accessors }
    function Get_Export: WideString;
    function Get_Name: WideString;
    function Get_Description: WideString;
    procedure Set_Export(Value: WideString);
    procedure Set_Name(Value: WideString);
    procedure Set_Description(Value: WideString);
    { Methods & Properties }
    property Export: WideString read Get_Export write Set_Export;
    property Name: WideString read Get_Name write Set_Name;
    property Description: WideString read Get_Description write Set_Description;
  end;

{ Forward Decls }

  TXMLMtxExportingResultType = class;
  TXMLRestrictionType = class;

{ TXMLMtxExportingResultType }

  TXMLMtxExportingResultType = class(TXMLNode, IXMLMtxExportingResultType)
  protected
    { IXMLMtxExportingResultType }
    function Get_Result: WideString;
    function Get_Date: WideString;
    function Get_Time: WideString;
    function Get_Restriction: IXMLRestrictionType;
    procedure Set_Result(Value: WideString);
    procedure Set_Date(Value: WideString);
    procedure Set_Time(Value: WideString);
  public
    procedure AfterConstruction; override;
  end;

{ TXMLRestrictionType }

  TXMLRestrictionType = class(TXMLNode, IXMLRestrictionType)
  protected
    { IXMLRestrictionType }
    function Get_Export: WideString;
    function Get_Name: WideString;
    function Get_Description: WideString;
    procedure Set_Export(Value: WideString);
    procedure Set_Name(Value: WideString);
    procedure Set_Description(Value: WideString);
  end;

{ Global Functions }

function GetMtxExportingResult(Doc: IXMLDocument): IXMLMtxExportingResultType;
function LoadMtxExportingResult(const FileName: WideString): IXMLMtxExportingResultType;
function NewMtxExportingResult: IXMLMtxExportingResultType;

const
  TargetNamespace = '';

implementation

{ Global Functions }

function GetMtxExportingResult(Doc: IXMLDocument): IXMLMtxExportingResultType;
begin
  Result := Doc.GetDocBinding('MtxExportingResult', TXMLMtxExportingResultType, TargetNamespace) as IXMLMtxExportingResultType;
end;

function LoadMtxExportingResult(const FileName: WideString): IXMLMtxExportingResultType;
begin
  Result := LoadXMLDocument(FileName).GetDocBinding('MtxExportingResult', TXMLMtxExportingResultType, TargetNamespace) as IXMLMtxExportingResultType;
end;

function NewMtxExportingResult: IXMLMtxExportingResultType;
begin
  Result := NewXMLDocument.GetDocBinding('MtxExportingResult', TXMLMtxExportingResultType, TargetNamespace) as IXMLMtxExportingResultType;
end;

{ TXMLMtxExportingResultType }

procedure TXMLMtxExportingResultType.AfterConstruction;
begin
  RegisterChildNode('Restriction', TXMLRestrictionType);
  inherited;
end;

function TXMLMtxExportingResultType.Get_Result: WideString;
begin
  Result := AttributeNodes['Result'].Text;
end;

procedure TXMLMtxExportingResultType.Set_Result(Value: WideString);
begin
  SetAttribute('Result', Value);
end;

function TXMLMtxExportingResultType.Get_Date: WideString;
begin
  Result := AttributeNodes['Date'].Text;
end;

procedure TXMLMtxExportingResultType.Set_Date(Value: WideString);
begin
  SetAttribute('Date', Value);
end;

function TXMLMtxExportingResultType.Get_Time: WideString;
begin
  Result := AttributeNodes['Time'].Text;
end;

procedure TXMLMtxExportingResultType.Set_Time(Value: WideString);
begin
  SetAttribute('Time', Value);
end;

function TXMLMtxExportingResultType.Get_Restriction: IXMLRestrictionType;
begin
  Result := ChildNodes['Restriction'] as IXMLRestrictionType;
end;

{ TXMLRestrictionType }

function TXMLRestrictionType.Get_Export: WideString;
begin
  Result := AttributeNodes['Export'].Text;
end;

procedure TXMLRestrictionType.Set_Export(Value: WideString);
begin
  SetAttribute('Export', Value);
end;

function TXMLRestrictionType.Get_Name: WideString;
begin
  Result := ChildNodes['Name'].Text;
end;

procedure TXMLRestrictionType.Set_Name(Value: WideString);
begin
  ChildNodes['Name'].NodeValue := Value;
end;

function TXMLRestrictionType.Get_Description: WideString;
begin
  Result := ChildNodes['Description'].Text;
end;

procedure TXMLRestrictionType.Set_Description(Value: WideString);
begin
  ChildNodes['Description'].NodeValue := Value;
end;

end.