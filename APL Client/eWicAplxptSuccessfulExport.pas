
{********************************************************************************************}
{                                                                                            }
{                                      XML Data Binding                                      }
{                                                                                            }
{         Generated on: 1/27/2011 11:10:56 AM                                                }
{       Generated from: Z:\827.0\APL Client\XML Samples\eWicApl.xpt(Successful Export).xml   }
{   Settings stored in: Z:\827.0\APL Client\XML Samples\eWicApl.xpt(Successful Export).xdb   }
{                                                                                            }
{********************************************************************************************}

unit eWicAplxptSuccessfulExport;

interface

uses xmldom, XMLDoc, XMLIntf;

type

{ Forward Decls }

  IXMLAPLResponseType = interface;
  IXMLAPLFilesType = interface;
  IXMLAPLFileType = interface;

{ IXMLAPLResponseType }

  IXMLAPLResponseType = interface(IXMLNode)
    ['{6DC08CF2-A32F-4A07-9F2C-BA19584D8EB9}']
    { Property Accessors }
    function Get_CompanyNumber: Integer;
    function Get_StoreNumber: Integer;
    function Get_UserName: WideString;
    function Get_Date: WideString;
    function Get_Time: WideString;
    function Get_FormatVersion: WideString;
    function Get_APLFiles: IXMLAPLFilesType;
    procedure Set_CompanyNumber(Value: Integer);
    procedure Set_StoreNumber(Value: Integer);
    procedure Set_UserName(Value: WideString);
    procedure Set_Date(Value: WideString);
    procedure Set_Time(Value: WideString);
    procedure Set_FormatVersion(Value: WideString);
    { Methods & Properties }
    property CompanyNumber: Integer read Get_CompanyNumber write Set_CompanyNumber;
    property StoreNumber: Integer read Get_StoreNumber write Set_StoreNumber;
    property UserName: WideString read Get_UserName write Set_UserName;
    property Date: WideString read Get_Date write Set_Date;
    property Time: WideString read Get_Time write Set_Time;
    property FormatVersion: WideString read Get_FormatVersion write Set_FormatVersion;
    property APLFiles: IXMLAPLFilesType read Get_APLFiles;
  end;

{ IXMLAPLFilesType }

  IXMLAPLFilesType = interface(IXMLNode)
    ['{209119B6-EE06-41B7-8420-135F0FC5EAF4}']
    { Property Accessors }
    function Get_APLFile: IXMLAPLFileType;
    { Methods & Properties }
    property APLFile: IXMLAPLFileType read Get_APLFile;
  end;

{ IXMLAPLFileType }

  IXMLAPLFileType = interface(IXMLNode)
    ['{C77D9C33-2181-4AEF-A0BB-DC13FD5A3FC3}']
    { Property Accessors }
    function Get_State: WideString;
    function Get_BatchId: Integer;
    function Get_ReceivedDate: WideString;
    procedure Set_State(Value: WideString);
    procedure Set_BatchId(Value: Integer);
    procedure Set_ReceivedDate(Value: WideString);
    { Methods & Properties }
    property State: WideString read Get_State write Set_State;
    property BatchId: Integer read Get_BatchId write Set_BatchId;
    property ReceivedDate: WideString read Get_ReceivedDate write Set_ReceivedDate;
  end;

{ Forward Decls }

  TXMLAPLResponseType = class;
  TXMLAPLFilesType = class;
  TXMLAPLFileType = class;

{ TXMLAPLResponseType }

  TXMLAPLResponseType = class(TXMLNode, IXMLAPLResponseType)
  protected
    { IXMLAPLResponseType }
    function Get_CompanyNumber: Integer;
    function Get_StoreNumber: Integer;
    function Get_UserName: WideString;
    function Get_Date: WideString;
    function Get_Time: WideString;
    function Get_FormatVersion: WideString;
    function Get_APLFiles: IXMLAPLFilesType;
    procedure Set_CompanyNumber(Value: Integer);
    procedure Set_StoreNumber(Value: Integer);
    procedure Set_UserName(Value: WideString);
    procedure Set_Date(Value: WideString);
    procedure Set_Time(Value: WideString);
    procedure Set_FormatVersion(Value: WideString);
  public
    procedure AfterConstruction; override;
  end;

{ TXMLAPLFilesType }

  TXMLAPLFilesType = class(TXMLNode, IXMLAPLFilesType)
  protected
    { IXMLAPLFilesType }
    function Get_APLFile: IXMLAPLFileType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLAPLFileType }

  TXMLAPLFileType = class(TXMLNode, IXMLAPLFileType)
  protected
    { IXMLAPLFileType }
    function Get_State: WideString;
    function Get_BatchId: Integer;
    function Get_ReceivedDate: WideString;
    procedure Set_State(Value: WideString);
    procedure Set_BatchId(Value: Integer);
    procedure Set_ReceivedDate(Value: WideString);
  end;

{ Global Functions }

function GetAPLResponse(Doc: IXMLDocument): IXMLAPLResponseType;
function LoadAPLResponse(const FileName: WideString): IXMLAPLResponseType;
function NewAPLResponse: IXMLAPLResponseType;

const
  TargetNamespace = '';

implementation

{ Global Functions }

function GetAPLResponse(Doc: IXMLDocument): IXMLAPLResponseType;
begin
  Result := Doc.GetDocBinding('APLResponse', TXMLAPLResponseType, TargetNamespace) as IXMLAPLResponseType;
end;

function LoadAPLResponse(const FileName: WideString): IXMLAPLResponseType;
begin
  Result := LoadXMLDocument(FileName).GetDocBinding('APLResponse', TXMLAPLResponseType, TargetNamespace) as IXMLAPLResponseType;
end;

function NewAPLResponse: IXMLAPLResponseType;
begin
  Result := NewXMLDocument.GetDocBinding('APLResponse', TXMLAPLResponseType, TargetNamespace) as IXMLAPLResponseType;
end;

{ TXMLAPLResponseType }

procedure TXMLAPLResponseType.AfterConstruction;
begin
  RegisterChildNode('APLFiles', TXMLAPLFilesType);
  inherited;
end;

function TXMLAPLResponseType.Get_CompanyNumber: Integer;
begin
  Result := AttributeNodes['CompanyNumber'].NodeValue;
end;

procedure TXMLAPLResponseType.Set_CompanyNumber(Value: Integer);
begin
  SetAttribute('CompanyNumber', Value);
end;

function TXMLAPLResponseType.Get_StoreNumber: Integer;
begin
  Result := AttributeNodes['StoreNumber'].NodeValue;
end;

procedure TXMLAPLResponseType.Set_StoreNumber(Value: Integer);
begin
  SetAttribute('StoreNumber', Value);
end;

function TXMLAPLResponseType.Get_UserName: WideString;
begin
  Result := AttributeNodes['UserName'].Text;
end;

procedure TXMLAPLResponseType.Set_UserName(Value: WideString);
begin
  SetAttribute('UserName', Value);
end;

function TXMLAPLResponseType.Get_Date: WideString;
begin
  Result := AttributeNodes['Date'].Text;
end;

procedure TXMLAPLResponseType.Set_Date(Value: WideString);
begin
  SetAttribute('Date', Value);
end;

function TXMLAPLResponseType.Get_Time: WideString;
begin
  Result := AttributeNodes['Time'].Text;
end;

procedure TXMLAPLResponseType.Set_Time(Value: WideString);
begin
  SetAttribute('Time', Value);
end;

function TXMLAPLResponseType.Get_FormatVersion: WideString;
begin
  Result := AttributeNodes['FormatVersion'].Text;
end;

procedure TXMLAPLResponseType.Set_FormatVersion(Value: WideString);
begin
  SetAttribute('FormatVersion', Value);
end;

function TXMLAPLResponseType.Get_APLFiles: IXMLAPLFilesType;
begin
  Result := ChildNodes['APLFiles'] as IXMLAPLFilesType;
end;

{ TXMLAPLFilesType }

procedure TXMLAPLFilesType.AfterConstruction;
begin
  RegisterChildNode('APLFile', TXMLAPLFileType);
  inherited;
end;

function TXMLAPLFilesType.Get_APLFile: IXMLAPLFileType;
begin
  Result := ChildNodes['APLFile'] as IXMLAPLFileType;
end;

{ TXMLAPLFileType }

function TXMLAPLFileType.Get_State: WideString;
begin
  Result := AttributeNodes['State'].Text;
end;

procedure TXMLAPLFileType.Set_State(Value: WideString);
begin
  SetAttribute('State', Value);
end;

function TXMLAPLFileType.Get_BatchId: Integer;
begin
  Result := AttributeNodes['BatchId'].NodeValue;
end;

procedure TXMLAPLFileType.Set_BatchId(Value: Integer);
begin
  SetAttribute('BatchId', Value);
end;

function TXMLAPLFileType.Get_ReceivedDate: WideString;
begin
  Result := AttributeNodes['ReceivedDate'].Text;
end;

procedure TXMLAPLFileType.Set_ReceivedDate(Value: WideString);
begin
  SetAttribute('ReceivedDate', Value);
end;

end.