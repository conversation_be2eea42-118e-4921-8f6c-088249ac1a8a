program APLClientGUI;

uses
  Forms,
  uAPLG<PERSON> in 'uAPLGUI.pas' {frmMain},
  eWicAplxptSuccessfulExport in 'eWicAplxptSuccessfulExport.pas',
  uAPL in 'uAPL.pas' {DM: TDataModule},
  ServerEPSConstants in '..\OpenEPS\ServerEPSConstants.pas',
  eWicMtxExportingResultError in 'eWicMtxExportingResultError.pas',
  eWicMtxExportingResultRestriction in 'eWicMtxExportingResultRestriction.pas',
  APLClientConfiguration in 'APLClientConfiguration.pas';

{$R *.res}

begin
  Application.Initialize;
  Application.MainFormOnTaskbar := True;
  Application.Title := 'APL Client Configuration';
  Application.CreateForm(TfrmMain, frmMain);
  Application.CreateForm(TDM, DM);
  Application.Run;
end.
