
{***********************************************************}
{                                                           }
{                     XML Data Binding                      }
{                                                           }
{         Generated on: 4/1/2011 2:37:42 PM                 }
{       Generated from: Z:\827.1\APL Client\APLStatus.xml   }
{   Settings stored in: Z:\827.1\APL Client\APLStatus.xdb   }
{                                                           }
{***********************************************************}

unit APLStatus;

interface

uses xmldom, XMLDoc, XMLIntf;

type

{ Forward Decls }

  IXMLLaneType = interface;
  IXMLDriveType = interface;
  IXMLIpAddressesType = interface;
  IXMLModulesType = interface;
  IXMLModuleType = interface;
  IXMLModuleType2 = interface;
  IXMLModuleType22 = interface;

{ IXMLLaneType }

  IXMLLaneType = interface(IXMLNode)
    ['{797DEB1A-1B67-4C56-BAFE-54CAF8A322E8}']
    { Property Accessors }
    function Get_Number: Integer;
    function Get_LaneType: WideString;
    function Get_UpdateTime: WideString;
    function Get_Drive: IXMLDriveType;
    function Get_OSVersion: WideString;
    function Get_IpAddresses: IXMLIpAddressesType;
    function Get_Modules: IXMLModulesType;
    procedure Set_Number(Value: Integer);
    procedure Set_LaneType(Value: WideString);
    procedure Set_UpdateTime(Value: WideString);
    procedure Set_OSVersion(Value: WideString);
    { Methods & Properties }
    property Number: Integer read Get_Number write Set_Number;
    property LaneType: WideString read Get_LaneType write Set_LaneType;
    property UpdateTime: WideString read Get_UpdateTime write Set_UpdateTime;
    property Drive: IXMLDriveType read Get_Drive;
    property OSVersion: WideString read Get_OSVersion write Set_OSVersion;
    property IpAddresses: IXMLIpAddressesType read Get_IpAddresses;
    property Modules: IXMLModulesType read Get_Modules;
  end;

{ IXMLDriveType }

  IXMLDriveType = interface(IXMLNode)
    ['{BABDEDF6-5E24-4361-9A9A-122A7807A29E}']
    { Property Accessors }
    function Get_Letter: WideString;
    function Get_DriveSize: Integer;
    function Get_FreeSpace: Integer;
    procedure Set_Letter(Value: WideString);
    procedure Set_DriveSize(Value: Integer);
    procedure Set_FreeSpace(Value: Integer);
    { Methods & Properties }
    property Letter: WideString read Get_Letter write Set_Letter;
    property DriveSize: Integer read Get_DriveSize write Set_DriveSize;
    property FreeSpace: Integer read Get_FreeSpace write Set_FreeSpace;
  end;

{ IXMLIpAddressesType }

  IXMLIpAddressesType = interface(IXMLNode)
    ['{39682E38-6B24-4E2A-A356-BA7ACECDF6C0}']
    { Property Accessors }
    function Get_IPAddress: WideString;
    procedure Set_IPAddress(Value: WideString);
    { Methods & Properties }
    property IPAddress: WideString read Get_IPAddress write Set_IPAddress;
  end;

{ IXMLModulesType }

  IXMLModulesType = interface(IXMLNodeCollection)
    ['{31A8A774-4FA5-433D-B560-EA0D0C600615}']
    { Property Accessors }
    function Get_Module(Index: Integer): IXMLModuleType;
    { Methods & Properties }
    function Add: IXMLModuleType;
    function Insert(const Index: Integer): IXMLModuleType;
    property Module[Index: Integer]: IXMLModuleType read Get_Module; default;
  end;

{ IXMLModuleType }

  IXMLModuleType = interface(IXMLNode)
    ['{0B30629C-0C75-40E5-9949-82933FE682A6}']
    { Property Accessors }
    function Get_Name: WideString;
    function Get_Version: WideString;
    procedure Set_Name(Value: WideString);
    procedure Set_Version(Value: WideString);
    { Methods & Properties }
    property Name: WideString read Get_Name write Set_Name;
    property Version: WideString read Get_Version write Set_Version;
  end;

{ IXMLModuleType2 }

  IXMLModuleType2 = interface(IXMLNode)
    ['{6249D456-0CE1-4094-9013-E0B2598B0760}']
    { Property Accessors }
    function Get_Name: WideString;
    function Get_Version: WideString;
    procedure Set_Name(Value: WideString);
    procedure Set_Version(Value: WideString);
    { Methods & Properties }
    property Name: WideString read Get_Name write Set_Name;
    property Version: WideString read Get_Version write Set_Version;
  end;

{ IXMLModuleType22 }

  IXMLModuleType22 = interface(IXMLNode)
    ['{F0079C73-F82F-4AA4-B4A7-58FD03F048BC}']
    { Property Accessors }
    function Get_Name: WideString;
    function Get_Version: WideString;
    procedure Set_Name(Value: WideString);
    procedure Set_Version(Value: WideString);
    { Methods & Properties }
    property Name: WideString read Get_Name write Set_Name;
    property Version: WideString read Get_Version write Set_Version;
  end;

{ Forward Decls }

  TXMLLaneType = class;
  TXMLDriveType = class;
  TXMLIpAddressesType = class;
  TXMLModulesType = class;
  TXMLModuleType = class;
  TXMLModuleType2 = class;
  TXMLModuleType22 = class;

{ TXMLLaneType }

  TXMLLaneType = class(TXMLNode, IXMLLaneType)
  protected
    { IXMLLaneType }
    function Get_Number: Integer;
    function Get_LaneType: WideString;
    function Get_UpdateTime: WideString;
    function Get_Drive: IXMLDriveType;
    function Get_OSVersion: WideString;
    function Get_IpAddresses: IXMLIpAddressesType;
    function Get_Modules: IXMLModulesType;
    procedure Set_Number(Value: Integer);
    procedure Set_LaneType(Value: WideString);
    procedure Set_UpdateTime(Value: WideString);
    procedure Set_OSVersion(Value: WideString);
  public
    procedure AfterConstruction; override;
  end;

{ TXMLDriveType }

  TXMLDriveType = class(TXMLNode, IXMLDriveType)
  protected
    { IXMLDriveType }
    function Get_Letter: WideString;
    function Get_DriveSize: Integer;
    function Get_FreeSpace: Integer;
    procedure Set_Letter(Value: WideString);
    procedure Set_DriveSize(Value: Integer);
    procedure Set_FreeSpace(Value: Integer);
  end;

{ TXMLIpAddressesType }

  TXMLIpAddressesType = class(TXMLNode, IXMLIpAddressesType)
  protected
    { IXMLIpAddressesType }
    function Get_IPAddress: WideString;
    procedure Set_IPAddress(Value: WideString);
  end;

{ TXMLModulesType }

  TXMLModulesType = class(TXMLNodeCollection, IXMLModulesType)
  protected
    { IXMLModulesType }
    function Get_Module(Index: Integer): IXMLModuleType;
    function Add: IXMLModuleType;
    function Insert(const Index: Integer): IXMLModuleType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLModuleType }

  TXMLModuleType = class(TXMLNode, IXMLModuleType)
  protected
    { IXMLModuleType }
    function Get_Name: WideString;
    function Get_Version: WideString;
    procedure Set_Name(Value: WideString);
    procedure Set_Version(Value: WideString);
  end;

{ TXMLModuleType2 }

  TXMLModuleType2 = class(TXMLNode, IXMLModuleType2)
  protected
    { IXMLModuleType2 }
    function Get_Name: WideString;
    function Get_Version: WideString;
    procedure Set_Name(Value: WideString);
    procedure Set_Version(Value: WideString);
  end;

{ TXMLModuleType22 }

  TXMLModuleType22 = class(TXMLNode, IXMLModuleType22)
  protected
    { IXMLModuleType22 }
    function Get_Name: WideString;
    function Get_Version: WideString;
    procedure Set_Name(Value: WideString);
    procedure Set_Version(Value: WideString);
  end;

{ Global Functions }

function GetLane(Doc: IXMLDocument): IXMLLaneType;
function LoadLane(const FileName: WideString): IXMLLaneType;
function NewLane: IXMLLaneType;

const
  TargetNamespace = '';

implementation

{ Global Functions }

function GetLane(Doc: IXMLDocument): IXMLLaneType;
begin
  Result := Doc.GetDocBinding('Lane', TXMLLaneType, TargetNamespace) as IXMLLaneType;
end;

function LoadLane(const FileName: WideString): IXMLLaneType;
begin
  Result := LoadXMLDocument(FileName).GetDocBinding('Lane', TXMLLaneType, TargetNamespace) as IXMLLaneType;
end;

function NewLane: IXMLLaneType;
begin
  Result := NewXMLDocument.GetDocBinding('Lane', TXMLLaneType, TargetNamespace) as IXMLLaneType;
end;

{ TXMLLaneType }

procedure TXMLLaneType.AfterConstruction;
begin
  RegisterChildNode('Drive', TXMLDriveType);
  RegisterChildNode('IpAddresses', TXMLIpAddressesType);
  RegisterChildNode('Modules', TXMLModulesType);
  inherited;
end;

function TXMLLaneType.Get_Number: Integer;
begin
  Result := AttributeNodes['Number'].NodeValue;
end;

procedure TXMLLaneType.Set_Number(Value: Integer);
begin
  SetAttribute('Number', Value);
end;

function TXMLLaneType.Get_LaneType: WideString;
begin
  Result := AttributeNodes['LaneType'].Text;
end;

procedure TXMLLaneType.Set_LaneType(Value: WideString);
begin
  SetAttribute('LaneType', Value);
end;

function TXMLLaneType.Get_UpdateTime: WideString;
begin
  Result := AttributeNodes['UpdateTime'].Text;
end;

procedure TXMLLaneType.Set_UpdateTime(Value: WideString);
begin
  SetAttribute('UpdateTime', Value);
end;

function TXMLLaneType.Get_Drive: IXMLDriveType;
begin
  Result := ChildNodes['Drive'] as IXMLDriveType;
end;

function TXMLLaneType.Get_OSVersion: WideString;
begin
  Result := ChildNodes['OSVersion'].Text;
end;

procedure TXMLLaneType.Set_OSVersion(Value: WideString);
begin
  ChildNodes['OSVersion'].NodeValue := Value;
end;

function TXMLLaneType.Get_IpAddresses: IXMLIpAddressesType;
begin
  Result := ChildNodes['IpAddresses'] as IXMLIpAddressesType;
end;

function TXMLLaneType.Get_Modules: IXMLModulesType;
begin
  Result := ChildNodes['Modules'] as IXMLModulesType;
end;

{ TXMLDriveType }

function TXMLDriveType.Get_Letter: WideString;
begin
  Result := AttributeNodes['Letter'].Text;
end;

procedure TXMLDriveType.Set_Letter(Value: WideString);
begin
  SetAttribute('Letter', Value);
end;

function TXMLDriveType.Get_DriveSize: Integer;
begin
  Result := AttributeNodes['DriveSize'].NodeValue;
end;

procedure TXMLDriveType.Set_DriveSize(Value: Integer);
begin
  SetAttribute('DriveSize', Value);
end;

function TXMLDriveType.Get_FreeSpace: Integer;
begin
  Result := AttributeNodes['FreeSpace'].NodeValue;
end;

procedure TXMLDriveType.Set_FreeSpace(Value: Integer);
begin
  SetAttribute('FreeSpace', Value);
end;

{ TXMLIpAddressesType }

function TXMLIpAddressesType.Get_IPAddress: WideString;
begin
  Result := ChildNodes['IPAddress'].Text;
end;

procedure TXMLIpAddressesType.Set_IPAddress(Value: WideString);
begin
  ChildNodes['IPAddress'].NodeValue := Value;
end;

{ TXMLModulesType }

procedure TXMLModulesType.AfterConstruction;
begin
  RegisterChildNode('Module', TXMLModuleType);
  ItemTag := 'Module';
  ItemInterface := IXMLModuleType;
  inherited;
end;

function TXMLModulesType.Get_Module(Index: Integer): IXMLModuleType;
begin
  Result := List[Index] as IXMLModuleType;
end;

function TXMLModulesType.Add: IXMLModuleType;
begin
  Result := AddItem(-1) as IXMLModuleType;
end;

function TXMLModulesType.Insert(const Index: Integer): IXMLModuleType;
begin
  Result := AddItem(Index) as IXMLModuleType;
end;

{ TXMLModuleType }

function TXMLModuleType.Get_Name: WideString;
begin
  Result := AttributeNodes['Name'].Text;
end;

procedure TXMLModuleType.Set_Name(Value: WideString);
begin
  SetAttribute('Name', Value);
end;

function TXMLModuleType.Get_Version: WideString;
begin
  Result := AttributeNodes['Version'].Text;
end;

procedure TXMLModuleType.Set_Version(Value: WideString);
begin
  SetAttribute('Version', Value);
end;

{ TXMLModuleType2 }

function TXMLModuleType2.Get_Name: WideString;
begin
  Result := AttributeNodes['Name'].Text;
end;

procedure TXMLModuleType2.Set_Name(Value: WideString);
begin
  SetAttribute('Name', Value);
end;

function TXMLModuleType2.Get_Version: WideString;
begin
  Result := AttributeNodes['Version'].Text;
end;

procedure TXMLModuleType2.Set_Version(Value: WideString);
begin
  SetAttribute('Version', Value);
end;

{ TXMLModuleType22 }

function TXMLModuleType22.Get_Name: WideString;
begin
  Result := AttributeNodes['Name'].Text;
end;

procedure TXMLModuleType22.Set_Name(Value: WideString);
begin
  SetAttribute('Name', Value);
end;

function TXMLModuleType22.Get_Version: WideString;
begin
  Result := AttributeNodes['Version'].Text;
end;

procedure TXMLModuleType22.Set_Version(Value: WideString);
begin
  SetAttribute('Version', Value);
end;

end.