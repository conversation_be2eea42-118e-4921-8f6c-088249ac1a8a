<?xml version="1.0"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xdb="http://www.borland.com/schemas/delphi/10.0/XMLDataBinding">
  <xs:element name="APLClientConfiguration" type="APLClientConfigurationType"/>
  <xs:complexType name="APLClientConfigurationType"><xs:annotation>
      <xs:appinfo xdb:docElement="APLClientConfiguration"/>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="StatePaths" type="StatePathsType"/>
    </xs:sequence>
    <xs:attribute name="CompanyNumber" type="xs:integer"/>
    <xs:attribute name="StoreNumber" type="xs:integer"/>
    <xs:attribute name="Username" type="xs:string"/>
    <xs:attribute name="Password" type="xs:string"/>
    <xs:attribute name="DownloadTime" type="xs:string"/>
    <xs:attribute name="LastDownload" type="xs:string"/>
  </xs:complexType>
  <xs:complexType name="StatePathsType">
    <xs:sequence>
      <xs:element name="Path" type="PathType" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PathType">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="StateCode" type="xs:string"/>
        <xs:attribute name="Filename" type="xs:string"/>
        <xs:attribute name="Zipped" type="xs:string"/>
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
</xs:schema>
