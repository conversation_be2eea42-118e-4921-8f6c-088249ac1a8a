
{*****************************************************************************************}
{                                                                                         }
{                                    XML Data Binding                                     }
{                                                                                         }
{         Generated on: 1/31/2011 12:25:43 PM                                             }
{       Generated from: Z:\827.0\APL Client\XML Samples\eWicMtxExportingResultError.xml   }
{   Settings stored in: Z:\827.0\APL Client\XML Samples\eWicMtxExportingResultError.xdb   }
{                                                                                         }
{*****************************************************************************************}

unit eWicMtxExportingResultError;

interface

uses xmldom, XMLDoc, XMLIntf;

type

{ Forward Decls }

  IXMLMtxExportingResultType = interface;
  IXMLErrorType = interface;

{ IXMLMtxExportingResultType }

  IXMLMtxExportingResultType = interface(IXMLNode)
    ['{14B85AC4-4E53-477E-AFB2-0D57C7778AF8}']
    { Property Accessors }
    function Get_Result: WideString;
    function Get_Date: WideString;
    function Get_Time: WideString;
    function Get_Error: IXMLErrorType;
    procedure Set_Result(Value: WideString);
    procedure Set_Date(Value: WideString);
    procedure Set_Time(Value: WideString);
    { Methods & Properties }
    property Result: WideString read Get_Result write Set_Result;
    property Date: WideString read Get_Date write Set_Date;
    property Time: WideString read Get_Time write Set_Time;
    property Error: IXMLErrorType read Get_Error;
  end;

{ IXMLErrorType }

  IXMLErrorType = interface(IXMLNode)
    ['{F48F4FAE-863D-4796-9821-0F39A160B1E7}']
    { Property Accessors }
    function Get_Type_: WideString;
    procedure Set_Type_(Value: WideString);
    { Methods & Properties }
    property Type_: WideString read Get_Type_ write Set_Type_;
  end;

{ Forward Decls }

  TXMLMtxExportingResultType = class;
  TXMLErrorType = class;

{ TXMLMtxExportingResultType }

  TXMLMtxExportingResultType = class(TXMLNode, IXMLMtxExportingResultType)
  protected
    { IXMLMtxExportingResultType }
    function Get_Result: WideString;
    function Get_Date: WideString;
    function Get_Time: WideString;
    function Get_Error: IXMLErrorType;
    procedure Set_Result(Value: WideString);
    procedure Set_Date(Value: WideString);
    procedure Set_Time(Value: WideString);
  public
    procedure AfterConstruction; override;
  end;

{ TXMLErrorType }

  TXMLErrorType = class(TXMLNode, IXMLErrorType)
  protected
    { IXMLErrorType }
    function Get_Type_: WideString;
    procedure Set_Type_(Value: WideString);
  end;

{ Global Functions }

function GetMtxExportingResult(Doc: IXMLDocument): IXMLMtxExportingResultType;
function LoadMtxExportingResult(const FileName: WideString): IXMLMtxExportingResultType;
function NewMtxExportingResult: IXMLMtxExportingResultType;

const
  TargetNamespace = '';

implementation

{ Global Functions }

function GetMtxExportingResult(Doc: IXMLDocument): IXMLMtxExportingResultType;
begin
  Result := Doc.GetDocBinding('MtxExportingResult', TXMLMtxExportingResultType, TargetNamespace) as IXMLMtxExportingResultType;
end;

function LoadMtxExportingResult(const FileName: WideString): IXMLMtxExportingResultType;
begin
  Result := LoadXMLDocument(FileName).GetDocBinding('MtxExportingResult', TXMLMtxExportingResultType, TargetNamespace) as IXMLMtxExportingResultType;
end;

function NewMtxExportingResult: IXMLMtxExportingResultType;
begin
  Result := NewXMLDocument.GetDocBinding('MtxExportingResult', TXMLMtxExportingResultType, TargetNamespace) as IXMLMtxExportingResultType;
end;

{ TXMLMtxExportingResultType }

procedure TXMLMtxExportingResultType.AfterConstruction;
begin
  RegisterChildNode('Error', TXMLErrorType);
  inherited;
end;

function TXMLMtxExportingResultType.Get_Result: WideString;
begin
  Result := AttributeNodes['Result'].Text;
end;

procedure TXMLMtxExportingResultType.Set_Result(Value: WideString);
begin
  SetAttribute('Result', Value);
end;

function TXMLMtxExportingResultType.Get_Date: WideString;
begin
  Result := AttributeNodes['Date'].Text;
end;

procedure TXMLMtxExportingResultType.Set_Date(Value: WideString);
begin
  SetAttribute('Date', Value);
end;

function TXMLMtxExportingResultType.Get_Time: WideString;
begin
  Result := AttributeNodes['Time'].Text;
end;

procedure TXMLMtxExportingResultType.Set_Time(Value: WideString);
begin
  SetAttribute('Time', Value);
end;

function TXMLMtxExportingResultType.Get_Error: IXMLErrorType;
begin
  Result := ChildNodes['Error'] as IXMLErrorType;
end;

{ TXMLErrorType }

function TXMLErrorType.Get_Type_: WideString;
begin
  Result := AttributeNodes['Type'].Text;
end;

procedure TXMLErrorType.Set_Type_(Value: WideString);
begin
  SetAttribute('Type', Value);
end;

end.