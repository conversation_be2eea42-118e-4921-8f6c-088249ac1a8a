
{************************************************************************}
{                                                                        }
{                            XML Data Binding                            }
{                                                                        }
{         Generated on: 2/7/2011 12:06:36 PM                             }
{       Generated from: Z:\827.0\APL Client\APLClientConfiguration.xml   }
{   Settings stored in: Z:\827.0\APL Client\APLClientConfiguration.xdb   }
{                                                                        }
{************************************************************************}

unit APLClientConfiguration;

interface

{$IF CompilerVersion < 32.0}
This must be compiled with Delphi 10.2 (Tokyo) or later
{$IFEND}

uses xmldom, XMLDoc, XMLIntf;

type

{ Forward Decls }

  IXMLAPLClientConfigurationType = interface;
  IXMLStatePathsType = interface;
  IXMLPathType = interface;

{ IXMLAPLClientConfigurationType }

  IXMLAPLClientConfigurationType = interface(IXMLNode)
    ['{16B1F285-30E8-4A01-B2B0-7E71602722EF}']
    { Property Accessors }
    function Get_CompanyNumber: Integer;
    function Get_StoreNumber: Integer;
    function Get_Username: WideString;
    function Get_Password: WideString;
    function Get_DownloadTime: WideString;
    function Get_LastDownload: WideString;
    function Get_StatePaths: IXMLStatePathsType;
    procedure Set_CompanyNumber(Value: Integer);
    procedure Set_StoreNumber(Value: Integer);
    procedure Set_Username(Value: WideString);
    procedure Set_Password(Value: WideString);
    procedure Set_DownloadTime(Value: WideString);
    procedure Set_LastDownload(Value: WideString);
    { Methods & Properties }
    property CompanyNumber: Integer read Get_CompanyNumber write Set_CompanyNumber;
    property StoreNumber: Integer read Get_StoreNumber write Set_StoreNumber;
    property Username: WideString read Get_Username write Set_Username;
    property Password: WideString read Get_Password write Set_Password;
    property DownloadTime: WideString read Get_DownloadTime write Set_DownloadTime;
    property LastDownload: WideString read Get_LastDownload write Set_LastDownload;
    property StatePaths: IXMLStatePathsType read Get_StatePaths;
  end;

{ IXMLStatePathsType }

  IXMLStatePathsType = interface(IXMLNodeCollection)
    ['{9C789449-E8D8-459F-930C-124FF3391D44}']
    { Property Accessors }
    function Get_Path(Index: Integer): IXMLPathType;
    { Methods & Properties }
    function Add: IXMLPathType;
    function Insert(const Index: Integer): IXMLPathType;
    property Path[Index: Integer]: IXMLPathType read Get_Path; default;
  end;

{ IXMLPathType }

  IXMLPathType = interface(IXMLNode)
    ['{7AAA2F2B-4429-4EE0-AA19-A91DF5155153}']
    { Property Accessors }
    function Get_StateCode: WideString;
    function Get_Filename: WideString;
    function Get_Zipped: WideString;
    procedure Set_StateCode(Value: WideString);
    procedure Set_Filename(Value: WideString);
    procedure Set_Zipped(Value: WideString);
    { Methods & Properties }
    property StateCode: WideString read Get_StateCode write Set_StateCode;
    property Filename: WideString read Get_Filename write Set_Filename;
    property Zipped: WideString read Get_Zipped write Set_Zipped;
  end;

{ Forward Decls }

  TXMLAPLClientConfigurationType = class;
  TXMLStatePathsType = class;
  TXMLPathType = class;

{ TXMLAPLClientConfigurationType }

  TXMLAPLClientConfigurationType = class(TXMLNode, IXMLAPLClientConfigurationType)
  protected
    { IXMLAPLClientConfigurationType }
    function Get_CompanyNumber: Integer;
    function Get_StoreNumber: Integer;
    function Get_Username: WideString;
    function Get_Password: WideString;
    function Get_DownloadTime: WideString;
    function Get_LastDownload: WideString;
    function Get_StatePaths: IXMLStatePathsType;
    procedure Set_CompanyNumber(Value: Integer);
    procedure Set_StoreNumber(Value: Integer);
    procedure Set_Username(Value: WideString);
    procedure Set_Password(Value: WideString);
    procedure Set_DownloadTime(Value: WideString);
    procedure Set_LastDownload(Value: WideString);
  public
    procedure AfterConstruction; override;
  end;

{ TXMLStatePathsType }

  TXMLStatePathsType = class(TXMLNodeCollection, IXMLStatePathsType)
  protected
    { IXMLStatePathsType }
    function Get_Path(Index: Integer): IXMLPathType;
    function Add: IXMLPathType;
    function Insert(const Index: Integer): IXMLPathType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLPathType }

  TXMLPathType = class(TXMLNode, IXMLPathType)
  protected
    { IXMLPathType }
    function Get_StateCode: WideString;
    function Get_Filename: WideString;
    function Get_Zipped: WideString;
    procedure Set_StateCode(Value: WideString);
    procedure Set_Filename(Value: WideString);
    procedure Set_Zipped(Value: WideString);
  end;

{ Global Functions }

function GetAPLClientConfiguration(Doc: IXMLDocument): IXMLAPLClientConfigurationType;
function LoadAPLClientConfiguration(const FileName: WideString): IXMLAPLClientConfigurationType;
function NewAPLClientConfiguration: IXMLAPLClientConfigurationType;

const
  TargetNamespace = '';

implementation

{ Global Functions }

function GetAPLClientConfiguration(Doc: IXMLDocument): IXMLAPLClientConfigurationType;
begin
  Result := Doc.GetDocBinding('APLClientConfiguration', TXMLAPLClientConfigurationType, TargetNamespace) as IXMLAPLClientConfigurationType;
end;

function LoadAPLClientConfiguration(const FileName: WideString): IXMLAPLClientConfigurationType;
begin
  Result := LoadXMLDocument(FileName).GetDocBinding('APLClientConfiguration', TXMLAPLClientConfigurationType, TargetNamespace) as IXMLAPLClientConfigurationType;
end;

function NewAPLClientConfiguration: IXMLAPLClientConfigurationType;
begin
  Result := NewXMLDocument.GetDocBinding('APLClientConfiguration', TXMLAPLClientConfigurationType, TargetNamespace) as IXMLAPLClientConfigurationType;
end;

{ TXMLAPLClientConfigurationType }

procedure TXMLAPLClientConfigurationType.AfterConstruction;
begin
  RegisterChildNode('StatePaths', TXMLStatePathsType);
  inherited;
end;

function TXMLAPLClientConfigurationType.Get_CompanyNumber: Integer;
begin
  Result := AttributeNodes['CompanyNumber'].NodeValue;
end;

procedure TXMLAPLClientConfigurationType.Set_CompanyNumber(Value: Integer);
begin
  SetAttribute('CompanyNumber', Value);
end;

function TXMLAPLClientConfigurationType.Get_StoreNumber: Integer;
begin
  Result := AttributeNodes['StoreNumber'].NodeValue;
end;

procedure TXMLAPLClientConfigurationType.Set_StoreNumber(Value: Integer);
begin
  SetAttribute('StoreNumber', Value);
end;

function TXMLAPLClientConfigurationType.Get_Username: WideString;
begin
  Result := AttributeNodes['Username'].Text;
end;

procedure TXMLAPLClientConfigurationType.Set_Username(Value: WideString);
begin
  SetAttribute('Username', Value);
end;

function TXMLAPLClientConfigurationType.Get_Password: WideString;
begin
  Result := AttributeNodes['Password'].Text;
end;

procedure TXMLAPLClientConfigurationType.Set_Password(Value: WideString);
begin
  SetAttribute('Password', Value);
end;

function TXMLAPLClientConfigurationType.Get_DownloadTime: WideString;
begin
  Result := AttributeNodes['DownloadTime'].Text;
end;

procedure TXMLAPLClientConfigurationType.Set_DownloadTime(Value: WideString);
begin
  SetAttribute('DownloadTime', Value);
end;

function TXMLAPLClientConfigurationType.Get_LastDownload: WideString;
begin
  Result := AttributeNodes['LastDownload'].Text;
end;

procedure TXMLAPLClientConfigurationType.Set_LastDownload(Value: WideString);
begin
  SetAttribute('LastDownload', Value);
end;

function TXMLAPLClientConfigurationType.Get_StatePaths: IXMLStatePathsType;
begin
  Result := ChildNodes['StatePaths'] as IXMLStatePathsType;
end;

{ TXMLStatePathsType }

procedure TXMLStatePathsType.AfterConstruction;
begin
  RegisterChildNode('Path', TXMLPathType);
  ItemTag := 'Path';
  ItemInterface := IXMLPathType;
  inherited;
end;

function TXMLStatePathsType.Get_Path(Index: Integer): IXMLPathType;
begin
  Result := List[Index] as IXMLPathType;
end;

function TXMLStatePathsType.Add: IXMLPathType;
begin
  Result := AddItem(-1) as IXMLPathType;
end;

function TXMLStatePathsType.Insert(const Index: Integer): IXMLPathType;
begin
  Result := AddItem(Index) as IXMLPathType;
end;

{ TXMLPathType }

function TXMLPathType.Get_StateCode: WideString;
begin
  Result := AttributeNodes['StateCode'].Text;
end;

procedure TXMLPathType.Set_StateCode(Value: WideString);
begin
  SetAttribute('StateCode', Value);
end;

function TXMLPathType.Get_Filename: WideString;
begin
  Result := AttributeNodes['Filename'].Text;
end;

procedure TXMLPathType.Set_Filename(Value: WideString);
begin
  SetAttribute('Filename', Value);
end;

function TXMLPathType.Get_Zipped: WideString;
begin
  Result := AttributeNodes['Zipped'].Text;
end;

procedure TXMLPathType.Set_Zipped(Value: WideString);
begin
  SetAttribute('Zipped', Value);
end;

end.
