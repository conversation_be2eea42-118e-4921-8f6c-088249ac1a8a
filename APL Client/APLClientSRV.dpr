program APLClientSRV;

{$IFDEF DEBUG}
  {$APPTYPE CONSOLE}
{$ENDIF}

uses
  VCL.SvcMgr,
  VCL.Forms,
  uAPLClientService in 'uAPLClientService.pas' {APLClientService: TService},
  APLSvcMainForm in 'APLSvcMainForm.pas' {MainForm},
  APLSvcThread in 'APLSvcThread.pas',
  System.SysUtils;

{$R *.RES}

begin
  // Windows 2003 Server requires StartServiceCtrlDispatcher to be
  // called before CoRegisterClassObject, which can be called indirectly
  // by Application.Initialize. TServiceApplication.DelayInitialize allows
  // Application.Initialize to be called from TService.Main (after
  // StartServiceCtrlDispatcher has been called).
  //
  // Delayed initialization of the Application object may affect
  // events which then occur prior to initialization, such as
  // TService.OnCreate. It is only recommended if the ServiceApplication
  // registers a class object with OLE and is intended for use with
  // Windows 2003 Server.
  //
  // Application.DelayInitialize := True;
  //

  Application.Title := 'APLClientService';
  if FindCmdLineSwitch('GUI', ['/'], True) then         // CPCLIENTS-10651
  begin
    VCL.Forms.Application.Initialize;
    VCL.Forms.Application.MainFormOnTaskBar := True;
    VCL.Forms.Application.CreateForm(TMainForm, MainForm);
    VCL.Forms.Application.Run;
  end
  else
  begin
    if not VCL.SvcMgr.Application.DelayInitialize or VCL.SvcMgr.Application.Installing then
      VCL.SvcMgr.Application.Initialize;
    VCL.SvcMgr.Application.CreateForm(TAPLClientService, APLClientService);
    VCL.SvcMgr.Application.CreateForm(TMainForm, MainForm);
    VCL.SvcMgr.Application.Run;
  end;
end.
