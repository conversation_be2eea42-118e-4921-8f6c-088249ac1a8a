unit uAPL;

interface

{$IF CompilerVersion < 32.0}
This must be compiled with Delphi 10.2 (Tokyo) or later
{$IFEND}

uses
  SysUtils, Classes,TypInfo,
  Windows, Messages, Variants, Graphics, Controls, Forms, Registry, Types,
  Dialogs, StdCtrls, Buttons, xmldom, XMLIntf, OleCtrls, SHDocVw, msxmldom, XMLDoc,
  IdBaseComponent, IdComponent, IdTCPConnection, IdTCPClient;

const
  CONFIGFILE = 'APLClientConfiguration.xml';
  DOMAIN = 'https://www.servereps.com';
  MAXSTATES = 5;
  NOT_FOUND = -1;

type
  enumConfigNumber = (eCompanyNumber,eStoreNumber);

  TDM = class(TDataModule)
    procedure DataModuleCreate(Sender: TObject);
  private
    { Private declarations }

  public
    { Public declarations }
  end;

  TIdleThread = class(TThread)
  private
    TargetTime: TDateTime;
    JournalUploadTime: TDateTime;
    CoInitResult: HResult;
    procedure SetFirstTargetTime(T: TDateTime);
    procedure SetJournalUploadTime;
  protected
    procedure Execute; override;
  public
    constructor Create;
    destructor Destroy;
    function GetTargetTimeStr: string;
  end;

  TGuiThread = class(TThread)
  private
  protected
    procedure Execute; override;
  public
    constructor Create;
  end;


function URL(Domain: string; Company,Store: integer; Username,Password,sFormat,LastDownload: string): string;
procedure URLRequest(Domain: string);
procedure URLRequestThreaded;
function GetUrlContent(const Url: string): AnsiString;         // CPCLIENTS-10531
procedure CreateConfigIfMissing;
procedure Log(Msg: string; BufferIt: boolean = false);
function GetVersionString(Filename,VerStr: string): string;
function GetConfigNumber(ConfigType: enumConfigNumber): integer;
procedure SetLocalDirAndLogName;
function InitAPLClient: boolean;
function QuitAPLClient: boolean;
function GetUPD: boolean;
function StringClear(S: string): string;
function StringEncrypted(S: string): string;
function GetAPLStatusXML: string;      // export this to allow unit testing...
procedure SetAPL_DLL_Busy(Value: boolean);
function APL_DLL_Busy: boolean;

var
  DM: TDM;
  Idle: TIdleThread;
  GuiThread: TGuiThread;

implementation

{$R *.dfm}

uses
  SyncObjs,    //critical section
  winsock,
  ActiveX,
  WinInet,
  DateUtils,
  eWicAplxptSuccessfulExport,
  StrUtils,
  ServerEPSConstants,
  APLClientConfiguration,
  SE_WebClient,
  AbUtils, AbBase, AbBrowse, AbZBrows, AbZipper, AbZipKit, AbArcTyp, AbZipPrc, AbUnzPrc, AbUnzper,
  SELoginResponse,
  SECodeFileResponse,
  Base64,
  {$IFNDEF WOLF}
  LBClass,
  LbCipher,
  {$ENDIF}
  ComputerInfo,
  //ZipForge,
  Compression,
  APLStatus,
  LaneService,
  eWicMtxExportingResultError,
  eWicMtxExportingResultRestriction;

const
  sOffOn: array[boolean] of string[3] = ('OFF','ON');
  sOK: array[boolean] of string[4] = ('FAIL','OK');
  sPlural: array[boolean] of string[3] = ('s','');
  HexChrSet = '0123456789ABCDEF';
  LOG_NAMEONLY = 'MTX_APL_Log';
  LOG_EXTONLY = '.txt';
  LOG_FILENAME = LOG_NAMEONLY + LOG_EXTONLY;
  OLDYEAR = 1900;
  DEFAULT_LANE_NUMBER = 9997;
  DEFAULTXML =
    '<APLClientConfiguration CompanyNumber="999" StoreNumber="999" Username="AplExport" Password="1ArgoFind" DownloadTime="23:59" LastDownload="11/11/2010">'+
    ' <StatePaths>'+
    '  <Path StateCode="" Filename="" Zipped=""></Path>'+
    '  <Path StateCode="" Filename="" Zipped=""></Path>'+
    '  <Path StateCode="" Filename="" Zipped=""></Path>'+
    '  <Path StateCode="" Filename="" Zipped=""></Path>'+
    '  <Path StateCode="" Filename="" Zipped=""></Path>'+
    ' </StatePaths>'+
    '</APLClientConfiguration>';
  KEY = 'The Glockenspiel is on the fritz';
  DLLNAME = 'mtx_apl.dll';
  GUINAME = 'APLClientGUI.exe';
  SRVNAME = 'APLClientSRV.exe';
  SETUPTXT = 'SETUP.TXT';
  NEVERPROXY = '';
  //JOURNAL_UPLOAD_INTERVAL = 1;       // for testing
  JOURNAL_UPLOAD_INTERVAL = 15;

type
  enumDocType = (docUnknown,docAPLResponse,docMtxExportingResult);

var
  LogFilename,LocalDir,IP: string;
  Lock: TCriticalSection;
  MTXSleepEvent: THandle;               // CPCLIENTS-10531
  FAPL_Dll_Busy: boolean;

{$REGION '<Utility Functions>'}

procedure SetAPL_DLL_Busy(Value: Boolean);
begin
  if Value <> FAPL_Dll_Busy then
  begin
    Log('API_DLL_Busy is now ' + BoolToStr(Value, True));
    FAPL_Dll_Busy := Value;
  end;
end;

function APL_DLL_Busy: boolean;
begin
  Result := FAPL_Dll_Busy;
end;

procedure MTXSleep(ms: cardinal);      // CPCLIENTS-10531
begin
  WaitForSingleObject(MTXSleepEvent, ms);
end;

procedure CreateLock;
begin
  if Lock = nil then Lock := TCriticalSection.Create;
end;

procedure AcquireLock;
begin
  if Lock = nil then Lock := TCriticalSection.Create;
  Lock.Acquire;
end;

procedure DestroyLock;
begin
  if Lock <> nil then FreeAndNil(Lock);
end;

procedure ReleaseLock;
begin
  if Lock <> nil then Lock.Release;
end;

function TimeZoneBiasInHours: double;
var
  ATimeZone: TTimeZoneInformation;
begin
  case GetTimeZoneInformation(ATimeZone) of
    TIME_ZONE_ID_DAYLIGHT:
      result := ATimeZone.Bias + ATimeZone.DaylightBias;
    TIME_ZONE_ID_STANDARD:
      result := ATimeZone.Bias + ATimeZone.StandardBias;
    TIME_ZONE_ID_UNKNOWN:
      result := ATimeZone.Bias;
    else
      result := -1440;  // results in an error indication of -24 hours...
  end;
  result := -result/60.0;
end;

function FileToDynArray(const Filename: string): TByteDynArray;
var
  f: file of byte;
  i,iFileSize: integer;
begin
  SetLength(result,0);  // default
  try
    if fileexists(Filename) then
      begin
      assignfile(f,Filename);
      reset(f);
      iFileSize := FileSize(f);
      SetLength(result,iFileSize);     // build the TByteDynArray Request
      try
        for i := 0 to iFileSize-1 do
          read(f,result[i]);
      finally
        closefile(f);
      end;
      end;
  except on e: exception do
    begin
    Log('EXCEPTION in FileToDynArray');
    raise;   // re-raise this so we know what calling routine the error occurred in
    end;
  end;
end;

function AddToZip(ZipFileName,Filename: string): integer;
var
  Dummy         : TComponent;
  AbZipKit1     : TAbZipKit;
  HasError      : boolean;
begin
  result := -1;
  if FileExists(ZipFileName) then
    begin
    result := 1;
    if not SysUtils.DeleteFile(ZipFileName) then
      exit;
    end;
  Dummy := TBasicAction.Create(nil);
  try
    AbZipKit1 := TAbZipKit.Create(Dummy);
    try
      result := 3;
      AbZipKit1.FileName := ZipFileName;
      AbZipKit1.StoreOptions := [TAbStoreOption(1)]; // strip path
      HasError := false;
      if FileExists(Filename) then
        try
          AbZipKit1.AddFiles(Filename, 0);
        except
          HasError := true;
          result := 14;
          //AbZipKit1.DeleteFiles(Filename);
        end;
      if NOT HasError then
        try
          result := 16;
          AbZipKit1.Save;
        except
          HasError := true;
          result := 17;
        end;
      if not HasError then
        result := 0;
    finally
      if NOT HasError then
        AbZipKit1.CloseArchive;
      AbZipKit1.Free;
    end;
  finally
    Dummy.Free;
  end;
end;

function Zip(const aFilename: string): string;
//var Z: TZipForge;
begin
  {
  result := '';
  try
    Z := TZipForge.Create(nil);
    try
      result := ChangeFileExt(aFilename,'.zip');
      if FileExists(result) then
        SysUtils.DeleteFile(result);
      Z.Filename := result;
      Z.OpenArchive;
      Z.BaseDir := ExtractFileDir(aFilename);
      Z.AddFiles(ExtractFilename(aFilename));
      Z.CloseArchive;
    finally
      Z.Free;
    end;
  except on e: exception do
    result := '';
  end;
  }
  result := TMTXZip.Zip(aFileName);
end;

function StringClear(S: string): string;
var
  Cipher: TLbBlowfish;
begin
  Cipher := TLbBlowfish.Create(nil);
  try
    Cipher.CipherMode := cmECB;
    Cipher.GenerateKey(KEY);
    result := Cipher.DecryptString(S);
  finally
    Cipher.Free;
  end;
end;

function StringEncrypted(S: string): string;
var
  Cipher: TLbBlowfish;
begin
  Cipher := TLbBlowfish.Create(nil);
  try
    Cipher.CipherMode := cmECB;
    Cipher.GenerateKey(KEY);
    result := Cipher.EncryptString(S);
  finally
    Cipher.Free;
  end;
end;

(*procedure Delay(ms: integer);         // CPCLIENTS-10531
begin
  repeat
    sleep(100);
    dec(ms,100);
  until ms <= 0;
end;*)

function ValidFilename(Filename: string): boolean;
const
  ForbiddenChars: set of Char = ['<','>','|','"','\','/',':','*','?'];
var
  i: integer;
begin
  result := Filename <> '';
  for i := 1 to length(Filename) do
    result := result and not (Filename[i] in ForbiddenChars);
end;

function RegLookup(FileName: string; const In_Key: string): string;
var
  Reg: TStringList;
begin
  result := '';
  Filename := ExtractFilePath(ParamStr(0)) + Filename;
  try
    if FileExists(Filename) then
      begin
      Reg := TStringList.Create;
      try
        Reg.Duplicates := dupAccept;
        Reg.CaseSensitive := false;
        Reg.Sorted := false;
        Reg.LoadFromFile(Filename);
        Reg.Sorted := true;    // optimum way.. load first THEN sort
        result := trim(Reg.Values[In_Key]);
        if length(result) > 0
          then Log(format('RegLookup: %s Value %s=%s is being used',[FileName,In_Key,result]))
          else Log(format('RegLookup: ****NOTICE: %s Value for %s not found',[FileName,In_Key]));
      finally
        Reg.Free;
      end;
      end
    else
      Log(format('RegLookup: ****NOTICE: File %s does not exist; so value for %s not found',[FileName,In_Key]));
  except on e: exception do
    Log('RegLookup: Exception - ' + e.Message);
  end;
end;

function WSDLURL: string;
begin
  result := RegLookup(SETUPTXT,_ServerEpsServicesHost1);
  if result = '' then
    result := URL_SVC1_PROD;
end;

function OldDate: TDateTime;
begin
  result := StartOfAYear(OLDYEAR);  // return a really OLD date...
end;

function GetDateTime(S: string): TDateTime;
var
  aYear,aMonth,aDay,aHour,aMinute,aSecond: word;
  OutDate: TDateTime;
begin
  // incoming string is of format:  '2007-01-31 12:59:59' 'YYYY-MM-DD HH:MM:SS'
  S := trim(S);
  aYear := StrToIntDef(copy(S,1,4),OLDYEAR);
  aMonth := StrToIntDef(copy(S,6,2),1);
  aDay := StrToIntDef(copy(S,9,2),1);
  aHour := StrToIntDef(copy(S,12,2),0);
  aMinute := StrToIntDef(copy(S,15,2),0);
  aSecond := StrToIntDef(copy(S,18,2),0);
  if TryEncodeDateTime(aYear,aMonth,aDay,aHour,aMinute,aSecond,0,OutDate)
    then result := OutDate
    else result := OldDate;  // return a really OLD date...
end;

function UnCompress(const S: string): string;
const
  GZIP_HEADER = 10;
  GZIP_1 = chr($1F);
  GZIP_2 = chr($8B);
var
  InStream: TStream;
  OutStream: TStringStream;
  sIn,sResult: string;
  GZipSignature: boolean;    // probably unnecessary to check this
begin
  GZipSignature := (length(S) >= 2) and (S[1] = GZIP_1) and (S[2] = GZIP_2);
  if GZipSignature then
    begin
    sIn := copy(S,GZIP_HEADER+1,length(S));   // strip off GZip header bytes
    sResult := '';
    InStream := TStringStream.Create(sIn);
    try
      OutStream := TStringStream.Create(sResult);
      try
        //JTG: cannot do this in D2007+
        InflateStream(InStream,OutStream);
        result := OutStream.DataString;
      finally
        OutStream.Free;
      end;
    finally
      InStream.Free;
    end;
    end
  else
    result := S;   // do nothing if not a gzip
end;

function UnZip(Filename: string): boolean;  // fully qualified file name
var
  Zip: TAbUnZipper;
begin
  result := false;
  try
    Zip := TAbUnZipper.Create(nil);
    try
      if pos(':',Filename) = 0 then
        Filename := ExtractFileDrive(GetCurrentDir) + Filename;  // copy drive letter if none exists
      Zip.BaseDirectory := ExtractFileDir(Filename);
      Zip.OpenArchive(Filename);
      Zip.ExtractFiles('*.*');
      result := true;
    finally
      Zip.CloseArchive;
      Zip.Free;
    end;
  except on e: exception do
    raise;
  end;
end;

function GetLocalIP: string;
var
  wsaData: TWSAData;
  addr: TSockAddrIn;
  Phe: PHostEnt;
  szHostName: array[0..128] of AnsiChar;
begin
  Result := '';
  if WSAStartup($101, WSAData) <> 0 then
    exit;
  try
    if GetHostName(szHostName, 128) <> SOCKET_ERROR then
      begin
      Phe := GetHostByName(szHostName);
      if Assigned(Phe) then
        begin
        addr.sin_addr.S_addr := longint(plongint(Phe^.h_addr_list^)^);
        Result := inet_ntoa(addr.sin_addr);
        end;
      end;
  finally
    WSACleanup;
  end;
end;

function GetVersionString(Filename,VerStr: string): string;
var
  Size,Handle: dword;
  Len: uint;
  Buffer,Value: pchar;
  TransNo: pLongInt;
  SFInfo: string;
begin
  result := '';
  Size := GetFileVersionInfoSize(pChar(FileName),Handle);
  if Size > 0 then
  begin
    Buffer := AllocMem(Size);
    try
      GetFileVersionInfo(pChar(FileName),0,Size,Buffer);
      VerQueryValue(Buffer, PChar('VarFileInfo\Translation'),Pointer(TransNo),Len);
      SFInfo := format('%s%.4x%.4x%s%s%',['StringFileInfo\',LoWord(TransNo^),HiWord(Transno^),'\',VerStr]);
      if VerQueryValue(Buffer,PChar(SFInfo),Pointer(Value),Len)
        then result := Value;
    finally
      if Assigned(Buffer) then
        FreeMem(Buffer,Size);    // always release memory that's hard-allocated
    end;
  end;
end;

function LocalFileIsDifferent(FileOnServer: NeededFileType): boolean;  // takes a single needed file type structure and returns true if needed to be updated
var
  FileVersionInfo,FullName: string;
begin
  FileVersionInfo := '';
  result := (trim(FileOnServer.File_Name) <> '') and ValidFilename(FileOnServer.File_Name);
  if not result then
  begin
    Log('No server file information.');
    exit;
  end;
  //result is true if we are here; it is the default; file is different unless we prove otherwise

  // this embeds the 'version' into the filename we just got from the login results
  // because said filename does not have the version in it already..
  if (FileOnServer.Category = lfCodeFiles) and
     (SameText(StrUtils.RightStr(FileOnServer.File_Name,4),ZIPEXTENSION)) then
    FileOnServer.File_Name := LeftStr(FileOnServer.File_Name,length(FileOnServer.File_Name)-3) +
       FileOnServer.File_Version + ZIPEXTENSION;

  FullName := LocalDir + FileOnServer.File_Name;
  if FileExists(FullName) then
    case FileOnServer.Category of
      lfCodeFiles:
        begin
        FileVersionInfo := GetVersionString(FullName,'FileVersion');
        if FileVersionInfo <> '' then   // if an exe or dll type of file...
          result := trim(FileVersionInfo) <> trim(FileOnServer.File_Version)
        else
          result := false; // file not exists, no versioning info... MUST DOWNLOAD.. // file is corrupt? then Download
        //LogCodeDownload(FullName, result, FileOnServer.File_Version, FileVersionInfo);
        end;
      end
  else
    Log(format('%s does not exist locally. Will download. (Server FileVersion=%s)', [FullName, FileOnServer.File_Version]));
end;

function CopyFileTo(const Source, Destination: string): Boolean;
begin
  Result := CopyFile(PChar(Source), PChar(Destination), true);
end;

function PosInStrArray(SearchStr: string; Contents: array of string; const CaseSensitive: Boolean = True): Integer;
begin
  for Result := Low(Contents) to High(Contents) do
    begin
    if CaseSensitive then
      begin
      if SearchStr = Contents[Result] then exit;
      end
    else
      begin
      if ANSISameText(SearchStr, Contents[Result]) then exit;
      end;
    end;
  Result := -1;
end;

function DateFromYYYYMMDD(DateTimeStr: string): TDateTime;   //JTG added as result of Micah 2007-02-28 request
const
  LenYYYYMMDD = 8;
var
  AYear,AMonth,ADay: Word;
  DT: TDateTime;
begin
  result := OldDate;    // if we die prematurely, this gets returned
  DateTimeStr := trim(dateTimeStr);

  // die if the lengths don't match
  if length(dateTimeStr) <> LenYYYYMMDD then exit;

  // now try to encode all the values
    try
      AYear := StrToIntDef(copy(DateTimeStr,1,4),0);    // AYear has to be a word value
      AMonth := StrToIntDef(copy(DateTimeStr,5,2),0);
      ADay := StrToIntDef(copy(DateTimeStr,7,2),0);
      if TryEncodeDateTime(AYear,AMonth,ADay,0,0,0,0,DT)
        then result := DT
        else result := OldDate;
    except
      result := OldDate;  // probably not necessary
    end;
end;

function DateFromDDMMYYYY(sDate: string): TDateTime;   // format 11/28/2011
const
  LenYYYYMMDD = 8;
var
  AYear,AMonth,ADay: Word;
  DT: TDateTime;
  S: string;

  function SetDatePart: integer;
  var
    i: integer;
  begin
    i := pos('/',sDate);
    S := trim(LeftStr(sDate,i-1));
    result := StrToIntDef(S,0);
    Delete(sDate,1,i);
  end;

begin
  sDate := trim(sDate);
  AMonth := SetDatePart;
  ADay := SetDatePart;
  AYear := StrToIntDef(sDate,0);

  if TryEncodeDateTime(AYear,AMonth,ADay,0,0,0,0,DT)
    then result := DT
    else result := OldDate;
end;

function GetIntKeyFromRegistry(root: hkey; dir, keyName: string): integer;
var
  reg: TRegistry;
begin
  result := NOT_FOUND;
  try
    reg := TRegistry.create;
    try
      reg.Access := KEY_EXECUTE;         // read-only access
      reg.rootkey := root;
      if reg.openkey(dir,false) then
        begin
        result := Reg.ReadInteger(keyName);
        Reg.CloseKey;
        end
      else
        result := NOT_FOUND;
    finally
      reg.free;
    end;
  except on e : Exception do ;
  end;
end;

function GetConfigNumber(ConfigType: enumConfigNumber): integer;
const
  sKey: array[enumConfigNumber] of string[20] = (COMPANY_KEY,STORE_KEY);
  Count: integer = 0;
var
  Config: IXMLAPLClientConfigurationType;
begin
  try
    result := GetIntKeyFromRegistry(HKEY_LOCAL_MACHINE,SERVEREPS_REGISTRY_LOCATION,sKey[ConfigType]);
    if result = NOT_FOUND then
      begin
      CreateConfigIfMissing;
      CoInitialize(nil);         //CoInitializeEx(nil,COINIT_APARTMENTTHREADED);
      try
        Config := LoadAPLClientConfiguration(CONFIGFILE);
        case ConfigType of
          eCompanyNumber: result := Config.CompanyNumber;
          eStoreNumber: result := Config.StoreNumber;
        end;
        inc(Count);
        if Count <= 4 then
          Log(format('%s NOT FOUND in Registry - using [%d] from Config XML file',[sKey[ConfigType],result]));
      finally
        CoUnInitialize;
      end;
      end;
  except on e: exception do
    Log(format('Get%s EXCEPTION = %s',[sKey[ConfigType],e.Message]));
  end;
end;

procedure StringToFile(const FileName: TFileName; const Content: string);
begin
  with TFileStream.Create(FileName, fmCreate) do
    try
      Write(Pointer(content)^, Length(content));
    finally
      Free;
    end;
end;

procedure SetLocalDirAndLogName;
begin
  LocalDir := ExtractFilePath(ParamStr(0));
  LogFilename :=  LocalDir + LOG_FILENAME;
end;

function LaneServiceResult(const ResultCode: integer): string;
begin
  case ResultCode of
    LS_SUCCESS:
      result := 'SUCCESS';
    LS_INTERNAL_DATABASE_ERROR:
      result := 'Internal database error';
    LS_INVALID_FILE_UPLOAD_REQUEST:
      result := 'The file upload request is no longer valid';
    LS_UNSUPPORTED_TYPE:
      result := 'Unsupported type as file upload attempt';
    LS_EXCHANGE_INFO_FAILED:
      result := 'Exchange info failed to return the data';
    LS_EVENT_PREOWNED_OR_IMPROPER:
      result := 'The event is owned by another lane or is not in the proper state';
    LS_STORE_NOT_DEFINED:
      result := 'Store not defined';
    LS_RECEIPT_ALREADY_UPLOADED:
      result := 'A receipt has already been uploaded for this transaction';
    LS_RECEIPT_UPLOADED_AND_ORPHANED:
      result := 'A receipt has already been uploaded for this transaction and it has been orphaned';
    ERR_UPLOADRECEIPT:
      result := 'The SE DLL had a HARD error in UploadReceipt';
    LS_STATUS_UPLOAD_INVALID_XML:
      result := 'Store Monitoring XML is invalid';
    LS_STATUS_UPLOAD_DISABLED:
      result := 'Store Monitoring StatusUpload is disabled';
    ERR_EXCEPTION:
      result := 'The SE DLL had an exception';
    else
      result := 'Unknown LaneService Error Code';
  end;
end;

{$ENDREGION}

{$REGION '<Logging Functions>'}
// This proc takes care of all the tedious parameters of the CreateProcess API call.
function EasyCreateProcessEx(cmdLine: string; var aHandle: THandle; const Wait: Boolean = false; const TimeoutInterval: Cardinal = INFINITE): Boolean;
var
  dw: integer;
  lb: longbool;
  lp: pointer;
  ts: TStartupInfo;
  tp: TProcessinformation;
begin
  result := false;
  try
    dw:=0;
    lb:=false;
    lp:=nil;
    fillchar(ts, sizeof(ts), 0);
    fillchar(tp, sizeof(tp), 0);
    ts.dwflags := STARTF_USESHOWWINDOW;
    ts.wShowWindow := SW_HIDE;
    Result := CreateProcess(nil,pchar(cmdLine),nil,nil,lb,dw,lp,nil,ts,tp);
    if Wait and Result then
      WaitForSingleObject(tp.hProcess, TimeoutInterval);
    aHandle := tp.hProcess;
    CloseHandle(tp.hProcess);
    CloseHandle(tp.hThread);
  except on E: Exception do
  end;
end;

procedure WriteToLog(Msg: string);
const
  SFORMATDT = 'yyyy-mm-dd hh:nn:ss.zzz ';
var
  i: integer;
  log: TextFile;
  sLog,LogSource: string;

  function DateTimeFromName(Filename: string): TDateTime;
  var
    YYYY,MM,DD,HH,NN,SS: word;
    i: integer;
    S: string;
  begin
    result := Now;
    for i := 1 to length(Filename) do
      if Filename[i] in ['0'..'9'] then
        begin
        S := copy(Filename,i,15);
        YYYY := StrToIntDef(copy(S,1,4),0);
        MM   := StrToIntDef(copy(S,5,2),0);
        DD   := StrToIntDef(copy(S,7,2),0);
        HH   := StrToIntDef(copy(S,10,2),0);
        NN   := StrToIntDef(copy(S,12,2),0);
        SS   := StrToIntDef(copy(S,14,2),0);
        if not TryEncodeDateTime(YYYY,MM,DD,HH,NN,SS,0,result)
          then result := Now;
        break;
        end;
  end;

  function CutOverToNewJournal: string;
  const
    KeepArchives: integer = 10;
  var
    Filename,YesterdaysLog: string;
    sr: TSearchRec;
  begin
    result := '';
    // delete files older than KeepArchives days old
    if FindFirst(LocalDir + LOG_NAMEONLY + '*' + LOG_EXTONLY, faAnyFile, sr) = 0 then
      try
        repeat
          Filename := LocalDir + sr.Name;
          if DaysBetween(Now,DateTimeFromName(sr.Name)) > KeepArchives then
            DeleteFile(PChar(Filename));
        until FindNext(sr) <> 0;
      finally
        SysUtils.FindClose(sr);
      end;
    // rename current archive to today's date...
    if KeepArchives > 0 then
      begin
      YesterdaysLog := LocalDir + LOG_NAMEONLY + '-' + FormatDateTime('yyyymmdd',Yesterday) + LOG_EXTONLY;
      if FileExists(YesterdaysLog) then
        result := format('Trying to rename %s to %s but it already exists!',[LogFilename,YesterdaysLog])
      else if RenameFile(LogFilename,YesterdaysLog) then
        result := format('Renamed %s to %s',[LogFilename,YesterdaysLog])
      else
        result := format('UNABLE to rename %s to %s',[LogFilename,YesterdaysLog]);
      end
    else
      begin
      SysUtils.DeleteFile(LogFilename);
      result := format('KeepArchives set to ZERO; Deleting current log: %s',[LogFilename]);
      end;
  end;

  function UseCurrentLog: boolean;
  var
    TimeStamp: TDateTime;
    FileDate: integer;
  begin
    result := true;
    FileDate := FileAge(LogFilename);
    if FileDate >= 0 then
      begin
      TimeStamp := FileDateToDateTime(FileDate);
      result := SameDate(TimeStamp,Today);   // if the current log has the same timestamp as today, go ahead an use it.
      end;
  end;

begin
  AcquireLock;
  sLog := '';
  if not UseCurrentLog
    then sLog := CutOverToNewJournal;
  AssignFile(log,LogFilename);
  {$I-} Append(log); {$I+}
  i := IOResult;
  if i <> 0 then
    if not FileExists(LogFilename) then
    begin
    {$I-} Rewrite(log); {$I+}
    i := IOResult;
    end;
  if i = 0 then
    try
      if sLog <> ''
        then writeln(log, FormatDateTime(SFORMATDT,Now) + sLog);
      LogSource := LeftStr(Msg,4);
      if not((LogSource = 'SVC ') or (LogSource = 'GUI ')) then
        begin
        LogSource := 'DLL ';
        writeln(log, FormatDateTime(SFORMATDT,Now) + LogSource + Msg);
        end
      else
        writeln(log, FormatDateTime(SFORMATDT,Now) + Msg);
    finally
      CloseFile(log);
    end;
  ReleaseLock;
end;

procedure Log(Msg: string; BufferIt: boolean = false);
const
  sBuffer: string = '';
  Count: integer = 0;
begin
  if BufferIt then
    begin
    if Count = 0 then
      begin
      sBuffer := Msg;
      inc(Count);
      end
    else
      begin
      if Msg = sBuffer then
        inc(Count)
      else
        begin
        if Count = 1
          then WriteToLog(sBuffer)    // we didn't get a repeat, so just log it, and then log the new line as well
          else WriteToLog(format('%s (repeated %d times)',[sBuffer,Count]));  // log the buffered line, with the repeat count
        WriteToLog(Msg);                                               // then log the NEW line.. this one is NOT Buffered however
        Count := 0;
        sBuffer := '';
        end;
      end;
    end
  else
    WriteToLog(Msg);
end;

function PrintBinary(buffer: PChar; bufferSize: Integer): string;
var
  i: integer;
begin
  Result := '';
  for i := 0 to bufferSize - 1 do
    if ord(buffer[i]) < 32
      then result := format('%s[%2.2x]',[result,Ord(buffer[i])])
      else result := Result + buffer[i];
end;

{$ENDREGION}

{$REGION '<Getting URL Content>'}

function MaskURL(S: string): string;
const
  TARGET = 'password=';
var
  i,j: integer;
begin
  result := S;
  i := pos(TARGET,lowercase(S));
  if i > 0 then
    begin
    for j := i+length(TARGET) to length(S) do
      if S[j] = '&'
        then break
        else result[j] := '*'
    end;
end;

function GetUrlContent(const Url: string): AnsiString;    // CPCLIENTS-10531
const
  BUF_SIZE = 4096;
var
  NetHandle,UrlHandle: HINTERNET;
  Buffer: array[0..BUF_SIZE - 1] of AnsiChar;     //was 1024
  BytesRead: dWord;
  TotalBytesRead: DWord;
begin
  try
    Log(format('GetUrlContent: Getting URL content from [%s]',[MaskURL(Url)]));
    Result := '';
    SetAPL_Dll_Busy(True);                  // CPCLIENTS-10531
    NetHandle := InternetOpen('Delphi 5.x', INTERNET_OPEN_TYPE_PRECONFIG, nil, nil, 0);
    //Log('GetUrlContent: InternetOpen OK');
    if Assigned(NetHandle) then
      begin
      //Log('GetUrlContent: NetHandle OK');
      UrlHandle := InternetOpenUrl(NetHandle, PChar(Url), nil, 0, INTERNET_FLAG_RELOAD, 0);
      //Log('GetUrlContent: UrlHandle OK');
      if Assigned(UrlHandle) then  // UrlHandle valid? Proceed with download
        begin
        Log('GetUrlContent: NetHandle/UrlHandle OK');
        FillChar(Buffer, SizeOf(Buffer), 0);
        repeat
          Result := Result + Buffer;
          FillChar(Buffer, BUF_SIZE, #0);
          InternetReadFile(UrlHandle, @Buffer, BUF_SIZE, BytesRead);
          Inc(TotalBytesRead, BytesRead);
        until BytesRead = 0;
        Log('TotalBytesRead=' + TotalBytesRead.ToString);
        Log('Result=' + Result);
        Log('Result at the end=' + AnsiRightStr(Result, 100));
        //Log('GetUrlContent: RESULT = '+result);
        if not InternetCloseHandle(UrlHandle)
          then Log('GetUrlContent: UrlHandle FAILED TO CLOSE');
        end
      else  // UrlHandle is not valid. Raise an exception.
        Log(format('GetUrlContent: Cannot open URL %s',[MaskURL(Url)]));  // CPCLIENTS-10326
      if not InternetCloseHandle(NetHandle)
        then Log('GetUrlContent: NetHandle FAILED TO CLOSE');
      end
    else   // NetHandle is not valid. Raise an exception }
      Log('GetUrlContent: Unable to initialize Wininet (NetHandle not ASSIGNED) to retrieve URL Content');
  except on e: exception do
    Log(format('GetUrlContent: EXCEPTION %s (URLContent = %s)',[e.Message,result]));
  end;
  SetAPL_Dll_Busy(False);                                    // CPCLIENTS-10531
end;

function URL(Domain:string; Company,Store: integer; Username,Password,sFormat,LastDownload: string): string;
begin
  result := format('%s/ServerEPS/Export/eWicApl.xpt?companynumber=%d&store=%d&username=%s&password=%s&format=%s&LastRetrievalDate=%s',
               [Domain,Company,Store,Username,Password,sFormat,LastDownload]);
end;

procedure CreateConfigIfMissing;
var
  f: textfile;
begin
  try
    if not FileExists(CONFIGFILE) then
    begin
      Log('Configuration file does not exist; creating default config file: '+CONFIGFILE);
      AssignFile(f,CONFIGFILE);
      Rewrite(f);
      try
        write(f,DEFAULTXML);
      finally
        Closefile(f);
      end;
    end;
  except on e: exception do
    Log('CreateConfigIfMissing EXCEPTION: '+e.message);
  end;
end;

function DocumentType(const S: string): enumDocType;
begin
  if pos('APLResponse',S) > 0 then
    result := docAPLResponse
  else if pos('MtxExportingResult',S) > 0 then
    result := docMtxExportingResult
  else
    result := docUnknown;
end;

procedure URLRequest(Domain: string);
const
  APL = 'APL';
  LF = #10;
  CRLF = #13#10;
var
  sURL,S,Filename: string;
  APLResponse: IXMLAPLResponseType;
  APLFile: IXMLAPLFileType;
  Restriction: eWicMtxExportingResultRestriction.IXMLMtxExportingResultType;
  Failure: eWicMtxExportingResultError.IXMLMtxExportingResultType;
  Node: IXMLNode;
  Config: IXMLAPLClientConfigurationType;
  GotAFile: boolean;
  Company,Store: integer;
  XMLDocument: TXMLDocument;
  AnsiS: AnsiString;      // MCD

  function FullFilename(APLStateCode: string): string;
  var
    j: integer;
    sPath,sFilename: string;
  begin
    result := '';
    for j := 0 to MAXSTATES - 1 do
      if SameText(Config.StatePaths[j].StateCode,APLStateCode) then
        begin
        if length(Config.StatePaths[j].Text) <= 1
          then sPath := ExtractFilePath(ParamStr(0))
          else sPath := IncludeTrailingPathDelimiter(Config.StatePaths[j].Text);
        sFilename := trim(Config.StatePaths[j].Filename);
        if length(sFilename) = 0
          then sFilename := format('SEPSAPL_%s.APL',[APLStateCode]);
        result := format('%s%s',[sPath,sFilename]);
        if not DirectoryExists(sPath) then
          ForceDirectories(sPath);
        end;
    if result = '' then
      result := format('%sSEPSAPL_%s.APL',[ExtractFilePath(ParamStr(0)),APLStateCode]);
  end;

  procedure FinalizeOrZipFile(APLStateCode,Filename: string);
  var
    j,ZipResult: integer;
    ZipFilename: string;
  begin
    for j := 0 to MAXSTATES - 1 do
      begin
      ZipFilename := Filename + '.temp.zip';
      if SameText(Config.StatePaths[j].StateCode,APLStateCode) and SameText(Config.StatePaths[j].Zipped,'Y') then
        begin
        ZipResult := AddToZip(ZipFileName,Filename);
        if ZipResult = 0 then
          begin
          Log(format('Compressed file %s created successfully.. now renaming to %s ',[ZipFilename,Filename]));
          if SysUtils.DeleteFile(Filename)  // delete the uncompressed file
            then RenameFile(ZipFilename,Filename)
            else Log(format('Unable to delete original file %s so cannot rename %s to %s',[Filename,ZipFilename,Filename]));
          end
        else
          Log(format('%s creation ERROR = %d',[ZipFilename,ZipResult]));
        end;
      end;
  end;

begin
  try
    SetAPL_Dll_Busy(True);
    Company := GetConfigNumber(eCompanyNumber);
    Store := GetConfigNumber(eStoreNumber);
    Config := LoadAPLClientConfiguration(CONFIGFILE);

    sURL := URL(Domain,Company,Store,Config.Username,StringClear(Config.Password),APL,Config.LastDownload);
    Log(format('APL File Download Request for Company[%d] Store(%d] LastDownload[%s]',[Company,Store,Config.LastDownload]));

    XMLDocument := TXMLDocument.Create(nil);
    AnsiS := GetURLContent(sURL);                  // MCD
    Log('End of AnsiS=' + Copy(AnsiS, Length(AnsiS) - 100, 101));
    Company := Pos('</APLResponse>', AnsiS);
    if Company > 0 then
      AnsiS := Copy(AnsiS, 1, Company + Length('</APLResponse>')-1);
    XMLDocument.XML.Text := AnsiS;                 // MCD
    case DocumentType(XMLDocument.XML.Text) of
      docAPLResponse:
        begin
        Log('URLRequest: Received an APLResponse document');
        // Log('XMLDocument.XML.Text=' + XMLDocument.XML.Text);
        APLResponse := GetAPLResponse(XMLDocument);
        APLFile := APLResponse.APLFiles.APLFile;
        GotAFile := false;
        repeat
          S := APLFile.Text;
          if length(S) > 0 then
            begin
            Filename := FullFilename(APLFile.State);
            S := StringReplace(S,LF,CRLF,[rfReplaceAll]);
            StringToFile(Filename,S);
            Log(format('URLRequest: Created %d byte file for state %s = %s [%s...]',[length(S),APLFile.State,Filename,LeftStr(S,100)]));
            FinalizeOrZipFile(APLFile.State,Filename);  // conditionally zips the file too...
            GotAFile := true;
            end
          else
            Log(format('URLRequest: File data for state %s is empty... not creating file',[APLFile.State]));
          Node := APLFile.NextSibling;
          if Node <> nil then
            APLFile := APLFile.NextSibling as IXMLAPLFileType;
        until Node = nil;
        if GotAFile then
          begin
          Config.LastDownload := FormatDateTime('mm/dd/yyyy',Today);
          Config.OwnerDocument.SaveToFile(CONFIGFILE);
          //Config.OwnerDocument.Options := Config.OwnerDocument.Options + [doAutoSave]; // use this instead of SaveToFile?
          S := XMLDocument.XML.Text;
          Delete(S,1,pos('>',S));          // get rid of <?xml...> section
          S := LeftStr(S,pos('>',S));      // just copy the header part
          Log('APLResponse: ' + S + '...');
          end
        else
          Log('APLResponse: ' + XMLDocument.XML.Text);   // log whole thing if really short so we can see if errors
        end;
      docMtxExportingResult:
        begin
        Log('Received MtxExportingResult: '+XMLDocument.XML.Text);
        S := XMLDocument.XML.Text;
        if pos('Restricted',S) > 0 then
          begin
          Restriction := eWicMtxExportingResultRestriction.GetMtxExportingResult(XMLDocument);
          Log(format('EXPORT of %s %s: %s (%s)',
            [Restriction.Restriction.Export,Restriction.Result,Restriction.Restriction.Name,Restriction.Restriction.Description]));
          end
        else
          begin
          Failure := eWicMtxExportingResultError.GetMtxExportingResult(XMLDocument);
          Log(format('EXPORT %s: %s (%s)',[Failure.Result,Failure.Error.Type_,Failure.Error.Text]));
          end;
        end;
      docUnknown:
        Log('URLRequest: Received Unknown Result: '+XMLDocument.XML.Text);
    end;
    XMLDocument.Active := false;
  except on e: exception do
    Log(format('URLRequest EXCEPTION: %s; URL[%s] RESULT[%s]',[e.Message,sURL,XMLDocument.XML.Text]));
  end;
  SetAPL_Dll_Busy(False);
end;

{$ENDREGION}

{$REGION '<Getting UPD File>'}

function APLDownloadableFile(Filename: string): boolean;
begin
  result := SameText(Filename,'mtx_apl.upd');
end;

function XML2CodeFile(XMLIn: WideString; var XMLError: integer): integer;
const
  COMPRESSION_NONE = 'None';    //jtg todo: remove this when SE makes this an int
  LOGMAX = 30;
var
  XMLCodeFile: TXMLDocument;
  CodeFileResponse: IXMLCodeFileResponseType;
  sContents,sCodeFile: string;
  f: textfile;
  L: integer;
begin
  SetAPL_Dll_Busy(True);
  CoInitialize(nil);         //CoInitializeEx(nil,COINIT_APARTMENTTHREADED);
  try
    result := ERR_OK;
    L := length(XMLIn);
    if L > LOGMAX*2 + 10
      then Log(format('XML2CodeFile: XMLIn (len %d) = [%s ... %s]',[L,LeftStr(XMLin,LOGMAX),RightStr(XMLIn,LOGMAX)]))
      else Log(format('XML2CodeFile: XMLIn (len %d) = [%s]',[L,XMLin]));

    XMLCodeFile := TXMLDocument.Create(nil);  // no need for 'try..finally since we don't explicitly free the interfaced obj.
    XMLCodeFile.XML.Text := XMLIn;

    CodeFileResponse := GetCodeFileResponse(XMLCodeFile);
    Log(format('XML2CodeFile: CodeFileResponse.Filename[%s] Size[%d] Action[%s]',
       [CodeFileResponse.Filename,CodeFileResponse.FileSize,CodeFileResponse.FileAction]));
    try
      XMLError := CodeFileResponse.ErrorCode;
      if XMLError <> 0 then
        begin
        result := ERR_SE_XMLError;
        Log(format('XML2CodeFile: CodeFileResponse.ErrorCode[%d] NOT ZERO - DISCONTINUING FILE CREATION...',[XMLError]));
        exit;
        end;
    except on e: exception do
      begin
      XMLError := 0;
      Log('XML2CodeFile: CodeFileResponse.ErrorCode is NULL; resetting to ZERO');
      end;
    end;

    sContents := B64Decode(CodeFileResponse.FileData);
    sCodeFile := Uncompress(sContents);
    assignfile(f,LocalDir + CodeFileResponse.FileName);

    rewrite(f);
    try
      write(f,sCodeFile);
    finally
      closefile(f);
    end;

    if length(CodeFileResponse.FileData) <> CodeFileResponse.FileSize then
      begin
      Log(format('XML2CodeFile ERROR: FileData length[%d] NOT EQUAL to reported Filesize[%d]',
         [length(CodeFileResponse.FileData),CodeFileResponse.FileSize]));
      result := ERR_WRONG_FILESIZE;  // jtg: should this be superceded by compression err?
      end;
    //XMLCodeFile.Free;    jtg: not necessary to free an interfaced object; will auto-free
    if (SameText(CodeFileResponse.FileAction,ACTION_EXTRACT_ZIP)) or
       (SameText(StrUtils.RightStr(CodeFileResponse.FileName,4),ZIPEXTENSION)) then
      begin
      Log('Unzipping ' + CodeFileResponse.FileName + ' in ' + LocalDir);
      if not UnZip(LocalDir + CodeFileResponse.FileName)
        then Log('Unzip FAILED');
      end;
    XMLCodeFile.Active := false;
  except on e: exception do
    begin
    Log('XML2CodeFile EXCEPTION: Writing CodeFileErrorXML.xml to default dir - ' + e.Message);
    result := ERR_SE_GetCodeFile_EXCEPTION;
    assignfile(f,LocalDir+'CodeFileErrorXML.xml');
    rewrite(f);
    write(f,XMLIn);
    closefile(f);
    end;
  end;
  CoUnInitialize;
  SetAPL_Dll_Busy(False);
end;

function APLGetLatestFiles(WSDLAddr: string; const CompanyNumber: Integer;
           const StoreNumber: Integer; const LaneNumber: Integer;
           var HardError: integer; var HardErrorStr: string; const Files: NeededFileTypeArray): TServerEpsLoginResult;
const
  TITLE = 'APLGetLatestFiles';
var
  i,XMLError: integer;
  xml: string;
  sProxyServer: string;
begin
  Log(format('%s.. looking at a list with %d filenames...',[TITLE,length(Files)]));
  sProxyServer := '';
  HardError := ERR_OK;
  HardErrorStr := '';
  SetAPL_Dll_Busy(True);
  try
    result := lrNone;
    for i := 0 to length(Files) - 1 do
      begin
      Log(format('%s examining file #%d = %s',[TITLE,i,Files[i].File_Name]));
      if APLDownloadableFile(Files[i].File_Name) then
        begin
        Log(format('%s file #%d (%s) is marked DOWNLOADABLE',[TITLE,i,Files[i].File_Name]));
        if LocalFileIsDifferent(Files[i]) then
          begin
          Log(format('%s file #%d (%s) is DIFFERENT from local file.. trying download...',[TITLE,i,Files[i].File_Name]));
          case Files[i].Category of
            lfCodeFiles:
              begin
              Log(format('Calling SE_GetCodeFile: WSDLAddr[%s] Company[%d] Store[%d] Lane[%d] FileName[%s]',[WSDLAddr,CompanyNumber,StoreNumber,LaneNumber,Files[i].File_Name]));
              xml := SE_GetCodeFile(WSDLAddr,sProxyServer,CompanyNumber,StoreNumber,LaneNumber,
                                  SE_AUTHENTICATION_KEY,Files[i].File_Name,HardError,HardErrorStr);
              if length(XML) = 0 then
                Log(format('%s: SE_GetCodeFile returned an EMPTY XML for %s; Err[%d] ErrStr[%s]',[TITLE,Files[i].File_Name,HardError,HardErrorStr]))
              else
                begin
                HardError := XML2CodeFile(xml,XMLError);
                if HardError = ERR_OK then
                  case result of
                    lrNewConfig        : result := lrNewCodeAndConfig;
                    lrNewCodeAndConfig : ;
                  else
                    result := lrNewCode;
                  end  //case
                else
                  Log(format('%s: SE_GetCodeFile Getting %s resulted in HardError %d',[TITLE,Files[i].File_Name,HardError]));
                end;
              end;
          end; //case
          end  // if Different
        else
          Log(format('%s file #%d (%s) is NOT DIFFERENT from local file.. NO download!',[TITLE,i,Files[i].File_Name]));
        end
      else
        Log(format('%s file #%d (%s) is NOT in DOWNLOADABLE list.. NO download!',[TITLE,i,Files[i].File_Name]));
      end;  // for
    if result = lrNone then
      if HardError = ERR_OK
        then result := lrNothingNew
        else result := lrFailure;
  except on e: exception do
    begin
      result := lrFailure;
      Log(format('%s: EXCEPTION[%s] Error[%d] = %S',[TITLE,e.Message,HardError,HardErrorStr]));
    end;
  end;
  SetAPL_Dll_Busy(False);
end;

function OpenEPSLoginResults(XMLIn: WideString; var ErrorCode: integer): NeededFileTypeArray;
var
  XMLLogin: TXMLDocument;
  LoginResponse: IXMLLoginResponseType;
  i: integer;
  FileNum: integer;
  sError: string;
begin
  Log('OpenEPSLoginResults: XML From ServerEPS = '+XMLIn);
  SetLength(result,0);
  if length(trim(XMLIn)) > 0 then
    try
      SetAPL_Dll_Busy(True);
      CoInitialize(nil);
      try
        XMLLogin := TXMLDocument.Create(nil);
        XMLLogin.XML.Text := XMLIn;
        LoginResponse := GetLoginResponse(XMLLogin);
        ErrorCode := LoginResponse.ErrorCode;      // pass ErrorCode back out...
        FileNum := 0;
        SetLength(result,LoginResponse.ConfigurationFiles.Count + LoginResponse.CodeFiles.Count);
        for i := 0 to LoginResponse.ConfigurationFiles.Count-1 do
          begin
          result[FileNum].Category := lfConfigFiles;
          result[FileNum].File_Type := LoginResponse.ConfigurationFiles.ConfigFile[i].Type_;
          result[FileNum].File_Name := lowercase(LoginResponse.ConfigurationFiles.ConfigFile[i].Name); // YHJ-691: lowercase
          result[FileNum].File_LastModified := GetDateTime(LoginResponse.ConfigurationFiles.ConfigFile[i].LastModified);
          result[FileNum].File_Version := '';
          Log(format('OpenEPSLoginResults: ConfigurationFiles - File #%d = %s',[Filenum,result[FileNum].File_Name]));
          inc(FileNum);
          end;
        for i := 0 to LoginResponse.CodeFiles.Count-1 do
          begin
          result[FileNum].Category := lfCodeFiles;
          result[FileNum].File_Name := lowercase(LoginResponse.CodeFiles.CodeFile[i].Name); // YHJ-691: lowercase
          result[FileNum].File_Version := LoginResponse.CodeFiles.CodeFile[i].Version;
          result[FileNum].File_LastModified := OldDate;  // return a really old date
          result[FileNum].File_Type := '';
          Log(format('OpenEPSLoginResults: CodeFiles - File #%d = %s',[Filenum,result[FileNum].File_Name]));
          inc(FileNum);
          end;
      except on e: exception do
        Log(format('OpenEPSLoginResults: EXCEPTION[%s] ErrorCode[%d] = %s',[e.Message,ErrorCode,sError]));
      end;
      XMLLogin.Active := false;
      SetAPL_Dll_Busy(False);
    finally
      CoUnInitialize;
    end
  else
    begin
    SetLength(result,0);  // zero file length
    ErrorCode := ERR_SERVER_EPS_EMPTY_LOGIN_XML;
    Log('The XML input to OpenEPSLoginResults is BLANK!!');
    end;
end;

function ServerEPS_Login(WSDLAddr: string; const CompanyNumber: Integer; const StoreNumber: Integer; const LaneNumber: Integer;
           var HardError: integer; var HardErrorStr: string; var Files: NeededFileTypeArray): TServerEpsLoginResult;
const
  TITLE = 'ServerEPS_Login';
var
  ErrorCode: integer;
  xml: string;
  sProxyServer: string;
begin
  sProxyServer := '';
  Log(format('%s: WSDLAddr[%s] Company[%d] Store[%d] Lane[%d]',[TITLE,WSDLAddr,CompanyNumber,StoreNumber,LaneNumber]));
  HardErrorStr := '';
  SetAPL_Dll_Busy(True);
  try
    result := lrNone;
    HardError := ERR_OK;
    //CoInitialize(nil);
    xml := SE_OpenEpsLogin(WSDLAddr,sProxyServer,CompanyNumber,StoreNumber,LaneNumber,SE_AUTHENTICATION_KEY,HardError,HardErrorStr);
    //CoUninitialize;
    if length(trim(xml)) = 0 then
      begin
      result := lrFailure;
      Log(format('%s: SE_OpenEpsLogin returned an empty XML. URL=[%s] HardError=[%d] HardErrorStr=[%s]',[TITLE,WSDLAddr,HardError,HardErrorStr]));
      end
    else
      begin
      Files := OpenEPSLoginResults(xml,ErrorCode);
      if length(Files) > 0 then
        result := lrWaitForFiles;
      end;
  except on e: exception do
    begin
    result := lrFailure;
    Log(format('%s: EXCEPTION[%s]',[TITLE,e.Message,HardError,HardErrorStr]));
    end;
  end;
  SetAPL_Dll_Busy(False);
end;

function Login: boolean;
const
  TITLE = 'Login';
var
  tempLoginResult,loginResult: TServerEpsLoginResult;
  Files: NeededFileTypeArray;
  sLoginResult: string;
  ErrorCode,Company,Store,Lane: integer;
  url,ErrorStr: string;
begin
  result := false;
  SetAPL_Dll_Busy(True);
  try
    url := RegLookup(SETUPTXT,_ServerEpsServicesHost1);
    if url = '' then
      url := URL_SVC1_PROD;
    Lane := DEFAULT_LANE_NUMBER;

    Company := GetConfigNumber(eCompanyNumber);
    Store := GetConfigNumber(eStoreNumber);

    tempLoginResult := ServerEPS_Login(url+WSDL_ADDR_FILE,Company,Store,Lane,ErrorCode,ErrorStr,Files);
    sLoginResult := GetEnumName(TypeInfo(TServerEpsLoginResult),ord(tempLoginResult));
    Log(format('%s: AFTER Login; Login result = %s; Files Count = %d ',[TITLE,sLoginResult,length(Files)]));
    loginResult := tempLoginResult;
    result := true;
  except on e: exception do
    Log(format('%s EXCEPTION: %s',[TITLE,e.message]));
  end;
  SetAPL_Dll_Busy(False);
end;


function LoginAndGetAPLFiles: boolean;
const
  TITLE = 'LoginAndGetAPLFiles';
var
  tempLoginResult,loginResult: TServerEpsLoginResult;
  Files: NeededFileTypeArray;
  sLoginResult: string;
  ErrorCode,Company,Store: integer;
  ErrorStr: string;
begin
  result := false;
  SetAPL_Dll_Busy(True);
  try
    Company := GetConfigNumber(eCompanyNumber);
    Store := GetConfigNumber(eStoreNumber);

    tempLoginResult := ServerEPS_Login(WSDLURL+WSDL_ADDR_FILE,Company,Store,DEFAULT_LANE_NUMBER,ErrorCode,ErrorStr,Files);
    sLoginResult := GetEnumName(TypeInfo(TServerEpsLoginResult),ord(tempLoginResult));
    Log(format('%s: AFTER Login; Login result = %s; Files Count = %d ',[TITLE,sLoginResult,length(Files)]));
    loginResult := tempLoginResult;

    if tempLoginResult in [lrFailure,lrWaitForLogin] then
      Log(format('%s: NOT DOING GetLatestFiles because previous Login returned ',[TITLE,sLoginResult]))
    else
      begin
      tempLoginResult := APLGetLatestFiles(WSDLURL+WSDL_ADDR_FILE,Company,Store,DEFAULT_LANE_NUMBER,ErrorCode,ErrorStr,Files);
      loginResult := tempLoginResult;
      end;
    Log(format('%s Finished.',[TITLE]));
    result := true;
  except on e: exception do
    Log(format('%s EXCEPTION: %s',[TITLE,e.message]));
  end;
  SetAPL_Dll_Busy(False);
end;

{$ENDREGION}

{$REGION 'Status Message'}

function GetAPLStatusXML: string;
const
  XML_ERROR = '<Lane Number="9999" LaneType="APL Client" UpdateTime="2000-01-01 00:00:01"></Lane>';
var
  CompInfo: TComputerInfo;
  APLStatusXML: IXMLLaneType;
  i: integer;

  procedure SetModule(sName: string);
  var
    XMLModule: IXMLModuleType;
  begin
    XMLModule := APLStatusXML.Modules.Add;
    XMLModule.Name := sName;
    XMLModule.Version := GetVersionString(sName,'FileVersion');
    //APLStatusXML.Modules.Module[i].Version := GetVersionString(sName,'FileVersion');
    //APLStatusXML.Modules.Module[i].Name := sName;
  end;

begin
  result := XML_ERROR;   // default Error XML
  try
    CompInfo := TComputerInfo.Create;
    try
      APLStatusXML := NewLane;
      APLStatusXML.Number := DEFAULT_LANE_NUMBER;
      APLStatusXML.LaneType := 'APL Client';
      APLStatusXML.UpdateTime := FormatDateTime('yyyy-mm-dd hh:nn:ss',Now);
      APLStatusXML.Drive.Letter := CompInfo.letter;
      APLStatusXML.Drive.DriveSize := CompInfo.capacity;
      APLStatusXML.Drive.FreeSpace := CompInfo.FreeSpace;
      APLStatusXML.OSVersion := CompInfo.GetOSString;
      APLStatusXML.IpAddresses.IPAddress := '';  // default;
  	  for i := 0 to pred(CompInfo.IP.Count) do
        APLStatusXML.IpAddresses.IPAddress := CompInfo.IP.Strings[i];
      SetModule(DLLNAME);
      SetModule(GUINAME);
      SetModule(SRVNAME);
      result := APLStatusXML.XML;
      Log('GetAPLStatusXML: '+result);
    finally
      CompInfo.Free;
    end;
  except on e: exception do
    Log('EXCEPTION: GetAPLStatusXMLString ' + e.message);
  end;
end;

function ServerEPS_SendStatusMsg(WSDLAddr: string; const Company,Store,Lane: Integer; const XMLData: string;
           var HardError: integer; var HardErrorStr: string): boolean;
const
  TITLE = 'ServerEPS_SendStatusMsg';
var
  Response: LaneServicesResponse;
  iResponse: integer;
begin
  Log(format('%s: WSDLAddr[%s] Company[%d] Store[%d] Lane[%d] Status[%s]',[TITLE,WSDLAddr,Company,Store,Lane,XMLData]));
  HardErrorStr := '';
  result := false;
  SetAPL_Dll_Busy(True);
  try
    HardError := ERR_OK;
    //CoInitialize(nil);
    Response := SE_LaneService_StatusUpload(WSDLAddr,NEVERPROXY,Company,Store,Lane,XMLData,HardError,HardErrorStr);
    //CoUninitialize;
    if Response = nil
      then iResponse := 0
      else iResponse := Response.Code;
    if HardError = ERR_OK
      then Log(format('%s SUCCESS: Response.Code = %d (%s)',[TITLE,iResponse,LaneServiceResult(iResponse)]))
      else Log(format('%s ERROR:   ErrorCode (%d) ErrorStr(%s)',[TITLE,HardError,HardErrorStr]));
    result := iResponse = LS_SUCCESS;
    SetAPL_Dll_Busy(False);
    MtxSleep(100);                       // CPCLIENTS-10531
  except on e: exception do
    Log(format('%s: EXCEPTION[%s] ErrorCode (%d) ErrorStr(%s)',[TITLE,e.Message,HardError,HardErrorStr]));
  end;
  SetAPL_Dll_Busy(False);
end;

procedure SendStatusMsg(sData: string);
var
  ErrorCode,Company,Store: integer;
  ErrorStr: string;
  OK: boolean;
begin
  try
    Company := GetConfigNumber(eCompanyNumber);
    Store := GetConfigNumber(eStoreNumber);
    ErrorCode := ERR_OK;
    ErrorStr := '';
    OK := ServerEPS_SendStatusMsg(WSDLURL+WSDL_ADDR_LANE,Company,Store,DEFAULT_LANE_NUMBER,sData,ErrorCode,ErrorStr);
  except on e: exception do
    Log(format('SendStatusMsg: EXCEPTION[%s]',[e.Message]));
  end;
end;

{$ENDREGION}

{$REGION 'Journal Upload'}

function StrToMonth(const AMonth: string): Byte;
begin
  Result := Succ(PosInStrArray(Uppercase(AMonth),
    ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'])); {do not localize}
end;

function Lpad(S: string; L: integer; PadChar: char = ' '): string;
begin
  result := S;
  while length(result) < L do
    result := PadChar + result;
end;

function ConvertFromSepsDate(ADate: string): string; // JTG: chg date to ADate, avoid confusion with intrinsic func of same name
var
  month,mm,dd,yyyy: string;
  p: integer;
begin
  try
    ADate := Trim(ADate);

    // discard time
    if UpperCase(ADate[Length(ADate)]) = 'M' then // this has a time stamp
      while ADate[Length(ADate)] <> ' ' do
        ADate := Copy(ADate, 1, Length(ADate)-1);
    ADate := Trim(ADate);

    // get mm [convert mmm to mm (Jan to 01)]
    p := Pos(' ', ADate);
    month := Copy(ADate, 1, p-1);
    mm := IntToStr(StrToMonth(month));
    mm := LPad(mm, 2, '0');

    // get dd
    ADate := Trim(Copy(ADate, p+1, Length(ADate))); // discard month
    p := Pos(' ', ADate);
    dd := Copy(ADate, 1, p-1);
    dd := LPad(dd, 2, '0');

    // get yyyy
    yyyy := Trim(Copy(ADate, p+1, Length(ADate)));  // discard day

    result := yyyy + mm + dd;
  except on e:exception do
    Log('ConvertFromSepsDate: EXCEPTION - '+e.message);
  end;
end;

function ExchangeInfoPerformAction(id: integer; actionCode, ADate: string): boolean;   // performs the LogUpload
const
  STATUS: array[boolean] of string[10] = ('FAIL','SUCCESSFUL');
var
  s,SourceFileName,DestFileName,ErrorStr,sProxyServer: string;
  Request: TFileUploadRequest;
  Response: LaneServicesResponse;
  isTodaysFile: boolean;
  L,ErrorCode,Lane,iResponse: integer;

  procedure SetError(errorCode, errorInfo: string);
  begin
    Request.ErrorCode := errorCode;
    Request.ErrorInfo := errorInfo;
  end;

  procedure CopyTodaysFile;  // also sets Request.ErrorCode if have problems...
  var
    s: string;
  begin
    s := '';
    if FileExists(SourceFileName) then
      begin
      if FileExists(DestFileName) then
        begin
        Log('CopyTodaysFile: trying to copy todays log, but destination file already exists.  Attempting to delete...');
        if not SysUtils.DeleteFile(DestFileName) then
          S := '[CopyTodaysFile: Destination file already exists. FAILED to delete ' + DestFileName + ']';
        end;
      Log('CopyTodaysFile: preparing to copy '+SourceFileName+' to '+DestFileName);
      if not CopyFileTo(SourceFileName, DestFileName) then
        s :=  '[CopyTodaysFile: FAILED to copy '+SourceFileName+' to '+DestFileName+']';
      end;

    if s <> '' then
      begin
      Log('ERROR: '+s);
      SetError(LS_ERROR_UNKNOWN, s);
      end;
  end;

  procedure UploadFile;   // also sets Request.ErrorCode if have problems...
  var
    ZipName: string;
  begin
    ZipName := Zip(DestFilename);
    Request.FileData := FileToDynArray(ZipName);
    L := length(Request.FileData);
    Log(format('UploadFile: DestFilename[%s] --> ZipName[%s] length = %0.n bytes',[DestFilename,ZipName,L*1.0]));
    if L > MAX_UPLOAD_FILESIZE then   // if too big, don't even bother uploading and set error codes and strings
      begin
      ErrorCode := ERR_UPLOADFILESIZE_TOOBIG;
      ErrorStr := format('File %s is %d bytes too big to upload; size = %d but max allowable size = %d',[ZipName,L-MAX_UPLOAD_FILESIZE,L,MAX_UPLOAD_FILESIZE]);
      Log(format('UploadFile ERROR [SE_LSFileUpload for (%s) NOT CALLED - ErrorCode=%d ErrorStr=%s]',[ZipName,ErrorCode,ErrorStr]));
      SetError(LS_ERROR_FILE_IS_TOO_LARGE,'File is too large');
      end
    else
      try
        Log('CoInitialize before SE_LSFileUpload...');
        CoInitialize(nil);         //CoInitializeEx(nil,COINIT_APARTMENTTHREADED);
        Response := SE_LSFileUpload(WSDLURL+WSDL_ADDR_LANE,NEVERPROXY,Request,ErrorCode,ErrorStr);
        Log('CoUninitialize after SE_LSFileUpload...');
        CoUninitialize;

        if ErrorCode = ERR_OK then
          if Response.Code = LS_SUCCESS
            then Log('UploadFile SUCCESSFUL (SE_LSFileUpload for '+ZipName+')')
            else Log(format('WARNING >> Upload physically OK for %s but ServerEPS Response = %d (%s)',[ZipName,Response.Code,LaneServiceResult(Response.Code)]))
          else
            Log(format('ERROR >> %s FAILED to upload; ErrorCode=%d ErrorStr=[%s]',[ZipName,ErrorCode,ErrorStr]));
      except on e: exception do
        Log(format('ERROR >> UploadFile SE_LSFileUpload: EXCEPTION - %s (%d) %s',[e.Message,ErrorCode,ErrorStr]));
      end;
    Log('Deleting '+ZipName);
    SysUtils.DeleteFile(ZipName);
    if isTodaysFile then
      begin
      Log(format('Deleting %s because it is a copy of the current log',[DestFileName]));
      SysUtils.DeleteFile(DestFileName);
      end
    else
      Log(format('NOT deleting %s because it was pre-existing',[DestFileName]));
  end;

  procedure PopulateRequest;
  begin
    FillChar(Request, sizeof(Request), 0);
    Request.Id := id;
    Request.Company := GetConfigNumber(eCompanyNumber);
    Request.Store := GetConfigNumber(eStoreNumber);
    Request.Lane := Lane;
  end;

begin
  try
    Log(format('ExchangeInfoPerformAction: ID(%d) ActionCode(%s) Date(%s)',[id,actionCode,ADate]));
    if actionCode = AC_UPLOAD_JRNL then
      Log('ExchangeInfoPerformAction: actionCode = AC_UPLOAD_JRNL.. Uploading Log')
    else
      begin
      result := true;
      exit;   //  <<<<<<<<<<<<< EXIT HERE  <<<<<<<<<<<<<<<<<<<<<<
      end;

    result := false;       // default is failure if upload attempted
    SetAPL_Dll_Busy(True);
    Lane := DEFAULT_LANE_NUMBER;
    PopulateRequest;       // set all values except Filedata
    sProxyServer := '';

    ADate := ConvertFromSepsDate(ADate);   // use ADate not Date (which is an intrinsic func)
    isTodaysFile := IsToday(DateFromYYYYMMDD(ADate));
    SourceFileName := LocalDir + LOG_FILENAME;
    DestFileName := LocalDir + LOG_NAMEONLY + '-' + ADate + LOG_EXTONLY;
    if isTodaysFile then
      CopyTodaysFile;        // also sets Request.ErrorCode in case of error

    if Request.ErrorCode = '' then   // if no error yet
      if FileExists(destFileName) then
        UploadFile          // also sets Request.ErrorCode in case of error
      else
        begin
        s := format('[ExchangeInfoPerformAction. Cannot upload log file - %s does not exist',[destFileName]);
        Log('ERROR: '+s);
        SetError(LS_ERROR_FILE_DOES_NOT_EXIST, s);
        end;

    if Request.ErrorCode = '' then
      result := true                 // if still no error
    else
      begin
      Log('ERRORS OCCURRED DURING FILE UPLOAD REQUEST. Calling SE_LSFileUpload to SEND error notification msg with the following data');
      Log(format('SE_LSFileUpload SENDING: ErrorCode = %s',[Request.ErrorCode]));
      Log(format('SE_LSFileUpload SENDING: ErrorInfo = %s',[Request.ErrorInfo]));
      Log(format('SE_LSFileUpload SENDING: ActionCode = %s',[Request.ActionCode]));
      Log(format('SE_LSFileUpload SENDING: Company(%d) Store(%d) Lane(%d) Id(%d) and Filedata is BLANK',[Request.Company,Request.Store,Request.Lane,Request.Id]));
      SetLength(Request.Filedata,0);
      Response := SE_LSFileUpload(WSDLURL+WSDL_ADDR_LANE,sProxyServer,Request,ErrorCode,ErrorStr);
      if Response <> nil
        then iResponse := Response.Code
        else iResponse := -1;
      Log(format('Error notification msg %s: Calling SE_LSFileUpload resulted in (ErrorCode=%d) and ServerEPS Response = %d (%s)',
          [STATUS[(ErrorCode = ERR_OK) and (Response.Code = LS_SUCCESS)],ErrorCode,iResponse,LaneServiceResult(Response.Code)]));
      end;
  except on e:exception do
    begin
    result := false;
    Log('ExchangeInfoPerformAction: EXCEPTION - '+e.message);
    end;
  end;
  SetAPL_Dll_Busy(False);
end;

function ExchangeInfo: boolean;
var
  Response: TActionEventArray;
  ErrorCode,i,Company,Store: integer;
  sURL,ErrorStr: string;
begin
  result := false;
  Company := GetConfigNumber(eCompanyNumber);
  Store := GetConfigNumber(eStoreNumber);
  sURL := WSDLURL+WSDL_ADDR_LANE;
  Log(format('ExchangeInfo URL(%s) Company(%d) Store(%d) Lane(%d)',[sURL,Company,Store,DEFAULT_LANE_NUMBER]));
  ErrorCode := ERR_OK;
  ErrorStr := '';
  result := false;
  SetAPL_Dll_Busy(True);
  try
    Response := SE_LSExchangeInfo(sURL,NEVERPROXY,GetLocalIP,'a-b-c-d',Now,TimeZoneBiasInHours,'OK',Company,Store,DEFAULT_LANE_NUMBER,ErrorCode,ErrorStr);
    result := ErrorCode = 0;
    Log(format('ExchangeInfo: SE_LSExchangeInfo response has %d events',[length(Response)]));
    for i := Low(Response) to High(Response) do
      with Response[i] do
        begin
        Log(format('ExchangeInfo: ActionEvent[%d] ID[%d] Code[%s] Arguments[%s]',[i,Id,ActionCode,Arguments]));
        ExchangeInfoPerformAction(Response[i].Id, Response[i].ActionCode, Response[i].Arguments);   // performs Log upload in here, conditionally
        end;
  except on e: exception do
    Log(format('ExchangeInfo EXCEPTION - %s (%d) %s',[e.Message,ErrorCode,ErrorStr]));
  end;
  Log(format('ExchangeInfo Finished. Next ExchangeInfo in %d minutes.',[JOURNAL_UPLOAD_INTERVAL]));
  SetAPL_Dll_Busy(False);
end;

{$ENDREGION}

{$REGION '<Idle Thread>'}

function GetDomainURL: String; //CPCLIENTS-1380
begin
  result := RegLookup(SETUPTXT,_ServerEPSWebPortalHost1);
  if result = '' then
    result := DOMAIN;
end;

procedure SRVRequest;
begin
  URLRequest(GetDomainURL); //CPCLIENTS-1380
end;

procedure IdleThreadDestroy;
begin
  try
    if Assigned(Idle) then
      begin
      Log(format('Idle Thread Destroyed: ID=%d Handle(%d) ',[Idle.ThreadID,Idle.Handle]));
      Idle.Terminate;
      SetEvent(MTXSleepEvent);
      end;
  except on e: exception do
    if pos('handle is invalid',e.Message) > 0
      then Log('IdleThreadDestroy: no freedom in death')
      else Log('IdleThreadDestroy: ' + e.Message);
  end;
end;

procedure IdleThreadCreate;
begin
  try
    if not Assigned(Idle) then
      begin
      Log('IdleThreadCreate');
      Idle := TIdleThread.Create;
      Idle.FreeOnTerminate := true;
      // Idle.Resume;
      Idle.Start;                  // CPCLIENTS-11457
      end;
  except on e: exception do
    Log('IdleThreadCreate ERROR: ' + e.Message);
  end;
end;

constructor TIdleThread.Create;
const
  CREATE_SUSPENDED = true;
begin
  inherited Create(CREATE_SUSPENDED);
end;

destructor TIdleThread.Destroy;
begin
  try
    if (CoInitResult = S_OK) or (CoInitResult = S_FALSE) then
      CoUnInitialize;
    inherited Destroy;
  except on e: exception do
    // nothing, but don't die in NTDLL
  end;
end;

procedure TIdleThread.SetFirstTargetTime(T: TDateTime);
begin
  TargetTime := T;
  ReplaceDate(TargetTime,Today);
  if TargetTime < Now then
    ReplaceDate(TargetTime,Tomorrow);
end;

function TIdleThread.GetTargetTimeStr;
begin
  result := FormatDateTime('yyyy-mm-dd hh:nn:ss',TargetTime);
end;

procedure TIdleThread.SetJournalUploadTime;
begin
  JournalUploadTime := IncMinute(Now,JOURNAL_UPLOAD_INTERVAL);
  Log('Next ServerEPS Exchange Info (& Journal Upload) check scheduled for: ' + FormatDateTime('yyyy-mm-dd hh:nn:ss',JournalUploadTime));
end;

procedure TIdleThread.Execute;
const
  SECOND = 1000;
  SLEEPTIME = SECOND*10;
  MINUTES_OF_DELAY = 30;
var
  Config: IXMLAPLClientConfigurationType;

  procedure GetTargetTime;
  begin
    Log('GetTargetTime');
    Config := LoadAPLClientConfiguration(CONFIGFILE);
    if Config <> nil then
      begin
      SetFirstTargetTime(StrToTime(Config.DownloadTime));
      Log(format('Idle Thread ID(%d) Handle(%d) EXECUTE - Download APL files daily at %s',[ThreadID,Handle,Config.DownloadTime]));
      end
    else
      begin
      SetFirstTargetTime(IncMinute(Now,MINUTES_OF_DELAY));
      Log(format('Idle Thread ID(%d) Handle(%d) EXECUTE - FAILED to read Config - will download APL in %d minutes',[ThreadID,Handle,MINUTES_OF_DELAY]));
      end;
    if SameDate(Today,TargetTime)
      then Log('NEXT APL file download check scheduled for: ' + GetTargetTimeStr)
      else Log('NEXT APL file download check scheduled for TOMORROW at: ' + GetTargetTimeStr);
  end;

begin
  try
    CoInitResult := CoInitialize(nil);
    try
      GetTargetTime;
      SetJournalUploadTime;
      SendStatusMsg(GetAPLStatusXML);
      Log('Initial SendStatusMsg Complete');
      while not Terminated do
      begin
        if Now > TargetTime then
        begin
          Log('Time to check for new APL files...');
          ReplaceDate(TargetTime,Tomorrow);
          Log('Initiating APL Request...');
          SRVRequest;
          GetUPD;
          if not Terminated then
            SendStatusMsg(GetAPLStatusXML);
          Log('Next download check scheduled for: ' + FormatDateTime('yyyy-mm-dd hh:nn:ss',TargetTime));
        end;
        if Now > JournalUploadTime then
        begin
          Log('Time to check if ServerEPS wants a journal...');
          if not Terminated then
          begin
            ExchangeInfo;
            SetJournalUploadTime;
          end;
        end;
        if not Terminated then
          MtxSleep(SLEEPTIME);       // CPCLIENTS-10531
      end;
      Log('Idle Thread TERMINATED...');
    finally
      if (CoInitResult = S_OK) or (CoInitResult = S_FALSE) then
        CoUnInitialize;
    end;
    except on e: exception do
    begin
      Log(format('EXCEPTION TIdleThread.Execute - ThreadID(%d) -> %s',[ThreadID,e.message]));
      Terminate;
    end;
  end;
end;

{$ENDREGION}

{$REGION '<Gui Thread>'}

procedure GuiThreadCreate;
begin
  try
    if not Assigned(GuiThread) then
      begin
      Log('GuiThreadCreate');
      GuiThread := TGuiThread.Create;
      GuiThread.FreeOnTerminate := true;
      GuiThread.Priority := tpIdle;
      //GuiThread.Resume;
      end;
  except on e: exception do
    Log('GuiThreadCreate ERROR: ' + e.Message);
  end;
end;

procedure GuiThreadResume;
begin
  try
    if not Assigned(GuiThread) then
      GuiThreadCreate;
    GuiThread.Resume;
  except on e: exception do
    Log('GuiThreadResume ERROR: ' + e.Message);
  end;
end;

procedure GuiThreadDestroy;
begin
  try
    if Assigned(GuiThread) then
      begin
      Log(format('GuiThread Thread (ID=%d) Handle(%d) Destroyed',[GuiThread.ThreadID,GuiThread.Handle]));
      GuiThread.Terminate;
      GuiThread.Free;
      end;
  except on e: exception do
    Log('GuiThreadDestroy ERROR: ' + e.Message);
  end;
end;

constructor TGuiThread.Create;
const
  CREATE_SUSPENDED = true;
begin
  inherited Create(CREATE_SUSPENDED);
end;

procedure TGuiThread.Execute;
const
  SECOND = 1000;
var
  Config: IXMLAPLClientConfigurationType;
begin
  try
    CoInitialize(nil);         //CoInitializeEx(nil,COINIT_APARTMENTTHREADED);
    while not Terminated do
      begin
      Config := LoadAPLClientConfiguration(CONFIGFILE);
      Log(format('GuiThread Thread ID(%d) Handle(%d) EXECUTE Download APL NOW',[ThreadID,Handle]));
      SRVRequest;
      GuiThread.Suspend;
      MtxSleep(SECOND*5);                   // CPCLIENTS-10531
      end;
    CoUnInitialize;
  except on e: exception do
    begin
    Log(format('EXCEPTION TGuiThread.Execute - ThreadID(%d) -> %s',[ThreadID,e.message]));
    Terminate;
    end;
  end;
end;

procedure URLRequestThreaded;
begin
  GuiThreadResume;
end;

{$ENDREGION}

{$REGION '<Exported Functions>'}

function InitAPLClient: boolean;
begin
  // MTXSleepEvent := TEvent.Create(Nil, False, False, 'Sleep');         // CPCLIENTS-10531
  MtxSleepEvent := CreateEvent(nil, True, False, nil);
  result := false;
  SetAPL_Dll_Busy(False);                                              // CPCLIE(1053);
  SetLocalDirAndLogName;
  Log('>>>>>>                ');
  Log('      >>>>>>>         ');
  Log('             >>>>>>>>>');
  Log(format('                      >>>>>>  InitAPLClient: %s Version = %s',[DLLNAME,GetVersionString(LocalDir + DLLNAME,'FileVersion')]));
  Log('             >>>>>>>>>');
  Log('      >>>>>>>         ');
  Log('>>>>>>                ');
  try
    IdleThreadCreate;
    result := true;
  except on e: exception do
    Log('InitAPLClient EXCEPTION - '+e.Message);
  end;
end;

function QuitAPLClient: boolean;
begin
  Log('<<<<<<                ');
  Log('      <<<<<<<         ');
  Log('             <<<<<<<<<');
  Log('                       <<<<<<<<  QuitAPLClient  <<<<<<<<<<');
  Log('             <<<<<<<<<');
  Log('      <<<<<<<         ');
  Log('<<<<<<                ');
  result := false;
  try
    IdleThreadDestroy;
    Log('QuitAPLClient: Main Thread Terminated OK');
    CloseHandle(MtxSleepEvent);
    result := true;
  except on e: exception do
    Log('QuitAPLClient EXCEPTION - ' + e.message);
  end;
end;

function GetUPD: boolean;
begin
  Log('GetUPD >>> ');
  SetLocalDirAndLogName;
  result := LoginAndGetAPLFiles;
end;

{$ENDREGION}

procedure TDM.DataModuleCreate(Sender: TObject);
begin
  Log('DataModuleCreated');
end;

{ TDM }

end.
