unit uAPLGUI;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, Buttons, {LMDMaskEdit, LMDCustomMaskEdit,} System.SyncObjs, {
  LMDCustomExtSpinEdit, LMDSpinEdit, LMDControl, LMDBaseControl,
  LMDBaseGraphicControl, LMDBaseLabel, LMDCustomLabel, LMDLabel, LMDCustomEdit,
  LMDEdit, LMDCustomControl, LMDCustomPanel, LMDCustomBevelPanel, LMDBaseEdit,
  LMDCustomMemo, LMDMemo, Xml.xmldom, Xml.XMLIntf, Vcl.ImgList, Vcl.ExtCtrls, Xml.Win.msxmldom, Xml.XMLDoc, Vcl.Mask, Vcl.OleCtrls, SHDocVw,} xmldom, XMLIntf, OleCtrls, SHDocVw, msxmldom, XMLDoc,
  IdBaseComponent, IdComponent, IdTCPConnection, IdTCPClient, {LMDCustomButton,
  {LMDButton, LMDCustomParentPanel, LMDCustomGroupBox, LMDGroupBox, ExtCtrls,
  SyncObjs, GIFImg, ImgList, LMDGraphicControl, LMDBaseImage, LMDCustomLImage,
  LMDLImage, LMDButtonControl, LMDCustomCheckBox, LMDCheckBox,} Vcl.Mask, Vcl.ImgList, Vcl.ExtCtrls, System.ImageList{, LMDControl, LMDCustomControl, LMDCustomPanel,
  LMDCustomBevelPanel, LMDCustomParentPanel, LMDCustomGroupBox, LMDGroupBox};    //critical section


type
  enumImageType = (imgConfig,imgIdle,imgRefreshing,imgGettingUPD,imgGettingURL);

  TfrmMain = class(TForm)
    boxTestRequest: TGroupBox;
    edtDomain: TEdit;
    lblDomain: TLabel;
    boxConfiguration: TGroupBox;
    lblConfigStoreNumber: TLabel;
    lblConfigCompanyNumber: TLabel;
    lblConfigDownloadTime: TLabel;
    edtState1: TEdit;
    lblState: TLabel;
    lblFilename: TLabel;
    lblPath: TLabel;
    edtState2: TEdit;
    edtState3: TEdit;
    edtState4: TEdit;
    edtState5: TEdit;
    edtPath1: TEdit;
    edtFilename1: TEdit;
    edtPath2: TEdit;
    edtPath3: TEdit;
    edtPath4: TEdit;
    edtPath5: TEdit;
    edtFilename2: TEdit;
    edtFilename3: TEdit;
    edtFilename4: TEdit;
    edtFilename5: TEdit;
    btnSave: TButton;
    btnTestRequest: TButton;
    edtDownloadTime: TEdit;
    XMLConfig: TXMLDocument;
    lblUsername: TLabel;
    edtUsername: TEdit;
    lblPassword: TLabel;
    edtPassword: TEdit;
    LMDLabel4: TLabel;
    edtLastDownload: TMaskEdit;
    lblResponse: TLabel;
    Memo: TMemo;
    LMDLabel1: TLabel;
    WebBrowser1: TWebBrowser;
    btnDLL: TButton;
    btnServiceStartStop: TButton;
    lblServiceRunning: TLabel;
    ServiceStatus: TTimer;
    btnGetUPD: TButton;
    ImageList: TImageList;
    Image1: TImage;
    chkZip1: TCheckBox;
    lblZip: TLabel;
    chkZip2: TCheckBox;
    chkZip3: TCheckBox;
    chkZip4: TCheckBox;
    chkZip5: TCheckBox;
    lblCompanyNumber: TLabel;
    lblStoreNumber: TLabel;
    btnDownloadNow: TButton;
    procedure btnMakeRequestClick(Sender: TObject);
    procedure btnTestRequestClick(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure btnSaveClick(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure btnDLLClick(Sender: TObject);
    procedure btnServiceStartStopClick(Sender: TObject);
    procedure ServiceStatusTimer(Sender: TObject);
    procedure btnGetUPDClick(Sender: TObject);
    procedure edtDownloadTimeEnter(Sender: TObject);
    procedure chkZip1Enter(Sender: TObject);
    procedure btnDownloadNowClick(Sender: TObject);
  private
    { Private declarations }
    Lock: TCriticalSection;
    UserIsEditing: boolean;
    procedure CreateLock;
    procedure AcquireLock;
    procedure DestroyLock;
    procedure ReleaseLock;
    procedure Request;
    procedure SaveConfiguration;
    procedure LoadConfigurationFromFile;
    procedure SetImage(Image: enumImageType);
    procedure UserEditBegin;
    procedure SetLabelSvcStopping;
    procedure SetLabelSvcStarting;
  public
    { Public declarations }
    procedure SetButtonLabels;
  end;

var
  frmMain: TfrmMain;
  ServiceRunning: boolean;

implementation

{$R *.dfm}

uses
  WinInet, WinSvc, DateUtils, eWicAplxptSuccessfulExport, StrUtils, uAPL, APLClientConfiguration;

const
  sOK: array[boolean] of string[4] = ('FAIL','OK');
  SERVICENAME = 'APLClientService';
  START_STR = 'Service Start';
  STOP_STR = 'Service Stop';
  LOCAL_MACHINE = '';


{$REGION '<Utility Functions>'}

procedure TfrmMain.CreateLock;
begin
  if Lock = nil then Lock := TCriticalSection.Create;
end;

procedure TfrmMain.AcquireLock;
begin
  if Lock = nil then Lock := TCriticalSection.Create;
  Lock.Acquire;
end;

procedure TfrmMain.DestroyLock;
begin
  if Lock <> nil then FreeAndNil(Lock);
end;

procedure TfrmMain.ReleaseLock;
begin
  if Lock <> nil then Lock.Release;
end;

procedure Log(Msg: string);
begin
  uAPL.Log('GUI '+Msg);
end;

{$ENDREGION}

procedure TfrmMain.SetImage(Image: enumImageType);
begin
  Image1.Picture.Bitmap := nil;
  ImageList.GetBitmap(ord(Image),Image1.Picture.Bitmap);
end;

procedure TfrmMain.SaveConfiguration;
var
  Config: IXMLAPLClientConfigurationType;
  i: integer;
  Comp: TComponent;
  S: string;
begin
  AcquireLock;
  Config := LoadAPLClientConfiguration(CONFIGFILE);  //load the disk version to get structure
  //XMLConfig.LoadFromFile(CONFIGFILE);
  //Config := GetAPLClientConfiguration(XMLConfig);

  Config.DownloadTime := edtDownloadTime.Text;
  Config.Username := edtUsername.Text;
  Config.Password := StringEncrypted(edtPassword.Text);
  Config.LastDownload := edtLastDownload.Text;

  for i := 0 to MAXSTATES -1 do
    begin
    Comp := FindComponent(format('edtState%d',[i+1]));
    Config.StatePaths.Path[i].StateCode := trim(TEdit(Comp).Text);

    Comp := FindComponent(format('edtPath%d',[i+1]));
    Config.StatePaths.Path[i].Text := trim(TEdit(Comp).Text);

    Comp := FindComponent(format('edtFilename%d',[i+1]));
    Config.StatePaths.Path[i].Filename := trim(TEdit(Comp).Text);

    Comp := FindComponent(format('chkZip%d',[i+1]));
    if TCheckBox(Comp).Checked
      then Config.StatePaths.Path[i].Zipped := 'Y'
      else Config.StatePaths.Path[i].Zipped := '';
    end;
  Config.OwnerDocument.SaveToFile(CONFIGFILE);
  UserIsEditing := false;
  SetImage(imgIdle);

  //XMLConfig.XML.Text := Config.XML;
  //XMLConfig.SaveToFile(CONFIGFILE);
  ReleaseLock;
  MessageDlg('You must STOP and then restart the APL Service '+#13#10+'for the configuration changes to take effect.', mtInformation, [mbOK], 0);
end;

procedure TfrmMain.btnSaveClick(Sender: TObject);
begin
  Log('User has saved the configuration');
  SaveConfiguration;
end;

{$REGION '<Service Control>'}

function GetServiceStatus(ServiceName: string): integer;
var
  scm, svc: SC_HANDLE;
  ServiceStatus: TServiceStatus;
begin
  result := SERVICE_STOPPED;
  try
    scm := OpenSCManager(nil, nil, SC_MANAGER_QUERY_LOCK_STATUS);
    try
      if scm > 0 then
        begin
        svc := OpenService(scm, pchar(serviceName), SERVICE_QUERY_STATUS);
        try
          if svc > 0 then
            if QueryServiceStatus(svc, ServiceStatus) then
              result := ServiceStatus.dwCurrentState;
        finally
          CloseServiceHandle(svc);
        end;
        end;
    finally
      CloseServiceHandle(scm);
    end;
  except on e: exception do
    Log('GetServiceStatus exception: ' + e.message);
  end;
end;

function APLServiceRunning: boolean;
var
  scm,svc: SC_HANDLE;
  ServiceStatus: TServiceStatus;
begin
  scm := OpenSCManager(nil, nil, SERVICE_QUERY_STATUS);
  if scm > 0 then
    begin
    result := false;
    svc := OpenService(scm,SERVICENAME,SERVICE_QUERY_STATUS);
    if svc > 0 then
      begin
      if QueryServiceStatus(svc, ServiceStatus) then
        result := ServiceStatus.dwCurrentState = SERVICE_RUNNING;
      CloseServiceHandle(svc);
      end
    else
      Log('APLServiceRunning: ERROR - Windows did not return a service handle from OpenService');
    CloseServiceHandle(scm);
    end
  else
    Log('APLServiceRunning: ERROR in getting SERVICE_QUERY_STATUS handle from Windows SCManager');
end;

const
  sTF: array[boolean] of string = ('FALSE','TRUE');

function ServiceStart(sMachine,sService: string): boolean;   // return TRUE if successful
// sMachine: machine name, ie: \SERVER   empty = local machine
// sService: service name, ie: APLClientService
var
  schm,                       // service control manager handle
  schs   : SC_Handle;         // service handle
  ss     : TServiceStatus;    // service status
  psTemp : PChar;             // temp char pointer
  dwChkP : DWord;             // check point
begin
  Log('ServiceStart (APL Service) from GUI');
  ss.dwCurrentState := 0;
  schm := OpenSCManager(PChar(sMachine), nil, SC_MANAGER_CONNECT);  // connect to the service control manager

  if schm <> 0 then    // if successful...   // CPCLIENTS-10651
  begin   // open a handle to the specified service
    schs := OpenService(schm, PChar(sService), SERVICE_START or SERVICE_QUERY_STATUS); // we want to start the service and query service status
    if schs <> 0 then     // if successful..   // CPCLIENTS-10651
    begin
      psTemp := Nil;
      if StartService(schs,0,psTemp) then
        if QueryServiceStatus(schs,ss) then       // check status
          while SERVICE_RUNNING <> ss.dwCurrentState do
            begin
            // dwCheckPoint contains a value that the service increments periodically to report its progress during a lengthy operation. save current value
            dwChkP := ss.dwCheckPoint;

            // wait a bit before checking status again
            // dwWaitHint is the estimated amount of time the calling program should wait before calling QueryServiceStatus() again
            sleep(ss.dwWaitHint);
            if not QueryServiceStatus(schs,ss) then
              break;   // couldn't check status break from the loop
            if ss.dwCheckPoint < dwChkP then // QueryServiceStatus didn't increment dwCheckPoint as it should have. avoid an infinite loop by breaking
              break;
            end;
      CloseServiceHandle(schs);
    end;
    CloseServiceHandle(schm);
  end;
  result := SERVICE_RUNNING = ss.dwCurrentState;     // return TRUE if the service status is running
  Log(format('ServiceStart (APL Service) END - SERVICE_RUNNING is %s (%d)',[sTF[result],ss.dwCurrentState]));
end;

procedure StartAPLService;
begin
  Log('|||||||||||||||||||||||||||||||||||||||||||||');
  Log('Start APL Service from GUI');
  Log('|||||||||||||||||||||||||||||||||||||||||||||');
  frmMain.ServiceStatus.Enabled := false;
  ServiceRunning := ServiceStart(LOCAL_MACHINE,SERVICENAME);
  frmMain.ServiceStatus.Enabled := true;
end;

function ServiceControlStr(i: integer): string;
const
  SC = 'SERVICE_CONTROL_';
begin
  case i of
    SERVICE_CONTROL_STOP:         result := 'STOP';
    SERVICE_CONTROL_PAUSE:        result := 'PAUSE';
    SERVICE_CONTROL_CONTINUE:     result := 'CONTINUE';
    SERVICE_CONTROL_INTERROGATE:  result := 'INTERROGATE';
    SERVICE_CONTROL_SHUTDOWN:     result := 'SHUTDOWN';
    else                          result := '???';
  end;
  result := SC + result;
end;

function ServiceStop(sMachine,sService: string): boolean;    // return TRUE if successful
// sMachine = machine name, ie: \SERVER  ; empty = local machine
// sService = service name, ie: Alerter
const
  TITLE = 'ServiceStop';
  MAXTIME = 30;
var
  schm,Svc: SC_Handle; // service control manager handle and service handle
  ss: TServiceStatus;   // service status
  dwChkP: DWord;        // check point
  OK: boolean;
  TZero,TLap: TDateTime;
begin
  Log(TITLE + ' (APL Service) from GUI');
  schm := OpenSCManager(PChar(sMachine),Nil,SC_MANAGER_CONNECT);   // connect to the service control manager
  if schm > 0 then                                   // if successful...
    begin
    Svc := OpenService(schm,PChar(sService),SERVICE_STOP or SERVICE_QUERY_STATUS);    // open a handle to the specified service
      // we want to stop the service and query service status
    if Svc > 0 then           // if successful...
      begin
      OK := ControlService(Svc,SERVICE_CONTROL_STOP,ss);
      Log(TITLE + ': ControlService '+sOK[OK]);
      if OK then
        if QueryServiceStatus(Svc,ss) then      // check status
          begin
          TZero := Now;
          TLap := Now;
          while SERVICE_STOPPED <> ss.dwCurrentState do
            begin
            dwChkP := ss.dwCheckPoint;  // dwCheckPoint contains a value that the service increments periodically to report its progress during a lengthy operation. save current value
            // wait a bit before checking status again
            // dwWaitHint is the estimated amount of time the calling program should wait before calling QueryServiceStatus() again
            sleep(ss.dwWaitHint);
            Application.ProcessMessages;
            if not QueryServiceStatus(Svc,ss) then
              break;    // couldn't check status break from the loop
            if ss.dwCheckPoint < dwChkP then
              break;     // didn't increment dwCheckPoint as it should have. avoid an infinite loop by breaking
            if SecondsBetween(Now,TZero) > MAXTIME then  // just stop after MAXTIME seconds
              begin
              Log(format('%s (APL Service) loop stop: exceeded %d seconds',[TITLE,MAXTIME]));
              break;
              end;
            if SecondSpan(Now,tLap) > 2.0 then           // log every second of failing to stop...
              begin
              Log(format('%s: Service still stopping CurrentState = %d = %s (it needs to be SERVICE_CONTROL_STOP = %d)',
                      [TITLE,ss.dwCurrentState,ServiceControlStr(ss.dwCurrentState),SERVICE_CONTROL_STOP]));
              tLap := Now;
              end;
            end;
          end;
      CloseServiceHandle(Svc);
      end;
    CloseServiceHandle(schm);
  end;
  result := SERVICE_STOPPED = ss.dwCurrentState;  // return TRUE if the service status is stopped
  Log(format('%s (APL Service) END - SERVICE_STOPPED is %s CurrentState = %d = %s',[TITLE,sTF[result],ss.dwCurrentState,ServiceControlStr(ss.dwCurrentState)]));
end;

procedure StopAPLService;
const
  TITLE = 'StopAPLService: ';
var
  scm,svc: SC_HANDLE;
  ServiceStatus: TServiceStatus;
  OK: boolean;
begin
  Log('|||||||||||||||||||||||||||||||||||||||||||||');
  Log('Stop APL Service from GUI');
  Log('|||||||||||||||||||||||||||||||||||||||||||||');

  frmMain.ServiceStatus.Enabled := false;
  ServiceRunning := ServiceStop(LOCAL_MACHINE,SERVICENAME);
  frmMain.ServiceStatus.Enabled := true;

  Log('*********************************************');
  Log('APL Service IS STOPPED');
  Log('*********************************************');

  {
  exit;   // <<<<<<<<<<<<<<<<<<<<<<<<<<<<<

  scm := OpenSCManager(nil, nil, SERVICE_STOP);
  if scm > 0 then
    begin
    svc := OpenService(scm, SERVICENAME, SERVICE_ALL_ACCESS);
    if svc > 0 then
      begin
      OK := ControlService(svc, SERVICE_CONTROL_STOP, ServiceStatus);
      Log(TITLE+'ControlService '+sOK[OK]);
      CloseServiceHandle(svc);
      Log(TITLE+'CloseServiceHandle '+sOK[OK]);
      frmMain.btnServiceStartStop.Caption := START_STR;
      sleep(250);
      end
    else
      Log(TITLE+'Unable to OpenService so we are unable to control the service');
    CloseServiceHandle(scm);
    end
  else
    Log(TITLE+'Unable to Open SCM Manager so we are unable to control the service');
  }
end;

{$ENDREGION}

procedure TfrmMain.ServiceStatusTimer(Sender: TObject);
begin
  if not UserIsEditing then
    LoadConfigurationFromFile;
  btnSave.Enabled := UserIsEditing;
  SetButtonLabels;
end;

procedure TfrmMain.SetLabelSvcStopping;
begin
  lblServiceRunning.Caption := 'SERVICE IS STOPPING';
  lblServiceRunning.Font.Color := clFuchsia;
  // lblServiceRunning.Twinkle := true;
end;

procedure TfrmMain.SetLabelSvcStarting;
begin
  lblServiceRunning.Caption := 'SERVICE IS STARTING';
  lblServiceRunning.Font.Color := clOlive;
  // lblServiceRunning.Twinkle := true;
end;

procedure TfrmMain.SetButtonLabels;
const
  sButtonCaption: array[boolean] of string[20] = (START_STR,STOP_STR);
  sLabelCaption: array[boolean] of string[20] = ('SERVICE IS STOPPED','SERVICE IS RUNNING');
  sFontColor: array[boolean] of TColor = (clRed,clGreen);
begin
  ServiceRunning := APLServiceRunning;
  btnServiceStartStop.Caption := sButtonCaption[ServiceRunning];
  lblServiceRunning.Caption := sLabelCaption[ServiceRunning];
  lblServiceRunning.Font.Color := sFontColor[ServiceRunning];
  // lblServiceRunning.Twinkle := false;
end;

procedure TfrmMain.btnServiceStartStopClick(Sender: TObject);
begin
  UserIsEditing := false;
  if ServiceRunning then
    begin
    SetLabelSvcStopping;
    StopAPLService;
    end
  else
    begin
    SetLabelSvcStarting;
    StartAPLService;
    end;
  sleep(1000);
  SetButtonLabels;
end;

procedure TfrmMain.btnTestRequestClick(Sender: TObject);
begin
  Request;
end;

procedure TfrmMain.chkZip1Enter(Sender: TObject);
begin
  UserEditBegin;
end;

procedure TfrmMain.UserEditBegin;
begin
  UserIsEditing := true;
  btnSave.Enabled := UserIsEditing;
  SetImage(imgConfig);
  Log('User is editing the configuration');
end;

procedure TfrmMain.edtDownloadTimeEnter(Sender: TObject);
begin
  UserEditBegin;
end;

procedure TfrmMain.LoadConfigurationFromFile;
var
  Config: IXMLAPLClientConfigurationType;
  i: integer;
  Comp: TComponent;
begin
  try
    AcquireLock;
    SetImage(imgRefreshing);
    CreateConfigIfMissing;
    Config := LoadAPLClientConfiguration(CONFIGFILE);
    lblCompanyNumber.Caption := format('%d',[GetConfigNumber(eCompanyNumber)]);
    lblStoreNumber.Caption := format('%d',[GetConfigNumber(eStoreNumber)]);
    edtDownloadTime.Text := Config.DownloadTime;
    edtUsername.Text := Config.Username;
    if String(Config.Password).ToUpper <> '1ArgoFind'.ToUpper then
      edtPassword.Text := StringClear(Config.Password)
    else
      edtPassword.Text := Config.Password;
    edtLastDownload.Text := Config.LastDownload;

    for i := 0 to Config.StatePaths.Count - 1 do
      begin
      Comp := FindComponent(format('edtState%d',[i+1]));
      TEdit(Comp).Text := Config.StatePaths.Path[i].StateCode;
      Comp := FindComponent(format('edtPath%d',[i+1]));
      TEdit(Comp).Text := Config.StatePaths.Path[i].Text;
      Comp := FindComponent(format('edtFilename%d',[i+1]));
      TEdit(Comp).Text := Config.StatePaths.Path[i].Filename;
      Comp := FindComponent(format('chkZip%d',[i+1]));
      TCheckBox(Comp).Checked := SameText(Config.StatePaths.Path[i].Zipped,'Y');
      end;

    ServiceRunning := APLServiceRunning;
    SetButtonLabels;
    SetImage(imgIdle);
    ReleaseLock;
  except on e: exception do
    Log('LoadConfigurationFromFile EXCEPTION: '+e.message);
  end;
end;

procedure TfrmMain.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  Log('APLConfiguration GUI Shutting down <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
  DestroyLock;
end;

procedure TfrmMain.FormShow(Sender: TObject);
var
  Filename: string;
begin
  SetLocalDirAndLogName;
  CreateLock;
  Filename := ParamStr(0);
  Log('||||||||||||||||||||||');
  Log('||||||||||||||||||||||');
  Log(format('APL Configuration GUI started (%s Version %s)',[Filename,GetVersionString(Filename,'FileVersion')]));
  Log('||||||||||||||||||||||');
  Log('||||||||||||||||||||||');
  UserIsEditing := false;
  SetImage(imgIdle);
  LoadConfigurationFromFile;
end;

procedure TfrmMain.Request;
var
  sURL: string;
  Company,Store: integer;
begin
  UserIsEditing := false;
  SetImage(imgGettingURL);
  URLRequest(edtDomain.Text);
  Company := GetConfigNumber(eCompanyNumber);
  Store := GetConfigNumber(eStoreNumber);
  sURL := URL(edtDomain.Text,Company,Store,edtUsername.Text,edtPassword.Text,'APL',edtLastDownload.Text);
  WebBrowser1.Navigate(sURL);
  Memo.Text := GetURLContent(sURL);
  SetImage(imgIdle);
end;

procedure TfrmMain.btnGetUPDClick(Sender: TObject);
begin
  UserIsEditing := false;
  SetImage(imgGettingUPD);
  btnGetUPD.Caption := 'Getting UPD';
  btnGetUPD.Font.Color := clRed;
  Application.ProcessMessages;
  GetUPD;
  btnGetUPD.Caption := 'Finished';
  btnGetUPD.Font.Color := clGreen;
  Application.ProcessMessages;
  sleep(2000);
  btnGetUPD.Caption := 'GetUPD';
  btnGetUPD.Font.Color := clBlack;
end;

procedure TfrmMain.btnMakeRequestClick(Sender: TObject);
begin
  Log('APLConfiguration GUI initiated request');
  Request;
end;

{$REGION '<DLL Load/Unload Functions>'}

type
  TInitAPLClient = function: boolean; stdcall;
  TQuitAPLClient = function: boolean; stdcall;

var
  APL_LHandle: THandle = 0;
  LogFilename,DefaultDir: string;
  InitAPLEntry: TInitAPLClient;
  QuitAPLEntry: TQuitAPLClient;

function LoadAPLClientDLL: boolean;
const
  APLDLL = 'mtx_apl.dll';
const
  TITLE = 'LoadAPLClientDLL: ';
var
  S: string;
  Err: integer;
begin
  result := false;
  try
    Err := GetLastError;      //just to clear it
    DefaultDir := ExtractFilePath(paramstr(0));
    Log(TITLE+'Try to load ' + DefaultDir + APLDLL);
    if APL_LHandle = 0 then
      begin
      APL_LHandle := LoadLibrary(PChar(APLDLL));
      if APL_LHandle <> 0 then
        begin
        Log(TITLE +'LoadLibrary OK');
        @InitAPLEntry := GetProcAddress(APL_LHandle, 'InitAPL');
        Log(TITLE+'InitAPLClient Function Call Address');
        @QuitAPLEntry := GetProcAddress(APL_LHandle, 'QuitAPL');
        Log(TITLE+'QuitAPLClient Function Call Address');
        InitAPLEntry;
        end
      else
        Log(format('LoadAPLClientDLL: ****ERROR: LoadLibrary (Error %d) could not load ',[GetLastError,APLDLL]));
      end
    else
      Log('LoadAPLClientDLL: Attempted to LoadLibrary, but handle not zero (already loaded)');
  except on e: exception do
    Log('LoadAPLClientDLL: EXCEPTION - ' + e.message);
  end;
end;

procedure UnloadAPLClientDLL;
const
  TITLE = 'UnloadAPLClientDLL: ';
begin
  if APL_LHandle <> 0 then
    begin
    QuitAPLEntry;
    Log(TITLE+'Unloading the APL DLL');
    if FreeLibrary(APL_LHandle) then
      begin
      Log(TITLE+'FreeLibrary SUCCESS - APL DLL Unloaded.');
      APL_LHandle := 0;
      end
    else
      Log(TITLE+'ERROR - FreeLibrary FAILED!');
    end;
end;

{$ENDREGION}

procedure TfrmMain.btnDLLClick(Sender: TObject);
begin
  UserIsEditing := false;
  LoadAPLClientDLL;
end;

procedure TfrmMain.btnDownloadNowClick(Sender: TObject);
begin
  URLRequestThreaded;
end;

end.

(*
function GetURLAsString(aURL: string): string;
var
  lHTTP: TIdHTTP;
  lStream: TStringStream;
begin
  lHTTP := TIdHTTP.Create(nil);
  lStream := TStringStream.Create(Result);
  try
    lHTTP.Get(aURL, lStream);
    lStream.Position := 0;
    Result := lStream.ReadString(lStream.Size);
  finally
    FreeAndNil(lHTTP);
    FreeAndNil(lStream);
  end;
end;
*)
