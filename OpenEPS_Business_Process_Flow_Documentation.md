# OpenEPS: Business Process Flow Documentation

## Introduction

This guide explains how business processes flow through the OpenEPS system and how they appear in logs. Understanding these flows is essential for interpreting system behavior and troubleshooting issues.

**Key Insight**: Every business transaction follows predictable patterns that generate specific log sequences. Recognizing these patterns helps you quickly identify where problems occur.

---

## Core Business Transaction Types

### 1. **Purchase Transaction** (Most Common)

**Business Purpose**: Customer pays for goods/services with electronic payment.

**Supported Payment Types**:
- Credit cards (Visa, MasterCard, Amex, Discover)
- Debit cards (PIN-based and signature-based)
- EBT Food Stamps (SNAP benefits)
- EBT Cash Benefits (TANF benefits)
- Gift cards and store cards
- Fleet cards (for commercial vehicles)
- Phone cards and prepaid cards

**Log Flow Pattern**:
```
[OpenEPS] MTX_POS_SET_PurchaseAmount: 4567 (cents = $45.67)
[OpenEPS] MTX_POS_SET_TenderType: 2 (Credit)
[OpenEPS] MTX_POS_SendTransaction: Starting
[OpenEPS] Transaction State: trsNone → trsTenderType
[OpenEPS] Transaction State: trsTenderType → trsScatReady
[TERM] Requesting card data from customer
[TERM] Card read successful: ****1234
[OpenEPS] Transaction State: trsScatReady → trsValidateData
[OpenEPS] Card validation passed
[OpenEPS] Transaction State: trsValidateData → trsSent
[HOST] Routing to Chase host
[HOST] ISO 8583 message sent
[HOST] Response received: 00 (Approved)
[OpenEPS] Transaction State: trsSent → trsApproved
[OpenEPS] MTX_POS_SET_ResponseCode: 00
[OpenEPS] MTX_POS_SET_AuthorizationNumber: 123456
[OpenEPS] Receipt data prepared
[OpenEPS] Transaction completed successfully
```

### 2. **Return Transaction**

**Business Purpose**: Customer returns merchandise and receives refund.

**Key Differences from Purchase**:
- Negative amount processing
- Original transaction lookup (sometimes required)
- Different authorization requirements
- Refund limits may apply

**Log Flow Pattern**:
```
[OpenEPS] MTX_POS_SET_PurchaseAmount: -2345 (negative for return)
[OpenEPS] MTX_POS_SET_TenderType: 2 (Credit)
[OpenEPS] MTX_POS_SET_TransactionType: 2 (Return)
[OpenEPS] Transaction State: trsNone → trsTenderType
[OpenEPS] Checking return limits for card ****1234
[OpenEPS] Return limit check passed
[HOST] Sending return transaction to host
[HOST] Response: 00 (Approved)
[OpenEPS] Return processed successfully
```

### 3. **Void Transaction**

**Business Purpose**: Cancel a previous transaction (same day only).

**Two Types**:
- **Void Last**: Cancel the most recent transaction
- **Void by Sequence**: Cancel specific transaction by MTX sequence number

**Log Flow Pattern**:
```
[OpenEPS] MTX_POS_SET_TransactionType: 6 (VoidLast)
[OpenEPS] Looking up last transaction for void
[OpenEPS] Found transaction: Seq 123456, Amount $45.67
[OpenEPS] Validating void eligibility
[OpenEPS] Void eligible - same day transaction
[HOST] Sending void to host
[HOST] Response: 00 (Void approved)
[OpenEPS] Original transaction voided successfully
```

### 4. **Balance Inquiry**

**Business Purpose**: Check available balance on debit/EBT card.

**Log Flow Pattern**:
```
[OpenEPS] MTX_POS_SET_TransactionType: 4 (Balance Inquiry)
[OpenEPS] MTX_POS_SET_TenderType: 1 (Debit)
[TERM] Requesting PIN entry
[TERM] PIN entered and encrypted
[HOST] Sending balance inquiry to Shazam
[HOST] Response: 00, Balance: $234.56
[OpenEPS] MTX_POS_SET_AccountBalance: 23456
[OpenEPS] Balance inquiry completed
```

### 5. **Pre-Authorization**

**Business Purpose**: Reserve funds for later completion (hotels, gas stations).

**Two-Step Process**:
1. **Pre-Auth**: Reserve funds
2. **Pre-Auth Completion**: Capture final amount

**Log Flow Pattern**:
```
# Step 1: Pre-Authorization
[OpenEPS] MTX_POS_SET_TransactionType: 12 (PreAuth)
[OpenEPS] MTX_POS_SET_PurchaseAmount: 10000 ($100.00 hold)
[HOST] Pre-auth request sent
[HOST] Response: 00, Auth: 789012
[OpenEPS] Pre-auth approved - funds reserved

# Step 2: Pre-Auth Completion (later)
[OpenEPS] MTX_POS_SET_TransactionType: 13 (PreAuthCompletion)
[OpenEPS] MTX_POS_SET_PurchaseAmount: 7543 ($75.43 actual)
[OpenEPS] MTX_POS_SET_AuthorizationNumber: 789012
[HOST] Pre-auth completion sent
[HOST] Response: 00 (Completed)
[OpenEPS] Final amount captured, excess released
```

---

## Lane-Specific Business Flows

### Attended Lanes (Type 'G' - General)

**Characteristics**:
- Cashier present to assist
- Full transaction capabilities
- Manual entry allowed
- Manager overrides available

**Typical Log Patterns**:
```
[OpenEPS] Lane 3 (Type: G): Cashier ID 1234 signed on
[OpenEPS] Transaction started by cashier
[OpenEPS] Manual card entry allowed
[OpenEPS] Manager override requested
[OpenEPS] Manager ID 5678 approved override
```

### Self-Checkout Lanes (Type 'U' - Unattended)

**Characteristics**:
- Customer operates terminal
- Limited transaction types
- Lower transaction limits
- More security restrictions

**Typical Log Patterns**:
```
[OpenEPS] Lane 5 (Type: U): Self-checkout transaction
[OpenEPS] Transaction limit check: $45.67 < $100.00 (OK)
[OpenEPS] Manual entry disabled for unattended lane
[OpenEPS] Customer assistance requested
[OpenEPS] Attendant override: ID 9999
```

### Fuel Lanes (Type 'F' - Fuel)

**Characteristics**:
- Pre-authorization required
- Pay-at-pump functionality
- Fleet card support
- Outdoor environment considerations

**Typical Log Patterns**:
```
[OpenEPS] Lane 12 (Type: F): Fuel dispenser transaction
[OpenEPS] Pre-auth for $75.00 requested
[OpenEPS] Fleet card detected: ****5678
[OpenEPS] Prompting for odometer reading
[OpenEPS] Odometer: 123456 entered
[OpenEPS] Fuel type: Regular selected
[OpenEPS] Dispensing authorized
[OpenEPS] Final amount: $42.35
[OpenEPS] Pre-auth completion processed
```

---

## Payment Type-Specific Flows

### Credit Card Processing

**Business Flow**:
1. Card presented (swipe, insert, or tap)
2. Card data encrypted and validated
3. Transaction routed to appropriate processor
4. Authorization requested from issuing bank
5. Response processed and receipt generated

**Log Characteristics**:
```
[OpenEPS] Credit card detected: ****1234
[OpenEPS] Card type: Visa
[OpenEPS] Routing to Chase processor
[OpenEPS] EMV chip transaction
[TERM] Please insert card
[TERM] Chip read successful
[OpenEPS] EMV authentication completed
[HOST] Authorization request sent
[HOST] Response: 00 (Approved)
```

### Debit Card Processing

**Business Flow**:
1. Card presented and PIN entered
2. PIN encrypted using DUKPT
3. Transaction routed to debit network
4. Real-time account verification
5. Funds immediately debited

**Log Characteristics**:
```
[OpenEPS] Debit card detected: ****5678
[OpenEPS] Routing to Shazam network
[TERM] Please enter PIN
[TERM] PIN entered and encrypted
[OpenEPS] DUKPT encryption applied
[HOST] Debit authorization sent
[HOST] Response: 00, Available: $234.56
[OpenEPS] Funds debited immediately
```

### EBT (Electronic Benefits Transfer)

**Business Flow**:
1. EBT card presented and PIN entered
2. Item eligibility verification (for SNAP)
3. Benefit balance checked
4. Transaction processed through state system
5. Benefits deducted from account

**Log Characteristics**:
```
[OpenEPS] EBT card detected: ****9012
[OpenEPS] EBT type: Food Stamps (SNAP)
[APL] Checking item eligibility
[APL] Item 12345: SNAP eligible
[APL] Item 67890: Not SNAP eligible
[OpenEPS] SNAP amount: $23.45
[OpenEPS] Non-SNAP amount: $12.34
[TERM] Please enter PIN
[HOST] EBT authorization sent to state
[HOST] Response: 00, SNAP Balance: $156.78
```

### Gift Card Processing

**Business Flow**:
1. Gift card presented or number entered
2. Card balance verified
3. Transaction amount validated against balance
4. Value deducted from card
5. Remaining balance updated

**Log Characteristics**:
```
[OpenEPS] Gift card detected: ****3456
[OpenEPS] Card balance inquiry
[HOST] Balance response: $50.00 available
[OpenEPS] Transaction amount: $35.67
[OpenEPS] Sufficient balance confirmed
[HOST] Gift card redemption sent
[HOST] Response: 00, New Balance: $14.33
```

---

## Offline Processing Business Flows

### When Network Connectivity is Lost

**Business Impact**: System must continue processing transactions to avoid business disruption.

**Offline Approval Criteria**:
- Transaction amount under offline limit
- Card not on negative file
- Merchant category allows offline
- Terminal supports offline processing

**Log Flow Pattern**:
```
[HOST] Connection to Chase lost
[OpenEPS] Network timeout detected
[OpenEPS] Switching to offline mode
[OpenEPS] Offline transaction #1: $45.67
[OpenEPS] Checking offline approval criteria
[OpenEPS] Amount $45.67 < offline limit $100.00 (OK)
[OpenEPS] Card ****1234 not on negative file (OK)
[OpenEPS] Offline approval granted
[OpenEPS] Transaction stored for later transmission
[OpenEPS] Receipt printed with "OFFLINE APPROVED"
```

### Network Recovery and Reconciliation

**Business Process**: When connectivity returns, offline transactions must be confirmed with host.

**Log Flow Pattern**:
```
[HOST] Network connectivity restored
[OpenEPS] Reconnected to Chase host
[OpenEPS] Found 5 offline transactions to upload
[OpenEPS] Uploading offline transaction #1: Seq 123456
[HOST] Offline confirmation: 00 (Approved)
[OpenEPS] Uploading offline transaction #2: Seq 123457
[HOST] Offline confirmation: 05 (Declined - insufficient funds)
[OpenEPS] Offline transaction #2 declined by host
[OpenEPS] Generating reversal for declined offline
[OpenEPS] All offline transactions processed
```

---

## End-of-Day Settlement Process

### Business Purpose
Daily reconciliation of all transactions with payment processors and banks.

### Settlement Components
1. **Transaction Totals**: Count and dollar amounts by card type
2. **Fee Calculations**: Processing fees and interchange
3. **Batch Closure**: Finalize the day's transactions
4. **Funding**: Initiate deposit to merchant account

### Log Flow Pattern
```
[OpenEPS] Starting end-of-day settlement at 23:30:00
[OpenEPS] Gathering transaction totals for the day
[OpenEPS] Credit transactions: 1,247 totaling $45,678.90
[OpenEPS] Debit transactions: 856 totaling $23,456.78
[OpenEPS] EBT transactions: 234 totaling $5,678.90
[OpenEPS] Gift card transactions: 123 totaling $2,345.67
[OpenEPS] Calculating processing fees
[OpenEPS] Total fees: $234.56
[OpenEPS] Net deposit amount: $76,924.69
[HOST] Sending settlement batch to Chase
[HOST] Settlement response: Batch accepted
[OpenEPS] Settlement completed successfully
[OpenEPS] Next settlement: Tomorrow 23:30:00
```

---

## Error Handling Business Flows

### Declined Transaction Handling

**Business Impact**: Customer payment rejected, alternative payment needed.

**Common Decline Reasons**:
- Insufficient funds
- Expired card
- Invalid PIN
- Card reported lost/stolen
- Transaction limit exceeded

**Log Flow Pattern**:
```
[HOST] Response received: 05 (Do Not Honor)
[OpenEPS] Transaction declined by issuer
[OpenEPS] Decline reason: Insufficient funds
[OpenEPS] Customer display: "DECLINED - CONTACT BANK"
[OpenEPS] Cashier display: "TRANSACTION DECLINED"
[OpenEPS] Transaction voided automatically
[OpenEPS] Ready for alternative payment method
```

### Card Read Error Handling

**Business Impact**: Physical card reading problems require alternative input.

**Log Flow Pattern**:
```
[TERM] Card swipe attempted
[TERM] ERROR: Bad card read - magnetic stripe damaged
[TERM] Requesting re-swipe
[TERM] Second swipe attempt failed
[OpenEPS] Magnetic stripe read failed
[OpenEPS] Prompting for manual entry
[TERM] Manual entry mode activated
[OpenEPS] Card number manually entered: ****1234
[OpenEPS] Manual entry transaction flagged
[OpenEPS] Additional verification required
```

### System Recovery Procedures

**Business Impact**: System failures require recovery without losing transaction data.

**Log Flow Pattern**:
```
[OpenEPS] CRITICAL: System exception detected
[OpenEPS] Saving current transaction state
[OpenEPS] Transaction state saved: trsValidateData
[OpenEPS] Initiating system recovery
[OpenEPS] Recovery completed
[OpenEPS] Restoring transaction state: trsValidateData
[OpenEPS] Transaction recovery successful
[OpenEPS] Continuing from validation step
```

---

## Multi-Tender Transaction Flows

### Business Scenario
Customer pays with multiple payment methods (e.g., gift card + credit card).

**Log Flow Pattern**:
```
[OpenEPS] Multi-tender transaction started
[OpenEPS] Total amount: $75.00
[OpenEPS] Tender 1: Gift card for $50.00
[OpenEPS] Gift card balance: $50.00 available
[HOST] Gift card redemption: 00 (Approved)
[OpenEPS] Remaining balance: $25.00
[OpenEPS] Tender 2: Credit card for $25.00
[HOST] Credit card authorization: 00 (Approved)
[OpenEPS] Multi-tender transaction completed
[OpenEPS] Total tendered: $75.00
```

---

## Compliance and Audit Flows

### PCI DSS Compliance Logging

**Business Requirement**: Maintain audit trail for payment card data security.

**Log Characteristics**:
```
[OpenEPS] PCI: Card data encrypted at point of entry
[OpenEPS] PCI: No clear text card data in logs
[OpenEPS] PCI: Transaction logged with masked PAN: ****1234
[OpenEPS] PCI: Encryption key rotated successfully
[OpenEPS] PCI: Audit log entry created
```

### Regulatory Reporting

**Business Requirement**: Generate reports for various regulatory bodies.

**Log Flow Pattern**:
```
[OpenEPS] Generating daily EBT report for state agency
[OpenEPS] EBT transactions: 234 totaling $5,678.90
[OpenEPS] SNAP benefits used: $4,123.45
[OpenEPS] Cash benefits used: $1,555.45
[OpenEPS] Report generated: EBT_Daily_20241211.xml
[OpenEPS] Report uploaded to state system
[OpenEPS] Upload confirmation received
```

---

## Performance Monitoring Business Impact

### Transaction Throughput Monitoring

**Business Metric**: Transactions per minute during peak hours.

**Log Pattern**:
```
[OpenEPS] Performance: Peak hour started (12:00 PM)
[OpenEPS] Performance: Current throughput 45 trx/min
[OpenEPS] Performance: Average response time 2.3 seconds
[OpenEPS] Performance: Peak throughput 67 trx/min
[OpenEPS] Performance: Peak hour ended (1:00 PM)
```

### Customer Experience Metrics

**Business Impact**: Long transaction times affect customer satisfaction.

**Log Pattern**:
```
[OpenEPS] Customer experience: Transaction started
[OpenEPS] Customer experience: Card read time 1.2 seconds
[OpenEPS] Customer experience: Host response time 2.8 seconds
[OpenEPS] Customer experience: Receipt print time 0.5 seconds
[OpenEPS] Customer experience: Total time 4.5 seconds
[OpenEPS] Customer experience: Within acceptable range (<6 seconds)
```

---

## Troubleshooting Business Impact

### Revenue Impact Analysis

When analyzing logs, consider the business impact:

**High Impact Issues** (Stop processing immediately):
- All transactions failing
- Security breaches
- Data corruption
- Compliance violations

**Medium Impact Issues** (Address within hours):
- Specific payment types failing
- Performance degradation
- Intermittent connectivity issues
- Configuration problems

**Low Impact Issues** (Address during maintenance):
- Minor display issues
- Non-critical warnings
- Cosmetic problems
- Enhancement requests

### Business Continuity Planning

**Log Patterns for Business Continuity**:
```
[OpenEPS] Business continuity: Primary system failure detected
[OpenEPS] Business continuity: Switching to backup system
[OpenEPS] Business continuity: Backup system online
[OpenEPS] Business continuity: Transaction processing resumed
[OpenEPS] Business continuity: Primary system recovery initiated
[OpenEPS] Business continuity: Failback completed
```

---

## Conclusion

Understanding business process flows in OpenEPS logs enables you to:

1. **Quickly identify business impact** of technical issues
2. **Prioritize problems** based on revenue and customer impact
3. **Trace complete business transactions** across system components
4. **Recognize normal vs. abnormal business patterns**
5. **Provide meaningful updates** to business stakeholders

**Key Takeaway**: Every log entry represents a business event. Understanding the business context makes technical troubleshooting more effective and helps you communicate impact to non-technical stakeholders.

Use this guide alongside the Architecture Guide and Log Analysis Guide for comprehensive OpenEPS system understanding.
